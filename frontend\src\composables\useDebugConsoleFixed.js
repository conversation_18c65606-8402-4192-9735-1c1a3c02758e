/**
 * 调试控制台组合式API - 修复版本
 * 提供调试控制台相关的状态管理和API调用功能
 */

import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'

// API基础URL
const API_BASE_URL = 'http://localhost:8000/api/v1'

// 全局状态
const debugState = reactive({
  // 系统状态
  systemStatus: {
    status: '未知',
    uptime: 0,
    scheduler_status: '未知',
    active_tasks: 0,
    completed_tasks: 0,
    failed_tasks: 0,
    current_game_state: null
  },
  
  // 连接状态
  connectionStatus: {
    api: false,
    websocket: false,
    lastUpdate: null
  },
  
  // 测试结果
  testResults: [],
  
  // 日志数据
  logs: [],
  
  // 配置数据
  config: {},
  
  // 错误信息
  errors: []
})

// 加载状态
const loading = reactive({
  systemStatus: false,
  config: false,
  test: false
})

/**
 * 调试控制台主要功能
 */
export function useDebugConsole() {
  
  /**
   * 获取系统状态
   */
  const getSystemStatus = async () => {
    loading.systemStatus = true
    try {
      const response = await fetch(`${API_BASE_URL}/status`)
      if (response.ok) {
        const data = await response.json()
        debugState.systemStatus = data
        debugState.connectionStatus.api = true
        debugState.connectionStatus.lastUpdate = new Date()
        return data
      } else {
        throw new Error(`HTTP ${response.status}`)
      }
    } catch (error) {
      console.error('获取系统状态失败:', error)
      debugState.connectionStatus.api = false
      debugState.errors.push({
        type: 'api_error',
        message: '获取系统状态失败',
        error: error.message,
        timestamp: new Date()
      })
      throw error
    } finally {
      loading.systemStatus = false
    }
  }

  /**
   * 控制系统操作
   */
  const controlSystem = async (action, parameters = null) => {
    try {
      const response = await fetch(`${API_BASE_URL}/control`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action,
          parameters
        })
      })
      
      if (response.ok) {
        const result = await response.json()
        
        // 添加操作结果到测试结果
        debugState.testResults.unshift({
          id: Date.now(),
          type: 'system_control',
          action,
          result,
          timestamp: new Date(),
          success: true
        })
        
        // 刷新系统状态
        await getSystemStatus()
        
        return result
      } else {
        throw new Error(`HTTP ${response.status}`)
      }
    } catch (error) {
      console.error('系统控制失败:', error)
      debugState.testResults.unshift({
        id: Date.now(),
        type: 'system_control',
        action,
        result: null,
        error: error.message,
        timestamp: new Date(),
        success: false
      })
      throw error
    }
  }

  /**
   * 获取配置信息
   */
  const getConfig = async () => {
    loading.config = true
    try {
      const response = await fetch(`${API_BASE_URL}/config`)
      if (response.ok) {
        const data = await response.json()
        debugState.config = data
        return data
      } else {
        throw new Error(`HTTP ${response.status}`)
      }
    } catch (error) {
      console.error('获取配置失败:', error)
      debugState.errors.push({
        type: 'config_error',
        message: '获取配置失败',
        error: error.message,
        timestamp: new Date()
      })
      throw error
    } finally {
      loading.config = false
    }
  }

  /**
   * 更新配置
   */
  const updateConfig = async (section, key, value) => {
    try {
      const response = await fetch(`${API_BASE_URL}/config`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          section,
          key,
          value
        })
      })
      
      if (response.ok) {
        const result = await response.json()
        
        // 更新本地配置状态
        if (!debugState.config[section]) {
          debugState.config[section] = {}
        }
        debugState.config[section][key] = value
        
        return result
      } else {
        throw new Error(`HTTP ${response.status}`)
      }
    } catch (error) {
      console.error('更新配置失败:', error)
      throw error
    }
  }

  /**
   * 测试截图功能
   */
  const testScreenshot = async (config = {}) => {
    const defaultConfig = {
      mode: 'window',
      format: 'png',
      quality: 90,
      save_to_disk: true,
      filename_prefix: 'debug_test'
    }
    
    const screenshotConfig = { ...defaultConfig, ...config }
    
    try {
      const response = await fetch(`${API_BASE_URL}/screenshot/capture`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          config: screenshotConfig
        })
      })
      
      if (response.ok) {
        return await response.json()
      } else {
        throw new Error(`HTTP ${response.status}`)
      }
    } catch (error) {
      console.error('截图测试失败:', error)
      throw error
    }
  }

  /**
   * 获取截图预览
   */
  const getScreenshotPreview = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/screenshot/preview`)
      
      if (response.ok) {
        // 将blob转换为URL
        const blob = await response.blob()
        const imageUrl = URL.createObjectURL(blob)
        return imageUrl
      } else {
        throw new Error(`HTTP ${response.status}`)
      }
    } catch (error) {
      console.error('获取截图预览失败:', error)
      throw error
    }
  }

  /**
   * 执行功能测试
   */
  const runFunctionTest = async (testType, parameters = {}) => {
    loading.test = true
    const testId = Date.now()
    
    try {
      let result = null
      
      switch (testType) {
        case 'screenshot':
          result = await testScreenshot(parameters)
          break
        case 'system_status':
          result = await getSystemStatus()
          break
        case 'config_load':
          result = await getConfig()
          break
        default:
          throw new Error(`未知的测试类型: ${testType}`)
      }
      
      // 记录测试结果
      debugState.testResults.unshift({
        id: testId,
        type: testType,
        parameters,
        result,
        timestamp: new Date(),
        success: true,
        duration: Date.now() - testId
      })
      
      return result
    } catch (error) {
      console.error('功能测试失败:', error)
      debugState.testResults.unshift({
        id: testId,
        type: testType,
        parameters,
        result: null,
        error: error.message,
        timestamp: new Date(),
        success: false,
        duration: Date.now() - testId
      })
      throw error
    } finally {
      loading.test = false
    }
  }

  /**
   * 清理测试结果
   */
  const clearTestResults = () => {
    debugState.testResults = []
  }

  /**
   * 清理错误信息
   */
  const clearErrors = () => {
    debugState.errors = []
  }

  /**
   * 添加日志
   */
  const addLog = (level, message, data = null) => {
    debugState.logs.unshift({
      id: Date.now(),
      level,
      message,
      data,
      timestamp: new Date()
    })
    
    // 限制日志数量
    if (debugState.logs.length > 1000) {
      debugState.logs = debugState.logs.slice(0, 1000)
    }
  }

  // 计算属性
  const isSystemHealthy = computed(() => {
    return debugState.connectionStatus.api && 
           debugState.systemStatus.status === '运行中'
  })

  const recentErrors = computed(() => {
    return debugState.errors.slice(0, 10)
  })

  const recentTestResults = computed(() => {
    return debugState.testResults.slice(0, 20)
  })

  return {
    // 状态
    debugState,
    loading,
    
    // 计算属性
    isSystemHealthy,
    recentErrors,
    recentTestResults,
    
    // 方法
    getSystemStatus,
    controlSystem,
    getConfig,
    updateConfig,
    runFunctionTest,
    testScreenshot,
    getScreenshotPreview,
    clearTestResults,
    clearErrors,
    addLog
  }
}
