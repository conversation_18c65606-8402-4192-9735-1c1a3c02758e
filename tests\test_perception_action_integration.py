"""
感知-行动模块集成测试
测试感知模块和行动模块的协作功能
"""

import pytest
import numpy as np
from unittest.mock import Mock, patch, MagicMock
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.modules.perception import PerceptionModule, MatchResult
from src.modules.action import ActionController
from src.core.data_structures import Action, ActionType, GameScene


class TestPerceptionActionIntegration:
    """测试感知-行动模块集成"""
    
    def setup_method(self):
        """测试前准备"""
        # 创建模拟的感知模块
        self.mock_perception = Mock(spec=PerceptionModule)

        # 创建模拟的屏幕捕获器
        mock_screen_capture = Mock()
        mock_screen_capture.get_window_rect.return_value = {"left": 0, "top": 0, "width": 1920, "height": 1080}
        mock_screen_capture.is_game_window_active.return_value = True
        self.mock_perception.screen_capture = mock_screen_capture

        # 创建行动控制器（使用模拟的感知模块）
        with patch.object(Path, 'exists', return_value=True):
            self.action_controller = ActionController(
                dmm_player_path="C:/fake/dmm/player.exe",
                perception_module=self.mock_perception
            )
    
    def test_click_ui_element_integration(self):
        """测试点击UI元素的完整流程"""
        # 模拟感知模块找到UI元素
        mock_match = MatchResult(100, 150, 80, 40, 0.9, "test_button")
        self.mock_perception.find_ui_element.return_value = mock_match
        
        # 模拟输入模拟器执行成功
        with patch.object(self.action_controller.input_simulator, 'execute_action', return_value=True):
            result = self.action_controller.click_ui_element("test_button")
            
            # 验证结果
            assert result is True
            
            # 验证感知模块被正确调用
            self.mock_perception.find_ui_element.assert_called_once_with("test_button")
            
            # 验证操作历史记录
            assert len(self.action_controller.action_history) == 1
            history = self.action_controller.action_history[0]
            assert history["action_type"] == "click"
            assert history["success"] is True
    
    def test_wait_for_scene_and_click_integration(self):
        """测试等待场景并点击的完整流程"""
        # 模拟感知模块等待场景成功
        self.mock_perception.wait_for_scene.return_value = True
        
        # 模拟找到UI元素
        mock_match = MatchResult(200, 250, 100, 50, 0.85, "menu_button")
        self.mock_perception.find_ui_element.return_value = mock_match
        
        # 模拟输入模拟器执行成功
        with patch.object(self.action_controller.input_simulator, 'execute_action', return_value=True):
            result = self.action_controller.wait_for_scene_and_click(
                GameScene.MAIN_MENU, "menu_button"
            )
            
            # 验证结果
            assert result is True
            
            # 验证感知模块调用
            self.mock_perception.wait_for_scene.assert_called_once_with(
                GameScene.MAIN_MENU, timeout=15.0
            )
            self.mock_perception.find_ui_element.assert_called_once_with("menu_button")
    
    def test_wait_for_scene_timeout(self):
        """测试等待场景超时"""
        # 模拟感知模块等待场景超时
        self.mock_perception.wait_for_scene.return_value = False
        
        result = self.action_controller.wait_for_scene_and_click(
            GameScene.PRODUCE_MAIN, "lesson_button"
        )
        
        # 验证结果
        assert result is False
        
        # 验证只调用了等待场景，没有调用点击
        self.mock_perception.wait_for_scene.assert_called_once()
        self.mock_perception.find_ui_element.assert_not_called()
    
    def test_ui_element_not_found(self):
        """测试UI元素未找到的情况"""
        # 模拟感知模块未找到UI元素
        self.mock_perception.find_ui_element.return_value = None
        
        result = self.action_controller.click_ui_element("nonexistent_button", timeout=0.1)
        
        # 验证结果
        assert result is False
        
        # 验证感知模块被调用
        self.mock_perception.find_ui_element.assert_called()
    
    def test_ui_element_low_confidence(self):
        """测试UI元素置信度过低的情况"""
        # 模拟感知模块找到低置信度的UI元素
        mock_match = MatchResult(100, 150, 80, 40, 0.5, "low_confidence_button")
        self.mock_perception.find_ui_element.return_value = mock_match
        
        result = self.action_controller.click_ui_element(
            "low_confidence_button", 
            confidence_threshold=0.8,
            timeout=0.1
        )
        
        # 验证结果
        assert result is False
    
    def test_ensure_game_focus_integration(self):
        """测试确保游戏焦点的集成功能"""
        # 模拟屏幕捕获器
        mock_screen_capture = Mock()
        mock_screen_capture.is_game_window_active.side_effect = [False, True]  # 第一次未激活，第二次激活
        mock_screen_capture.bring_game_window_to_front.return_value = True
        
        self.mock_perception.screen_capture = mock_screen_capture
        
        result = self.action_controller.ensure_game_focus()
        
        # 验证结果
        assert result is True
        
        # 验证调用序列
        assert mock_screen_capture.is_game_window_active.call_count == 2
        mock_screen_capture.bring_game_window_to_front.assert_called_once()
    
    def test_action_with_verification(self):
        """测试带验证的操作执行"""
        from src.modules.action.action_verifier import VerificationResult

        # 创建带验证函数的操作
        verification_mock = Mock(return_value=VerificationResult.SUCCESS)
        action = Action(
            action_type=ActionType.CLICK,
            target=(100, 200),
            description="测试验证操作",
            verify_func=verification_mock
        )

        # 模拟输入模拟器执行成功
        with patch.object(self.action_controller.input_simulator, 'execute_action', return_value=True):
            result = self.action_controller.execute_action(action, verify=True)

            # 验证结果
            assert result is True

            # 验证验证函数被调用
            verification_mock.assert_called_once()
    
    def test_action_sequence_with_perception(self):
        """测试结合感知的操作序列"""
        # 创建操作序列
        actions = [
            Action(ActionType.CLICK, (100, 100), "点击按钮1"),
            Action(ActionType.CLICK, (200, 200), "点击按钮2"),
            Action(ActionType.KEYPRESS, "enter", "按回车键")
        ]
        
        # 模拟所有操作都成功
        with patch.object(self.action_controller, 'execute_action', return_value=True), \
             patch('time.sleep'):
            
            result = self.action_controller.execute_action_sequence(actions)
            
            # 验证结果
            assert result is True
            
            # 验证执行次数
            assert self.action_controller.execute_action.call_count == 3
    
    def test_safety_check_integration(self):
        """测试安全检查集成"""
        # 模拟不安全的位置
        unsafe_action = Action(
            action_type=ActionType.CLICK,
            target=(5, 5),  # 屏幕边缘位置
            description="不安全点击"
        )

        # 临时禁用错误恢复以确保测试结果准确
        original_auto_recovery = self.action_controller.enable_auto_recovery
        self.action_controller.enable_auto_recovery = False

        try:
            # 模拟输入模拟器的安全检查
            with patch.object(self.action_controller.input_simulator, 'is_position_safe', return_value=False):
                result = self.action_controller.execute_action(unsafe_action)

                # 验证结果
                assert result is False
        finally:
            # 恢复原始设置
            self.action_controller.enable_auto_recovery = original_auto_recovery
    
    def test_error_recovery_integration(self):
        """测试错误恢复集成"""
        action = Action(
            action_type=ActionType.CLICK,
            target=(100, 200),
            description="测试错误恢复"
        )
        
        # 模拟第一次失败，恢复后成功
        with patch.object(self.action_controller.input_simulator, 'execute_action', side_effect=[False, True]), \
             patch.object(self.action_controller, 'ensure_game_focus', return_value=True), \
             patch('time.sleep'):
            
            result = self.action_controller.execute_action(action)
            
            # 验证最终成功
            assert result is True
            
            # 验证重试机制
            assert self.action_controller.input_simulator.execute_action.call_count == 2


class TestPerceptionActionWorkflow:
    """测试感知-行动工作流程"""
    
    def setup_method(self):
        """测试前准备"""
        self.mock_perception = Mock(spec=PerceptionModule)

        # 创建模拟的屏幕捕获器
        mock_screen_capture = Mock()
        mock_screen_capture.get_window_rect.return_value = {"left": 0, "top": 0, "width": 1920, "height": 1080}
        mock_screen_capture.is_game_window_active.return_value = True
        self.mock_perception.screen_capture = mock_screen_capture

        with patch.object(Path, 'exists', return_value=True):
            self.action_controller = ActionController(
                dmm_player_path="C:/fake/dmm/player.exe",
                perception_module=self.mock_perception
            )
    
    def test_complete_ui_interaction_workflow(self):
        """测试完整的UI交互工作流程"""
        # 场景1: 等待主菜单出现
        self.mock_perception.wait_for_scene.return_value = True
        
        # 场景2: 找到并点击育成按钮
        produce_button_match = MatchResult(400, 300, 120, 60, 0.9, "produce_button")
        self.mock_perception.find_ui_element.return_value = produce_button_match
        
        # 模拟输入执行成功
        with patch.object(self.action_controller.input_simulator, 'execute_action', return_value=True):
            
            # 执行工作流程
            step1_result = self.action_controller.wait_for_scene_and_click(
                GameScene.MAIN_MENU, "produce_button"
            )
            
            # 验证第一步成功
            assert step1_result is True
            
            # 验证调用序列
            self.mock_perception.wait_for_scene.assert_called_with(
                GameScene.MAIN_MENU, timeout=15.0
            )
            self.mock_perception.find_ui_element.assert_called_with("produce_button")
    
    def test_multi_step_navigation_workflow(self):
        """测试多步导航工作流程"""
        # 模拟多个UI元素查找
        ui_elements = {
            "main_menu_button": MatchResult(100, 100, 80, 40, 0.9, "main_menu_button"),
            "produce_button": MatchResult(200, 200, 100, 50, 0.85, "produce_button"),
            "start_button": MatchResult(300, 300, 90, 45, 0.88, "start_button")
        }
        
        def mock_find_ui_element(template_name):
            return ui_elements.get(template_name)
        
        self.mock_perception.find_ui_element.side_effect = mock_find_ui_element
        
        # 模拟输入执行成功
        with patch.object(self.action_controller.input_simulator, 'execute_action', return_value=True):
            
            # 执行多步导航
            results = []
            for template_name in ui_elements.keys():
                result = self.action_controller.click_ui_element(template_name)
                results.append(result)
            
            # 验证所有步骤都成功
            assert all(results)
            
            # 验证调用次数
            assert self.mock_perception.find_ui_element.call_count == 3
    
    def test_conditional_workflow(self):
        """测试条件性工作流程"""
        # 模拟条件检查：如果找到特定UI元素则执行操作
        special_button_match = MatchResult(150, 250, 70, 35, 0.92, "special_button")
        
        # 第一次查找找到元素，第二次查找未找到
        self.mock_perception.find_ui_element.side_effect = [special_button_match, None]
        
        with patch.object(self.action_controller.input_simulator, 'execute_action', return_value=True):
            
            # 第一次应该成功
            result1 = self.action_controller.click_ui_element("special_button")
            assert result1 is True
            
            # 第二次应该失败（元素不存在）
            result2 = self.action_controller.click_ui_element("special_button", timeout=0.1)
            assert result2 is False


if __name__ == "__main__":
    pytest.main([__file__])
