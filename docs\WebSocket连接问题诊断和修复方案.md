# WebSocket连接问题诊断和修复方案

## 一、问题分析

### 1.1 错误现象总结
- **连接目标**: `ws://localhost:3000/ws`
- **错误代码**: 1006（异常关闭）
- **重连失败**: 连续5次重连均失败
- **影响范围**: 截图工具的实时功能无法正常工作

### 1.2 根本原因分析
通过代码分析发现以下关键问题：

1. **端口不匹配问题**：
   - 前端Vite开发服务器运行在端口3000
   - 后端FastAPI服务器配置在端口8001
   - WebSocket连接尝试连接到localhost:3000，但实际服务在8001

2. **WebSocket URL构建逻辑问题**：
   - `useWebSocket.js`中使用`window.location.host`构建WebSocket URL
   - 在开发环境下，这会指向前端开发服务器而非后端API服务器

3. **代理配置问题**：
   - Vite配置中有WebSocket代理设置，但可能存在配置问题

## 二、解决方案设计

### 2.1 短期修复方案（立即可用）

#### 方案A：修复WebSocket URL构建逻辑
- 修改`useWebSocket.js`中的连接逻辑
- 在开发环境下使用正确的后端服务器地址
- 在生产环境下保持现有逻辑

#### 方案B：优化Vite代理配置
- 确保WebSocket代理正确转发到后端服务器
- 添加更详细的代理日志和错误处理

### 2.2 长期优化方案

#### 方案C：环境变量配置
- 使用环境变量管理不同环境的WebSocket连接地址
- 提供更灵活的配置选项

#### 方案D：连接状态管理优化
- 改进重连机制
- 添加更详细的错误日志和用户反馈

## 三、实施步骤

### 3.1 第一阶段：问题诊断确认
1. 检查后端服务器运行状态
2. 验证端口8001上的WebSocket服务
3. 测试直接WebSocket连接

### 3.2 第二阶段：修复WebSocket连接
1. 修改`useWebSocket.js`的连接逻辑
2. 更新Vite代理配置
3. 添加环境变量支持

### 3.3 第三阶段：测试和验证
1. 启动后端服务器
2. 启动前端开发服务器
3. 测试WebSocket连接和功能
4. 验证重连机制

### 3.4 第四阶段：优化和文档
1. 优化错误处理和用户体验
2. 更新技术文档
3. 创建故障排除指南

## 四、技术实现细节

### 4.1 WebSocket连接修复
```javascript
// 修复后的连接逻辑
const getWebSocketUrl = () => {
  if (import.meta.env.DEV) {
    // 开发环境：直接连接到后端服务器
    return 'ws://localhost:8001/ws'
  } else {
    // 生产环境：使用相对路径
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
    return `${protocol}//${window.location.host}/ws`
  }
}
```

### 4.2 环境变量配置
```javascript
// 使用环境变量的灵活配置
const wsUrl = import.meta.env.VITE_WS_URL || getWebSocketUrl()
```

### 4.3 改进的错误处理
```javascript
// 更详细的错误日志和用户反馈
socket.value.onerror = (error) => {
  console.error('WebSocket连接错误:', error)
  console.error('尝试连接的URL:', wsUrl)
  console.error('当前环境:', import.meta.env.MODE)
  // 用户友好的错误提示
}
```

## 五、预期效果

### 5.1 立即效果
- WebSocket连接成功建立
- 截图工具实时功能恢复正常
- 消除连接错误和重连循环

### 5.2 长期效果
- 更稳定的WebSocket连接管理
- 更好的错误处理和用户体验
- 更灵活的环境配置支持

## 六、风险评估

### 6.1 低风险
- 修改WebSocket连接逻辑：影响范围明确，易于回滚

### 6.2 注意事项
- 确保生产环境配置不受影响
- 测试不同网络环境下的连接稳定性
- 验证所有WebSocket功能正常工作

## 七、实施进度更新

### 7.1 已完成的修复
1. ✅ **端口配置修复**：
   - 修复了Vite配置中的端口不匹配问题（8001 → 8000）
   - 更新了WebSocket代理配置

2. ✅ **WebSocket连接逻辑优化**：
   - 添加了环境检测逻辑，开发环境直接连接到后端服务器
   - 改进了错误处理和日志输出
   - 添加了详细的连接状态信息

3. ✅ **测试环境搭建**：
   - 创建了简单的WebSocket测试服务器
   - 创建了WebSocket连接测试页面

### 7.2 当前问题分析
经过测试发现，问题可能在于：
1. **前端路由问题**：截图工具页面可能无法正确加载
2. **代理配置问题**：Vite代理可能没有正确转发WebSocket连接
3. **组件初始化问题**：useWebSocket组合式函数可能没有正确初始化

### 7.3 下一步行动计划
1. **验证前端路由**：确保截图工具页面能正确加载
2. **测试代理配置**：验证Vite代理是否正确工作
3. **简化测试**：创建最小化的WebSocket连接测试
4. **修复原始服务器**：解决原始后端服务器的初始化问题

## 八、后续维护

### 8.1 监控要点
- WebSocket连接成功率
- 重连频率和成功率
- 用户反馈的连接问题

### 8.2 优化方向
- 实现更智能的重连策略
- 添加连接质量监控
- 提供连接状态的可视化反馈
