"""
使用print语句调试枚举转换问题
"""

import requests
import json
import time


def test_screenshot_with_debug():
    """测试截图API并查看调试输出"""
    print("🧪 测试截图API - 查看调试输出")
    print("=" * 50)
    
    # 等待服务器启动
    time.sleep(2)
    
    # 检查服务器状态
    try:
        health_response = requests.get("http://localhost:8000/health", timeout=5)
        if health_response.status_code != 200:
            print("❌ 服务器未运行")
            return False
        print("✅ 服务器运行正常")
    except:
        print("❌ 无法连接到服务器")
        return False
    
    # 发送截图请求
    test_data = {
        "config": {
            "mode": "window",
            "format": "png",
            "quality": 90,
            "save_to_disk": True,
            "filename_prefix": "debug_print_test"
        }
    }
    
    print(f"\n📡 发送请求:")
    print(f"   URL: http://localhost:8000/api/v1/screenshot/capture")
    print(f"   数据: {json.dumps(test_data, indent=2)}")
    print(f"\n📝 请查看后端控制台输出，寻找以下调试信息:")
    print(f"   🔍 [DEBUG] API接收到的配置")
    print(f"   🔍 [DEBUG] 转换后的配置")
    print(f"   🔍 [DEBUG] _convert_screenshot_config 接收到配置")
    print(f"   🔍 [DEBUG] 原始模式值")
    print(f"   🔍 [DEBUG] 最终转换的配置对象")
    print(f"\n⏳ 发送请求...")
    
    try:
        response = requests.post(
            "http://localhost:8000/api/v1/screenshot/capture",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"\n📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API调用成功!")
            
            if result.get("success"):
                print(f"🎉 截图执行成功: {result.get('filename')}")
                return True
            else:
                print(f"⚠️ 截图执行失败: {result.get('error_message')}")
                return False
        else:
            print(f"❌ API调用失败")
            print(f"📋 错误响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False


def main():
    """主函数"""
    print("🚀 开始调试测试")
    print("📝 注意：请同时查看后端控制台的调试输出")
    print("=" * 60)
    
    success = test_screenshot_with_debug()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试完成 - 请检查后端调试输出")
    else:
        print("❌ 测试失败 - 请检查后端调试输出和错误信息")
    
    print("\n📝 如果看到调试输出，说明我们的代码被执行了")
    print("📝 如果没有看到调试输出，说明请求没有到达我们的API端点")
    
    return success


if __name__ == "__main__":
    main()
