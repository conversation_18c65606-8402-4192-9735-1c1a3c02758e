"""
游戏截图数据收集器模块
负责游戏截图的收集、管理和存储
"""

import os
import time
import uuid
import asyncio
import base64
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, AsyncGenerator
from dataclasses import dataclass, asdict
from enum import Enum
import cv2
import numpy as np

from ..utils.logger import get_logger
from ..core.data_structures import GakumasuBotException
from .perception.enhanced_screen_capture import EnhancedScreenCapture, ScreenCaptureError, GameWindowNotFound


class ScreenshotMode(Enum):
    """截图模式枚举"""
    FULLSCREEN = "fullscreen"  # 全屏截图
    WINDOW = "window"          # 窗口截图
    REGION = "region"          # 区域截图


class ScreenshotFormat(Enum):
    """截图格式枚举"""
    PNG = "png"
    JPEG = "jpeg"
    BMP = "bmp"


@dataclass
class ScreenshotConfig:
    """截图配置"""
    mode: ScreenshotMode = ScreenshotMode.WINDOW
    format: ScreenshotFormat = ScreenshotFormat.PNG
    quality: int = 90  # JPEG质量 (1-100)
    region: Optional[Dict[str, int]] = None  # 区域坐标 {"x": 0, "y": 0, "width": 100, "height": 100}
    save_to_disk: bool = True
    filename_prefix: str = "screenshot"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)


@dataclass
class ScreenshotResult:
    """截图结果"""
    id: str
    filename: str
    filepath: str
    config: ScreenshotConfig
    timestamp: datetime
    file_size: int
    image_size: tuple  # (width, height)
    success: bool
    error_message: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = asdict(self)
        result['timestamp'] = self.timestamp.isoformat()
        result['config'] = self.config.to_dict()
        return result


@dataclass
class ScreenshotRecord:
    """截图历史记录"""
    id: str
    filename: str
    filepath: str
    thumbnail_path: Optional[str]
    timestamp: datetime
    file_size: int
    image_size: tuple
    config: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = asdict(self)
        result['timestamp'] = self.timestamp.isoformat()
        return result


class ScreenshotCollectorError(GakumasuBotException):
    """截图收集器错误"""
    pass


class ScreenshotStorageManager:
    """截图存储管理器"""
    
    def __init__(self, base_dir: str = "screenshots"):
        """
        初始化存储管理器
        
        Args:
            base_dir: 基础存储目录
        """
        self.base_dir = Path(base_dir)
        self.thumbnails_dir = self.base_dir / "thumbnails"
        self.logger = get_logger("ScreenshotStorageManager")
        
        # 创建目录
        self.base_dir.mkdir(exist_ok=True)
        self.thumbnails_dir.mkdir(exist_ok=True)
        
        self.logger.info(f"截图存储管理器初始化完成，存储目录: {self.base_dir}")
    
    def generate_filename(self, config: ScreenshotConfig) -> str:
        """
        生成文件名
        
        Args:
            config: 截图配置
            
        Returns:
            生成的文件名
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        unique_id = str(uuid.uuid4())[:8]
        extension = config.format.value
        
        return f"{config.filename_prefix}_{timestamp}_{unique_id}.{extension}"
    
    def get_filepath(self, filename: str) -> Path:
        """
        获取文件完整路径
        
        Args:
            filename: 文件名
            
        Returns:
            文件完整路径
        """
        return self.base_dir / filename
    
    def save_image(self, image: np.ndarray, config: ScreenshotConfig, filename: str) -> bool:
        """
        保存图像到文件
        
        Args:
            image: 图像数组
            config: 截图配置
            filename: 文件名
            
        Returns:
            是否保存成功
        """
        try:
            filepath = self.get_filepath(filename)
            
            # 根据格式保存
            if config.format == ScreenshotFormat.PNG:
                cv2.imwrite(str(filepath), image)
            elif config.format == ScreenshotFormat.JPEG:
                cv2.imwrite(str(filepath), image, [cv2.IMWRITE_JPEG_QUALITY, config.quality])
            elif config.format == ScreenshotFormat.BMP:
                cv2.imwrite(str(filepath), image)
            
            self.logger.debug(f"图像已保存: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存图像失败: {e}")
            return False
    
    def create_thumbnail(self, image: np.ndarray, filename: str, size: tuple = (200, 150)) -> Optional[str]:
        """
        创建缩略图
        
        Args:
            image: 原始图像
            filename: 原始文件名
            size: 缩略图尺寸
            
        Returns:
            缩略图文件名，失败返回None
        """
        try:
            # 生成缩略图文件名
            name_parts = filename.rsplit('.', 1)
            thumbnail_filename = f"{name_parts[0]}_thumb.{name_parts[1]}"
            thumbnail_path = self.thumbnails_dir / thumbnail_filename
            
            # 调整图像尺寸
            height, width = image.shape[:2]
            aspect_ratio = width / height
            
            if aspect_ratio > size[0] / size[1]:
                new_width = size[0]
                new_height = int(size[0] / aspect_ratio)
            else:
                new_height = size[1]
                new_width = int(size[1] * aspect_ratio)
            
            thumbnail = cv2.resize(image, (new_width, new_height))
            
            # 保存缩略图
            cv2.imwrite(str(thumbnail_path), thumbnail)
            
            self.logger.debug(f"缩略图已创建: {thumbnail_path}")
            return thumbnail_filename
            
        except Exception as e:
            self.logger.error(f"创建缩略图失败: {e}")
            return None
    
    def delete_file(self, filename: str) -> bool:
        """
        删除文件
        
        Args:
            filename: 文件名
            
        Returns:
            是否删除成功
        """
        try:
            filepath = self.get_filepath(filename)
            if filepath.exists():
                filepath.unlink()
                
                # 删除对应的缩略图
                name_parts = filename.rsplit('.', 1)
                thumbnail_filename = f"{name_parts[0]}_thumb.{name_parts[1]}"
                thumbnail_path = self.thumbnails_dir / thumbnail_filename
                if thumbnail_path.exists():
                    thumbnail_path.unlink()
                
                self.logger.debug(f"文件已删除: {filepath}")
                return True
            else:
                self.logger.warning(f"文件不存在: {filepath}")
                return False
                
        except Exception as e:
            self.logger.error(f"删除文件失败: {e}")
            return False
    
    def get_file_info(self, filename: str) -> Optional[Dict[str, Any]]:
        """
        获取文件信息
        
        Args:
            filename: 文件名
            
        Returns:
            文件信息字典，失败返回None
        """
        try:
            filepath = self.get_filepath(filename)
            if not filepath.exists():
                return None
            
            stat = filepath.stat()
            
            # 读取图像获取尺寸
            image = cv2.imread(str(filepath))
            if image is not None:
                height, width = image.shape[:2]
                image_size = (width, height)
            else:
                image_size = (0, 0)
            
            return {
                "filename": filename,
                "filepath": str(filepath),
                "file_size": stat.st_size,
                "image_size": image_size,
                "created_time": datetime.fromtimestamp(stat.st_ctime),
                "modified_time": datetime.fromtimestamp(stat.st_mtime)
            }
            
        except Exception as e:
            self.logger.error(f"获取文件信息失败: {e}")
            return None


class ScreenshotCollector:
    """截图数据收集器"""
    
    def __init__(self,
                 game_window_title: str = "gakumas",
                 storage_dir: str = "screenshots"):
        """
        初始化截图收集器

        Args:
            game_window_title: 游戏窗口标题
            storage_dir: 存储目录
        """
        self.logger = get_logger("ScreenshotCollector")

        # 初始化组件
        self.screen_capture = EnhancedScreenCapture(game_window_title)
        self.storage_manager = ScreenshotStorageManager(storage_dir)

        # 历史记录
        self.history: List[ScreenshotRecord] = []
        self.max_history_size = 1000

        # 预览相关
        self.preview_active = False
        self.preview_fps = 2  # 预览帧率

        # 从文件系统重建历史记录
        self._rebuild_history_from_filesystem()

        self.logger.info("截图收集器初始化完成")
    
    async def capture_single_shot(self, config: ScreenshotConfig) -> ScreenshotResult:
        """
        执行单次截图

        Args:
            config: 截图配置

        Returns:
            截图结果
        """
        screenshot_id = str(uuid.uuid4())
        timestamp = datetime.now()
        start_time = time.time()

        try:
            self.logger.info(f"开始截图，模式: {config.mode.value}, ID: {screenshot_id}")

            # 验证配置
            self._validate_config(config)

            # 根据模式捕获截图
            image = await self._capture_by_mode(config)

            if image is None:
                raise ScreenshotCollectorError("截图捕获返回空图像")

            # 验证图像有效性
            if image.size == 0:
                raise ScreenshotCollectorError("捕获的图像为空")

            # 生成文件名
            filename = self.storage_manager.generate_filename(config)
            filepath = str(self.storage_manager.get_filepath(filename))

            # 保存到磁盘
            thumbnail_filename = None
            if config.save_to_disk:
                success = self.storage_manager.save_image(image, config, filename)
                if not success:
                    raise ScreenshotCollectorError("保存截图文件失败")

                # 创建缩略图
                thumbnail_filename = self.storage_manager.create_thumbnail(image, filename)
                if not thumbnail_filename:
                    self.logger.warning(f"创建缩略图失败: {filename}")
            else:
                success = True

            # 获取图像信息
            height, width = image.shape[:2]
            image_size = (width, height)

            # 获取文件大小
            if config.save_to_disk:
                file_info = self.storage_manager.get_file_info(filename)
                file_size = file_info["file_size"] if file_info else 0
            else:
                file_size = image.nbytes

            # 创建结果
            result = ScreenshotResult(
                id=screenshot_id,
                filename=filename,
                filepath=filepath,
                config=config,
                timestamp=timestamp,
                file_size=file_size,
                image_size=image_size,
                success=success
            )

            # 添加到历史记录
            if success:
                # 统一路径格式为正斜杠，确保跨平台兼容性
                thumbnail_path_normalized = None
                if thumbnail_filename:
                    thumbnail_path_str = str(self.storage_manager.thumbnails_dir / thumbnail_filename)
                    thumbnail_path_normalized = thumbnail_path_str.replace('\\', '/')

                record = ScreenshotRecord(
                    id=screenshot_id,
                    filename=filename,
                    filepath=filepath.replace('\\', '/'),  # 统一路径格式
                    thumbnail_path=thumbnail_path_normalized,
                    timestamp=timestamp,
                    file_size=file_size,
                    image_size=image_size,
                    config=config.to_dict()
                )
                self._add_to_history(record)

            elapsed_time = time.time() - start_time
            self.logger.info(f"截图完成: {filename}, 耗时: {elapsed_time:.2f}秒")
            return result

        except GameWindowNotFound as e:
            error_msg = f"游戏窗口未找到: {e}"
            self.logger.error(error_msg)
            return self._create_error_result(screenshot_id, config, timestamp, error_msg)

        except ScreenCaptureError as e:
            error_msg = f"屏幕捕获错误: {e}"
            self.logger.error(error_msg)
            return self._create_error_result(screenshot_id, config, timestamp, error_msg)

        except ScreenshotCollectorError as e:
            error_msg = str(e)
            self.logger.error(f"截图收集器错误: {error_msg}")
            return self._create_error_result(screenshot_id, config, timestamp, error_msg)

        except Exception as e:
            error_msg = f"未知错误: {e}"
            self.logger.error(f"截图失败: {error_msg}", exc_info=True)
            return self._create_error_result(screenshot_id, config, timestamp, error_msg)

    def _validate_config(self, config: ScreenshotConfig):
        """验证截图配置"""
        if config.mode == ScreenshotMode.REGION and not config.region:
            raise ScreenshotCollectorError("区域截图需要指定区域坐标")

        if config.region:
            region = config.region
            if region["width"] <= 0 or region["height"] <= 0:
                raise ScreenshotCollectorError("区域尺寸必须大于0")

            if region["x"] < 0 or region["y"] < 0:
                raise ScreenshotCollectorError("区域坐标不能为负数")

        if not (1 <= config.quality <= 100):
            raise ScreenshotCollectorError("图片质量必须在1-100之间")

    async def _capture_by_mode(self, config: ScreenshotConfig):
        """根据模式执行截图"""
        print(f"[CRITICAL] _capture_by_mode 接收到的模式: {config.mode} (类型: {type(config.mode)})")
        self.logger.info(f"_capture_by_mode 接收到的模式: {config.mode} (类型: {type(config.mode)})")

        # 检查模式类型并强制转换
        if hasattr(config.mode, 'value'):
            print(f"[WARNING] 接收到枚举对象，值为: {config.mode.value}")
            # 这里不应该接收到枚举对象，说明转换失败了
        
        try:
            if config.mode == ScreenshotMode.FULLSCREEN:
                return self.screen_capture.capture_screen()
            elif config.mode == ScreenshotMode.WINDOW:
                return self.screen_capture.capture_screen()
            elif config.mode == ScreenshotMode.REGION:
                # 转换区域格式
                region = {
                    "left": config.region["x"],
                    "top": config.region["y"],
                    "width": config.region["width"],
                    "height": config.region["height"]
                }
                return self.screen_capture.capture_screen(region)
            else:
                raise ScreenshotCollectorError(f"不支持的截图模式: {config.mode}")
        except Exception as e:
            self.logger.error(f"截图捕获失败: {e}")
            raise

    def _create_error_result(self, screenshot_id: str, config: ScreenshotConfig,
                           timestamp: datetime, error_message: str) -> ScreenshotResult:
        """创建错误结果"""
        return ScreenshotResult(
            id=screenshot_id,
            filename="",
            filepath="",
            config=config,
            timestamp=timestamp,
            file_size=0,
            image_size=(0, 0),
            success=False,
            error_message=error_message
        )
    
    async def start_preview_stream(self) -> AsyncGenerator[bytes, None]:
        """
        启动预览流

        Yields:
            JPEG格式的图像数据
        """
        self.preview_active = True
        self.logger.info("预览流已启动")

        # 预览优化参数
        last_capture_time = 0
        frame_interval = 1.0 / self.preview_fps
        error_count = 0
        max_errors = 10

        try:
            while self.preview_active:
                try:
                    current_time = time.time()

                    # 帧率控制 - 避免过于频繁的截图
                    if current_time - last_capture_time < frame_interval:
                        await asyncio.sleep(0.1)
                        continue

                    # 捕获截图
                    image = self.screen_capture.capture_screen()
                    if image is not None:
                        # 缩放图像以减少传输量
                        height, width = image.shape[:2]
                        if width > 1280:  # 限制预览最大宽度
                            scale = 1280 / width
                            new_width = int(width * scale)
                            new_height = int(height * scale)
                            image = cv2.resize(image, (new_width, new_height))

                        # 转换为JPEG格式，使用较低质量以减少数据量
                        _, buffer = cv2.imencode('.jpg', image, [cv2.IMWRITE_JPEG_QUALITY, 60])
                        yield buffer.tobytes()

                        last_capture_time = current_time
                        error_count = 0  # 重置错误计数
                    else:
                        error_count += 1
                        if error_count >= max_errors:
                            self.logger.error("连续截图失败次数过多，停止预览流")
                            break

                    # 短暂休眠以释放CPU
                    await asyncio.sleep(0.05)

                except Exception as e:
                    error_count += 1
                    self.logger.error(f"预览流错误: {e}")

                    if error_count >= max_errors:
                        self.logger.error("预览流错误次数过多，停止预览")
                        break

                    await asyncio.sleep(1.0)

        finally:
            self.preview_active = False
            self.logger.info("预览流已停止")
    
    def stop_preview_stream(self):
        """停止预览流"""
        self.preview_active = False
    
    def get_capture_history(self, limit: int = 100) -> List[ScreenshotRecord]:
        """
        获取截图历史记录
        
        Args:
            limit: 返回记录数量限制
            
        Returns:
            历史记录列表
        """
        return self.history[-limit:] if limit > 0 else self.history
    
    def delete_screenshot(self, screenshot_id: str) -> bool:
        """
        删除截图
        
        Args:
            screenshot_id: 截图ID
            
        Returns:
            是否删除成功
        """
        try:
            # 从历史记录中查找
            record = None
            for r in self.history:
                if r.id == screenshot_id:
                    record = r
                    break
            
            if not record:
                self.logger.warning(f"未找到截图记录: {screenshot_id}")
                return False
            
            # 删除文件
            success = self.storage_manager.delete_file(record.filename)
            
            # 从历史记录中移除
            if success:
                self.history.remove(record)
                self.logger.info(f"截图已删除: {record.filename}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"删除截图失败: {e}")
            return False
    
    def _add_to_history(self, record: ScreenshotRecord):
        """
        添加记录到历史
        
        Args:
            record: 截图记录
        """
        self.history.append(record)
        
        # 限制历史记录数量
        if len(self.history) > self.max_history_size:
            # 删除最旧的记录
            old_record = self.history.pop(0)
            self.storage_manager.delete_file(old_record.filename)
    
    def _rebuild_history_from_filesystem(self):
        """
        从文件系统重建历史记录
        """
        try:
            self.logger.info("开始从文件系统重建历史记录...")

            # 获取所有截图文件
            screenshot_files = []
            for file_path in self.storage_manager.base_dir.glob("*.png"):
                if not file_path.name.endswith("_thumb.png"):  # 排除缩略图
                    screenshot_files.append(file_path)

            for file_path in self.storage_manager.base_dir.glob("*.jpg"):
                screenshot_files.append(file_path)

            for file_path in self.storage_manager.base_dir.glob("*.jpeg"):
                screenshot_files.append(file_path)

            for file_path in self.storage_manager.base_dir.glob("*.bmp"):
                screenshot_files.append(file_path)

            # 按修改时间排序
            screenshot_files.sort(key=lambda x: x.stat().st_mtime)

            # 为每个文件创建历史记录
            for file_path in screenshot_files:
                try:
                    file_info = self.storage_manager.get_file_info(file_path.name)
                    if not file_info:
                        continue

                    # 检查是否有对应的缩略图
                    name_parts = file_path.name.rsplit('.', 1)
                    thumbnail_filename = f"{name_parts[0]}_thumb.{name_parts[1]}"
                    thumbnail_path = self.storage_manager.thumbnails_dir / thumbnail_filename

                    thumbnail_path_normalized = None
                    if thumbnail_path.exists():
                        thumbnail_path_normalized = str(thumbnail_path).replace('\\', '/')

                    # 生成记录ID（基于文件名）
                    record_id = f"rebuilt_{name_parts[0]}"

                    # 创建历史记录
                    record = ScreenshotRecord(
                        id=record_id,
                        filename=file_path.name,
                        filepath=str(file_path).replace('\\', '/'),
                        thumbnail_path=thumbnail_path_normalized,
                        timestamp=file_info["modified_time"],
                        file_size=file_info["file_size"],
                        image_size=file_info["image_size"],
                        config={"mode": "unknown", "format": name_parts[1], "rebuilt": True}
                    )

                    self.history.append(record)

                except Exception as e:
                    self.logger.warning(f"重建文件记录失败 {file_path.name}: {e}")
                    continue

            # 限制历史记录数量
            if len(self.history) > self.max_history_size:
                self.history = self.history[-self.max_history_size:]

            self.logger.info(f"历史记录重建完成，共 {len(self.history)} 条记录")

        except Exception as e:
            self.logger.error(f"重建历史记录失败: {e}")

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            统计信息字典
        """
        total_size = sum(record.file_size for record in self.history)

        return {
            "total_screenshots": len(self.history),
            "total_size_bytes": total_size,
            "total_size_mb": round(total_size / (1024 * 1024), 2),
            "preview_active": self.preview_active,
            "storage_directory": str(self.storage_manager.base_dir),
            "capture_stats": self.screen_capture.get_capture_stats()
        }
