<template>
  <div class="debug-console-test">
    <h1>调试控制台测试页面</h1>
    <p>如果您能看到这个页面，说明路由配置正常。</p>
    
    <el-card>
      <template #header>
        <div class="card-header">
          <span>基础功能测试</span>
        </div>
      </template>
      
      <div class="test-content">
        <el-button type="primary" @click="testApi">测试API连接</el-button>
        <el-button type="success" @click="testComponents">测试组件加载</el-button>
        
        <div v-if="testResults.length > 0" class="test-results">
          <h3>测试结果：</h3>
          <ul>
            <li v-for="result in testResults" :key="result.id">
              {{ result.message }}
            </li>
          </ul>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const testResults = ref([])

const testApi = async () => {
  try {
    const response = await fetch('http://localhost:8000/health')
    if (response.ok) {
      testResults.value.push({
        id: Date.now(),
        message: '✅ API连接成功'
      })
    } else {
      testResults.value.push({
        id: Date.now(),
        message: '❌ API连接失败: ' + response.status
      })
    }
  } catch (error) {
    testResults.value.push({
      id: Date.now(),
      message: '❌ API连接错误: ' + error.message
    })
  }
}

const testComponents = () => {
  testResults.value.push({
    id: Date.now(),
    message: '✅ Vue组件加载正常'
  })
}
</script>

<style scoped>
.debug-console-test {
  padding: 20px;
}

.test-content {
  padding: 20px 0;
}

.test-results {
  margin-top: 20px;
  padding: 15px;
  background: #f5f5f5;
  border-radius: 4px;
}

.test-results ul {
  margin: 0;
  padding-left: 20px;
}

.test-results li {
  margin: 5px 0;
}
</style>
