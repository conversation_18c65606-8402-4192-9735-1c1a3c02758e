"""
测试基类
提供通用的测试工具和断言方法
"""

import unittest
import time
import tempfile
import shutil
import os
from typing import Any, Dict, List, Optional
from unittest.mock import Mock, patch, MagicMock

from . import TEST_CONFIG


class BaseTestCase(unittest.TestCase):
    """基础测试用例类"""
    
    def setUp(self):
        """测试前置设置"""
        self.start_time = time.time()
        self.temp_dir = tempfile.mkdtemp(dir=TEST_CONFIG["temp_dir"])
        self.mock_objects = []
        
    def tearDown(self):
        """测试后置清理"""
        # 清理临时目录
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
        
        # 清理mock对象
        for mock_obj in self.mock_objects:
            if hasattr(mock_obj, 'stop'):
                mock_obj.stop()
        
        # 记录测试执行时间
        execution_time = time.time() - self.start_time
        if execution_time > TEST_CONFIG["timeout"]:
            self.fail(f"测试执行时间过长: {execution_time:.2f}秒")
    
    def create_mock(self, target: str, **kwargs) -> Mock:
        """创建并注册mock对象"""
        mock_obj = patch(target, **kwargs)
        self.mock_objects.append(mock_obj)
        return mock_obj.start()
    
    def assert_performance(self, func, threshold_key: str, *args, **kwargs):
        """断言性能要求"""
        start_time = time.time()
        result = func(*args, **kwargs)
        execution_time = time.time() - start_time
        
        threshold = TEST_CONFIG["performance_threshold"].get(threshold_key, 1.0)
        self.assertLess(
            execution_time, 
            threshold,
            f"性能测试失败: {func.__name__} 执行时间 {execution_time:.3f}s 超过阈值 {threshold}s"
        )
        
        return result
    
    def assert_memory_usage(self, func, *args, **kwargs):
        """断言内存使用"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        memory_before = process.memory_info().rss
        
        result = func(*args, **kwargs)
        
        memory_after = process.memory_info().rss
        memory_used = memory_after - memory_before
        
        threshold = TEST_CONFIG["performance_threshold"]["memory_usage"]
        self.assertLess(
            memory_used,
            threshold,
            f"内存使用测试失败: {func.__name__} 使用内存 {memory_used} bytes 超过阈值 {threshold} bytes"
        )
        
        return result
    
    def assert_dict_contains(self, expected: Dict[str, Any], actual: Dict[str, Any], msg: str = None):
        """断言字典包含关系"""
        for key, value in expected.items():
            self.assertIn(key, actual, msg or f"缺少键: {key}")
            if isinstance(value, dict) and isinstance(actual[key], dict):
                self.assert_dict_contains(value, actual[key], msg)
            else:
                self.assertEqual(value, actual[key], msg or f"键 {key} 的值不匹配")
    
    def assert_list_contains_type(self, lst: List[Any], expected_type: type, msg: str = None):
        """断言列表包含指定类型的元素"""
        self.assertIsInstance(lst, list, msg or "不是列表类型")
        for item in lst:
            self.assertIsInstance(item, expected_type, msg or f"列表项不是 {expected_type} 类型")
    
    def create_temp_file(self, content: str = "", suffix: str = ".txt") -> str:
        """创建临时文件"""
        fd, path = tempfile.mkstemp(dir=self.temp_dir, suffix=suffix)
        with os.fdopen(fd, 'w', encoding='utf-8') as f:
            f.write(content)
        return path
    
    def load_test_data(self, filename: str) -> Any:
        """加载测试数据"""
        import json
        
        data_path = os.path.join(TEST_CONFIG["test_data_dir"], filename)
        if not os.path.exists(data_path):
            self.skipTest(f"测试数据文件不存在: {filename}")
        
        with open(data_path, 'r', encoding='utf-8') as f:
            if filename.endswith('.json'):
                return json.load(f)
            else:
                return f.read()


class UITestCase(BaseTestCase):
    """UI测试用例基类"""
    
    def setUp(self):
        """UI测试前置设置"""
        super().setUp()
        
        # Mock UI相关依赖
        self.mock_perception = self.create_mock('src.modules.ui.perception.ui_perception.UIPerception')
        self.mock_action_controller = self.create_mock('src.modules.ui.actions.action_controller.ActionController')
        
        # 创建测试用的UI配置
        self.test_ui_config = {
            "confidence_threshold": 0.8,
            "timeout": 5.0,
            "retry_count": 3,
            "debug_mode": True
        }
    
    def create_mock_ui_element(self, element_id: str, element_type: str = "button", **kwargs):
        """创建模拟UI元素"""
        from src.modules.ui.elements.base_ui_element import BaseUIElement
        from src.modules.ui.config.ui_element_config import UIElementConfig
        
        config = UIElementConfig(**self.test_ui_config)
        
        # 创建mock元素
        mock_element = Mock(spec=BaseUIElement)
        mock_element.element_id = element_id
        mock_element.element_type = element_type
        mock_element.config = config
        mock_element.is_visible.return_value = kwargs.get('visible', True)
        mock_element.is_enabled.return_value = kwargs.get('enabled', True)
        mock_element.get_bounds.return_value = kwargs.get('bounds', (0, 0, 100, 50))
        
        return mock_element
    
    def assert_ui_element_state(self, element, expected_state: Dict[str, Any]):
        """断言UI元素状态"""
        if 'visible' in expected_state:
            self.assertEqual(element.is_visible(), expected_state['visible'])
        if 'enabled' in expected_state:
            self.assertEqual(element.is_enabled(), expected_state['enabled'])
        if 'bounds' in expected_state:
            self.assertEqual(element.get_bounds(), expected_state['bounds'])


class SceneTestCase(UITestCase):
    """场景测试用例基类"""
    
    def setUp(self):
        """场景测试前置设置"""
        super().setUp()
        
        # Mock场景相关依赖
        self.mock_scene_manager = self.create_mock('src.modules.ui.scenes.scene_manager.SceneManager')
        
        # 创建测试用的场景配置
        self.test_scene_config = {
            "scene_id": "test_scene",
            "timeout": 10.0,
            "retry_count": 3,
            "auto_detect": True
        }
    
    def create_mock_scene(self, scene_type: str, **kwargs):
        """创建模拟场景"""
        from src.modules.ui.scenes.base_game_scene import BaseGameScene
        from src.modules.ui.config.scene_config import SceneConfig
        
        config = SceneConfig(**self.test_scene_config)
        
        # 创建mock场景
        mock_scene = Mock(spec=BaseGameScene)
        mock_scene.scene_type = scene_type
        mock_scene.config = config
        mock_scene.is_scene_ready.return_value = kwargs.get('ready', True)
        mock_scene.get_scene_state.return_value = kwargs.get('state', 'active')
        
        return mock_scene
    
    def assert_scene_transition(self, from_scene: str, to_scene: str, transition_time: float = None):
        """断言场景转换"""
        if transition_time:
            threshold = TEST_CONFIG["performance_threshold"]["scene_transition"]
            self.assertLess(transition_time, threshold, f"场景转换时间过长: {transition_time:.2f}s")


class IntegrationTestCase(BaseTestCase):
    """集成测试用例基类"""
    
    def setUp(self):
        """集成测试前置设置"""
        super().setUp()
        
        # 设置集成测试环境
        self.integration_config = {
            "test_mode": True,
            "mock_external_services": True,
            "log_level": "DEBUG"
        }
    
    def run_integration_test(self, test_scenario: str, steps: List[Dict[str, Any]]):
        """运行集成测试场景"""
        results = []
        
        for i, step in enumerate(steps):
            step_name = step.get('name', f'Step {i+1}')
            step_func = step.get('function')
            step_args = step.get('args', [])
            step_kwargs = step.get('kwargs', {})
            expected_result = step.get('expected')
            
            try:
                start_time = time.time()
                result = step_func(*step_args, **step_kwargs)
                execution_time = time.time() - start_time
                
                step_result = {
                    'step': step_name,
                    'result': result,
                    'execution_time': execution_time,
                    'success': True
                }
                
                if expected_result is not None:
                    if result != expected_result:
                        step_result['success'] = False
                        step_result['error'] = f"期望结果: {expected_result}, 实际结果: {result}"
                
                results.append(step_result)
                
            except Exception as e:
                results.append({
                    'step': step_name,
                    'result': None,
                    'execution_time': 0,
                    'success': False,
                    'error': str(e)
                })
        
        # 验证所有步骤都成功
        failed_steps = [r for r in results if not r['success']]
        if failed_steps:
            error_msg = f"集成测试 '{test_scenario}' 失败:\n"
            for step in failed_steps:
                error_msg += f"  - {step['step']}: {step.get('error', '未知错误')}\n"
            self.fail(error_msg)
        
        return results


class PerformanceTestCase(BaseTestCase):
    """性能测试用例基类"""
    
    def setUp(self):
        """性能测试前置设置"""
        super().setUp()
        
        self.performance_results = []
    
    def benchmark(self, func, iterations: int = 100, warmup: int = 10):
        """性能基准测试"""
        import statistics
        
        # 预热
        for _ in range(warmup):
            func()
        
        # 测试
        times = []
        for _ in range(iterations):
            start_time = time.perf_counter()
            func()
            end_time = time.perf_counter()
            times.append(end_time - start_time)
        
        result = {
            'function': func.__name__,
            'iterations': iterations,
            'min_time': min(times),
            'max_time': max(times),
            'avg_time': statistics.mean(times),
            'median_time': statistics.median(times),
            'std_dev': statistics.stdev(times) if len(times) > 1 else 0
        }
        
        self.performance_results.append(result)
        return result
    
    def assert_performance_regression(self, current_result: Dict[str, float], 
                                    baseline_result: Dict[str, float], 
                                    tolerance: float = 0.1):
        """断言性能回归"""
        current_avg = current_result['avg_time']
        baseline_avg = baseline_result['avg_time']
        
        regression = (current_avg - baseline_avg) / baseline_avg
        
        self.assertLess(
            regression,
            tolerance,
            f"性能回归检测失败: 当前平均时间 {current_avg:.4f}s, "
            f"基线平均时间 {baseline_avg:.4f}s, 回归率 {regression:.2%}"
        )
    
    def memory_profile(self, func, *args, **kwargs):
        """内存性能分析"""
        import tracemalloc
        
        tracemalloc.start()
        
        result = func(*args, **kwargs)
        
        current, peak = tracemalloc.get_traced_memory()
        tracemalloc.stop()
        
        return {
            'result': result,
            'current_memory': current,
            'peak_memory': peak
        }


def run_all_tests():
    """运行所有测试"""
    # 发现并运行所有测试
    loader = unittest.TestLoader()
    start_dir = os.path.dirname(__file__)
    suite = loader.discover(start_dir, pattern='test_*.py')
    
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()


if __name__ == '__main__':
    success = run_all_tests()
    exit(0 if success else 1)
