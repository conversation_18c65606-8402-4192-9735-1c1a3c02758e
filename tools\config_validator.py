#!/usr/bin/env python3
"""
Gakumasu-Bot 配置验证和测试工具
用于验证配置文件的正确性和测试不同配置的性能影响
"""

import sys
import os
import yaml
import time
import json
from pathlib import Path
from typing import Dict, Any, List, Tuple

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

try:
    # 尝试导入项目模块
    if os.path.exists("src/utils/config_loader.py"):
        from src.utils.config_loader import ConfigLoader
        PROJECT_MODULES_AVAILABLE = True
        print("✅ 项目模块导入成功")
    else:
        PROJECT_MODULES_AVAILABLE = False
        print("⚠️ 项目模块不可用，使用基础验证模式")
except ImportError as e:
    PROJECT_MODULES_AVAILABLE = False
    print(f"⚠️ 项目模块导入失败: {e}")
    print("使用基础验证模式")

class ConfigValidator:
    """配置验证器"""
    
    def __init__(self):
        self.errors = []
        self.warnings = []
        
    def validate_settings(self, settings: Dict[str, Any]) -> bool:
        """验证系统设置"""
        print("🔍 验证系统设置...")
        
        # 验证必需的配置项
        required_sections = ['system', 'dmm', 'performance', 'template_matching']
        for section in required_sections:
            if section not in settings:
                self.errors.append(f"缺少必需的配置节: {section}")
        
        # 验证DMM Player路径
        if 'dmm' in settings and 'dmm_player_path' in settings['dmm']:
            dmm_path = settings['dmm']['dmm_player_path']
            if not os.path.exists(dmm_path):
                self.warnings.append(f"DMM Player路径不存在: {dmm_path}")
        
        # 验证数值范围
        self._validate_numeric_ranges(settings)
        
        # 验证文件路径
        self._validate_file_paths(settings)
        
        return len(self.errors) == 0
    
    def _validate_numeric_ranges(self, settings: Dict[str, Any]):
        """验证数值参数的取值范围"""
        validations = [
            ('template_matching.confidence_threshold', 0.0, 1.0),
            ('performance.screenshot_interval', 0.1, 5.0),
            ('performance.action_delay_min', 0.01, 1.0),
            ('performance.action_delay_max', 0.01, 2.0),
            ('ai.mcts_iterations', 100, 50000),
            ('ocr.confidence_threshold', 0.0, 1.0),
        ]
        
        for path, min_val, max_val in validations:
            value = self._get_nested_value(settings, path)
            if value is not None:
                if not (min_val <= value <= max_val):
                    self.errors.append(f"{path} 值 {value} 超出范围 [{min_val}, {max_val}]")
    
    def _validate_file_paths(self, settings: Dict[str, Any]):
        """验证文件路径配置"""
        path_configs = [
            ('template_matching.template_directory', True),  # True表示必须存在
            ('paths.config_directory', True),
            ('paths.logs_directory', False),  # False表示可以不存在
            ('paths.temp_directory', False),
        ]
        
        for path_key, must_exist in path_configs:
            path_value = self._get_nested_value(settings, path_key)
            if path_value:
                if must_exist and not os.path.exists(path_value):
                    self.errors.append(f"必需的目录不存在: {path_value}")
                elif not must_exist and not os.path.exists(path_value):
                    self.warnings.append(f"目录不存在，将自动创建: {path_value}")
    
    def _get_nested_value(self, data: Dict[str, Any], path: str) -> Any:
        """获取嵌套字典中的值"""
        keys = path.split('.')
        current = data
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return None
        return current
    
    def print_results(self):
        """打印验证结果"""
        if self.errors:
            print("\n❌ 配置错误:")
            for error in self.errors:
                print(f"  • {error}")
        
        if self.warnings:
            print("\n⚠️ 配置警告:")
            for warning in self.warnings:
                print(f"  • {warning}")
        
        if not self.errors and not self.warnings:
            print("\n✅ 配置验证通过，无错误或警告")
        elif not self.errors:
            print("\n✅ 配置验证通过，但有警告需要注意")

class PerformanceTester:
    """性能测试器"""
    
    def __init__(self):
        self.results = []
    
    def test_template_matching_performance(self, confidence_thresholds: List[float],
                                         screenshot_intervals: List[float]) -> Dict[str, Any]:
        """测试模板匹配性能"""
        print("\n🚀 开始模板匹配性能测试...")

        # 检查是否有测试图片
        test_image_path = "screenshots/menu_main.png"
        if not os.path.exists(test_image_path):
            print(f"⚠️ 测试图片不存在: {test_image_path}")
            print("请先截取一张游戏主菜单截图并保存到该路径")
            return {}

        if not PROJECT_MODULES_AVAILABLE:
            print("⚠️ 项目模块不可用，跳过性能测试")
            return {}
        
        for confidence in confidence_thresholds:
            for interval in screenshot_intervals:
                print(f"  测试配置: 置信度={confidence}, 间隔={interval}s")
                
                # 模拟性能测试
                start_time = time.time()
                success_count = 0
                
                for i in range(5):  # 测试5次
                    try:
                        # 这里应该加载测试图片并进行匹配
                        # result = template_matcher.match_template(test_image, "main_menu_logo", confidence)
                        # if result: success_count += 1
                        time.sleep(interval)  # 模拟截图间隔
                        success_count += 1  # 模拟成功
                    except Exception as e:
                        print(f"    测试异常: {e}")
                
                total_time = time.time() - start_time
                success_rate = success_count / 5
                
                result = {
                    'confidence_threshold': confidence,
                    'screenshot_interval': interval,
                    'total_time': total_time,
                    'success_rate': success_rate,
                    'avg_time_per_operation': total_time / 5
                }
                
                self.results.append(result)
                print(f"    结果: 总耗时={total_time:.2f}s, 成功率={success_rate:.1%}")
        
        return self.results
    
    def generate_performance_report(self) -> str:
        """生成性能测试报告"""
        if not self.results:
            return "无性能测试数据"
        
        report = "\n📊 性能测试报告\n"
        report += "=" * 50 + "\n"
        
        # 找出最佳配置
        best_config = min(self.results, key=lambda x: x['avg_time_per_operation'])
        report += f"🏆 最佳性能配置:\n"
        report += f"  置信度阈值: {best_config['confidence_threshold']}\n"
        report += f"  截图间隔: {best_config['screenshot_interval']}s\n"
        report += f"  平均操作时间: {best_config['avg_time_per_operation']:.3f}s\n"
        report += f"  成功率: {best_config['success_rate']:.1%}\n\n"
        
        # 详细结果表格
        report += "详细测试结果:\n"
        report += f"{'置信度':<8} {'间隔(s)':<8} {'总时间(s)':<10} {'成功率':<8} {'平均时间(s)':<12}\n"
        report += "-" * 50 + "\n"
        
        for result in self.results:
            report += f"{result['confidence_threshold']:<8} "
            report += f"{result['screenshot_interval']:<8} "
            report += f"{result['total_time']:<10.2f} "
            report += f"{result['success_rate']:<8.1%} "
            report += f"{result['avg_time_per_operation']:<12.3f}\n"
        
        return report

def main():
    """主函数"""
    print("🔧 Gakumasu-Bot 配置验证和性能测试工具")
    print("=" * 60)
    
    # 1. 配置验证
    print("\n📋 第一步: 配置文件验证")
    validator = ConfigValidator()
    
    try:
        if PROJECT_MODULES_AVAILABLE:
            config_loader = ConfigLoader()
            settings = config_loader.load_settings()
        else:
            # 基础模式：直接加载YAML文件
            settings_path = "config/settings.yaml"
            if not os.path.exists(settings_path):
                print(f"❌ 配置文件不存在: {settings_path}")
                return False

            with open(settings_path, 'r', encoding='utf-8') as f:
                settings = yaml.safe_load(f)

        if validator.validate_settings(settings):
            print("✅ 配置验证通过")
        else:
            print("❌ 配置验证失败")
            validator.print_results()
            return False

    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False
    
    validator.print_results()
    
    # 2. 性能测试（可选）
    print("\n📋 第二步: 性能测试")
    response = input("是否进行性能测试？(y/N): ").lower().strip()
    
    if response == 'y':
        tester = PerformanceTester()
        
        # 测试不同的配置组合
        confidence_thresholds = [0.7, 0.8, 0.9]
        screenshot_intervals = [0.3, 0.5, 0.8]
        
        results = tester.test_template_matching_performance(
            confidence_thresholds, screenshot_intervals
        )
        
        if results:
            report = tester.generate_performance_report()
            print(report)
            
            # 保存报告到文件
            report_file = f"logs/performance_report_{int(time.time())}.txt"
            os.makedirs("logs", exist_ok=True)
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)
            print(f"\n📄 性能报告已保存到: {report_file}")
    
    # 3. 配置建议
    print("\n📋 第三步: 配置优化建议")
    print_optimization_suggestions(settings)
    
    print("\n🎉 配置验证和测试完成！")
    return True

def print_optimization_suggestions(settings: Dict[str, Any]):
    """打印配置优化建议"""
    suggestions = []
    
    # 检查置信度阈值
    confidence = settings.get('template_matching', {}).get('confidence_threshold', 0.8)
    if confidence > 0.9:
        suggestions.append("置信度阈值过高(>0.9)，可能导致识别失败，建议降低到0.8-0.85")
    elif confidence < 0.6:
        suggestions.append("置信度阈值过低(<0.6)，可能导致误识别，建议提高到0.7-0.8")
    
    # 检查截图间隔
    interval = settings.get('performance', {}).get('screenshot_interval', 0.5)
    if interval < 0.2:
        suggestions.append("截图间隔过短(<0.2s)，可能导致CPU占用过高")
    elif interval > 1.0:
        suggestions.append("截图间隔过长(>1.0s)，可能影响响应速度")
    
    # 检查MCTS设置
    mcts_iterations = settings.get('ai', {}).get('mcts_iterations', 1000)
    if mcts_iterations > 5000:
        suggestions.append("MCTS迭代次数过多(>5000)，可能影响决策速度")
    elif mcts_iterations < 500:
        suggestions.append("MCTS迭代次数过少(<500)，可能影响决策质量")
    
    if suggestions:
        print("💡 配置优化建议:")
        for i, suggestion in enumerate(suggestions, 1):
            print(f"  {i}. {suggestion}")
    else:
        print("✅ 当前配置已经很好，无需特别优化")

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
