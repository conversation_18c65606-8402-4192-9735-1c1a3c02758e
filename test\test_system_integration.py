"""
系统集成测试脚本
测试截图工具的完整功能流程
"""

import sys
import asyncio
import time
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.web.main import api_adapter
from src.modules.screenshot_collector import ScreenshotConfig, ScreenshotMode, ScreenshotFormat


async def test_system_initialization():
    """测试系统初始化"""
    print("🧪 测试系统初始化...")
    
    try:
        # 初始化核心模块
        api_adapter.initialize_core_modules()
        
        # 验证模块是否正确初始化
        assert api_adapter.scheduler is not None, "调度器未初始化"
        assert api_adapter.config_manager is not None, "配置管理器未初始化"
        assert api_adapter.state_manager is not None, "状态管理器未初始化"
        assert api_adapter.screenshot_collector is not None, "截图收集器未初始化"
        
        print("✅ 系统初始化成功")
        return True
        
    except Exception as e:
        print(f"❌ 系统初始化失败: {e}")
        return False


async def test_screenshot_functionality():
    """测试截图功能"""
    print("\n🧪 测试截图功能...")
    
    try:
        # 测试不同模式的截图
        test_configs = [
            {
                "mode": "window",
                "format": "png",
                "quality": 90,
                "save_to_disk": True,
                "filename_prefix": "integration_test"
            },
            {
                "mode": "fullscreen",
                "format": "jpeg",
                "quality": 80,
                "save_to_disk": True,
                "filename_prefix": "integration_test"
            }
        ]
        
        results = []
        for config in test_configs:
            print(f"📸 测试 {config['mode']} 模式截图...")
            
            result = await api_adapter.capture_screenshot(config)
            results.append(result)
            
            if result.success:
                print(f"✅ {config['mode']} 截图成功: {result.filename}")
            else:
                print(f"❌ {config['mode']} 截图失败: {result.error_message}")
        
        # 验证至少有一个截图成功
        success_count = sum(1 for r in results if r.success)
        if success_count > 0:
            print(f"✅ 截图功能测试通过 ({success_count}/{len(results)} 成功)")
            return True
        else:
            print("❌ 所有截图都失败了")
            return False
            
    except Exception as e:
        print(f"❌ 截图功能测试失败: {e}")
        return False


async def test_history_management():
    """测试历史记录管理"""
    print("\n🧪 测试历史记录管理...")
    
    try:
        # 获取历史记录
        history = await api_adapter.get_screenshot_history(10)
        print(f"📋 当前历史记录数量: {len(history)}")
        
        if len(history) > 0:
            # 测试删除功能
            test_record = history[0]
            screenshot_id = test_record["id"]
            
            print(f"🗑️ 测试删除截图: {test_record['filename']}")
            success = await api_adapter.delete_screenshot(screenshot_id)
            
            if success:
                print("✅ 删除功能正常")
                
                # 验证删除后历史记录减少
                new_history = await api_adapter.get_screenshot_history(10)
                if len(new_history) == len(history) - 1:
                    print("✅ 历史记录更新正常")
                else:
                    print("⚠️ 历史记录更新可能有问题")
            else:
                print("❌ 删除功能失败")
                return False
        
        print("✅ 历史记录管理测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 历史记录管理测试失败: {e}")
        return False


async def test_statistics():
    """测试统计功能"""
    print("\n🧪 测试统计功能...")
    
    try:
        # 获取截图统计
        stats = await api_adapter.get_screenshot_stats()
        
        required_fields = [
            'total_screenshots', 'total_size_bytes', 'total_size_mb',
            'preview_active', 'storage_directory'
        ]
        
        for field in required_fields:
            if field not in stats:
                print(f"❌ 统计信息缺少字段: {field}")
                return False
        
        print(f"📊 统计信息:")
        print(f"   总截图数: {stats['total_screenshots']}")
        print(f"   总大小: {stats['total_size_mb']} MB")
        print(f"   存储目录: {stats['storage_directory']}")
        print(f"   预览状态: {'活动' if stats['preview_active'] else '停止'}")
        
        print("✅ 统计功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 统计功能测试失败: {e}")
        return False


async def test_websocket_stats():
    """测试WebSocket统计"""
    print("\n🧪 测试WebSocket统计...")
    
    try:
        # 获取WebSocket统计
        ws_stats = api_adapter.get_websocket_stats()
        
        required_fields = [
            'total_connections', 'active_connections', 
            'messages_sent', 'messages_received', 'errors'
        ]
        
        for field in required_fields:
            if field not in ws_stats:
                print(f"❌ WebSocket统计缺少字段: {field}")
                return False
        
        print(f"🔌 WebSocket统计:")
        print(f"   总连接数: {ws_stats['total_connections']}")
        print(f"   活动连接: {ws_stats['active_connections']}")
        print(f"   发送消息: {ws_stats['messages_sent']}")
        print(f"   接收消息: {ws_stats['messages_received']}")
        print(f"   错误次数: {ws_stats['errors']}")
        
        print("✅ WebSocket统计测试通过")
        return True
        
    except Exception as e:
        print(f"❌ WebSocket统计测试失败: {e}")
        return False


async def test_error_handling():
    """测试错误处理"""
    print("\n🧪 测试错误处理...")
    
    try:
        # 测试无效配置
        invalid_config = {
            "mode": "invalid_mode",
            "format": "png",
            "quality": 90
        }
        
        try:
            result = await api_adapter.capture_screenshot(invalid_config)
            if not result.success:
                print("✅ 无效配置错误处理正常")
            else:
                print("⚠️ 无效配置应该失败但成功了")
        except Exception:
            print("✅ 无效配置抛出异常，错误处理正常")
        
        # 测试删除不存在的截图
        try:
            success = await api_adapter.delete_screenshot("non_existent_id")
            if not success:
                print("✅ 删除不存在截图的错误处理正常")
            else:
                print("⚠️ 删除不存在的截图应该失败")
        except Exception:
            print("✅ 删除不存在截图抛出异常，错误处理正常")
        
        print("✅ 错误处理测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False


async def test_performance():
    """测试性能"""
    print("\n🧪 测试性能...")
    
    try:
        # 测试连续截图性能
        start_time = time.time()
        
        config = {
            "mode": "window",
            "format": "jpeg",
            "quality": 70,
            "save_to_disk": True,
            "filename_prefix": "perf_test"
        }
        
        success_count = 0
        test_count = 3  # 减少测试数量以节省时间
        
        for i in range(test_count):
            result = await api_adapter.capture_screenshot(config)
            if result.success:
                success_count += 1
        
        end_time = time.time()
        total_time = end_time - start_time
        avg_time = total_time / test_count
        
        print(f"⏱️ 性能测试结果:")
        print(f"   总时间: {total_time:.2f} 秒")
        print(f"   平均时间: {avg_time:.2f} 秒/截图")
        print(f"   成功率: {success_count}/{test_count} ({success_count/test_count*100:.1f}%)")
        
        if avg_time < 5.0 and success_count >= test_count * 0.8:
            print("✅ 性能测试通过")
            return True
        else:
            print("⚠️ 性能可能需要优化")
            return True  # 不作为失败条件
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False


async def cleanup_test_files():
    """清理测试文件"""
    print("\n🧹 清理测试文件...")
    
    try:
        screenshots_dir = Path("screenshots")
        if screenshots_dir.exists():
            # 清理测试截图
            test_patterns = [
                "integration_test_*.png",
                "integration_test_*.jpeg", 
                "integration_test_*.jpg",
                "perf_test_*.png",
                "perf_test_*.jpeg",
                "perf_test_*.jpg"
            ]
            
            deleted_count = 0
            for pattern in test_patterns:
                for file_path in screenshots_dir.glob(pattern):
                    try:
                        file_path.unlink()
                        deleted_count += 1
                    except Exception as e:
                        print(f"⚠️ 删除文件失败 {file_path}: {e}")
            
            # 清理缩略图
            thumbnails_dir = screenshots_dir / "thumbnails"
            if thumbnails_dir.exists():
                for pattern in test_patterns:
                    thumb_pattern = pattern.replace(".", "_thumb.")
                    for file_path in thumbnails_dir.glob(thumb_pattern):
                        try:
                            file_path.unlink()
                            deleted_count += 1
                        except Exception as e:
                            print(f"⚠️ 删除缩略图失败 {file_path}: {e}")
            
            print(f"🗑️ 已删除 {deleted_count} 个测试文件")
        
        print("✅ 测试文件清理完成")
        return True
        
    except Exception as e:
        print(f"❌ 清理测试文件失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始系统集成测试")
    print("=" * 60)
    
    # 测试项目列表
    test_functions = [
        ("系统初始化", test_system_initialization),
        ("截图功能", test_screenshot_functionality),
        ("历史记录管理", test_history_management),
        ("统计功能", test_statistics),
        ("WebSocket统计", test_websocket_stats),
        ("错误处理", test_error_handling),
        ("性能测试", test_performance)
    ]
    
    # 执行测试
    results = []
    for test_name, test_func in test_functions:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 清理测试文件
    await cleanup_test_files()
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 系统集成测试结果汇总:")
    
    passed_count = 0
    total_count = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed_count += 1
    
    print(f"\n总体结果: {passed_count}/{total_count} 通过 ({passed_count/total_count*100:.1f}%)")
    
    if passed_count == total_count:
        print("🎉 所有集成测试通过！系统功能正常")
        return True
    elif passed_count >= total_count * 0.8:
        print("⚠️ 大部分测试通过，系统基本功能正常")
        return True
    else:
        print("❌ 多项测试失败，系统可能存在问题")
        return False


if __name__ == "__main__":
    # 运行集成测试
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
