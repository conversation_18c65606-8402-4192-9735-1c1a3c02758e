"""
Web API 数据模型定义
定义所有API接口使用的数据模型
"""

from datetime import datetime
from typing import Dict, List, Optional, Any, Union, Tuple
from pydantic import BaseModel, Field, validator
from enum import Enum


# 系统相关模型
class SystemStatus(BaseModel):
    """系统状态模型"""
    is_running: bool = Field(description="系统是否运行中")
    current_task: Optional[str] = Field(description="当前任务")
    task_queue_size: int = Field(description="任务队列大小")
    uptime: float = Field(description="运行时间（秒）")
    last_update: Optional[datetime] = Field(description="最后更新时间")
    resource_usage: Dict[str, Any] = Field(default_factory=dict, description="资源使用情况")
    game_status: Dict[str, Any] = Field(default_factory=dict, description="游戏状态")


class ControlRequest(BaseModel):
    """系统控制请求模型"""
    action: str = Field(description="控制动作", pattern="^(start|stop|pause|resume|restart)$")
    parameters: Optional[Dict[str, Any]] = Field(default_factory=dict, description="控制参数")


class TaskRequest(BaseModel):
    """任务请求模型"""
    task_type: str = Field(description="任务类型")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="任务参数")


class ConfigUpdateRequest(BaseModel):
    """配置更新请求模型"""
    section: str = Field(description="配置节")
    key: str = Field(description="配置键")
    value: Any = Field(description="配置值")


# 截图相关模型
class ScreenshotModeEnum(str, Enum):
    """截图模式枚举"""
    FULLSCREEN = "fullscreen"
    WINDOW = "window"
    REGION = "region"


class ScreenshotFormatEnum(str, Enum):
    """截图格式枚举"""
    PNG = "png"
    JPEG = "jpeg"
    BMP = "bmp"


class RegionConfig(BaseModel):
    """区域配置模型"""
    x: int = Field(ge=0, description="X坐标")
    y: int = Field(ge=0, description="Y坐标")
    width: int = Field(gt=0, description="宽度")
    height: int = Field(gt=0, description="高度")


class ScreenshotConfig(BaseModel):
    """截图配置模型"""
    mode: ScreenshotModeEnum = Field(default=ScreenshotModeEnum.WINDOW, description="截图模式")
    format: ScreenshotFormatEnum = Field(default=ScreenshotFormatEnum.PNG, description="图片格式")
    quality: int = Field(default=90, ge=1, le=100, description="图片质量(1-100)")
    region: Optional[RegionConfig] = Field(default=None, description="区域坐标")
    save_to_disk: bool = Field(default=True, description="是否保存到磁盘")
    filename_prefix: str = Field(default="screenshot", description="文件名前缀")


class ScreenshotRequest(BaseModel):
    """截图请求模型"""
    config: ScreenshotConfig = Field(description="截图配置")


class ImageSize(BaseModel):
    """图像尺寸模型"""
    width: int = Field(ge=0, description="宽度")
    height: int = Field(ge=0, description="高度")

    @classmethod
    def from_tuple(cls, size_tuple: Tuple[int, int]) -> 'ImageSize':
        """从元组创建ImageSize实例的便捷方法"""
        if not isinstance(size_tuple, tuple) or len(size_tuple) != 2:
            raise ValueError("size_tuple must be a tuple of (width, height)")
        width, height = size_tuple
        return cls(width=width, height=height)


class ScreenshotResult(BaseModel):
    """截图结果模型"""
    id: str = Field(description="截图ID")
    filename: str = Field(description="文件名")
    filepath: str = Field(description="文件路径")
    config: ScreenshotConfig = Field(description="截图配置")
    timestamp: datetime = Field(description="创建时间")
    file_size: int = Field(ge=0, description="文件大小(字节)")
    image_size: ImageSize = Field(description="图像尺寸")
    success: bool = Field(description="是否成功")
    error_message: Optional[str] = Field(default=None, description="错误信息")


class ScreenshotRecord(BaseModel):
    """截图历史记录模型"""
    id: str = Field(description="截图ID")
    filename: str = Field(description="文件名")
    filepath: str = Field(description="文件路径")
    thumbnail_path: Optional[str] = Field(default=None, description="缩略图路径")
    timestamp: datetime = Field(description="创建时间")
    file_size: int = Field(ge=0, description="文件大小(字节)")
    image_size: ImageSize = Field(description="图像尺寸")
    config: Dict[str, Any] = Field(description="截图配置")


class ScreenshotHistoryResponse(BaseModel):
    """截图历史响应模型"""
    total: int = Field(ge=0, description="总数量")
    records: List[ScreenshotRecord] = Field(description="记录列表")


class ScreenshotStatsResponse(BaseModel):
    """截图统计响应模型"""
    total_screenshots: int = Field(ge=0, description="总截图数")
    total_size_bytes: int = Field(ge=0, description="总大小(字节)")
    total_size_mb: float = Field(ge=0, description="总大小(MB)")
    preview_active: bool = Field(description="预览是否激活")
    storage_directory: str = Field(description="存储目录")
    capture_stats: Dict[str, Any] = Field(description="捕获统计")


class PreviewConfig(BaseModel):
    """预览配置模型"""
    fps: int = Field(default=2, ge=1, le=30, description="预览帧率")
    quality: int = Field(default=80, ge=10, le=100, description="预览质量")


class PreviewRequest(BaseModel):
    """预览请求模型"""
    config: Optional[PreviewConfig] = Field(default=None, description="预览配置")


# WebSocket 消息模型
class WebSocketMessageType(str, Enum):
    """WebSocket消息类型"""
    PING = "ping"
    PONG = "pong"
    STATUS_UPDATE = "status_update"
    SCREENSHOT_PREVIEW = "screenshot_preview"
    SCREENSHOT_COMPLETED = "screenshot_completed"
    SCREENSHOT_ERROR = "screenshot_error"
    TASK_UPDATE = "task_update"
    LOG_MESSAGE = "log_message"


class WebSocketMessage(BaseModel):
    """WebSocket消息模型"""
    type: WebSocketMessageType = Field(description="消息类型")
    data: Optional[Dict[str, Any]] = Field(default=None, description="消息数据")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")


class WebSocketResponse(BaseModel):
    """WebSocket响应模型"""
    type: WebSocketMessageType = Field(description="响应类型")
    data: Optional[Dict[str, Any]] = Field(default=None, description="响应数据")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")
    success: bool = Field(default=True, description="是否成功")
    error: Optional[str] = Field(default=None, description="错误信息")


# 通用响应模型
class ApiResponse(BaseModel):
    """通用API响应模型"""
    success: bool = Field(description="是否成功")
    message: str = Field(description="响应消息")
    data: Optional[Any] = Field(default=None, description="响应数据")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")


class ErrorResponse(BaseModel):
    """错误响应模型"""
    success: bool = Field(default=False, description="是否成功")
    error: str = Field(description="错误信息")
    error_code: Optional[str] = Field(default=None, description="错误代码")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")


# 文件操作模型
class FileInfo(BaseModel):
    """文件信息模型"""
    filename: str = Field(description="文件名")
    filepath: str = Field(description="文件路径")
    file_size: int = Field(ge=0, description="文件大小(字节)")
    created_time: datetime = Field(description="创建时间")
    modified_time: datetime = Field(description="修改时间")


class DeleteRequest(BaseModel):
    """删除请求模型"""
    id: str = Field(description="要删除的项目ID")


class BatchDeleteRequest(BaseModel):
    """批量删除请求模型"""
    ids: List[str] = Field(description="要删除的项目ID列表")


class BatchOperationResponse(BaseModel):
    """批量操作响应模型"""
    total: int = Field(ge=0, description="总数量")
    success_count: int = Field(ge=0, description="成功数量")
    failed_count: int = Field(ge=0, description="失败数量")
    failed_ids: List[str] = Field(default_factory=list, description="失败的ID列表")
    errors: List[str] = Field(default_factory=list, description="错误信息列表")


# 健康检查模型
class HealthCheckResponse(BaseModel):
    """健康检查响应模型"""
    status: str = Field(default="healthy", description="健康状态")
    timestamp: datetime = Field(default_factory=datetime.now, description="检查时间")
    version: str = Field(default="1.0.0", description="版本号")
    uptime: float = Field(ge=0, description="运行时间(秒)")
    components: Dict[str, str] = Field(default_factory=dict, description="组件状态")


# 配置模型
class GameWindowConfig(BaseModel):
    """游戏窗口配置模型"""
    window_title: str = Field(default="gakumas", description="窗口标题")
    enable_background_mode: bool = Field(default=True, description="启用后台模式")
    capture_method: str = Field(default="auto", description="捕获方法")


class StorageConfig(BaseModel):
    """存储配置模型"""
    base_directory: str = Field(default="screenshots", description="基础目录")
    max_history_size: int = Field(default=1000, ge=1, description="最大历史记录数")
    auto_cleanup: bool = Field(default=True, description="自动清理")
    cleanup_days: int = Field(default=30, ge=1, description="清理天数")


class ScreenshotToolConfig(BaseModel):
    """截图工具配置模型"""
    game_window: GameWindowConfig = Field(default_factory=GameWindowConfig, description="游戏窗口配置")
    storage: StorageConfig = Field(default_factory=StorageConfig, description="存储配置")
    preview_fps: int = Field(default=2, ge=1, le=30, description="预览帧率")
    default_quality: int = Field(default=90, ge=1, le=100, description="默认质量")
    default_format: ScreenshotFormatEnum = Field(default=ScreenshotFormatEnum.PNG, description="默认格式")


# 导出所有模型
__all__ = [
    # 系统相关
    "SystemStatus", "ControlRequest", "TaskRequest", "ConfigUpdateRequest",
    
    # 截图相关
    "ScreenshotModeEnum", "ScreenshotFormatEnum", "RegionConfig", 
    "ScreenshotConfig", "ScreenshotRequest", "ImageSize", 
    "ScreenshotResult", "ScreenshotRecord", "ScreenshotHistoryResponse",
    "ScreenshotStatsResponse", "PreviewConfig", "PreviewRequest",
    
    # WebSocket相关
    "WebSocketMessageType", "WebSocketMessage", "WebSocketResponse",
    
    # 通用响应
    "ApiResponse", "ErrorResponse",
    
    # 文件操作
    "FileInfo", "DeleteRequest", "BatchDeleteRequest", "BatchOperationResponse",
    
    # 健康检查
    "HealthCheckResponse",
    
    # 配置相关
    "GameWindowConfig", "StorageConfig", "ScreenshotToolConfig"
]
