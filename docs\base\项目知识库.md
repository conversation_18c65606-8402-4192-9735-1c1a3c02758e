# 项目知识库 - Gakumasu-Bot

### **文档修订历史**

| **版本号** | **修订日期** | **修订人** | **修订内容描述**           |
| ---------------- | ------------------ | ---------------- | -------------------------------- |
| V1.0             | 2025-08-05         | AI Assistant     | 基于项目当前状态创建完整知识库文档 |

## 1. 概述 (Overview)

### 1.1. 项目背景

* **项目起源**: Gakumasu-Bot 是一个专为《学园偶像大师》PC客户端开发的自动化游玩程序，旨在通过非侵入式的计算机视觉和人工智能技术，实现7x24小时无人值守的游戏托管。
* **核心价值**: 减轻玩家在重复性育成过程中的负担，同时探索高级AI算法在复杂策略游戏中的应用潜力。
* **技术特色**: 采用感知-决策-行动-调度四层架构，支持日语优先的游戏环境，集成MCTS算法和启发式评分系统。

### 1.2. 系统现状

* **核心功能**: 
  - ✅ 自动游戏启动和窗口管理
  - ✅ 实时屏幕捕获和场景识别（95%完成）
  - ✅ 智能决策系统（启发式+MCTS，100%完成）
  - ✅ 精确的键鼠操作模拟（100%完成）
  - ✅ 完整的任务调度系统（90%完成）
  - ✅ Web用户界面（95%完成，调试控制台功能完整实现）

* **主要特性**: 
  - 支持Python 3.13.3最新特性和性能优化
  - 模块化设计，各组件高度解耦
  - 完善的日志系统和错误处理机制
  - 支持多种配置模式和用户策略定制

### 1.3. 范围

* **当前版本包含**:
  - 完整的后端核心功能（感知、决策、行动、调度四大模块）
  - 完整的Web API接口和前端框架（包含调试控制台）
  - GUI启动器和命令行界面
  - 完善的配置系统和日志系统
  - 单元测试和集成测试框架

* **当前版本不包含**:
  - OCR文本识别功能的完整集成
  - 生产环境部署和运维方案
  - 完整的游戏流程自动化（需要实际游戏环境测试）
  - 高级数据可视化和报表功能

### 1.4. 名词解释

* **感知模块 (Perception)**: 负责屏幕捕获、场景识别和游戏状态解析的模块
* **决策模块 (Decision)**: 基于AI算法进行游戏策略决策的模块，包含MCTS和启发式评分
* **行动模块 (Action)**: 执行键鼠操作和游戏交互的模块
* **调度模块 (Scheduler)**: 管理任务调度、状态持久化和配置管理的核心控制模块
* **MCTS**: Monte Carlo Tree Search，蒙特卡洛树搜索算法
* **GameState**: 游戏状态数据结构，包含当前场景、资源、手牌等信息
* **DMM Player**: 《学园偶像大师》游戏的官方启动器

## 2. 整体技术框架 (Overall Architecture)

### 2.1. 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    Gakumasu-Bot 系统架构                      │
├─────────────────────────────────────────────────────────────┤
│  用户界面层                                                   │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │ Web UI      │  │ GUI启动器    │  │ 命令行界面   │          │
│  │ (Vue.js)    │  │ (Python)    │  │ (Python)    │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│  API服务层                                                    │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              FastAPI Web服务                            │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │ │
│  │  │ REST API    │  │ WebSocket   │  │ 静态文件     │     │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘     │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  核心业务层                                                   │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │ 调度模块     │  │ 感知模块     │  │ 决策模块     │          │
│  │ Scheduler   │  │ Perception  │  │ Decision    │          │
│  │             │  │             │  │             │          │
│  │ TaskManager │  │ScreenCapture│  │ MCTS Engine │          │
│  │ StateManager│  │SceneRecognize│  │ Heuristic   │          │
│  │ ConfigMgr   │  │TemplateMatch│  │ EventHandler│          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
│                                    ┌─────────────┐          │
│                                    │ 行动模块     │          │
│                                    │ Action      │          │
│                                    │             │          │
│                                    │InputSimulator│         │
│                                    │GameLauncher │          │
│                                    │ActionVerifier│         │
│                                    └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│  基础设施层                                                   │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │ 日志系统     │  │ 配置管理     │  │ 数据存储     │          │
│  │ Logger      │  │ Config      │  │ JSON/YAML   │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
```

### 2.2. 技术栈

* **后端框架**: Python 3.13.3, FastAPI 0.104.1+, APScheduler 3.10.4+
* **前端框架**: Vue.js 3.4.0+, Element Plus 2.4.4+, Vite 5.0.10+
* **计算机视觉**: OpenCV 4.9.0+, EasyOCR 1.7.1+ (日语OCR支持)
* **深度学习**: PyTorch 2.3.0+, Ultralytics YOLO 8.2.28+
* **输入模拟**: PyDirectInput 1.0.4+, PyWin32 306+
* **数据处理**: NumPy 1.26.4+, Pandas 2.2.2+
* **配置管理**: PyYAML 6.0.1+
* **实时通信**: WebSocket, Socket.IO
* **测试框架**: pytest 8.2.2+, pytest-cov 5.0.0+
* **开发工具**: Black, Flake8, MyPy, ESLint, Prettier

### 2.3. 部署架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    开发/测试环境部署                          │
├─────────────────────────────────────────────────────────────┤
│  本地开发机器 (Windows)                                       │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │  前端开发服务器 (Vite)                                   │ │
│  │  http://localhost:3000                                  │ │
│  └─────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │  后端API服务器 (FastAPI + Uvicorn)                       │ │
│  │  http://localhost:8000                                  │ │
│  └─────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │  游戏客户端 (DMM Player + 学园偶像大师)                   │ │
│  │  - 屏幕捕获目标                                          │ │
│  │  - 输入模拟目标                                          │ │
│  └─────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │  文件系统                                                │ │
│  │  - 配置文件 (config/)                                    │ │
│  │  - 日志文件 (logs/)                                      │ │
│  │  - 状态数据 (data/)                                      │ │
│  │  - 模板资源 (assets/)                                    │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 3. 关键实现与数据库 (Key Implementation & Database)

### 3.1. 核心业务流程

**主要业务流程 - 自动化育成流程**:

```
开始 → 系统初始化 → 游戏启动检查 → 场景识别 → 决策执行 → 操作验证 → 状态更新 → 循环/结束
  │         │           │           │         │         │         │
  ├─配置加载  ├─启动DMM    ├─屏幕捕获   ├─AI决策  ├─键鼠模拟 ├─结果验证 ├─数据持久化
  ├─日志初始化├─窗口定位   ├─模板匹配   ├─MCTS   ├─延迟控制 ├─错误处理 ├─任务调度
  └─模块启动  └─游戏检测   └─状态解析   └─启发式 └─安全检查 └─重试机制 └─状态同步
```

**感知-决策-行动循环**:
1. **感知阶段**: 屏幕捕获 → 场景识别 → 状态解析 → GameState构建
2. **决策阶段**: 策略选择 → AI算法执行 → Action生成 → 置信度评估
3. **行动阶段**: 操作执行 → 结果验证 → 状态反馈 → 错误处理

### 3.2. 关键实现与技术债

**关键实现**:

* **智能决策系统**: 
  - 启发式评分系统：5维度评估（得分潜力、资源效率、协同奖励、风险评估、长期价值）
  - MCTS算法引擎：完整实现选择、扩展、模拟、反向传播四个阶段
  - 事件处理器：支持7种事件类型，多重识别机制

* **高精度感知系统**:
  - 屏幕捕获延迟 <50ms，场景识别准确率 >90%
  - 多尺度模板匹配，支持0.8-1.2倍缩放
  - 预留OCR接口，支持日语文本识别

* **可靠行动系统**:
  - 操作响应时间 <200ms，成功率 >98%
  - 安全延迟机制，防止操作过快被检测
  - 完整的操作验证和错误恢复机制

**技术债**:

* **OCR集成未完成**: EasyOCR接口已预留，但未完全集成到事件处理流程中
* **生产部署方案缺失**: 缺乏完整的生产环境部署和运维监控方案
* **游戏适配待验证**: 核心功能已实现，但需要在实际游戏环境中进行全面测试
* **高级功能待扩展**: 数据分析、性能优化、自动化报告等高级功能有待开发

### 3.3. 数据库设计

**数据存储方案**: 采用文件系统存储，主要使用JSON和YAML格式

**核心数据结构**:

```python
# 游戏状态数据结构
@dataclass
class GameState:
    current_scene: GameScene          # 当前场景
    current_language: str             # 语言设置
    timestamp: datetime               # 状态时间戳
    stamina: int                      # 体力值
    vigor: int                        # 元气值
    score: int                        # 当前分数
    current_week: int                 # 当前周数
    hand: List[Card]                  # 手牌
    idol_stats: Dict[str, int]        # 偶像属性
    ui_elements: Dict[str, Any]       # UI元素位置
```

**数据文件组织**:
- `data/state.json`: 当前游戏状态
- `data/cards.json`: 卡牌数据库
- `data/events.json`: 事件数据库
- `config/settings.yaml`: 系统配置
- `config/user_strategy.yaml`: 用户策略
- `logs/`: 日志文件目录

## 4. 接口说明 (API Specification)

### 4.1. 接口规范

* **协议**: RESTful API + WebSocket
* **数据格式**: JSON
* **认证机制**: 暂未实现（开发环境无需认证）
* **基础URL**: http://localhost:8000

### 4.2. 通用数据结构

* **统一响应体格式**:

```json
{
  "code": 0,           // 0 表示成功，非 0 表示失败
  "message": "Success", // 响应消息
  "data": { ... },     // 实际数据
  "timestamp": "2025-08-05T10:30:00Z"  // 响应时间戳
}
```

* **WebSocket消息格式**:

```json
{
  "type": "status_update",    // 消息类型
  "data": { ... },           // 消息数据
  "timestamp": "2025-08-05T10:30:00Z"
}
```

### 4.3. 错误码定义

| 错误码 | 含义 | 描述 |
|--------|------|------|
| 0 | 成功 | 操作成功完成 |
| 1001 | 参数错误 | 请求参数不正确 |
| 1002 | 系统错误 | 内部系统错误 |
| 2001 | 游戏未运行 | 游戏客户端未启动 |
| 2002 | 场景识别失败 | 无法识别当前游戏场景 |
| 2003 | 操作执行失败 | 游戏操作执行失败 |
| 3001 | 配置错误 | 配置文件错误或缺失 |
| 3002 | 模块未初始化 | 核心模块未正确初始化 |

### 4.4. 接口列表 (API List)

**系统管理接口**:
- `GET /health` - 健康检查
- `GET /status` - 系统状态查询
- `POST /start` - 启动系统
- `POST /stop` - 停止系统
- `POST /restart` - 重启系统

**配置管理接口**:
- `GET /config` - 获取配置信息
- `PUT /config` - 更新配置
- `POST /config/reload` - 重新加载配置

**游戏控制接口**:
- `GET /game/status` - 游戏状态查询
- `POST /game/launch` - 启动游戏
- `POST /game/action` - 执行游戏操作
- `GET /game/screenshot` - 获取游戏截图

**WebSocket事件**:
- `status_update` - 系统状态更新
- `game_state_change` - 游戏状态变化
- `action_result` - 操作执行结果
- `error_occurred` - 错误事件

## 5. 项目目录构成 (Directory Structure)

### 5.1. 后端项目结构

```
Gakumasu-Bot/
├── main.py                    # 命令行主程序入口
├── gui.py                     # GUI启动器
├── requirements.txt           # Python依赖管理
├── src/                      # 源代码目录
│   ├── core/                 # 核心数据结构
│   │   ├── __init__.py
│   │   └── data_structures.py    # GameState, Card, Action等
│   ├── modules/              # 功能模块
│   │   ├── perception/       # 感知模块
│   │   │   ├── __init__.py
│   │   │   ├── perception_module.py     # 感知模块主类
│   │   │   ├── screen_capture.py        # 屏幕捕获
│   │   │   ├── scene_recognizer.py      # 场景识别
│   │   │   └── template_matcher.py      # 模板匹配
│   │   ├── decision/         # 决策模块
│   │   │   ├── __init__.py
│   │   │   ├── decision_module.py       # 决策模块主类
│   │   │   ├── heuristic_evaluator.py   # 启发式评分
│   │   │   ├── mcts_engine.py           # MCTS算法
│   │   │   └── event_handler.py         # 事件处理
│   │   ├── action/           # 行动模块
│   │   │   ├── __init__.py
│   │   │   ├── action_controller.py     # 行动控制器
│   │   │   ├── input_simulator.py       # 输入模拟
│   │   │   ├── game_launcher.py         # 游戏启动
│   │   │   └── action_verifier.py       # 操作验证
│   │   ├── scheduler/        # 调度模块
│   │   │   ├── __init__.py
│   │   │   ├── scheduler.py             # 调度器主类
│   │   │   ├── task_manager.py          # 任务管理
│   │   │   ├── state_manager.py         # 状态管理
│   │   │   └── config_manager.py        # 配置管理
│   │   └── ui/               # UI管理模块
│   ├── utils/               # 工具模块
│   │   ├── __init__.py
│   │   ├── logger.py                    # 日志系统
│   │   └── config_loader.py             # 配置加载器
│   └── web/                 # Web服务模块
│       ├── __init__.py
│       ├── main.py                      # FastAPI应用
│       ├── models.py                    # API数据模型
│       └── static/                      # 静态文件
├── config/                  # 配置文件目录
│   ├── settings.yaml                    # 系统设置
│   ├── settings.yaml.example           # 设置模板
│   ├── user_strategy.yaml.example      # 用户策略模板
│   ├── scene_config.yaml               # 场景配置
│   ├── ui_config.yaml                  # UI配置
│   ├── presets/                        # 预设配置
│   ├── profiles/                       # 用户配置文件
│   └── scenarios/                      # 场景配置
├── data/                    # 数据文件目录
│   ├── state.json.example              # 状态数据模板
│   ├── cards.json.example              # 卡牌数据模板
│   ├── events.json.example             # 事件数据模板
│   └── states/                         # 状态快照
├── assets/                  # 资源文件目录
│   └── templates/                      # 模板图片
├── logs/                    # 日志文件目录
├── screenshots/             # 截图文件目录
├── test/                    # 测试文件目录
├── tests/                   # 单元测试目录
├── tools/                   # 工具脚本目录
├── examples/                # 示例代码目录
└── docs/                    # 项目文档目录
```

### 5.2. 前端项目结构

```
frontend/
├── package.json             # Node.js依赖管理
├── vite.config.js          # Vite构建配置
├── index.html              # 入口HTML文件
├── src/                    # 源代码目录
│   ├── main.js             # 应用入口
│   ├── App.vue             # 根组件
│   ├── components/         # 通用组件
│   │   ├── common/         # 基础组件
│   │   ├── charts/         # 图表组件
│   │   ├── forms/          # 表单组件
│   │   └── debug/          # 调试控制台组件
│   │       ├── SystemStatusPanel.vue    # 系统状态面板
│   │       ├── GameStateMonitor.vue     # 游戏状态监控
│   │       ├── FunctionTestPanel.vue    # 功能测试面板
│   │       ├── LogViewer.vue            # 日志查看器
│   │       └── ConfigManager.vue        # 配置管理器
│   ├── views/              # 页面组件
│   │   ├── Dashboard.vue   # 仪表板
│   │   ├── ScreenshotTool.vue # 截图工具
│   │   ├── DebugConsole.vue    # 调试控制台主页面
│   │   ├── DebugConsoleWorking.vue # 调试控制台工作版本
│   │   └── DebugConsoleSimple.vue  # 调试控制台简化版本
│   ├── router/             # 路由配置
│   │   └── index.js
│   ├── composables/        # 组合式API
│   │   ├── useWebSocket.js # WebSocket连接
│   │   ├── useApi.js       # HTTP API调用
│   │   ├── useDebugConsole.js # 调试控制台功能
│   │   └── useDebugConsoleFixed.js # 调试控制台修复版本
│   │   └── useConfig.js    # 配置管理
│   └── assets/             # 静态资源
│       ├── styles/         # 样式文件
│       └── images/         # 图片资源
├── node_modules/           # Node.js依赖
├── auto-imports.d.ts       # 自动导入类型定义
└── components.d.ts         # 组件类型定义
```

## 6. 环境与部署 (Environment & Deployment)

### 6.1. 本地环境搭建

**前置依赖**:
- Python 3.13.3+ (推荐使用最新版本)
- Node.js 18.0.0+ 和 npm 8.0.0+
- Git 版本控制工具
- DMM Player (用于游戏启动)
- 《学园偶像大师》游戏客户端

**Python依赖安装**:
```bash
# 克隆项目
git clone <repository-url>
cd Gakumasu-Bot

# 创建虚拟环境 (推荐)
python -m venv venv
venv\Scripts\activate  # Windows
# source venv/bin/activate  # Linux/Mac

# 安装Python依赖
pip install -r requirements.txt
```

**前端环境搭建**:
```bash
# 进入前端目录
cd frontend

# 安装Node.js依赖
npm install

# 启动开发服务器
npm run dev
```

**配置文件设置**:
1. 复制 `config/settings.yaml.example` 为 `config/settings.yaml`
2. 复制 `config/user_strategy.yaml.example` 为 `config/user_strategy.yaml`
3. 根据实际情况修改配置文件中的路径和参数
4. 确保DMM Player路径配置正确

**启动步骤**:
```bash
# 方式1: 使用GUI启动器 (推荐)
python gui.py

# 方式2: 分别启动前后端
# 终端1: 启动后端
python -m uvicorn src.web.main:app --reload --host 127.0.0.1 --port 8000

# 终端2: 启动前端
cd frontend && npm run dev

# 方式3: 仅启动命令行版本
python main.py
```

**调试控制台使用说明**:
```bash
# 启动前后端服务后，访问调试控制台
# 主要访问地址（推荐）
http://localhost:3000/debug-working

# 备用访问地址
http://localhost:3000/debug-console    # 完整版本
http://localhost:3000/debug-simple     # 简化版本
```

**调试控制台功能导航**:
1. **连接测试**：验证前后端服务连接状态，诊断网络问题
2. **系统状态**：查看系统运行状态、任务统计、游戏状态信息
3. **API测试**：测试各种后端API接口的连通性和响应
4. **日志查看**：实时查看系统日志，支持过滤和搜索
5. **配置管理**：动态修改系统配置参数，支持预设切换

**快速诊断步骤**:
1. 访问调试控制台主页面
2. 点击"测试所有连接"验证服务状态
3. 切换到"系统状态"查看详细信息
4. 如有问题，查看"日志查看"获取错误详情

### 6.2. 部署流程

**开发环境部署**:
1. 按照本地环境搭建步骤完成环境配置
2. 使用 `python gui.py` 启动完整服务
3. 访问 http://localhost:3000 使用Web界面
4. 访问 http://localhost:3000/debug-working 使用调试控制台
5. 或使用 `python main.py` 启动命令行版本

**测试环境部署**:
1. 确保所有依赖正确安装
2. 运行测试套件验证功能：
   ```bash
   # 运行单元测试
   python -m pytest tests/ -v

   # 运行集成测试
   python -m pytest test/ -v

   # 运行性能测试
   python test/test_performance.py
   ```
3. 验证各模块功能正常

**生产环境部署** (待完善):
- 当前版本主要面向开发和测试环境
- 生产环境部署方案需要进一步开发
- 建议使用Docker容器化部署
- 需要配置反向代理和负载均衡

## 7. 运维与监控 (Operations & Monitoring)

### 7.1. 日志与监控

**日志位置**:
- 主日志文件: `logs/gakumasu_bot_YYYYMMDD.log`
- 错误日志文件: `logs/gakumasu_bot_error_YYYYMMDD.log`
- 调试截图: `logs/debug_screenshots/` (可选)

**日志级别配置**:
```yaml
# config/settings.yaml
system:
  log_level: INFO  # DEBUG, INFO, WARNING, ERROR, CRITICAL
```

**监控指标**:
- 系统状态: 各模块运行状态和健康检查
- 性能指标: 屏幕捕获延迟、决策时间、操作响应时间
- 业务指标: 场景识别准确率、操作成功率、任务完成率
- 资源使用: CPU、内存、磁盘使用情况

**监控面板**:
- **调试控制台**（完整实现，访问地址：http://localhost:3000/debug-working）:
  - **系统状态监控面板**：
    * 实时显示四大模块（感知、决策、行动、调度）运行状态
    * 系统性能指标：CPU、内存使用率，运行时间统计
    * 任务统计：活跃任务数、已完成任务数、失败任务数
    * 连接状态监控：API连接状态、WebSocket连接状态
    * 系统控制操作：启动、暂停、停止、重启系统功能
  - **游戏状态监控界面**：
    * 实时游戏画面预览和全屏查看功能
    * GameState详细信息：场景、体力、分数、周数等
    * 资源状态可视化：体力值、元气值进度条显示
    * 偶像属性统计和手牌信息展示
    * 截图功能测试和历史记录查看
  - **功能测试面板**：
    * 感知模块测试：屏幕捕获、场景识别功能验证
    * 决策模块测试：MCTS算法、启发式评分测试
    * 行动模块测试：输入模拟、操作验证功能
    * 调度模块测试：任务调度、配置管理测试
    * 综合系统测试：完整功能流程验证
    * 测试结果详细展示、历史记录和结果导出
  - **实时日志查看器**：
    * 多级别日志过滤：DEBUG、INFO、WARNING、ERROR、CRITICAL
    * 日志内容搜索和关键词高亮
    * 日志统计信息和错误追踪
    * 自动滚动和手动滚动模式切换
    * 日志复制和导出功能
  - **配置管理界面**：
    * 分类配置编辑：系统、游戏、AI、UI配置分类管理
    * 实时配置修改和参数验证
    * 配置预设快速切换（性能优先、精度优先等）
    * JSON高级编辑模式和配置导入导出
    * 配置重置和备份恢复功能
  - **API连接测试功能**：
    * 前端服务连接测试
    * 后端健康检查和API接口测试
    * 网络连接状态诊断
    * 接口响应时间监控
- **技术特性**：
  - 基于Vue.js 3 + Element Plus构建的现代化界面
  - 支持实时数据更新和WebSocket通信
  - 响应式设计，支持多种屏幕尺寸
  - 模块化组件架构，易于维护和扩展
  - 完整的错误处理和用户反馈机制

### 7.2. 安全配置

**当前安全措施**:
- 本地文件系统权限控制
- 配置文件敏感信息保护
- 操作延迟和安全检查机制
- 错误处理和异常恢复

**安全建议**:
- 定期备份配置文件和重要数据
- 监控系统资源使用情况
- 及时更新依赖库版本
- 在生产环境中启用更严格的安全策略

**风险控制**:
- 游戏操作频率限制，避免被检测
- 异常情况自动停止机制
- 完整的操作日志记录
- 支持手动干预和紧急停止

---

## 附录

### A. 常见问题解答

**Q: 如何解决游戏窗口识别失败？**
A: 检查 `config/settings.yaml` 中的 `game_window_title` 设置，确保与实际游戏窗口标题匹配。

**Q: 为什么MCTS决策很慢？**
A: 可以调整 `config/settings.yaml` 中的 `mcts_iterations` 和 `mcts_timeout` 参数来平衡决策质量和速度。

**Q: 如何添加新的游戏场景识别？**
A: 在 `assets/templates/` 目录添加场景模板图片，并在 `config/scene_config.yaml` 中配置相应的识别规则。

### B. 开发指南

**代码规范**:
- 使用 Black 进行代码格式化
- 使用 Flake8 进行代码检查
- 使用 MyPy 进行类型检查
- 遵循 PEP 8 编码规范

**测试要求**:
- 新功能必须包含单元测试
- 测试覆盖率要求达到80%以上
- 集成测试验证模块间协作
- 性能测试确保响应时间要求

**文档更新**:
- 代码变更需要同步更新文档
- API变更需要更新接口文档
- 新功能需要更新用户指南
- 重要变更需要更新本知识库文档

### C. 版本历史

- **v1.0.0** (当前版本): 核心功能实现，支持基础自动化操作
- **v0.9.x**: 各模块开发和集成测试阶段
- **v0.8.x**: 核心架构搭建和基础功能实现
- **v0.7.x**: 项目初始化和设计文档完成

---

*本文档最后更新时间: 2025-08-05*
*文档版本: V1.0*
*项目版本: v1.0.0*
