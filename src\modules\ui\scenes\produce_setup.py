"""
育成准备场景实现
处理偶像选择、支援卡配置等育成前的准备工作
"""

import time
from typing import List, Dict, Any, Optional, Tuple

from ....core.data_structures import GameScene
from ..config.scene_config import SceneConfig
from ..config.ui_element_config import ButtonConfig, LabelConfig
from .base_game_scene import BaseGameScene


class ProduceSetupScene(BaseGameScene):
    """育成准备场景类"""
    
    def __init__(self, config: SceneConfig, perception_module, action_controller):
        """
        初始化育成准备场景
        
        Args:
            config: 场景配置
            perception_module: 感知模块
            action_controller: 行动控制器
        """
        super().__init__(config, perception_module, action_controller)
        
        # 育成准备特定状态
        self._idol_selected = False
        self._support_cards_configured = False
        self._setup_complete = False
        self._selected_idol_info = {}
        self._support_card_count = 0
        self._max_support_cards = 6
        
        self.scene_logger.info("育成准备场景初始化完成")
    
    def _create_scene_ui_elements(self):
        """创建育成准备特定的UI元素"""
        try:
            # 场景标识元素
            self._create_scene_indicators()
            
            # 偶像选择相关元素
            self._create_idol_selection_elements()
            
            # 支援卡配置相关元素
            self._create_support_card_elements()
            
            # 控制按钮
            self._create_control_buttons()
            
            # 信息显示元素
            self._create_info_elements()
            
            self.scene_logger.info(f"育成准备UI元素创建完成，共{len(self.ui_elements)}个元素")
            
        except Exception as e:
            self.scene_logger.error(f"育成准备UI元素创建失败: {e}")
            raise
    
    def _create_scene_indicators(self):
        """创建场景标识元素"""
        # 育成准备标题
        self.ui_elements["produce_setup_title"] = self.ui_factory.create_label(
            "produce_setup_title",
            confidence_threshold=0.9,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
        
        # 偶像选择区域
        self.ui_elements["idol_selection_area"] = self.ui_factory.create_label(
            "idol_selection_area",
            confidence_threshold=0.8,
            text_recognition_enabled=False
        )
        
        # 支援卡区域
        self.ui_elements["support_card_area"] = self.ui_factory.create_label(
            "support_card_area",
            confidence_threshold=0.8,
            text_recognition_enabled=False
        )
    
    def _create_idol_selection_elements(self):
        """创建偶像选择相关元素"""
        # 偶像选择按钮
        self.ui_elements["idol_selection_button"] = self.ui_factory.create_enhanced_button(
            "idol_selection_button",
            confidence_threshold=0.8,
            timeout=5.0,
            verify_click_result=True
        )
        
        # 偶像列表区域
        self.ui_elements["idol_list"] = self.ui_factory.create_label(
            "idol_list",
            confidence_threshold=0.7,
            text_recognition_enabled=False
        )
        
        # 偶像详情显示
        self.ui_elements["idol_details"] = self.ui_factory.create_label(
            "idol_details",
            confidence_threshold=0.7,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
        
        # 偶像确认按钮
        self.ui_elements["idol_confirm_button"] = self.ui_factory.create_button(
            "idol_confirm_button",
            confidence_threshold=0.8,
            timeout=3.0
        )
        
        # 偶像取消按钮
        self.ui_elements["idol_cancel_button"] = self.ui_factory.create_button(
            "idol_cancel_button",
            confidence_threshold=0.8,
            timeout=3.0
        )
    
    def _create_support_card_elements(self):
        """创建支援卡配置相关元素"""
        # 支援卡配置按钮
        self.ui_elements["support_card_button"] = self.ui_factory.create_enhanced_button(
            "support_card_button",
            confidence_threshold=0.8,
            timeout=5.0,
            verify_click_result=True
        )
        
        # 支援卡槽位（6个槽位）
        for i in range(1, 7):
            self.ui_elements[f"support_slot_{i}"] = self.ui_factory.create_button(
                f"support_slot_{i}",
                confidence_threshold=0.7,
                timeout=3.0
            )
        
        # 支援卡列表
        self.ui_elements["support_card_list"] = self.ui_factory.create_label(
            "support_card_list",
            confidence_threshold=0.7,
            text_recognition_enabled=False
        )
        
        # 支援卡详情
        self.ui_elements["support_card_details"] = self.ui_factory.create_label(
            "support_card_details",
            confidence_threshold=0.7,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
        
        # 支援卡确认按钮
        self.ui_elements["support_confirm_button"] = self.ui_factory.create_button(
            "support_confirm_button",
            confidence_threshold=0.8,
            timeout=3.0
        )
        
        # 自动配置按钮
        self.ui_elements["auto_config_button"] = self.ui_factory.create_button(
            "auto_config_button",
            confidence_threshold=0.8,
            timeout=3.0
        )
    
    def _create_control_buttons(self):
        """创建控制按钮"""
        # 开始育成按钮
        self.ui_elements["start_produce_button"] = self.ui_factory.create_enhanced_button(
            "start_produce_button",
            confidence_threshold=0.9,
            timeout=5.0,
            expected_scene_after_click="produce_main",
            verify_click_result=True
        )
        
        # 返回按钮
        self.ui_elements["back_button"] = self.ui_factory.create_button(
            "back_button",
            confidence_threshold=0.8,
            timeout=3.0
        )
        
        # 重置按钮
        self.ui_elements["reset_button"] = self.ui_factory.create_button(
            "reset_button",
            confidence_threshold=0.8,
            timeout=3.0
        )
    
    def _create_info_elements(self):
        """创建信息显示元素"""
        # 选中偶像名称
        self.ui_elements["selected_idol_name"] = self.ui_factory.create_enhanced_label(
            "selected_idol_name",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
        
        # 支援卡数量显示
        self.ui_elements["support_count_label"] = self.ui_factory.create_label(
            "support_count_label",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="en"
        )
        
        # 预期育成时间
        self.ui_elements["estimated_time_label"] = self.ui_factory.create_label(
            "estimated_time_label",
            confidence_threshold=0.7,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
    
    def _get_critical_elements(self) -> List[str]:
        """获取育成准备的关键UI元素"""
        return [
            "produce_setup_title",
            "idol_selection_button",
            "support_card_button",
            "start_produce_button"
        ]
    
    def _verify_scene_specific_state(self) -> bool:
        """验证育成准备特定状态"""
        try:
            # 验证标题可见
            title_element = self.get_ui_element("produce_setup_title")
            if not title_element or not title_element.is_visible():
                self.scene_logger.debug("育成准备标题不可见")
                return False
            
            # 验证主要功能区域可见
            idol_area = self.get_ui_element("idol_selection_area")
            support_area = self.get_ui_element("support_card_area")
            
            if not (idol_area and idol_area.is_visible()):
                self.scene_logger.debug("偶像选择区域不可见")
                return False
            
            if not (support_area and support_area.is_visible()):
                self.scene_logger.debug("支援卡区域不可见")
                return False
            
            return True
            
        except Exception as e:
            self.scene_logger.error(f"育成准备状态验证异常: {e}")
            return False
    
    def select_idol(self, idol_name: Optional[str] = None) -> bool:
        """
        选择偶像
        
        Args:
            idol_name: 偶像名称，None表示使用默认选择
            
        Returns:
            是否选择成功
        """
        try:
            self.scene_logger.info(f"开始选择偶像: {idol_name or '默认'}")
            
            # 点击偶像选择按钮
            if not self.interact_with_element("idol_selection_button", "click"):
                self.scene_logger.error("点击偶像选择按钮失败")
                return False
            
            # 等待偶像列表加载
            time.sleep(1.5)
            
            # 如果指定了偶像名称，尝试选择特定偶像
            if idol_name:
                success = self._select_specific_idol(idol_name)
            else:
                # 使用默认选择（通常是第一个可用偶像）
                success = self._select_default_idol()
            
            if success:
                # 确认选择
                if self.interact_with_element("idol_confirm_button", "click"):
                    self._idol_selected = True
                    self._update_selected_idol_info()
                    self.scene_logger.info("偶像选择成功")
                    return True
                else:
                    self.scene_logger.error("确认偶像选择失败")
                    return False
            else:
                # 取消选择
                self.interact_with_element("idol_cancel_button", "click")
                return False
                
        except Exception as e:
            self.scene_logger.error(f"选择偶像失败: {e}")
            return False
    
    def _select_specific_idol(self, idol_name: str) -> bool:
        """选择特定偶像"""
        try:
            # 这里需要实现具体的偶像选择逻辑
            # 可能需要滚动列表、识别偶像名称等
            self.scene_logger.info(f"尝试选择特定偶像: {idol_name}")
            
            # 简化实现：假设点击偶像列表中的第一个偶像
            time.sleep(1.0)
            return True
            
        except Exception as e:
            self.scene_logger.error(f"选择特定偶像失败: {e}")
            return False
    
    def _select_default_idol(self) -> bool:
        """选择默认偶像"""
        try:
            self.scene_logger.info("选择默认偶像")
            
            # 简化实现：等待一段时间后返回成功
            time.sleep(1.0)
            return True
            
        except Exception as e:
            self.scene_logger.error(f"选择默认偶像失败: {e}")
            return False
    
    def _update_selected_idol_info(self):
        """更新选中偶像信息"""
        try:
            # 读取偶像名称
            name_element = self.get_ui_element("selected_idol_name")
            if name_element:
                idol_name = name_element.read_text()
                if idol_name:
                    self._selected_idol_info["name"] = idol_name.strip()
            
            # 读取偶像详情
            details_element = self.get_ui_element("idol_details")
            if details_element:
                details_text = details_element.read_text()
                if details_text:
                    self._selected_idol_info["details"] = details_text.strip()
            
            self.scene_logger.debug(f"更新偶像信息: {self._selected_idol_info}")
            
        except Exception as e:
            self.scene_logger.error(f"更新偶像信息失败: {e}")
    
    def configure_support_cards(self, auto_config: bool = True) -> bool:
        """
        配置支援卡
        
        Args:
            auto_config: 是否使用自动配置
            
        Returns:
            是否配置成功
        """
        try:
            self.scene_logger.info(f"开始配置支援卡，自动配置: {auto_config}")
            
            # 点击支援卡配置按钮
            if not self.interact_with_element("support_card_button", "click"):
                self.scene_logger.error("点击支援卡配置按钮失败")
                return False
            
            # 等待支援卡界面加载
            time.sleep(1.5)
            
            if auto_config:
                # 使用自动配置
                success = self._auto_configure_support_cards()
            else:
                # 手动配置
                success = self._manual_configure_support_cards()
            
            if success:
                # 确认配置
                if self.interact_with_element("support_confirm_button", "click"):
                    self._support_cards_configured = True
                    self._update_support_card_count()
                    self.scene_logger.info("支援卡配置成功")
                    return True
                else:
                    self.scene_logger.error("确认支援卡配置失败")
                    return False
            else:
                return False
                
        except Exception as e:
            self.scene_logger.error(f"配置支援卡失败: {e}")
            return False
    
    def _auto_configure_support_cards(self) -> bool:
        """自动配置支援卡"""
        try:
            self.scene_logger.info("使用自动配置支援卡")
            
            # 点击自动配置按钮
            if self.interact_with_element("auto_config_button", "click"):
                time.sleep(2.0)  # 等待自动配置完成
                return True
            else:
                self.scene_logger.error("点击自动配置按钮失败")
                return False
                
        except Exception as e:
            self.scene_logger.error(f"自动配置支援卡失败: {e}")
            return False
    
    def _manual_configure_support_cards(self) -> bool:
        """手动配置支援卡"""
        try:
            self.scene_logger.info("手动配置支援卡")
            
            # 简化实现：依次点击支援卡槽位
            configured_count = 0
            for i in range(1, 7):
                slot_name = f"support_slot_{i}"
                if self.interact_with_element(slot_name, "click"):
                    time.sleep(0.5)
                    configured_count += 1
                    
                    # 简化：假设每次点击都成功配置了一张卡
                    if configured_count >= 4:  # 配置至少4张卡
                        break
            
            return configured_count >= 4
            
        except Exception as e:
            self.scene_logger.error(f"手动配置支援卡失败: {e}")
            return False
    
    def _update_support_card_count(self):
        """更新支援卡数量"""
        try:
            count_element = self.get_ui_element("support_count_label")
            if count_element:
                count_text = count_element.read_text()
                if count_text:
                    import re
                    count_match = re.search(r'(\d+)/(\d+)', count_text)
                    if count_match:
                        self._support_card_count = int(count_match.group(1))
                        self._max_support_cards = int(count_match.group(2))
            
            self.scene_logger.debug(f"支援卡数量: {self._support_card_count}/{self._max_support_cards}")
            
        except Exception as e:
            self.scene_logger.error(f"更新支援卡数量失败: {e}")
    
    def start_produce(self) -> bool:
        """开始育成"""
        try:
            self.scene_logger.info("准备开始育成")
            
            # 检查准备状态
            if not self._check_setup_complete():
                self.scene_logger.error("育成准备未完成")
                return False
            
            # 点击开始育成按钮
            if not self.interact_with_element("start_produce_button", "click"):
                self.scene_logger.error("点击开始育成按钮失败")
                return False
            
            # 等待场景切换
            time.sleep(3.0)
            
            self.scene_logger.info("育成开始成功")
            return True
            
        except Exception as e:
            self.scene_logger.error(f"开始育成失败: {e}")
            return False
    
    def _check_setup_complete(self) -> bool:
        """检查育成准备是否完成"""
        try:
            # 检查偶像是否已选择
            if not self._idol_selected:
                self.scene_logger.warning("偶像未选择")
                return False
            
            # 检查支援卡是否已配置
            if not self._support_cards_configured:
                self.scene_logger.warning("支援卡未配置")
                return False
            
            # 检查开始按钮是否可用
            start_button = self.get_ui_element("start_produce_button")
            if not start_button or not start_button.is_visible():
                self.scene_logger.warning("开始育成按钮不可用")
                return False
            
            self._setup_complete = True
            return True
            
        except Exception as e:
            self.scene_logger.error(f"检查育成准备状态失败: {e}")
            return False
    
    def reset_setup(self) -> bool:
        """重置育成准备"""
        try:
            self.scene_logger.info("重置育成准备")
            
            # 点击重置按钮
            if self.interact_with_element("reset_button", "click"):
                # 重置状态
                self._idol_selected = False
                self._support_cards_configured = False
                self._setup_complete = False
                self._selected_idol_info.clear()
                self._support_card_count = 0
                
                time.sleep(1.0)
                self.scene_logger.info("育成准备重置成功")
                return True
            else:
                self.scene_logger.error("点击重置按钮失败")
                return False
                
        except Exception as e:
            self.scene_logger.error(f"重置育成准备失败: {e}")
            return False
    
    def get_setup_status(self) -> Dict[str, Any]:
        """获取育成准备状态"""
        return {
            "idol_selected": self._idol_selected,
            "support_cards_configured": self._support_cards_configured,
            "setup_complete": self._setup_complete,
            "selected_idol_info": self._selected_idol_info.copy(),
            "support_card_count": self._support_card_count,
            "max_support_cards": self._max_support_cards
        }
    
    def _navigate_to_produce_main(self, timeout: float) -> bool:
        """导航到育成主界面"""
        try:
            self.scene_logger.info("导航到育成主界面")
            
            # 确保准备完成
            if not self._check_setup_complete():
                # 尝试快速完成准备
                if not self._quick_setup():
                    return False
            
            # 开始育成
            return self.start_produce()
            
        except Exception as e:
            self.scene_logger.error(f"导航到育成主界面失败: {e}")
            return False
    
    def _quick_setup(self) -> bool:
        """快速完成育成准备"""
        try:
            self.scene_logger.info("执行快速育成准备")
            
            # 快速选择偶像
            if not self._idol_selected:
                if not self.select_idol():
                    return False
            
            # 快速配置支援卡
            if not self._support_cards_configured:
                if not self.configure_support_cards(auto_config=True):
                    return False
            
            return True
            
        except Exception as e:
            self.scene_logger.error(f"快速育成准备失败: {e}")
            return False
