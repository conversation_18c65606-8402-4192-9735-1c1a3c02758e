<template>
  <div class="system-status-panel">
    <div class="panel-header">
      <h2>系统状态监控</h2>
      <div class="header-actions">
        <el-button 
          type="primary" 
          :icon="Refresh" 
          :loading="loading.systemStatus"
          @click="refreshStatus"
          size="small"
        >
          刷新状态
        </el-button>
        <el-switch
          v-model="autoRefresh"
          active-text="自动刷新"
          inactive-text="手动刷新"
          @change="toggleAutoRefresh"
        />
      </div>
    </div>

    <!-- 系统概览卡片 -->
    <div class="status-cards">
      <el-row :gutter="20">
        <!-- 系统状态卡片 -->
        <el-col :span="6">
          <el-card class="status-card">
            <div class="card-content">
              <div class="card-icon system-status" :class="systemStatusClass">
                <el-icon><Monitor /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">系统状态</div>
                <div class="card-value">{{ debugState.systemStatus.status }}</div>
                <div class="card-subtitle">运行时间: {{ formatUptime(debugState.systemStatus.uptime) }}</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 调度器状态卡片 -->
        <el-col :span="6">
          <el-card class="status-card">
            <div class="card-content">
              <div class="card-icon scheduler-status" :class="schedulerStatusClass">
                <el-icon><Timer /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">调度器状态</div>
                <div class="card-value">{{ debugState.systemStatus.scheduler_status }}</div>
                <div class="card-subtitle">活跃任务: {{ debugState.systemStatus.active_tasks }}</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 任务统计卡片 -->
        <el-col :span="6">
          <el-card class="status-card">
            <div class="card-content">
              <div class="card-icon task-stats">
                <el-icon><DataAnalysis /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">任务统计</div>
                <div class="card-value">{{ debugState.systemStatus.completed_tasks }}</div>
                <div class="card-subtitle">失败: {{ debugState.systemStatus.failed_tasks }}</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 连接状态卡片 -->
        <el-col :span="6">
          <el-card class="status-card">
            <div class="card-content">
              <div class="card-icon connection-status" :class="connectionStatusClass">
                <el-icon><Connection /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">连接状态</div>
                <div class="card-value">{{ connectionStatusText }}</div>
                <div class="card-subtitle">最后更新: {{ formatLastUpdate() }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 详细信息区域 -->
    <div class="detail-section">
      <el-row :gutter="20">
        <!-- 游戏状态信息 -->
        <el-col :span="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>游戏状态信息</span>
                <el-tag v-if="debugState.systemStatus.current_game_state" type="success">已连接</el-tag>
                <el-tag v-else type="info">未连接</el-tag>
              </div>
            </template>
            <div v-if="debugState.systemStatus.current_game_state" class="game-state-info">
              <el-descriptions :column="2" size="small">
                <el-descriptions-item label="当前场景">
                  {{ debugState.systemStatus.current_game_state.current_scene || '未知' }}
                </el-descriptions-item>
                <el-descriptions-item label="语言设置">
                  {{ debugState.systemStatus.current_game_state.current_language || '未知' }}
                </el-descriptions-item>
                <el-descriptions-item label="体力值">
                  {{ debugState.systemStatus.current_game_state.stamina || 0 }}
                </el-descriptions-item>
                <el-descriptions-item label="元气值">
                  {{ debugState.systemStatus.current_game_state.vigor || 0 }}
                </el-descriptions-item>
                <el-descriptions-item label="当前分数">
                  {{ debugState.systemStatus.current_game_state.score || 0 }}
                </el-descriptions-item>
                <el-descriptions-item label="当前周数">
                  {{ debugState.systemStatus.current_game_state.current_week || 0 }}
                </el-descriptions-item>
              </el-descriptions>
            </div>
            <div v-else class="no-game-state">
              <el-empty description="暂无游戏状态信息" :image-size="80" />
            </div>
          </el-card>
        </el-col>

        <!-- 系统控制面板 -->
        <el-col :span="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>系统控制</span>
              </div>
            </template>
            <div class="control-buttons">
              <el-button-group>
                <el-button 
                  type="success" 
                  :icon="VideoPlay"
                  @click="controlSystem('start')"
                  :disabled="debugState.systemStatus.status === '运行中'"
                >
                  启动系统
                </el-button>
                <el-button 
                  type="warning" 
                  :icon="VideoPause"
                  @click="controlSystem('pause')"
                  :disabled="debugState.systemStatus.status !== '运行中'"
                >
                  暂停系统
                </el-button>
                <el-button 
                  type="danger" 
                  :icon="VideoStop"
                  @click="controlSystem('stop')"
                  :disabled="debugState.systemStatus.status === '已停止'"
                >
                  停止系统
                </el-button>
              </el-button-group>
              <el-button 
                type="primary" 
                :icon="Refresh"
                @click="controlSystem('restart')"
                style="margin-left: 10px;"
              >
                重启系统
              </el-button>
            </div>
            
            <!-- 最近操作结果 -->
            <div v-if="recentTestResults.length > 0" class="recent-operations">
              <el-divider content-position="left">最近操作</el-divider>
              <div class="operation-list">
                <div 
                  v-for="result in recentTestResults.slice(0, 3)" 
                  :key="result.id"
                  class="operation-item"
                  :class="{ success: result.success, failed: !result.success }"
                >
                  <div class="operation-info">
                    <span class="operation-type">{{ getOperationTypeName(result.type) }}</span>
                    <span class="operation-time">{{ formatTime(result.timestamp) }}</span>
                  </div>
                  <div class="operation-status">
                    <el-tag v-if="result.success" type="success" size="small">成功</el-tag>
                    <el-tag v-else type="danger" size="small">失败</el-tag>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { 
  Monitor, Timer, DataAnalysis, Connection, Refresh, 
  VideoPlay, VideoPause, VideoStop 
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useDebugConsole } from '../../composables/useDebugConsoleFixed.js'

// 使用调试控制台功能
const {
  debugState,
  loading,
  isSystemHealthy,
  recentTestResults,
  getSystemStatus,
  controlSystem
} = useDebugConsole()

// 响应式数据
const autoRefresh = ref(false)
let refreshTimer = null

// 计算属性
const systemStatusClass = computed(() => {
  const status = debugState.systemStatus.status
  if (status === '运行中') return 'status-running'
  if (status === '已停止') return 'status-stopped'
  return 'status-unknown'
})

const schedulerStatusClass = computed(() => {
  const status = debugState.systemStatus.scheduler_status
  if (status === '运行中') return 'status-running'
  if (status === '已停止') return 'status-stopped'
  return 'status-unknown'
})

const connectionStatusClass = computed(() => {
  return debugState.connectionStatus.api ? 'status-connected' : 'status-disconnected'
})

const connectionStatusText = computed(() => {
  return debugState.connectionStatus.api ? 'API已连接' : 'API未连接'
})

// 方法
const refreshStatus = async () => {
  try {
    await getSystemStatus()
    ElMessage.success('状态刷新成功')
  } catch (error) {
    ElMessage.error('状态刷新失败')
  }
}

const toggleAutoRefresh = (enabled) => {
  if (enabled) {
    refreshTimer = setInterval(refreshStatus, 5000) // 每5秒刷新一次
    ElMessage.info('已启用自动刷新')
  } else {
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
    ElMessage.info('已关闭自动刷新')
  }
}

const formatUptime = (seconds) => {
  if (!seconds) return '0秒'
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟${secs}秒`
  } else {
    return `${secs}秒`
  }
}

const formatLastUpdate = () => {
  if (!debugState.connectionStatus.lastUpdate) return '从未'
  
  const now = new Date()
  const lastUpdate = new Date(debugState.connectionStatus.lastUpdate)
  const diff = Math.floor((now - lastUpdate) / 1000)
  
  if (diff < 60) return `${diff}秒前`
  if (diff < 3600) return `${Math.floor(diff / 60)}分钟前`
  return `${Math.floor(diff / 3600)}小时前`
}

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString()
}

const getOperationTypeName = (type) => {
  const typeNames = {
    'system_control': '系统控制',
    'screenshot': '截图测试',
    'system_status': '状态查询',
    'config_load': '配置加载'
  }
  return typeNames[type] || type
}

// 生命周期
onMounted(async () => {
  // 初始加载状态
  await refreshStatus()
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script>

<style scoped>
.system-status-panel {
  height: 100%;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e6e6e6;
}

.panel-header h2 {
  margin: 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.status-cards {
  margin-bottom: 20px;
}

.status-card {
  height: 120px;
}

.card-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.card-icon.status-running {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.card-icon.status-stopped {
  background: linear-gradient(135deg, #f56c6c, #f78989);
}

.card-icon.status-unknown {
  background: linear-gradient(135deg, #909399, #b1b3b8);
}

.card-icon.status-connected {
  background: linear-gradient(135deg, #409eff, #66b1ff);
}

.card-icon.status-disconnected {
  background: linear-gradient(135deg, #e6a23c, #ebb563);
}

.card-info {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 5px;
}

.card-value {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 5px;
}

.card-subtitle {
  font-size: 12px;
  color: #c0c4cc;
}

.detail-section {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.game-state-info {
  padding: 10px 0;
}

.no-game-state {
  padding: 20px 0;
  text-align: center;
}

.control-buttons {
  margin-bottom: 20px;
}

.recent-operations {
  margin-top: 15px;
}

.operation-list {
  max-height: 150px;
  overflow-y: auto;
}

.operation-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 5px;
  border-radius: 4px;
  background: #f8f9fa;
  border-left: 3px solid #e6e6e6;
}

.operation-item.success {
  border-left-color: #67c23a;
}

.operation-item.failed {
  border-left-color: #f56c6c;
}

.operation-info {
  display: flex;
  flex-direction: column;
}

.operation-type {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.operation-time {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}
</style>
