"""
输入模拟器
负责模拟键盘和鼠标输入，实现人性化的操作模拟
"""

import time
import random
import math
from typing import Tuple, Optional, List, Union
import pydirectinput
import win32api
import win32con

from ...utils.logger import get_logger
from ...core.data_structures import Action, ActionType, GakumasuBotException


class InputSimulationError(GakumasuBotException):
    """输入模拟错误"""
    pass


class InputSimulator:
    """输入模拟器类"""
    
    def __init__(self):
        """初始化输入模拟器"""
        self.logger = get_logger("InputSimulator")
        
        # 配置PyDirectInput
        pydirectinput.FAILSAFE = True  # 启用安全模式
        pydirectinput.PAUSE = 0.01     # 设置基础延迟
        
        # 人性化参数配置
        self.mouse_speed = 1.0          # 鼠标移动速度倍数
        self.click_duration = 0.1       # 点击持续时间
        self.key_press_duration = 0.05  # 按键持续时间
        
        # 随机化参数
        self.position_variance = 3      # 位置随机偏移范围（像素）
        self.timing_variance = 0.05     # 时间随机偏移范围（秒）
        
        # 安全参数
        self.min_delay = 0.05          # 最小操作间隔
        self.max_delay = 0.2           # 最大操作间隔
        self.safety_margin = 10        # 屏幕边缘安全边距
        
        self.logger.info("输入模拟器初始化完成")
    
    def move_mouse_smooth(self, target_x: int, target_y: int, duration: float = 0.5) -> bool:
        """
        平滑移动鼠标到目标位置
        
        Args:
            target_x: 目标X坐标
            target_y: 目标Y坐标
            duration: 移动持续时间（秒）
            
        Returns:
            是否移动成功
        """
        try:
            # 获取当前鼠标位置
            current_x, current_y = pydirectinput.position()
            
            # 添加随机偏移，模拟人类操作的不精确性
            offset_x = random.randint(-self.position_variance, self.position_variance)
            offset_y = random.randint(-self.position_variance, self.position_variance)
            target_x += offset_x
            target_y += offset_y
            
            # 计算移动距离和步数
            distance = math.sqrt((target_x - current_x)**2 + (target_y - current_y)**2)
            steps = max(int(distance / 10), 5)  # 至少5步
            
            # 计算每步的时间间隔
            step_duration = duration / steps
            
            # 生成贝塞尔曲线路径点
            path_points = self._generate_bezier_path(
                (current_x, current_y), (target_x, target_y), steps
            )
            
            # 沿路径移动
            for point in path_points:
                pydirectinput.moveTo(int(point[0]), int(point[1]))
                time.sleep(step_duration)
            
            # 确保到达目标位置
            pydirectinput.moveTo(target_x, target_y)
            
            self.logger.debug(f"鼠标平滑移动到 ({target_x}, {target_y})")
            return True
            
        except Exception as e:
            self.logger.error(f"鼠标移动失败: {e}")
            raise InputSimulationError(f"鼠标移动失败: {e}")
    
    def _generate_bezier_path(self, start: Tuple[int, int], end: Tuple[int, int], 
                             steps: int) -> List[Tuple[float, float]]:
        """
        生成贝塞尔曲线路径，模拟自然的鼠标移动轨迹
        
        Args:
            start: 起始点
            end: 结束点
            steps: 路径步数
            
        Returns:
            路径点列表
        """
        # 生成控制点，添加一些随机性
        mid_x = (start[0] + end[0]) / 2
        mid_y = (start[1] + end[1]) / 2
        
        # 添加随机偏移到中点，创建更自然的曲线
        offset_range = max(abs(end[0] - start[0]), abs(end[1] - start[1])) * 0.2
        control_x = mid_x + random.uniform(-offset_range, offset_range)
        control_y = mid_y + random.uniform(-offset_range, offset_range)
        
        control_point = (control_x, control_y)
        
        # 生成二次贝塞尔曲线点
        path_points = []
        for i in range(steps + 1):
            t = i / steps
            
            # 二次贝塞尔曲线公式
            x = (1-t)**2 * start[0] + 2*(1-t)*t * control_point[0] + t**2 * end[0]
            y = (1-t)**2 * start[1] + 2*(1-t)*t * control_point[1] + t**2 * end[1]
            
            path_points.append((x, y))
        
        return path_points
    
    def click(self, x: int, y: int, button: str = 'left', clicks: int = 1, 
              interval: float = 0.1) -> bool:
        """
        在指定位置点击鼠标
        
        Args:
            x: X坐标
            y: Y坐标
            button: 鼠标按钮 ('left', 'right', 'middle')
            clicks: 点击次数
            interval: 多次点击间隔
            
        Returns:
            是否点击成功
        """
        try:
            # 移动到目标位置
            self.move_mouse_smooth(x, y, duration=0.3)
            
            # 添加点击前的短暂延迟
            delay = self.min_delay + random.uniform(0, self.timing_variance)
            time.sleep(delay)
            
            # 执行点击
            for i in range(clicks):
                if i > 0:
                    time.sleep(interval)
                
                # 模拟按下和释放
                if button == 'left':
                    pydirectinput.mouseDown(button='left')
                    time.sleep(self.click_duration)
                    pydirectinput.mouseUp(button='left')
                elif button == 'right':
                    pydirectinput.mouseDown(button='right')
                    time.sleep(self.click_duration)
                    pydirectinput.mouseUp(button='right')
                elif button == 'middle':
                    pydirectinput.mouseDown(button='middle')
                    time.sleep(self.click_duration)
                    pydirectinput.mouseUp(button='middle')
            
            self.logger.debug(f"在 ({x}, {y}) 执行 {clicks} 次 {button} 点击")
            return True
            
        except Exception as e:
            self.logger.error(f"点击操作失败: {e}")
            raise InputSimulationError(f"点击操作失败: {e}")
    
    def double_click(self, x: int, y: int) -> bool:
        """
        双击指定位置
        
        Args:
            x: X坐标
            y: Y坐标
            
        Returns:
            是否双击成功
        """
        return self.click(x, y, clicks=2, interval=0.1)
    
    def drag(self, start_x: int, start_y: int, end_x: int, end_y: int, 
             duration: float = 1.0, button: str = 'left') -> bool:
        """
        拖拽操作
        
        Args:
            start_x: 起始X坐标
            start_y: 起始Y坐标
            end_x: 结束X坐标
            end_y: 结束Y坐标
            duration: 拖拽持续时间
            button: 鼠标按钮
            
        Returns:
            是否拖拽成功
        """
        try:
            # 移动到起始位置
            self.move_mouse_smooth(start_x, start_y, duration=0.3)
            time.sleep(0.1)
            
            # 按下鼠标
            pydirectinput.mouseDown(button=button)
            time.sleep(0.1)
            
            # 拖拽到目标位置
            self.move_mouse_smooth(end_x, end_y, duration=duration)
            time.sleep(0.1)
            
            # 释放鼠标
            pydirectinput.mouseUp(button=button)
            
            self.logger.debug(f"拖拽从 ({start_x}, {start_y}) 到 ({end_x}, {end_y})")
            return True
            
        except Exception as e:
            self.logger.error(f"拖拽操作失败: {e}")
            raise InputSimulationError(f"拖拽操作失败: {e}")
    
    def key_press(self, key: Union[str, List[str]], duration: Optional[float] = None) -> bool:
        """
        按键操作
        
        Args:
            key: 按键名称或按键组合列表
            duration: 按键持续时间
            
        Returns:
            是否按键成功
        """
        try:
            if duration is None:
                duration = self.key_press_duration
            
            if isinstance(key, str):
                # 单个按键
                pydirectinput.keyDown(key)
                time.sleep(duration)
                pydirectinput.keyUp(key)
                self.logger.debug(f"按键: {key}")
            else:
                # 组合按键
                # 按下所有按键
                for k in key:
                    pydirectinput.keyDown(k)
                    time.sleep(0.01)
                
                time.sleep(duration)
                
                # 释放所有按键（逆序）
                for k in reversed(key):
                    pydirectinput.keyUp(k)
                    time.sleep(0.01)
                
                self.logger.debug(f"组合按键: {'+'.join(key)}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"按键操作失败: {e}")
            raise InputSimulationError(f"按键操作失败: {e}")
    
    def type_text(self, text: str, interval: float = 0.05) -> bool:
        """
        输入文本
        
        Args:
            text: 要输入的文本
            interval: 字符间隔时间
            
        Returns:
            是否输入成功
        """
        try:
            for char in text:
                pydirectinput.write(char)
                # 添加随机间隔，模拟人类打字
                delay = interval + random.uniform(-interval*0.3, interval*0.3)
                time.sleep(max(delay, 0.01))
            
            self.logger.debug(f"输入文本: {text}")
            return True
            
        except Exception as e:
            self.logger.error(f"文本输入失败: {e}")
            raise InputSimulationError(f"文本输入失败: {e}")
    
    def scroll(self, x: int, y: int, direction: str, clicks: int = 3) -> bool:
        """
        滚轮操作
        
        Args:
            x: X坐标
            y: Y坐标
            direction: 滚动方向 ('up', 'down')
            clicks: 滚动次数
            
        Returns:
            是否滚动成功
        """
        try:
            # 移动到目标位置
            self.move_mouse_smooth(x, y, duration=0.2)
            time.sleep(0.1)
            
            # 执行滚动
            scroll_amount = clicks if direction == 'up' else -clicks
            pydirectinput.scroll(scroll_amount)
            
            self.logger.debug(f"在 ({x}, {y}) 滚动 {direction} {clicks} 次")
            return True
            
        except Exception as e:
            self.logger.error(f"滚动操作失败: {e}")
            raise InputSimulationError(f"滚动操作失败: {e}")
    
    def execute_action(self, action: Action) -> bool:
        """
        执行Action对象定义的操作
        
        Args:
            action: 行动对象
            
        Returns:
            是否执行成功
        """
        try:
            # 执行前延迟
            if action.delay_before > 0:
                time.sleep(action.delay_before)
            
            success = False
            
            if action.action_type == ActionType.CLICK:
                if isinstance(action.target, (tuple, list)) and len(action.target) == 2:
                    success = self.click(action.target[0], action.target[1])
                else:
                    raise InputSimulationError(f"无效的点击目标: {action.target}")
            
            elif action.action_type == ActionType.KEYPRESS:
                success = self.key_press(action.target)
            
            elif action.action_type == ActionType.SCROLL:
                if isinstance(action.target, dict):
                    success = self.scroll(
                        action.target.get('x', 0),
                        action.target.get('y', 0),
                        action.target.get('direction', 'up'),
                        action.target.get('clicks', 3)
                    )
                else:
                    raise InputSimulationError(f"无效的滚动目标: {action.target}")
            
            elif action.action_type == ActionType.DRAG:
                if isinstance(action.target, dict):
                    success = self.drag(
                        action.target.get('start_x', 0),
                        action.target.get('start_y', 0),
                        action.target.get('end_x', 0),
                        action.target.get('end_y', 0),
                        action.target.get('duration', 1.0)
                    )
                else:
                    raise InputSimulationError(f"无效的拖拽目标: {action.target}")
            
            else:
                raise InputSimulationError(f"不支持的操作类型: {action.action_type}")
            
            # 执行后延迟
            if action.delay_after > 0:
                time.sleep(action.delay_after)
            
            if success:
                self.logger.info(f"成功执行操作: {action.description}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"执行操作失败: {action.description}, 错误: {e}")
            raise InputSimulationError(f"执行操作失败: {e}")
    
    def get_mouse_position(self) -> Tuple[int, int]:
        """
        获取当前鼠标位置
        
        Returns:
            鼠标位置坐标
        """
        return pydirectinput.position()
    
    def is_position_safe(self, x: int, y: int) -> bool:
        """
        检查位置是否安全（不在屏幕边缘）
        
        Args:
            x: X坐标
            y: Y坐标
            
        Returns:
            位置是否安全
        """
        screen_width = win32api.GetSystemMetrics(win32con.SM_CXSCREEN)
        screen_height = win32api.GetSystemMetrics(win32con.SM_CYSCREEN)
        
        return (self.safety_margin <= x <= screen_width - self.safety_margin and
                self.safety_margin <= y <= screen_height - self.safety_margin)
    
    def get_simulator_info(self) -> dict:
        """
        获取模拟器信息
        
        Returns:
            模拟器配置信息
        """
        return {
            "mouse_speed": self.mouse_speed,
            "click_duration": self.click_duration,
            "key_press_duration": self.key_press_duration,
            "position_variance": self.position_variance,
            "timing_variance": self.timing_variance,
            "min_delay": self.min_delay,
            "max_delay": self.max_delay,
            "safety_margin": self.safety_margin,
            "current_mouse_position": self.get_mouse_position()
        }
