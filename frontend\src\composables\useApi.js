/**
 * API调用组合式函数
 * 提供统一的HTTP API调用接口
 */

import { ref, reactive } from 'vue'
import axios from 'axios'
import { ElMessage } from 'element-plus'

// API配置
const API_CONFIG = {
  baseURL: 'http://localhost:8000/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
}

// 创建axios实例
const apiClient = axios.create(API_CONFIG)

// 全局加载状态
const globalLoading = ref(false)

// 请求统计
const requestStats = reactive({
  total: 0,
  success: 0,
  failed: 0,
  pending: 0
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    requestStats.total++
    requestStats.pending++
    globalLoading.value = requestStats.pending > 0
    
    console.log(`[API] 发送请求: ${config.method?.toUpperCase()} ${config.url}`)
    return config
  },
  (error) => {
    requestStats.failed++
    requestStats.pending--
    globalLoading.value = requestStats.pending > 0
    
    console.error('[API] 请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    requestStats.success++
    requestStats.pending--
    globalLoading.value = requestStats.pending > 0
    
    console.log(`[API] 响应成功: ${response.config.method?.toUpperCase()} ${response.config.url}`)
    return response
  },
  (error) => {
    requestStats.failed++
    requestStats.pending--
    globalLoading.value = requestStats.pending > 0
    
    console.error('[API] 响应错误:', error)
    
    // 显示错误消息
    const message = error.response?.data?.message || error.message || '请求失败'
    ElMessage.error(message)
    
    return Promise.reject(error)
  }
)

/**
 * API调用组合式函数
 */
export function useApi() {
  
  /**
   * GET请求
   */
  const get = async (url, config = {}) => {
    try {
      const response = await apiClient.get(url, config)
      return response.data
    } catch (error) {
      throw error
    }
  }

  /**
   * POST请求
   */
  const post = async (url, data = {}, config = {}) => {
    try {
      const response = await apiClient.post(url, data, config)
      return response.data
    } catch (error) {
      throw error
    }
  }

  /**
   * PUT请求
   */
  const put = async (url, data = {}, config = {}) => {
    try {
      const response = await apiClient.put(url, data, config)
      return response.data
    } catch (error) {
      throw error
    }
  }

  /**
   * DELETE请求
   */
  const del = async (url, config = {}) => {
    try {
      const response = await apiClient.delete(url, config)
      return response.data
    } catch (error) {
      throw error
    }
  }

  /**
   * 上传文件
   */
  const upload = async (url, file, onProgress = null) => {
    const formData = new FormData()
    formData.append('file', file)
    
    const config = {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }
    
    if (onProgress) {
      config.onUploadProgress = (progressEvent) => {
        const progress = Math.round(
          (progressEvent.loaded * 100) / progressEvent.total
        )
        onProgress(progress)
      }
    }
    
    try {
      const response = await apiClient.post(url, formData, config)
      return response.data
    } catch (error) {
      throw error
    }
  }

  /**
   * 下载文件
   */
  const download = async (url, filename = null) => {
    try {
      const response = await apiClient.get(url, {
        responseType: 'blob'
      })
      
      // 创建下载链接
      const blob = new Blob([response.data])
      const downloadUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = downloadUrl
      
      // 设置文件名
      if (filename) {
        link.download = filename
      } else {
        // 从响应头获取文件名
        const contentDisposition = response.headers['content-disposition']
        if (contentDisposition) {
          const filenameMatch = contentDisposition.match(/filename="(.+)"/)
          if (filenameMatch) {
            link.download = filenameMatch[1]
          }
        }
      }
      
      // 触发下载
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(downloadUrl)
      
      return response.data
    } catch (error) {
      throw error
    }
  }

  /**
   * 健康检查
   */
  const healthCheck = async () => {
    try {
      const response = await get('/health')
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * 获取请求统计
   */
  const getRequestStats = () => {
    return {
      ...requestStats,
      successRate: requestStats.total > 0 ? 
        Math.round((requestStats.success / requestStats.total) * 100) : 0
    }
  }

  /**
   * 重置统计
   */
  const resetStats = () => {
    requestStats.total = 0
    requestStats.success = 0
    requestStats.failed = 0
    requestStats.pending = 0
  }

  /**
   * 设置基础URL
   */
  const setBaseURL = (baseURL) => {
    apiClient.defaults.baseURL = baseURL
  }

  /**
   * 设置请求头
   */
  const setHeader = (key, value) => {
    apiClient.defaults.headers[key] = value
  }

  /**
   * 移除请求头
   */
  const removeHeader = (key) => {
    delete apiClient.defaults.headers[key]
  }

  return {
    // 状态
    globalLoading,
    requestStats,
    
    // HTTP方法
    get,
    post,
    put,
    delete: del,
    upload,
    download,
    
    // 工具方法
    healthCheck,
    getRequestStats,
    resetStats,
    setBaseURL,
    setHeader,
    removeHeader,
    
    // axios实例（用于高级用法）
    apiClient
  }
}

// 导出默认实例
export default useApi
