"""
事件处理器
负责识别和处理游戏中的各种事件
"""

import re
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

from ...utils.logger import get_logger
from ...core.data_structures import GameState, Action, ActionType, UserStrategy


class EventType(Enum):
    """事件类型枚举"""
    LESSON_CHOICE = "lesson_choice"         # 课程选择
    EXAM_PREPARATION = "exam_preparation"   # 考试准备
    SPECIAL_EVENT = "special_event"         # 特殊事件
    CARD_SELECTION = "card_selection"       # 卡牌选择
    REST_DECISION = "rest_decision"         # 休息决策
    STORY_EVENT = "story_event"            # 剧情事件
    UNKNOWN_EVENT = "unknown_event"         # 未知事件


@dataclass
class EventDecision:
    """事件决策结果"""
    event_type: EventType                   # 事件类型
    recommended_action: Action              # 推荐行动
    confidence: float                       # 置信度
    reasoning: str                          # 决策理由
    alternative_actions: List[Action]       # 备选行动
    metadata: Dict[str, Any] = None         # 额外信息
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class EventHandler:
    """事件处理器类"""
    
    def __init__(self, user_strategy: Optional[UserStrategy] = None):
        """
        初始化事件处理器
        
        Args:
            user_strategy: 用户策略配置
        """
        self.logger = get_logger("EventHandler")
        self.user_strategy = user_strategy or UserStrategy()
        
        # 事件识别模式
        self.event_patterns = self._init_event_patterns()
        
        # 事件处理策略
        self.event_strategies = self._init_event_strategies()
        
        # 统计信息
        self.events_processed = 0
        self.event_type_counts = {event_type: 0 for event_type in EventType}
        
        self.logger.info("事件处理器初始化完成")
    
    def _init_event_patterns(self) -> Dict[EventType, List[str]]:
        """初始化事件识别模式"""
        return {
            EventType.LESSON_CHOICE: [
                r"レッスン.*選択",
                r"授業.*選ぶ",
                r"lesson.*choice",
                r"课程.*选择"
            ],
            EventType.EXAM_PREPARATION: [
                r"試験.*準備",
                r"テスト.*対策",
                r"exam.*preparation",
                r"考试.*准备"
            ],
            EventType.SPECIAL_EVENT: [
                r"特別.*イベント",
                r"スペシャル.*発生",
                r"special.*event",
                r"特殊.*事件"
            ],
            EventType.CARD_SELECTION: [
                r"カード.*選択",
                r"スキル.*選ぶ",
                r"card.*selection",
                r"卡牌.*选择"
            ],
            EventType.REST_DECISION: [
                r"休憩.*判断",
                r"体力.*回復",
                r"rest.*decision",
                r"休息.*决策"
            ],
            EventType.STORY_EVENT: [
                r"ストーリー.*イベント",
                r"物語.*進行",
                r"story.*event",
                r"剧情.*事件"
            ]
        }
    
    def _init_event_strategies(self) -> Dict[EventType, Dict[str, Any]]:
        """初始化事件处理策略"""
        return {
            EventType.LESSON_CHOICE: {
                "priority_order": ["vocal", "dance", "visual", "mental"],
                "stamina_threshold": 30,
                "prefer_sp_lessons": True
            },
            EventType.EXAM_PREPARATION: {
                "preparation_threshold": 0.8,
                "focus_weak_stats": True,
                "use_best_cards": True
            },
            EventType.SPECIAL_EVENT: {
                "risk_tolerance": 0.7,
                "prefer_positive_outcomes": True,
                "consider_long_term": True
            },
            EventType.CARD_SELECTION: {
                "evaluation_criteria": ["score_potential", "synergy", "cost_efficiency"],
                "max_evaluation_time": 5.0
            },
            EventType.REST_DECISION: {
                "stamina_threshold": 20,
                "vigor_threshold": 30,
                "force_rest_threshold": 10
            },
            EventType.STORY_EVENT: {
                "default_choice": 1,
                "prefer_recommended": True,
                "skip_if_seen": False
            }
        }
    
    def identify_event(self, game_state: GameState, 
                      screen_text: Optional[str] = None) -> EventType:
        """
        识别当前事件类型
        
        Args:
            game_state: 当前游戏状态
            screen_text: 屏幕文本内容（OCR结果）
            
        Returns:
            识别出的事件类型
        """
        try:
            # 基于游戏场景的初步判断
            scene_based_type = self._identify_by_scene(game_state)
            
            # 基于屏幕文本的精确识别
            if screen_text:
                text_based_type = self._identify_by_text(screen_text)
                if text_based_type != EventType.UNKNOWN_EVENT:
                    return text_based_type
            
            # 基于UI元素的识别
            ui_based_type = self._identify_by_ui_elements(game_state)
            if ui_based_type != EventType.UNKNOWN_EVENT:
                return ui_based_type
            
            return scene_based_type
            
        except Exception as e:
            self.logger.error(f"事件识别失败: {e}")
            return EventType.UNKNOWN_EVENT
    
    def handle_event(self, event_type: EventType, 
                    game_state: GameState,
                    available_options: Optional[List[str]] = None) -> EventDecision:
        """
        处理识别出的事件
        
        Args:
            event_type: 事件类型
            game_state: 当前游戏状态
            available_options: 可用选项列表
            
        Returns:
            事件处理决策
        """
        try:
            self.logger.info(f"处理事件: {event_type.value}")
            
            # 更新统计信息
            self.events_processed += 1
            self.event_type_counts[event_type] += 1
            
            # 根据事件类型选择处理方法
            if event_type == EventType.LESSON_CHOICE:
                return self._handle_lesson_choice(game_state, available_options)
            elif event_type == EventType.EXAM_PREPARATION:
                return self._handle_exam_preparation(game_state, available_options)
            elif event_type == EventType.SPECIAL_EVENT:
                return self._handle_special_event(game_state, available_options)
            elif event_type == EventType.CARD_SELECTION:
                return self._handle_card_selection(game_state, available_options)
            elif event_type == EventType.REST_DECISION:
                return self._handle_rest_decision(game_state, available_options)
            elif event_type == EventType.STORY_EVENT:
                return self._handle_story_event(game_state, available_options)
            else:
                return self._handle_unknown_event(game_state, available_options)
                
        except Exception as e:
            self.logger.error(f"事件处理失败: {event_type.value}, 错误: {e}")
            return self._create_default_decision(event_type)
    
    def _identify_by_scene(self, game_state: GameState) -> EventType:
        """基于游戏场景识别事件类型"""
        scene = game_state.current_scene
        
        scene_mapping = {
            "produce_main": EventType.LESSON_CHOICE,
            "produce_battle": EventType.CARD_SELECTION,
            "produce_exam": EventType.EXAM_PREPARATION,
            "main_menu": EventType.UNKNOWN_EVENT
        }
        
        return scene_mapping.get(scene.value, EventType.UNKNOWN_EVENT)
    
    def _identify_by_text(self, screen_text: str) -> EventType:
        """基于屏幕文本识别事件类型"""
        for event_type, patterns in self.event_patterns.items():
            for pattern in patterns:
                if re.search(pattern, screen_text, re.IGNORECASE):
                    self.logger.debug(f"文本匹配事件: {event_type.value}, 模式: {pattern}")
                    return event_type
        
        return EventType.UNKNOWN_EVENT
    
    def _identify_by_ui_elements(self, game_state: GameState) -> EventType:
        """基于UI元素识别事件类型"""
        if not game_state.ui_elements:
            return EventType.UNKNOWN_EVENT
        
        ui_elements = game_state.ui_elements.keys()
        
        # 检查特定UI元素的存在
        if any("lesson" in element for element in ui_elements):
            return EventType.LESSON_CHOICE
        elif any("card" in element for element in ui_elements):
            return EventType.CARD_SELECTION
        elif any("rest" in element for element in ui_elements):
            return EventType.REST_DECISION
        
        return EventType.UNKNOWN_EVENT
    
    def _handle_lesson_choice(self, game_state: GameState, 
                             options: Optional[List[str]]) -> EventDecision:
        """处理课程选择事件"""
        strategy = self.event_strategies[EventType.LESSON_CHOICE]
        
        # 检查体力状态
        if game_state.stamina < strategy["stamina_threshold"]:
            action = Action(ActionType.CLICK, (500, 400), "选择休息")
            return EventDecision(
                event_type=EventType.LESSON_CHOICE,
                recommended_action=action,
                confidence=0.9,
                reasoning="体力不足，选择休息",
                alternative_actions=[]
            )
        
        # 基于用户策略选择课程
        priority_weights = self.user_strategy.produce_goal.priority_weights
        best_lesson = max(priority_weights.items(), key=lambda x: x[1])
        
        # 创建对应的点击行动
        lesson_positions = {
            "vocal": (200, 300),
            "dance": (300, 300),
            "visual": (400, 300),
            "mental": (500, 300)
        }
        
        position = lesson_positions.get(best_lesson[0], (300, 300))
        action = Action(ActionType.CLICK, position, f"选择{best_lesson[0]}课程")
        
        return EventDecision(
            event_type=EventType.LESSON_CHOICE,
            recommended_action=action,
            confidence=0.8,
            reasoning=f"基于用户策略选择{best_lesson[0]}课程",
            alternative_actions=[]
        )
    
    def _handle_exam_preparation(self, game_state: GameState,
                                options: Optional[List[str]]) -> EventDecision:
        """处理考试准备事件"""
        action = Action(ActionType.CLICK, (400, 500), "开始考试")
        
        return EventDecision(
            event_type=EventType.EXAM_PREPARATION,
            recommended_action=action,
            confidence=0.7,
            reasoning="准备充分，开始考试",
            alternative_actions=[]
        )
    
    def _handle_special_event(self, game_state: GameState,
                             options: Optional[List[str]]) -> EventDecision:
        """处理特殊事件"""
        # 默认选择第一个选项
        action = Action(ActionType.CLICK, (300, 400), "选择第一个选项")
        
        return EventDecision(
            event_type=EventType.SPECIAL_EVENT,
            recommended_action=action,
            confidence=0.6,
            reasoning="特殊事件，选择默认选项",
            alternative_actions=[]
        )
    
    def _handle_card_selection(self, game_state: GameState,
                              options: Optional[List[str]]) -> EventDecision:
        """处理卡牌选择事件"""
        # 如果有手牌，选择第一张
        if game_state.hand:
            action = Action(ActionType.CLICK, (150, 500), f"使用卡牌: {game_state.hand[0].name_jp}")
        else:
            action = Action(ActionType.KEYPRESS, "space", "跳过卡牌选择")
        
        return EventDecision(
            event_type=EventType.CARD_SELECTION,
            recommended_action=action,
            confidence=0.7,
            reasoning="选择最佳可用卡牌",
            alternative_actions=[]
        )
    
    def _handle_rest_decision(self, game_state: GameState,
                             options: Optional[List[str]]) -> EventDecision:
        """处理休息决策事件"""
        strategy = self.event_strategies[EventType.REST_DECISION]
        
        should_rest = (game_state.stamina < strategy["stamina_threshold"] or
                      game_state.vigor < strategy["vigor_threshold"])
        
        if should_rest:
            action = Action(ActionType.CLICK, (400, 350), "选择休息")
            reasoning = "体力或元气不足，选择休息"
            confidence = 0.9
        else:
            action = Action(ActionType.CLICK, (300, 350), "继续活动")
            reasoning = "状态良好，继续活动"
            confidence = 0.7
        
        return EventDecision(
            event_type=EventType.REST_DECISION,
            recommended_action=action,
            confidence=confidence,
            reasoning=reasoning,
            alternative_actions=[]
        )
    
    def _handle_story_event(self, game_state: GameState,
                           options: Optional[List[str]]) -> EventDecision:
        """处理剧情事件"""
        strategy = self.event_strategies[EventType.STORY_EVENT]
        default_choice = strategy["default_choice"]
        
        # 计算点击位置（假设选项垂直排列）
        y_position = 300 + (default_choice - 1) * 50
        action = Action(ActionType.CLICK, (400, y_position), f"选择选项{default_choice}")
        
        return EventDecision(
            event_type=EventType.STORY_EVENT,
            recommended_action=action,
            confidence=0.6,
            reasoning=f"剧情事件，选择默认选项{default_choice}",
            alternative_actions=[]
        )
    
    def _handle_unknown_event(self, game_state: GameState,
                             options: Optional[List[str]]) -> EventDecision:
        """处理未知事件"""
        # 默认行动：等待或点击屏幕中央
        action = Action(ActionType.CLICK, (400, 400), "点击屏幕中央")
        
        return EventDecision(
            event_type=EventType.UNKNOWN_EVENT,
            recommended_action=action,
            confidence=0.3,
            reasoning="未知事件，使用默认处理",
            alternative_actions=[
                Action(ActionType.KEYPRESS, "space", "按空格键"),
                Action(ActionType.KEYPRESS, "enter", "按回车键")
            ]
        )
    
    def _create_default_decision(self, event_type: EventType) -> EventDecision:
        """创建默认决策"""
        action = Action(ActionType.KEYPRESS, "space", "默认行动")
        
        return EventDecision(
            event_type=event_type,
            recommended_action=action,
            confidence=0.1,
            reasoning="事件处理失败，使用默认行动",
            alternative_actions=[]
        )
    
    def get_event_statistics(self) -> Dict[str, Any]:
        """获取事件处理统计信息"""
        return {
            "total_events_processed": self.events_processed,
            "event_type_counts": {k.value: v for k, v in self.event_type_counts.items()},
            "most_common_event": max(self.event_type_counts.items(), 
                                   key=lambda x: x[1])[0].value if self.events_processed > 0 else None
        }
    
    def reset_statistics(self):
        """重置统计信息"""
        self.events_processed = 0
        self.event_type_counts = {event_type: 0 for event_type in EventType}
        self.logger.info("事件处理器统计信息已重置")
    
    def get_handler_info(self) -> Dict[str, Any]:
        """获取处理器信息"""
        return {
            "supported_event_types": [event_type.value for event_type in EventType],
            "event_patterns_count": {k.value: len(v) for k, v in self.event_patterns.items()},
            "user_strategy_target": self.user_strategy.produce_goal.target,
            "statistics": self.get_event_statistics()
        }
