"""
优化功能测试
测试性能监控和场景优化功能
"""

import unittest
import time
from unittest.mock import Mock, patch

from .test_utils import UITestCase
from ....core.data_structures import GameScene
from ..utils.performance_monitor import PerformanceMonitor, get_performance_monitor, measure, measure_block
from ..utils.scene_optimizer import SceneOptimizer, get_scene_optimizer


class TestPerformanceMonitor(UITestCase):
    """性能监控器测试"""
    
    def setUp(self):
        super().setUp()
        self.monitor = PerformanceMonitor(max_history=100)
    
    def test_monitor_initialization(self):
        """测试监控器初始化"""
        self.assertIsNotNone(self.monitor)
        self.assertTrue(self.monitor._monitoring_enabled)
        self.assertEqual(self.monitor.max_history, 100)
    
    def test_measurement_lifecycle(self):
        """测试测量生命周期"""
        # 开始测量
        measurement_id = self.monitor.start_measurement("test_operation")
        self.assertIsNotNone(measurement_id)
        self.assertNotEqual(measurement_id, "")
        
        # 模拟一些工作
        time.sleep(0.1)
        
        # 结束测量
        metric = self.monitor.end_measurement(measurement_id)
        self.assertIsNotNone(metric)
        self.assertEqual(metric.name, "test_operation")
        self.assertGreater(metric.duration, 0.05)
        self.assertIsNotNone(metric.memory_before)
        self.assertIsNotNone(metric.memory_after)
    
    def test_function_decorator(self):
        """测试函数装饰器"""
        @self.monitor.measure_function("test_function")
        def test_func():
            time.sleep(0.05)
            return "result"
        
        result = test_func()
        self.assertEqual(result, "result")
        
        # 检查统计数据
        stats = self.monitor.get_stats("test_function")
        self.assertGreater(stats.get('count', 0), 0)
        self.assertGreater(stats.get('avg_duration', 0), 0.04)
    
    def test_context_manager(self):
        """测试上下文管理器"""
        with self.monitor.measure_block("test_block"):
            time.sleep(0.05)
        
        # 检查统计数据
        stats = self.monitor.get_stats("test_block")
        self.assertGreater(stats.get('count', 0), 0)
        self.assertGreater(stats.get('avg_duration', 0), 0.04)
    
    def test_stats_calculation(self):
        """测试统计数据计算"""
        # 执行多次测量
        for i in range(5):
            with self.monitor.measure_block("repeated_operation"):
                time.sleep(0.01 * (i + 1))  # 递增的执行时间
        
        stats = self.monitor.get_stats("repeated_operation")
        
        self.assertEqual(stats['count'], 5)
        self.assertGreater(stats['total_duration'], 0.1)
        self.assertGreater(stats['avg_duration'], 0.02)
        self.assertGreater(stats['max_duration'], stats['min_duration'])
    
    def test_recent_metrics(self):
        """测试最近指标获取"""
        # 执行多次测量
        for i in range(10):
            with self.monitor.measure_block("metric_test"):
                time.sleep(0.001)
        
        recent_metrics = self.monitor.get_recent_metrics("metric_test", 5)
        self.assertEqual(len(recent_metrics), 5)
        
        # 验证指标完整性
        for metric in recent_metrics:
            self.assertEqual(metric.name, "metric_test")
            self.assertIsNotNone(metric.duration)
    
    def test_clear_stats(self):
        """测试清除统计数据"""
        # 先生成一些数据
        with self.monitor.measure_block("clear_test"):
            time.sleep(0.01)
        
        stats_before = self.monitor.get_stats("clear_test")
        self.assertGreater(stats_before.get('count', 0), 0)
        
        # 清除特定统计
        self.monitor.clear_stats("clear_test")
        stats_after = self.monitor.get_stats("clear_test")
        self.assertEqual(stats_after.get('count', 0), 0)
    
    def test_system_info(self):
        """测试系统信息获取"""
        system_info = self.monitor.get_system_info()
        
        self.assertIn('memory_usage_mb', system_info)
        self.assertIn('cpu_percent', system_info)
        self.assertIn('monitoring_enabled', system_info)
        self.assertGreater(system_info['memory_usage_mb'], 0)
    
    def test_global_monitor(self):
        """测试全局监控器"""
        global_monitor = get_performance_monitor()
        self.assertIsNotNone(global_monitor)
        
        # 测试便捷装饰器
        @measure("global_test")
        def global_test_func():
            time.sleep(0.01)
            return True
        
        result = global_test_func()
        self.assertTrue(result)
        
        # 测试便捷上下文管理器
        with measure_block("global_block_test"):
            time.sleep(0.01)
        
        # 验证统计数据
        stats = global_monitor.get_stats()
        self.assertIn("global_test", stats)
        self.assertIn("global_block_test", stats)


class TestSceneOptimizer(UITestCase):
    """场景优化器测试"""
    
    def setUp(self):
        super().setUp()
        self.optimizer = SceneOptimizer()
    
    def test_optimizer_initialization(self):
        """测试优化器初始化"""
        self.assertIsNotNone(self.optimizer)
        self.assertTrue(self.optimizer._optimization_enabled)
    
    def test_scene_access_recording(self):
        """测试场景访问记录"""
        # 记录场景访问
        self.optimizer.record_scene_access(GameScene.MAIN_MENU, 1.5)
        self.optimizer.record_scene_access(GameScene.MAIN_MENU, 1.2)
        self.optimizer.record_scene_access(GameScene.PRODUCE_SETUP, 2.0)
        
        # 验证统计数据
        stats = self.optimizer._scene_usage_stats[GameScene.MAIN_MENU]
        self.assertEqual(stats['access_count'], 2)
        self.assertAlmostEqual(stats['avg_load_time'], 1.35, places=2)
    
    def test_ui_element_usage_recording(self):
        """测试UI元素使用记录"""
        # 记录UI元素使用
        self.optimizer.record_ui_element_usage(GameScene.MAIN_MENU, "produce_button")
        self.optimizer.record_ui_element_usage(GameScene.MAIN_MENU, "produce_button")
        self.optimizer.record_ui_element_usage(GameScene.MAIN_MENU, "shop_button")
        
        # 验证统计数据
        stats = self.optimizer._scene_usage_stats[GameScene.MAIN_MENU]
        self.assertEqual(stats['ui_element_usage']['produce_button'], 2)
        self.assertEqual(stats['ui_element_usage']['shop_button'], 1)
    
    def test_navigation_pattern_recording(self):
        """测试导航模式记录"""
        # 记录导航模式
        self.optimizer.record_navigation_pattern(GameScene.MAIN_MENU, GameScene.PRODUCE_SETUP)
        self.optimizer.record_navigation_pattern(GameScene.MAIN_MENU, GameScene.PRODUCE_SETUP)
        self.optimizer.record_navigation_pattern(GameScene.PRODUCE_SETUP, GameScene.PRODUCE_MAIN)
        
        # 验证统计数据
        stats = self.optimizer._scene_usage_stats[GameScene.MAIN_MENU]
        pattern_key = f"{GameScene.MAIN_MENU.value}->{GameScene.PRODUCE_SETUP.value}"
        self.assertEqual(stats['navigation_patterns'][pattern_key], 2)
    
    def test_hot_scenes_detection(self):
        """测试热门场景检测"""
        # 模拟不同场景的访问
        for _ in range(10):
            self.optimizer.record_scene_access(GameScene.MAIN_MENU)
        for _ in range(5):
            self.optimizer.record_scene_access(GameScene.PRODUCE_SETUP)
        for _ in range(3):
            self.optimizer.record_scene_access(GameScene.PRODUCE_MAIN)
        
        hot_scenes = self.optimizer.get_hot_scenes(2)
        self.assertEqual(len(hot_scenes), 2)
        self.assertEqual(hot_scenes[0], GameScene.MAIN_MENU)
        self.assertEqual(hot_scenes[1], GameScene.PRODUCE_SETUP)
    
    def test_slow_loading_scenes_detection(self):
        """测试加载缓慢场景检测"""
        # 模拟不同的加载时间
        self.optimizer.record_scene_access(GameScene.MAIN_MENU, 0.5)  # 快速
        self.optimizer.record_scene_access(GameScene.PRODUCE_SETUP, 3.0)  # 缓慢
        self.optimizer.record_scene_access(GameScene.PRODUCE_MAIN, 1.0)  # 正常
        
        slow_scenes = self.optimizer.get_slow_loading_scenes(2.0)
        self.assertIn(GameScene.PRODUCE_SETUP, slow_scenes)
        self.assertNotIn(GameScene.MAIN_MENU, slow_scenes)
    
    def test_optimization_suggestions(self):
        """测试优化建议生成"""
        # 模拟一些使用数据
        for _ in range(20):
            self.optimizer.record_scene_access(GameScene.MAIN_MENU, 0.5)
        for _ in range(15):
            self.optimizer.record_scene_access(GameScene.PRODUCE_SETUP, 3.0)
        
        suggestions = self.optimizer.generate_optimization_suggestions()
        self.assertGreater(len(suggestions), 0)
        
        # 验证建议类型
        suggestion_types = [s['type'] for s in suggestions]
        self.assertIn('preload', suggestion_types)  # 应该建议预加载热门场景
        self.assertIn('performance', suggestion_types)  # 应该建议优化缓慢场景
    
    def test_optimization_application(self):
        """测试优化应用"""
        # 创建一个预加载优化建议
        suggestion = {
            'type': 'preload',
            'scenes': [GameScene.MAIN_MENU, GameScene.PRODUCE_SETUP],
            'priority': 'high'
        }
        
        result = self.optimizer.apply_optimization(suggestion)
        self.assertTrue(result)
    
    def test_optimization_report(self):
        """测试优化报告生成"""
        # 模拟一些使用数据
        self.optimizer.record_scene_access(GameScene.MAIN_MENU, 1.0)
        self.optimizer.record_ui_element_usage(GameScene.MAIN_MENU, "produce_button")
        self.optimizer.record_navigation_pattern(GameScene.MAIN_MENU, GameScene.PRODUCE_SETUP)
        
        report = self.optimizer.get_optimization_report()
        
        self.assertIn('summary', report)
        self.assertIn('hot_scenes', report)
        self.assertIn('slow_scenes', report)
        self.assertIn('optimization_suggestions', report)
        self.assertIn('scene_details', report)
        
        # 验证摘要数据
        summary = report['summary']
        self.assertGreater(summary['total_scene_accesses'], 0)
        self.assertGreater(summary['unique_scenes_accessed'], 0)
    
    def test_stats_reset(self):
        """测试统计数据重置"""
        # 先生成一些数据
        self.optimizer.record_scene_access(GameScene.MAIN_MENU)
        self.assertEqual(len(self.optimizer._scene_usage_stats), 1)
        
        # 重置统计数据
        self.optimizer.reset_stats()
        self.assertEqual(len(self.optimizer._scene_usage_stats), 0)
    
    def test_global_optimizer(self):
        """测试全局优化器"""
        global_optimizer = get_scene_optimizer()
        self.assertIsNotNone(global_optimizer)
        
        # 测试基本功能
        global_optimizer.record_scene_access(GameScene.MAIN_MENU)
        stats = global_optimizer._scene_usage_stats[GameScene.MAIN_MENU]
        self.assertEqual(stats['access_count'], 1)


class TestIntegratedOptimization(UITestCase):
    """集成优化测试"""
    
    def test_performance_and_optimization_integration(self):
        """测试性能监控和场景优化的集成"""
        monitor = get_performance_monitor()
        optimizer = get_scene_optimizer()
        
        # 模拟场景使用流程
        with monitor.measure_block("scene_creation"):
            time.sleep(0.1)  # 模拟场景创建时间
            optimizer.record_scene_access(GameScene.MAIN_MENU, 0.1)
        
        with monitor.measure_block("ui_interaction"):
            time.sleep(0.05)  # 模拟UI交互时间
            optimizer.record_ui_element_usage(GameScene.MAIN_MENU, "produce_button")
        
        # 验证数据记录
        perf_stats = monitor.get_stats()
        self.assertIn("scene_creation", perf_stats)
        self.assertIn("ui_interaction", perf_stats)
        
        opt_report = optimizer.get_optimization_report()
        self.assertGreater(opt_report['summary']['total_scene_accesses'], 0)
    
    def test_optimization_workflow(self):
        """测试完整的优化工作流程"""
        optimizer = get_scene_optimizer()
        
        # 1. 模拟大量使用数据
        for i in range(50):
            scene = GameScene.MAIN_MENU if i % 2 == 0 else GameScene.PRODUCE_SETUP
            load_time = 0.5 if scene == GameScene.MAIN_MENU else 2.5
            optimizer.record_scene_access(scene, load_time)
        
        # 2. 生成优化建议
        suggestions = optimizer.generate_optimization_suggestions()
        self.assertGreater(len(suggestions), 0)
        
        # 3. 应用优化建议
        applied_count = 0
        for suggestion in suggestions:
            if optimizer.apply_optimization(suggestion):
                applied_count += 1
        
        self.assertGreater(applied_count, 0)
        
        # 4. 生成优化报告
        report = optimizer.get_optimization_report()
        self.assertIsNotNone(report)
        self.assertGreater(report['summary']['total_scene_accesses'], 40)


if __name__ == '__main__':
    unittest.main(verbosity=2)
