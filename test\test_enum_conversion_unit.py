"""
单元测试枚举转换逻辑
不依赖于运行的服务器
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.web.models import ScreenshotConfig, ScreenshotModeEnum, ScreenshotFormatEnum, ScreenshotRequest
from src.web.main import api_adapter


def test_enum_conversion_logic():
    """测试枚举转换逻辑"""
    print("🧪 测试枚举转换逻辑...")
    
    try:
        # 创建包含枚举的配置
        config = ScreenshotConfig(
            mode=ScreenshotModeEnum.WINDOW,
            format=ScreenshotFormatEnum.PNG,
            quality=90,
            save_to_disk=True,
            filename_prefix="unit_test"
        )
        
        print(f"📋 原始配置: mode={config.mode}, format={config.format}")
        print(f"📋 枚举类型: mode={type(config.mode)}, format={type(config.format)}")
        
        # 模拟API端点的转换逻辑
        config_dict = {
            "mode": config.mode.value if hasattr(config.mode, 'value') else str(config.mode),
            "format": config.format.value if hasattr(config.format, 'value') else str(config.format),
            "quality": config.quality,
            "region": config.region.dict() if config.region else None,
            "save_to_disk": config.save_to_disk,
            "filename_prefix": config.filename_prefix
        }
        
        print(f"📋 转换后配置: {config_dict}")
        print(f"📋 转换后类型: mode={type(config_dict['mode'])}, format={type(config_dict['format'])}")
        
        # 验证转换结果
        if isinstance(config_dict['mode'], str) and isinstance(config_dict['format'], str):
            print("✅ 枚举转换逻辑正确")
            return True
        else:
            print("❌ 枚举转换逻辑错误")
            return False
            
    except Exception as e:
        print(f"❌ 枚举转换测试异常: {e}")
        return False


def test_api_adapter_conversion():
    """测试API适配器的转换逻辑"""
    print("\n🧪 测试API适配器转换逻辑...")
    
    try:
        # 初始化API适配器
        api_adapter.initialize_core_modules()
        
        # 测试不同格式的配置
        test_configs = [
            # 字符串格式
            {
                "mode": "window",
                "format": "png",
                "quality": 90,
                "save_to_disk": True,
                "filename_prefix": "adapter_test_str"
            },
            # 枚举格式（模拟可能的情况）
            {
                "mode": ScreenshotModeEnum.FULLSCREEN,
                "format": ScreenshotFormatEnum.JPEG,
                "quality": 80,
                "save_to_disk": True,
                "filename_prefix": "adapter_test_enum"
            }
        ]
        
        for i, config_dict in enumerate(test_configs, 1):
            print(f"\n📋 测试配置 {i}: {config_dict}")
            
            try:
                # 测试转换方法
                converted_config = api_adapter._convert_screenshot_config(config_dict)
                print(f"✅ 配置 {i} 转换成功: mode={converted_config.mode}, format={converted_config.format}")
                
            except Exception as e:
                print(f"❌ 配置 {i} 转换失败: {e}")
                return False
        
        print("✅ API适配器转换逻辑正确")
        return True
        
    except Exception as e:
        print(f"❌ API适配器转换测试异常: {e}")
        return False


def test_pydantic_serialization():
    """测试Pydantic序列化"""
    print("\n🧪 测试Pydantic序列化...")
    
    try:
        # 创建请求对象
        config = ScreenshotConfig(
            mode=ScreenshotModeEnum.WINDOW,
            format=ScreenshotFormatEnum.PNG,
            quality=90
        )
        
        request = ScreenshotRequest(config=config)
        
        print(f"📋 请求对象: {request}")
        
        # 测试不同的序列化方法
        serialization_methods = [
            ("dict()", lambda: request.config.dict()),
            ("dict(by_alias=True)", lambda: request.config.dict(by_alias=True)),
            ("dict(exclude_unset=False)", lambda: request.config.dict(exclude_unset=False)),
            ("json()", lambda: request.config.json()),
        ]
        
        for method_name, method_func in serialization_methods:
            try:
                result = method_func()
                print(f"📋 {method_name}: {result}")
                
                if isinstance(result, str):
                    import json
                    result = json.loads(result)
                
                mode_type = type(result.get('mode'))
                format_type = type(result.get('format'))
                print(f"   类型: mode={mode_type}, format={format_type}")
                
            except Exception as e:
                print(f"❌ {method_name} 失败: {e}")
        
        print("✅ Pydantic序列化测试完成")
        return True
        
    except Exception as e:
        print(f"❌ Pydantic序列化测试异常: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始枚举转换单元测试")
    print("=" * 50)
    
    # 执行测试
    tests = [
        ("枚举转换逻辑", test_enum_conversion_logic),
        ("API适配器转换", test_api_adapter_conversion),
        ("Pydantic序列化", test_pydantic_serialization)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📊 单元测试结果:")
    
    passed_count = 0
    total_count = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed_count += 1
    
    print(f"\n总体结果: {passed_count}/{total_count} 通过 ({passed_count/total_count*100:.1f}%)")
    
    if passed_count == total_count:
        print("🎉 枚举转换逻辑修复成功！")
        return True
    else:
        print("⚠️ 枚举转换逻辑仍有问题")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
