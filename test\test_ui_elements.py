"""
UI元素测试
测试基础UI元素和各种具体UI元素的功能
"""

import unittest
import time
from unittest.mock import Mock, patch, MagicMock

from .base_test import UITestCase
from src.modules.ui.elements.base_ui_element import BaseUIElement
from src.modules.ui.elements.button import Button
from src.modules.ui.elements.input_field import InputField
from src.modules.ui.elements.label import Label
from src.modules.ui.config.ui_element_config import UIElementConfig


class TestBaseUIElement(UITestCase):
    """基础UI元素测试"""
    
    def setUp(self):
        super().setUp()
        self.config = UIElementConfig(
            confidence_threshold=0.8,
            timeout=5.0,
            retry_count=3
        )
    
    def test_element_creation(self):
        """测试元素创建"""
        element = BaseUIElement("test_element", self.config)
        
        self.assertEqual(element.element_id, "test_element")
        self.assertEqual(element.config, self.config)
        self.assertIsNotNone(element.logger)
        self.assertFalse(element._is_interacting)
    
    def test_element_visibility_detection(self):
        """测试元素可见性检测"""
        element = BaseUIElement("test_element", self.config)
        
        # Mock perception返回可见
        self.mock_perception.return_value.is_element_visible.return_value = True
        
        result = element.is_visible()
        self.assertTrue(result)
        
        # Mock perception返回不可见
        self.mock_perception.return_value.is_element_visible.return_value = False
        
        result = element.is_visible()
        self.assertFalse(result)
    
    def test_element_bounds_detection(self):
        """测试元素边界检测"""
        element = BaseUIElement("test_element", self.config)
        expected_bounds = (100, 200, 150, 50)
        
        self.mock_perception.return_value.get_element_bounds.return_value = expected_bounds
        
        bounds = element.get_bounds()
        self.assertEqual(bounds, expected_bounds)
    
    def test_wait_for_element(self):
        """测试等待元素出现"""
        element = BaseUIElement("test_element", self.config)
        
        # 模拟元素立即可见
        self.mock_perception.return_value.is_element_visible.return_value = True
        
        start_time = time.time()
        result = element.wait_for_element()
        end_time = time.time()
        
        self.assertTrue(result)
        self.assertLess(end_time - start_time, 1.0)  # 应该很快返回
    
    def test_wait_for_element_timeout(self):
        """测试等待元素超时"""
        element = BaseUIElement("test_element", UIElementConfig(timeout=1.0))
        
        # 模拟元素不可见
        self.mock_perception.return_value.is_element_visible.return_value = False
        
        start_time = time.time()
        result = element.wait_for_element()
        end_time = time.time()
        
        self.assertFalse(result)
        self.assertGreaterEqual(end_time - start_time, 1.0)  # 应该等待超时时间
    
    def test_performance_element_creation(self):
        """测试元素创建性能"""
        def create_element():
            return BaseUIElement("perf_test", self.config)
        
        self.assert_performance(create_element, "ui_element_creation")


class TestButton(UITestCase):
    """按钮测试"""
    
    def setUp(self):
        super().setUp()
        self.button = Button("test_button", confidence_threshold=0.9)
    
    def test_button_creation(self):
        """测试按钮创建"""
        self.assertEqual(self.button.element_id, "test_button")
        self.assertEqual(self.button.config.confidence_threshold, 0.9)
        self.assertTrue(self.button.verify_click_result)
    
    def test_button_click_success(self):
        """测试按钮点击成功"""
        # Mock点击成功
        self.mock_action_controller.return_value.click.return_value = True
        self.mock_perception.return_value.is_element_visible.return_value = True
        
        result = self.button.click()
        self.assertTrue(result)
        
        # 验证调用了正确的方法
        self.mock_action_controller.return_value.click.assert_called_once()
    
    def test_button_click_failure(self):
        """测试按钮点击失败"""
        # Mock点击失败
        self.mock_action_controller.return_value.click.return_value = False
        self.mock_perception.return_value.is_element_visible.return_value = True
        
        result = self.button.click()
        self.assertFalse(result)
    
    def test_button_click_with_retry(self):
        """测试按钮点击重试"""
        button = Button("retry_button", retry_count=3)
        
        # Mock前两次失败，第三次成功
        self.mock_action_controller.return_value.click.side_effect = [False, False, True]
        self.mock_perception.return_value.is_element_visible.return_value = True
        
        result = button.click()
        self.assertTrue(result)
        self.assertEqual(self.mock_action_controller.return_value.click.call_count, 3)
    
    def test_button_double_click(self):
        """测试按钮双击"""
        self.mock_action_controller.return_value.double_click.return_value = True
        self.mock_perception.return_value.is_element_visible.return_value = True
        
        result = self.button.double_click()
        self.assertTrue(result)
        
        self.mock_action_controller.return_value.double_click.assert_called_once()
    
    def test_button_right_click(self):
        """测试按钮右键点击"""
        self.mock_action_controller.return_value.right_click.return_value = True
        self.mock_perception.return_value.is_element_visible.return_value = True
        
        result = self.button.right_click()
        self.assertTrue(result)
        
        self.mock_action_controller.return_value.right_click.assert_called_once()


class TestInputField(UITestCase):
    """输入框测试"""
    
    def setUp(self):
        super().setUp()
        self.input_field = InputField("test_input", timeout=3.0)
    
    def test_input_field_creation(self):
        """测试输入框创建"""
        self.assertEqual(self.input_field.element_id, "test_input")
        self.assertEqual(self.input_field.config.timeout, 3.0)
        self.assertTrue(self.input_field.verify_input_result)
    
    def test_input_text_success(self):
        """测试文本输入成功"""
        test_text = "Hello, World!"
        
        # Mock输入成功
        self.mock_action_controller.return_value.type_text.return_value = True
        self.mock_perception.return_value.is_element_visible.return_value = True
        self.mock_perception.return_value.get_element_text.return_value = test_text
        
        result = self.input_field.input_text(test_text)
        self.assertTrue(result)
        
        self.mock_action_controller.return_value.type_text.assert_called_once_with(
            self.input_field, test_text
        )
    
    def test_input_text_with_clear(self):
        """测试清空后输入文本"""
        test_text = "New Text"
        
        self.mock_action_controller.return_value.clear_text.return_value = True
        self.mock_action_controller.return_value.type_text.return_value = True
        self.mock_perception.return_value.is_element_visible.return_value = True
        self.mock_perception.return_value.get_element_text.return_value = test_text
        
        result = self.input_field.input_text(test_text, clear_first=True)
        self.assertTrue(result)
        
        # 验证先清空再输入
        self.mock_action_controller.return_value.clear_text.assert_called_once()
        self.mock_action_controller.return_value.type_text.assert_called_once()
    
    def test_get_text(self):
        """测试获取文本"""
        expected_text = "Current Text"
        
        self.mock_perception.return_value.get_element_text.return_value = expected_text
        
        text = self.input_field.get_text()
        self.assertEqual(text, expected_text)
    
    def test_clear_text(self):
        """测试清空文本"""
        self.mock_action_controller.return_value.clear_text.return_value = True
        self.mock_perception.return_value.is_element_visible.return_value = True
        
        result = self.input_field.clear()
        self.assertTrue(result)
        
        self.mock_action_controller.return_value.clear_text.assert_called_once()
    
    def test_input_validation(self):
        """测试输入验证"""
        # 测试空文本输入
        result = self.input_field.input_text("")
        self.assertFalse(result)
        
        # 测试None输入
        result = self.input_field.input_text(None)
        self.assertFalse(result)


class TestLabel(UITestCase):
    """标签测试"""
    
    def setUp(self):
        super().setUp()
        self.label = Label("test_label", text_recognition_enabled=True)
    
    def test_label_creation(self):
        """测试标签创建"""
        self.assertEqual(self.label.element_id, "test_label")
        self.assertTrue(self.label.text_recognition_enabled)
    
    def test_get_text(self):
        """测试获取标签文本"""
        expected_text = "Label Text"
        
        self.mock_perception.return_value.get_element_text.return_value = expected_text
        
        text = self.label.get_text()
        self.assertEqual(text, expected_text)
    
    def test_wait_for_text(self):
        """测试等待特定文本出现"""
        target_text = "Expected Text"
        
        # Mock文本匹配
        self.mock_perception.return_value.get_element_text.return_value = target_text
        self.mock_perception.return_value.is_element_visible.return_value = True
        
        result = self.label.wait_for_text(target_text)
        self.assertTrue(result)
    
    def test_wait_for_text_timeout(self):
        """测试等待文本超时"""
        label = Label("timeout_label", UIElementConfig(timeout=1.0))
        target_text = "Expected Text"
        
        # Mock文本不匹配
        self.mock_perception.return_value.get_element_text.return_value = "Different Text"
        self.mock_perception.return_value.is_element_visible.return_value = True
        
        start_time = time.time()
        result = label.wait_for_text(target_text)
        end_time = time.time()
        
        self.assertFalse(result)
        self.assertGreaterEqual(end_time - start_time, 1.0)
    
    def test_text_contains(self):
        """测试文本包含检查"""
        label_text = "This is a test label"
        
        self.mock_perception.return_value.get_element_text.return_value = label_text
        
        # 测试包含
        result = self.label.text_contains("test")
        self.assertTrue(result)
        
        # 测试不包含
        result = self.label.text_contains("missing")
        self.assertFalse(result)
    
    def test_text_matches_pattern(self):
        """测试文本模式匹配"""
        label_text = "Score: 1234"
        
        self.mock_perception.return_value.get_element_text.return_value = label_text
        
        # 测试正则匹配
        result = self.label.text_matches_pattern(r"Score: \d+")
        self.assertTrue(result)
        
        # 测试不匹配
        result = self.label.text_matches_pattern(r"Level: \d+")
        self.assertFalse(result)


class TestUIElementInteraction(UITestCase):
    """UI元素交互测试"""
    
    def test_concurrent_interaction_prevention(self):
        """测试并发交互防护"""
        button = Button("concurrent_test")
        
        # Mock长时间操作
        def slow_click(*args, **kwargs):
            time.sleep(0.1)
            return True
        
        self.mock_action_controller.return_value.click.side_effect = slow_click
        self.mock_perception.return_value.is_element_visible.return_value = True
        
        # 启动第一个点击
        import threading
        result1 = None
        result2 = None
        
        def click1():
            nonlocal result1
            result1 = button.click()
        
        def click2():
            nonlocal result2
            result2 = button.click()
        
        thread1 = threading.Thread(target=click1)
        thread2 = threading.Thread(target=click2)
        
        thread1.start()
        time.sleep(0.01)  # 确保第一个线程先开始
        thread2.start()
        
        thread1.join()
        thread2.join()
        
        # 第一个应该成功，第二个应该被阻止
        self.assertTrue(result1)
        self.assertFalse(result2)
    
    def test_element_state_caching(self):
        """测试元素状态缓存"""
        element = BaseUIElement("cache_test", UIElementConfig(cache_duration=1.0))
        
        # 第一次调用
        self.mock_perception.return_value.is_element_visible.return_value = True
        result1 = element.is_visible()
        
        # 第二次调用（应该使用缓存）
        self.mock_perception.return_value.is_element_visible.return_value = False
        result2 = element.is_visible()
        
        self.assertTrue(result1)
        self.assertTrue(result2)  # 应该返回缓存的结果
        
        # 验证只调用了一次
        self.assertEqual(self.mock_perception.return_value.is_element_visible.call_count, 1)
        
        # 等待缓存过期
        time.sleep(1.1)
        
        # 第三次调用（缓存已过期）
        result3 = element.is_visible()
        self.assertFalse(result3)  # 应该返回新的结果
        
        # 验证调用了两次
        self.assertEqual(self.mock_perception.return_value.is_element_visible.call_count, 2)


if __name__ == '__main__':
    unittest.main()
