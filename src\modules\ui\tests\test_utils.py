"""
测试工具类
提供模拟对象和测试辅助功能
"""

import time
import unittest
from unittest.mock import Mock, MagicMock
from typing import Optional, Dict, Any, List

from ....core.data_structures import GameScene
from ..config.ui_element_config import UIElementConfig, ButtonConfig, InputFieldConfig, LabelConfig, Position, Size, MatchResult
from ..config.scene_config import SceneConfig


class MockPerceptionModule:
    """模拟感知模块"""
    
    def __init__(self):
        self.mock_templates = {}
        self.mock_game_state = None
        self.mock_ocr_results = {}
        
    def find_ui_element(self, template_name: str) -> Optional[MatchResult]:
        """模拟UI元素查找"""
        if template_name in self.mock_templates:
            template_data = self.mock_templates[template_name]
            return MatchResult(
                position=Position(template_data['x'], template_data['y']),
                size=Size(template_data.get('width', 100), template_data.get('height', 50)),
                confidence=template_data.get('confidence', 0.9),
                template_name=template_name
            )
        return None
    
    def get_game_state(self):
        """模拟获取游戏状态"""
        return self.mock_game_state
    
    def read_text_from_region(self, region: Dict[str, int], 
                             language: str = "ja", 
                             confidence_threshold: float = 0.7) -> Optional[str]:
        """模拟OCR文本识别"""
        region_key = f"{region['x']}_{region['y']}_{region['width']}_{region['height']}"
        return self.mock_ocr_results.get(region_key)
    
    def wait_for_scene(self, scene_type: GameScene, timeout: float = 10.0) -> bool:
        """模拟等待场景"""
        return True  # 默认总是成功
    
    def set_mock_template(self, template_name: str, x: int, y: int, 
                         confidence: float = 0.9, width: int = 100, height: int = 50):
        """设置模拟模板"""
        self.mock_templates[template_name] = {
            'x': x, 'y': y, 'confidence': confidence,
            'width': width, 'height': height
        }
    
    def set_mock_game_state(self, current_scene: GameScene):
        """设置模拟游戏状态"""
        self.mock_game_state = Mock()
        self.mock_game_state.current_scene = current_scene
    
    def set_mock_ocr_result(self, region: Dict[str, int], text: str):
        """设置模拟OCR结果"""
        region_key = f"{region['x']}_{region['y']}_{region['width']}_{region['height']}"
        self.mock_ocr_results[region_key] = text
    
    def clear_mocks(self):
        """清理所有模拟数据"""
        self.mock_templates.clear()
        self.mock_game_state = None
        self.mock_ocr_results.clear()


class MockActionController:
    """模拟行动控制器"""
    
    def __init__(self):
        self.click_results = {}
        self.input_results = {}
        self.action_history = []
        self.input_simulator = MockInputSimulator()
        
    def click_ui_element(self, template_name: str, 
                        confidence_threshold: float = 0.8,
                        timeout: float = 5.0) -> bool:
        """模拟UI元素点击"""
        result = self.click_results.get(template_name, True)  # 默认成功
        
        self.action_history.append({
            'action': 'click',
            'template': template_name,
            'confidence_threshold': confidence_threshold,
            'timeout': timeout,
            'result': result,
            'timestamp': time.time()
        })
        
        return result
    
    def long_press(self, x: int, y: int, duration: float) -> bool:
        """模拟长按"""
        self.action_history.append({
            'action': 'long_press',
            'x': x, 'y': y, 'duration': duration,
            'result': True,
            'timestamp': time.time()
        })
        return True
    
    def hover(self, x: int, y: int, duration: float) -> bool:
        """模拟悬停"""
        self.action_history.append({
            'action': 'hover',
            'x': x, 'y': y, 'duration': duration,
            'result': True,
            'timestamp': time.time()
        })
        return True
    
    def set_click_result(self, template_name: str, result: bool):
        """设置点击结果"""
        self.click_results[template_name] = result
    
    def get_action_history(self) -> List[Dict[str, Any]]:
        """获取操作历史"""
        return self.action_history.copy()
    
    def clear_history(self):
        """清理操作历史"""
        self.action_history.clear()


class MockInputSimulator:
    """模拟输入模拟器"""
    
    def __init__(self):
        self.typed_text = []
        self.key_presses = []
        
    def type_text(self, text: str):
        """模拟文本输入"""
        self.typed_text.append(text)
        return True
    
    def key_press(self, key):
        """模拟按键"""
        self.key_presses.append(key)
        return True
    
    def key_combination(self, keys: List[str]):
        """模拟组合键"""
        self.key_presses.append(keys)
        return True
    
    def get_typed_text(self) -> List[str]:
        """获取输入的文本"""
        return self.typed_text.copy()
    
    def get_key_presses(self) -> List:
        """获取按键记录"""
        return self.key_presses.copy()
    
    def clear_history(self):
        """清理输入历史"""
        self.typed_text.clear()
        self.key_presses.clear()


def create_test_config(template_name: str = "test_element", **kwargs) -> UIElementConfig:
    """创建测试用的UI元素配置"""
    default_config = {
        'confidence_threshold': 0.8,
        'timeout': 5.0,
        'retry_count': 3,
        'enabled': True
    }
    default_config.update(kwargs)
    
    return UIElementConfig(template_name=template_name, **default_config)


def create_test_button_config(template_name: str = "test_button", **kwargs) -> ButtonConfig:
    """创建测试用的按钮配置"""
    default_config = {
        'confidence_threshold': 0.8,
        'timeout': 5.0,
        'retry_count': 3,
        'enabled': True,
        'double_click_enabled': False,
        'long_press_duration': 1.0
    }
    default_config.update(kwargs)
    
    return ButtonConfig(template_name=template_name, **default_config)


def create_test_input_config(template_name: str = "test_input", **kwargs) -> InputFieldConfig:
    """创建测试用的输入框配置"""
    default_config = {
        'confidence_threshold': 0.8,
        'timeout': 5.0,
        'retry_count': 3,
        'enabled': True,
        'clear_before_input': True,
        'input_delay': 0.1,
        'input_method': 'direct'
    }
    default_config.update(kwargs)
    
    return InputFieldConfig(template_name=template_name, **default_config)


def create_test_label_config(template_name: str = "test_label", **kwargs) -> LabelConfig:
    """创建测试用的标签配置"""
    default_config = {
        'confidence_threshold': 0.8,
        'timeout': 5.0,
        'retry_count': 3,
        'enabled': True,
        'text_recognition_enabled': True,
        'ocr_language': 'ja',
        'ocr_confidence_threshold': 0.7
    }
    default_config.update(kwargs)
    
    return LabelConfig(template_name=template_name, **default_config)


def create_test_scene_config(scene_type: GameScene, **kwargs) -> SceneConfig:
    """创建测试用的场景配置"""
    default_config = {
        'scene_name': scene_type.value,
        'scene_indicators': [f"{scene_type.value}_indicator"],
        'recognition_confidence': 0.8,
        'recognition_timeout': 10.0,
        'ui_elements': {}
    }
    default_config.update(kwargs)
    
    return SceneConfig(scene_type=scene_type, **default_config)


class UITestCase(unittest.TestCase):
    """UI元素测试基类"""
    
    def setUp(self):
        """测试设置"""
        self.perception = MockPerceptionModule()
        self.action = MockActionController()
        
        # 设置默认模拟数据
        self.perception.set_mock_template("test_button", 100, 200, 0.9)
        self.perception.set_mock_template("test_input", 300, 400, 0.85)
        self.perception.set_mock_template("test_label", 500, 600, 0.8)
    
    def tearDown(self):
        """测试清理"""
        self.perception.clear_mocks()
        self.action.clear_history()
    
    def assert_element_clicked(self, template_name: str):
        """断言元素被点击"""
        history = self.action.get_action_history()
        click_actions = [a for a in history if a['action'] == 'click' and a['template'] == template_name]
        self.assertTrue(len(click_actions) > 0, f"元素 {template_name} 未被点击")
    
    def assert_text_typed(self, expected_text: str):
        """断言文本被输入"""
        typed_text = self.action.input_simulator.get_typed_text()
        self.assertIn(expected_text, typed_text, f"文本 '{expected_text}' 未被输入")
    
    def assert_key_pressed(self, expected_key):
        """断言按键被按下"""
        key_presses = self.action.input_simulator.get_key_presses()
        self.assertIn(expected_key, key_presses, f"按键 {expected_key} 未被按下")


class SceneTestCase(unittest.TestCase):
    """场景测试基类"""
    
    def setUp(self):
        """测试设置"""
        self.perception = MockPerceptionModule()
        self.action = MockActionController()
        
        # 设置场景相关的模拟数据
        self.setup_scene_mocks()
    
    def setup_scene_mocks(self):
        """设置场景模拟数据"""
        # 主菜单场景
        self.perception.set_mock_template("main_menu_logo", 960, 100, 0.9)
        self.perception.set_mock_template("produce_button", 500, 400, 0.85)
        
        # 育成准备场景
        self.perception.set_mock_template("produce_setup_title", 960, 100, 0.9)
        self.perception.set_mock_template("start_produce_button", 960, 800, 0.85)
        
        # 育成主界面场景
        self.perception.set_mock_template("produce_main_ui", 960, 100, 0.9)
        self.perception.set_mock_template("vocal_lesson_button", 200, 300, 0.85)
    
    def tearDown(self):
        """测试清理"""
        self.perception.clear_mocks()
        self.action.clear_history()
    
    def assert_scene_navigation(self, from_scene: GameScene, to_scene: GameScene):
        """断言场景导航"""
        # 这里可以添加场景导航的验证逻辑
        pass
    
    def set_current_scene(self, scene_type: GameScene):
        """设置当前场景"""
        self.perception.set_mock_game_state(scene_type)
