# Gakumasu-Bot GUI启动器使用说明

**版本：** 1.0  
**日期：** 2025年7月26日  
**文件：** `gui.py`

## 一、功能概述

GUI启动器 (`gui.py`) 是一个简洁高效的一键启动工具，提供以下核心功能：

- **一键启动**：同时启动后端服务（FastAPI）和前端开发服务器（Vue.js）
- **自动浏览器**：启动完成后自动打开默认浏览器并导航到前端应用
- **进程管理**：统一管理前后端进程的生命周期
- **优雅关闭**：支持 Ctrl+C 优雅关闭所有服务
- **端口检测**：自动检测端口占用并处理冲突
- **错误处理**：完善的错误处理和日志输出

## 二、环境要求

### 2.1 Python环境
- Python 3.13.3+
- 必要的Python依赖包（见requirements.txt）

### 2.2 Node.js环境
- Node.js 18+
- npm 8+
- 前端依赖已安装（在frontend目录运行 `npm install`）

### 2.3 必要依赖
确保已安装以下依赖：
```bash
pip install fastapi uvicorn requests
```

## 三、基本使用

### 3.1 快速启动
```bash
# 默认启动（开发模式）
python gui.py
```

启动后将：
1. 检查端口占用情况
2. 启动后端服务（默认端口8000）
3. 启动前端服务（默认端口3000）
4. 自动打开浏览器访问 http://localhost:3000

### 3.2 命令行选项
```bash
# 指定端口启动
python gui.py --backend-port 8080 --frontend-port 3001

# 不自动打开浏览器
python gui.py --no-browser

# 生产模式启动
python gui.py --mode production

# 调试模式
python gui.py --log-level DEBUG

# 查看帮助
python gui.py --help
```

### 3.3 停止服务
- 按 `Ctrl+C` 优雅关闭所有服务
- 启动器会自动停止前后端进程

## 四、配置说明

### 4.1 默认配置
```python
DEFAULT_CONFIG = {
    'backend': {
        'host': '127.0.0.1',
        'port': 8000,
        'module': 'src.web.main:app',
        'reload': True
    },
    'frontend': {
        'host': '127.0.0.1', 
        'port': 3000,
        'command': 'npm run dev',
        'working_dir': 'frontend'
    },
    'browser': {
        'auto_open': True,
        'url': 'http://localhost:3000',
        'delay': 3  # 启动后等待时间
    }
}
```

### 4.2 端口冲突处理
- 如果默认端口被占用，启动器会自动查找下一个可用端口
- 前端代理配置会自动更新以匹配后端端口
- 浏览器URL会自动更新为实际使用的前端端口

## 五、日志输出

### 5.1 启动日志示例
```
===================================================
== Gakumasu-Bot GUI启动器 v1.0 - 一键启动前后端 ==
===================================================

2025-07-26 10:30:00 [INFO] GUI启动器初始化完成
2025-07-26 10:30:01 [INFO] 后端端口 8000 可用
2025-07-26 10:30:01 [INFO] 前端端口 3000 可用
2025-07-26 10:30:02 [INFO] 启动后端服务: uvicorn src.web.main:app --host 127.0.0.1 --port 8000 --reload
2025-07-26 10:30:05 [INFO] 服务就绪: http://127.0.0.1:8000
2025-07-26 10:30:06 [INFO] 启动前端服务: npm run dev
2025-07-26 10:30:10 [INFO] 服务就绪: http://127.0.0.1:3000
2025-07-26 10:30:13 [INFO] 自动打开浏览器: http://127.0.0.1:3000
2025-07-26 10:30:13 [INFO] 所有服务启动完成，按 Ctrl+C 退出
```

### 5.2 错误处理
- 端口冲突：自动分配可用端口
- 依赖缺失：提供详细的错误信息和解决建议
- 服务启动失败：显示具体错误原因
- 进程异常退出：自动停止所有相关服务

## 六、故障排除

### 6.1 常见问题

**Q: uvicorn 未找到**
```bash
# 安装FastAPI和uvicorn
pip install fastapi uvicorn[standard]
```

**Q: npm 命令未找到**
```bash
# 安装Node.js和npm
# 访问 https://nodejs.org/ 下载安装
```

**Q: 前端依赖未安装**
```bash
# 进入前端目录安装依赖
cd frontend
npm install
```

**Q: 端口被占用**
- 启动器会自动处理端口冲突
- 或者手动指定其他端口：`python gui.py --backend-port 8080`

**Q: 浏览器未自动打开**
```bash
# 手动访问前端地址
# 查看日志中显示的实际URL
```

### 6.2 调试模式
```bash
# 启用详细日志
python gui.py --log-level DEBUG
```

## 七、与现有架构的集成

### 7.1 兼容性
- **完全兼容**现有的 Scheduler、StateManager、ConfigManager 模块
- **无侵入式**集成，不影响现有业务逻辑
- **保持**原有的 WebSocket 通信功能

### 7.2 WebSocket支持
- 后端WebSocket服务会随后端一起启动
- 前端代理配置自动转发WebSocket连接
- 实时状态更新功能正常工作

### 7.3 配置复用
- 使用现有的配置文件结构
- 集成现有的日志系统
- 保持与原有模块的配置一致性

## 八、开发和生产环境

### 8.1 开发环境（默认）
- 后端启用热重载（--reload）
- 前端使用开发服务器（npm run dev）
- 自动打开浏览器
- 详细的日志输出

### 8.2 生产环境
```bash
python gui.py --mode production
```
- 后端禁用热重载
- 前端使用构建后的静态文件
- 优化的性能配置

## 九、测试验证

### 9.1 功能测试
运行测试脚本验证功能：
```bash
python test_gui_launcher.py
```

测试内容包括：
- 端口检测功能
- 配置更新功能
- 依赖检查
- 干运行测试
- 信号处理测试

### 9.2 手动测试
1. 运行 `python gui.py`
2. 验证后端服务：访问 http://localhost:8000
3. 验证前端服务：访问 http://localhost:3000
4. 验证WebSocket连接
5. 测试 Ctrl+C 优雅关闭

## 十、技术特性

### 10.1 核心特性
- 使用Python标准库subprocess管理进程
- 自动端口检测和冲突处理
- 健康检查机制确保服务就绪
- 优雅的信号处理和进程关闭
- 完善的错误处理和日志记录

### 10.2 安全性
- 本地服务绑定（127.0.0.1）
- 进程隔离和资源管理
- 超时机制防止无限等待
- 异常处理确保资源清理

## 十一、后续扩展

### 11.1 可能的增强功能
- 图形化界面（可选）
- 服务状态实时显示
- 配置文件热重载
- 多环境配置支持
- 性能监控和告警

### 11.2 集成建议
- 可以与现有的launcher.py共存
- 作为快速开发启动的便捷工具
- 适合日常开发和测试使用
