"""
策略优化器
基于性能数据和目标优化育成策略
"""

import time
import json
from typing import List, Dict, Any, Optional, Tuple
from enum import Enum
from dataclasses import dataclass, field
from collections import defaultdict
import numpy as np

from ....utils.logger import get_logger
from ....core.data_structures import GameScene
from ..utils.performance_monitor import measure_block


class OptimizationGoal(Enum):
    """优化目标枚举"""
    MAXIMIZE_TOTAL_STATS = "maximize_total_stats"      # 最大化总属性
    BALANCE_STATS = "balance_stats"                    # 平衡属性
    MINIMIZE_TIME = "minimize_time"                    # 最小化时间
    MAXIMIZE_EFFICIENCY = "maximize_efficiency"        # 最大化效率
    MINIMIZE_RISK = "minimize_risk"                    # 最小化风险
    CUSTOM_TARGET = "custom_target"                    # 自定义目标


class StrategyType(Enum):
    """策略类型枚举"""
    AGGRESSIVE = "aggressive"          # 激进策略
    CONSERVATIVE = "conservative"      # 保守策略
    BALANCED = "balanced"             # 平衡策略
    SPECIALIZED = "specialized"        # 专精策略
    ADAPTIVE = "adaptive"             # 自适应策略


class OptimizationMethod(Enum):
    """优化方法枚举"""
    GENETIC_ALGORITHM = "genetic_algorithm"    # 遗传算法
    SIMULATED_ANNEALING = "simulated_annealing"  # 模拟退火
    GRADIENT_DESCENT = "gradient_descent"      # 梯度下降
    RANDOM_SEARCH = "random_search"            # 随机搜索
    BAYESIAN_OPTIMIZATION = "bayesian_optimization"  # 贝叶斯优化


@dataclass
class PerformanceMetrics:
    """性能指标"""
    total_stats: int = 0
    stat_balance: float = 0.0
    completion_time: int = 0
    efficiency_score: float = 0.0
    success_rate: float = 0.0
    risk_score: float = 0.0
    satisfaction_score: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class OptimizationStrategy:
    """优化策略"""
    name: str
    strategy_type: StrategyType
    parameters: Dict[str, Any]
    expected_performance: PerformanceMetrics
    confidence: float = 0.0
    risk_level: float = 0.0
    estimated_time: int = 0
    description: str = ""
    alternatives: List[Dict[str, Any]] = field(default_factory=list)


class StrategyOptimizer:
    """策略优化器"""
    
    def __init__(self, optimization_method: OptimizationMethod = OptimizationMethod.GENETIC_ALGORITHM):
        """
        初始化策略优化器
        
        Args:
            optimization_method: 优化方法
        """
        self.optimization_method = optimization_method
        self.logger = get_logger("StrategyOptimizer")
        
        # 优化参数
        self._population_size = 20
        self._generations = 50
        self._mutation_rate = 0.1
        self._crossover_rate = 0.8
        
        # 策略数据库
        self._strategy_database: List[OptimizationStrategy] = []
        self._performance_history: List[Dict[str, Any]] = []
        self._optimization_history: List[Dict[str, Any]] = []
        
        # 当前最优策略
        self._best_strategy: Optional[OptimizationStrategy] = None
        self._current_generation = 0
        
        # 统计数据
        self._stats = {
            "total_optimizations": 0,
            "successful_optimizations": 0,
            "average_improvement": 0.0,
            "best_performance": 0.0
        }
        
        self.logger.info(f"策略优化器初始化完成，使用{optimization_method.value}方法")
    
    def optimize_strategy(self, goal: OptimizationGoal, constraints: Dict[str, Any] = None,
                         target_performance: PerformanceMetrics = None) -> OptimizationStrategy:
        """
        优化策略
        
        Args:
            goal: 优化目标
            constraints: 约束条件
            target_performance: 目标性能
            
        Returns:
            优化后的策略
        """
        try:
            with measure_block("strategy_optimization"):
                self.logger.info(f"开始策略优化，目标: {goal.value}")
                
                # 初始化优化参数
                optimization_params = {
                    "goal": goal,
                    "constraints": constraints or {},
                    "target_performance": target_performance,
                    "start_time": time.time()
                }
                
                # 根据优化方法执行优化
                if self.optimization_method == OptimizationMethod.GENETIC_ALGORITHM:
                    strategy = self._genetic_algorithm_optimization(optimization_params)
                elif self.optimization_method == OptimizationMethod.SIMULATED_ANNEALING:
                    strategy = self._simulated_annealing_optimization(optimization_params)
                elif self.optimization_method == OptimizationMethod.RANDOM_SEARCH:
                    strategy = self._random_search_optimization(optimization_params)
                else:
                    strategy = self._default_optimization(optimization_params)
                
                # 记录优化结果
                self._record_optimization(optimization_params, strategy)
                
                self.logger.info(f"策略优化完成: {strategy.name}")
                return strategy
                
        except Exception as e:
            self.logger.error(f"策略优化失败: {e}")
            return self._create_fallback_strategy(goal)
    
    def _genetic_algorithm_optimization(self, params: Dict[str, Any]) -> OptimizationStrategy:
        """遗传算法优化"""
        goal = params["goal"]
        constraints = params["constraints"]
        
        # 初始化种群
        population = self._initialize_population(goal, constraints)
        
        best_fitness = 0.0
        best_individual = None
        
        for generation in range(min(10, self._generations)):  # 限制代数以控制时间
            # 评估适应度
            fitness_scores = [self._evaluate_fitness(individual, goal) for individual in population]
            
            # 找到最佳个体
            max_fitness_idx = fitness_scores.index(max(fitness_scores))
            if fitness_scores[max_fitness_idx] > best_fitness:
                best_fitness = fitness_scores[max_fitness_idx]
                best_individual = population[max_fitness_idx].copy()
            
            # 选择、交叉、变异
            new_population = []
            
            # 保留最佳个体（精英策略）
            new_population.append(best_individual.copy())
            
            # 生成新个体
            while len(new_population) < self._population_size:
                # 选择父母
                parent1 = self._tournament_selection(population, fitness_scores)
                parent2 = self._tournament_selection(population, fitness_scores)
                
                # 交叉
                if np.random.random() < self._crossover_rate:
                    child1, child2 = self._crossover(parent1, parent2)
                else:
                    child1, child2 = parent1.copy(), parent2.copy()
                
                # 变异
                if np.random.random() < self._mutation_rate:
                    child1 = self._mutate(child1, constraints)
                if np.random.random() < self._mutation_rate:
                    child2 = self._mutate(child2, constraints)
                
                new_population.extend([child1, child2])
            
            population = new_population[:self._population_size]
            self._current_generation = generation
        
        # 创建最优策略
        return self._create_strategy_from_individual(best_individual, goal, best_fitness)
    
    def _initialize_population(self, goal: OptimizationGoal, constraints: Dict[str, Any]) -> List[Dict[str, Any]]:
        """初始化种群"""
        population = []
        
        for _ in range(self._population_size):
            individual = self._create_random_individual(goal, constraints)
            population.append(individual)
        
        return population
    
    def _create_random_individual(self, goal: OptimizationGoal, constraints: Dict[str, Any]) -> Dict[str, Any]:
        """创建随机个体"""
        individual = {
            "lesson_weights": {
                "vocal": np.random.uniform(0.1, 1.0),
                "dance": np.random.uniform(0.1, 1.0),
                "visual": np.random.uniform(0.1, 1.0),
                "mental": np.random.uniform(0.1, 1.0)
            },
            "action_preferences": {
                "rest_threshold": np.random.uniform(0.2, 0.8),
                "outing_threshold": np.random.uniform(0.2, 0.6),
                "lesson_intensity": np.random.uniform(0.3, 1.0)
            },
            "strategy_type": np.random.choice(list(StrategyType)),
            "risk_tolerance": np.random.uniform(0.1, 0.9),
            "efficiency_focus": np.random.uniform(0.1, 1.0)
        }
        
        # 应用约束条件
        if constraints:
            if "max_risk" in constraints:
                individual["risk_tolerance"] = min(individual["risk_tolerance"], constraints["max_risk"])
            if "min_efficiency" in constraints:
                individual["efficiency_focus"] = max(individual["efficiency_focus"], constraints["min_efficiency"])
        
        return individual
    
    def _evaluate_fitness(self, individual: Dict[str, Any], goal: OptimizationGoal) -> float:
        """评估个体适应度"""
        try:
            # 模拟策略执行并计算性能指标
            performance = self._simulate_strategy_performance(individual)
            
            # 根据目标计算适应度
            if goal == OptimizationGoal.MAXIMIZE_TOTAL_STATS:
                fitness = performance.total_stats / 2000.0  # 归一化
            elif goal == OptimizationGoal.BALANCE_STATS:
                fitness = performance.stat_balance
            elif goal == OptimizationGoal.MINIMIZE_TIME:
                fitness = 1.0 - (performance.completion_time / 100.0)  # 假设最大100天
            elif goal == OptimizationGoal.MAXIMIZE_EFFICIENCY:
                fitness = performance.efficiency_score
            elif goal == OptimizationGoal.MINIMIZE_RISK:
                fitness = 1.0 - performance.risk_score
            else:
                # 综合评分
                fitness = (performance.total_stats / 2000.0 * 0.3 +
                          performance.stat_balance * 0.2 +
                          performance.efficiency_score * 0.3 +
                          (1.0 - performance.risk_score) * 0.2)
            
            return max(0.0, min(1.0, fitness))
            
        except Exception as e:
            self.logger.error(f"评估适应度失败: {e}")
            return 0.0
    
    def _simulate_strategy_performance(self, individual: Dict[str, Any]) -> PerformanceMetrics:
        """模拟策略性能"""
        # 简化的性能模拟
        lesson_weights = individual["lesson_weights"]
        action_prefs = individual["action_preferences"]
        risk_tolerance = individual["risk_tolerance"]
        efficiency_focus = individual["efficiency_focus"]
        
        # 计算预期总属性
        total_weight = sum(lesson_weights.values())
        normalized_weights = {k: v/total_weight for k, v in lesson_weights.items()}
        
        # 基于权重分布计算平衡度
        weight_values = list(normalized_weights.values())
        avg_weight = sum(weight_values) / len(weight_values)
        variance = sum((w - avg_weight) ** 2 for w in weight_values) / len(weight_values)
        balance = 1.0 - min(1.0, variance * 4)  # 归一化方差
        
        # 估算总属性（基于权重和效率）
        base_stats = 1200
        efficiency_bonus = efficiency_focus * 400
        balance_bonus = balance * 200
        total_stats = int(base_stats + efficiency_bonus + balance_bonus)
        
        # 估算完成时间（基于强度和风险容忍度）
        base_time = 78  # 基础天数
        intensity_factor = action_prefs["lesson_intensity"]
        time_reduction = intensity_factor * risk_tolerance * 20
        completion_time = max(50, int(base_time - time_reduction))
        
        # 计算效率分数
        efficiency_score = (total_stats / completion_time) / 20.0  # 归一化
        efficiency_score = min(1.0, efficiency_score)
        
        # 计算风险分数
        risk_score = 1.0 - risk_tolerance
        
        return PerformanceMetrics(
            total_stats=total_stats,
            stat_balance=balance,
            completion_time=completion_time,
            efficiency_score=efficiency_score,
            success_rate=0.8 + risk_tolerance * 0.15,
            risk_score=risk_score,
            satisfaction_score=0.7 + balance * 0.2 + efficiency_focus * 0.1
        )

    def _tournament_selection(self, population: List[Dict[str, Any]], fitness_scores: List[float]) -> Dict[str, Any]:
        """锦标赛选择"""
        tournament_size = 3
        tournament_indices = np.random.choice(len(population), tournament_size, replace=False)
        tournament_fitness = [fitness_scores[i] for i in tournament_indices]
        winner_idx = tournament_indices[tournament_fitness.index(max(tournament_fitness))]
        return population[winner_idx].copy()

    def _crossover(self, parent1: Dict[str, Any], parent2: Dict[str, Any]) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """交叉操作"""
        child1 = parent1.copy()
        child2 = parent2.copy()

        # 课程权重交叉
        for stat in ["vocal", "dance", "visual", "mental"]:
            if np.random.random() < 0.5:
                child1["lesson_weights"][stat] = parent2["lesson_weights"][stat]
                child2["lesson_weights"][stat] = parent1["lesson_weights"][stat]

        # 行动偏好交叉
        for pref in ["rest_threshold", "outing_threshold", "lesson_intensity"]:
            if np.random.random() < 0.5:
                child1["action_preferences"][pref] = parent2["action_preferences"][pref]
                child2["action_preferences"][pref] = parent1["action_preferences"][pref]

        # 其他参数交叉
        if np.random.random() < 0.5:
            child1["risk_tolerance"] = parent2["risk_tolerance"]
            child2["risk_tolerance"] = parent1["risk_tolerance"]

        if np.random.random() < 0.5:
            child1["efficiency_focus"] = parent2["efficiency_focus"]
            child2["efficiency_focus"] = parent1["efficiency_focus"]

        return child1, child2

    def _mutate(self, individual: Dict[str, Any], constraints: Dict[str, Any]) -> Dict[str, Any]:
        """变异操作"""
        mutated = individual.copy()

        # 课程权重变异
        for stat in ["vocal", "dance", "visual", "mental"]:
            if np.random.random() < 0.3:
                mutation = np.random.normal(0, 0.1)
                mutated["lesson_weights"][stat] = max(0.1, min(1.0,
                    mutated["lesson_weights"][stat] + mutation))

        # 行动偏好变异
        if np.random.random() < 0.3:
            mutation = np.random.normal(0, 0.05)
            mutated["action_preferences"]["rest_threshold"] = max(0.1, min(0.9,
                mutated["action_preferences"]["rest_threshold"] + mutation))

        if np.random.random() < 0.3:
            mutation = np.random.normal(0, 0.05)
            mutated["action_preferences"]["outing_threshold"] = max(0.1, min(0.8,
                mutated["action_preferences"]["outing_threshold"] + mutation))

        if np.random.random() < 0.3:
            mutation = np.random.normal(0, 0.1)
            mutated["action_preferences"]["lesson_intensity"] = max(0.1, min(1.0,
                mutated["action_preferences"]["lesson_intensity"] + mutation))

        # 风险容忍度变异
        if np.random.random() < 0.2:
            mutation = np.random.normal(0, 0.1)
            mutated["risk_tolerance"] = max(0.1, min(0.9, mutated["risk_tolerance"] + mutation))

        # 效率关注度变异
        if np.random.random() < 0.2:
            mutation = np.random.normal(0, 0.1)
            mutated["efficiency_focus"] = max(0.1, min(1.0, mutated["efficiency_focus"] + mutation))

        # 应用约束
        if constraints:
            if "max_risk" in constraints:
                mutated["risk_tolerance"] = min(mutated["risk_tolerance"], constraints["max_risk"])
            if "min_efficiency" in constraints:
                mutated["efficiency_focus"] = max(mutated["efficiency_focus"], constraints["min_efficiency"])

        return mutated

    def _create_strategy_from_individual(self, individual: Dict[str, Any], goal: OptimizationGoal, fitness: float) -> OptimizationStrategy:
        """从个体创建策略"""
        performance = self._simulate_strategy_performance(individual)

        strategy_name = f"优化策略_{goal.value}_{int(time.time() % 10000)}"

        description = self._generate_strategy_description(individual, goal, performance)

        return OptimizationStrategy(
            name=strategy_name,
            strategy_type=individual["strategy_type"],
            parameters=individual.copy(),
            expected_performance=performance,
            confidence=fitness,
            risk_level=individual["risk_tolerance"],
            estimated_time=performance.completion_time,
            description=description
        )

    def _generate_strategy_description(self, individual: Dict[str, Any], goal: OptimizationGoal, performance: PerformanceMetrics) -> str:
        """生成策略描述"""
        lesson_weights = individual["lesson_weights"]
        dominant_stat = max(lesson_weights.keys(), key=lambda k: lesson_weights[k])

        strategy_type = individual["strategy_type"].value
        risk_level = "高" if individual["risk_tolerance"] > 0.7 else "中" if individual["risk_tolerance"] > 0.4 else "低"

        description = f"这是一个{strategy_type}策略，主要关注{dominant_stat}属性的提升。"
        description += f"风险容忍度为{risk_level}，预期完成时间{performance.completion_time}天。"
        description += f"预期总属性{performance.total_stats}，平衡度{performance.stat_balance:.2f}。"

        return description

    def _simulated_annealing_optimization(self, params: Dict[str, Any]) -> OptimizationStrategy:
        """模拟退火优化"""
        goal = params["goal"]
        constraints = params["constraints"]

        # 初始解
        current_solution = self._create_random_individual(goal, constraints)
        current_fitness = self._evaluate_fitness(current_solution, goal)

        best_solution = current_solution.copy()
        best_fitness = current_fitness

        # 模拟退火参数
        initial_temp = 1.0
        final_temp = 0.01
        cooling_rate = 0.95

        temperature = initial_temp

        for iteration in range(100):  # 限制迭代次数
            # 生成邻居解
            neighbor = self._mutate(current_solution.copy(), constraints)
            neighbor_fitness = self._evaluate_fitness(neighbor, goal)

            # 接受准则
            if neighbor_fitness > current_fitness:
                current_solution = neighbor
                current_fitness = neighbor_fitness

                if neighbor_fitness > best_fitness:
                    best_solution = neighbor.copy()
                    best_fitness = neighbor_fitness
            else:
                # 以一定概率接受较差解
                delta = neighbor_fitness - current_fitness
                probability = np.exp(delta / temperature)
                if np.random.random() < probability:
                    current_solution = neighbor
                    current_fitness = neighbor_fitness

            # 降温
            temperature *= cooling_rate
            if temperature < final_temp:
                break

        return self._create_strategy_from_individual(best_solution, goal, best_fitness)

    def _random_search_optimization(self, params: Dict[str, Any]) -> OptimizationStrategy:
        """随机搜索优化"""
        goal = params["goal"]
        constraints = params["constraints"]

        best_solution = None
        best_fitness = 0.0

        # 随机搜索
        for _ in range(50):  # 限制搜索次数
            solution = self._create_random_individual(goal, constraints)
            fitness = self._evaluate_fitness(solution, goal)

            if fitness > best_fitness:
                best_solution = solution
                best_fitness = fitness

        return self._create_strategy_from_individual(best_solution, goal, best_fitness)

    def _default_optimization(self, params: Dict[str, Any]) -> OptimizationStrategy:
        """默认优化方法"""
        goal = params["goal"]

        # 创建基于目标的默认策略
        if goal == OptimizationGoal.BALANCE_STATS:
            individual = {
                "lesson_weights": {"vocal": 0.25, "dance": 0.25, "visual": 0.25, "mental": 0.25},
                "action_preferences": {"rest_threshold": 0.4, "outing_threshold": 0.3, "lesson_intensity": 0.7},
                "strategy_type": StrategyType.BALANCED,
                "risk_tolerance": 0.5,
                "efficiency_focus": 0.6
            }
        elif goal == OptimizationGoal.MAXIMIZE_TOTAL_STATS:
            individual = {
                "lesson_weights": {"vocal": 0.3, "dance": 0.3, "visual": 0.2, "mental": 0.2},
                "action_preferences": {"rest_threshold": 0.3, "outing_threshold": 0.2, "lesson_intensity": 0.9},
                "strategy_type": StrategyType.AGGRESSIVE,
                "risk_tolerance": 0.8,
                "efficiency_focus": 0.9
            }
        else:
            individual = {
                "lesson_weights": {"vocal": 0.25, "dance": 0.25, "visual": 0.25, "mental": 0.25},
                "action_preferences": {"rest_threshold": 0.5, "outing_threshold": 0.4, "lesson_intensity": 0.6},
                "strategy_type": StrategyType.BALANCED,
                "risk_tolerance": 0.4,
                "efficiency_focus": 0.5
            }

        fitness = self._evaluate_fitness(individual, goal)
        return self._create_strategy_from_individual(individual, goal, fitness)

    def _create_fallback_strategy(self, goal: OptimizationGoal) -> OptimizationStrategy:
        """创建后备策略"""
        return OptimizationStrategy(
            name="后备策略",
            strategy_type=StrategyType.BALANCED,
            parameters={
                "lesson_weights": {"vocal": 0.25, "dance": 0.25, "visual": 0.25, "mental": 0.25},
                "action_preferences": {"rest_threshold": 0.5, "outing_threshold": 0.4, "lesson_intensity": 0.6},
                "risk_tolerance": 0.5,
                "efficiency_focus": 0.5
            },
            expected_performance=PerformanceMetrics(
                total_stats=1400,
                stat_balance=0.8,
                completion_time=70,
                efficiency_score=0.6,
                success_rate=0.8,
                risk_score=0.3
            ),
            confidence=0.5,
            risk_level=0.5,
            estimated_time=70,
            description="系统异常时使用的平衡策略"
        )

    def _record_optimization(self, params: Dict[str, Any], strategy: OptimizationStrategy):
        """记录优化结果"""
        optimization_record = {
            "timestamp": time.time(),
            "goal": params["goal"].value,
            "method": self.optimization_method.value,
            "strategy_name": strategy.name,
            "confidence": strategy.confidence,
            "expected_performance": {
                "total_stats": strategy.expected_performance.total_stats,
                "efficiency_score": strategy.expected_performance.efficiency_score,
                "completion_time": strategy.expected_performance.estimated_time
            },
            "optimization_time": time.time() - params["start_time"]
        }

        self._optimization_history.append(optimization_record)
        self._strategy_database.append(strategy)
        self._stats["total_optimizations"] += 1

        # 更新最佳策略
        if not self._best_strategy or strategy.confidence > self._best_strategy.confidence:
            self._best_strategy = strategy
            self._stats["best_performance"] = strategy.expected_performance.total_stats

        # 保持历史记录在合理范围内
        if len(self._optimization_history) > 100:
            self._optimization_history = self._optimization_history[-50:]
        if len(self._strategy_database) > 50:
            self._strategy_database = self._strategy_database[-25:]

    def get_best_strategy(self) -> Optional[OptimizationStrategy]:
        """获取最佳策略"""
        return self._best_strategy

    def get_optimization_stats(self) -> Dict[str, Any]:
        """获取优化统计"""
        return {
            "total_optimizations": self._stats["total_optimizations"],
            "successful_optimizations": self._stats["successful_optimizations"],
            "average_improvement": self._stats["average_improvement"],
            "best_performance": self._stats["best_performance"],
            "current_generation": self._current_generation,
            "strategy_database_size": len(self._strategy_database),
            "optimization_method": self.optimization_method.value
        }

    def compare_strategies(self, strategies: List[OptimizationStrategy]) -> Dict[str, Any]:
        """比较策略"""
        if not strategies:
            return {}

        comparison = {
            "strategy_count": len(strategies),
            "performance_comparison": {},
            "best_strategy": None,
            "recommendations": []
        }

        # 性能比较
        for metric in ["total_stats", "stat_balance", "efficiency_score", "completion_time"]:
            values = [getattr(s.expected_performance, metric) for s in strategies]
            comparison["performance_comparison"][metric] = {
                "min": min(values),
                "max": max(values),
                "average": sum(values) / len(values),
                "best_strategy_index": values.index(max(values) if metric != "completion_time" else min(values))
            }

        # 找出最佳策略
        best_idx = max(range(len(strategies)), key=lambda i: strategies[i].confidence)
        comparison["best_strategy"] = strategies[best_idx].name

        return comparison
