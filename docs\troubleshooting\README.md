# 故障排除指南

## 概述

本指南提供了Gakumasu-Bot UI模块常见问题的诊断和解决方案，帮助您快速解决使用过程中遇到的问题。

## 目录

- [系统诊断](#系统诊断)
- [常见问题](#常见问题)
- [错误代码](#错误代码)
- [性能问题](#性能问题)
- [日志分析](#日志分析)
- [高级故障排除](#高级故障排除)

## 系统诊断

### 快速诊断工具

运行系统诊断工具获取系统状态：

```bash
# 运行完整系统诊断
python -m src.modules.ui.tools.system_diagnostic

# 快速健康检查
python -m src.modules.ui.tools.health_check

# 检查特定组件
python -m src.modules.ui.tools.component_check --component ui_elements
```

### 诊断报告示例

```
=== Gakumasu-Bot UI模块系统诊断报告 ===
诊断时间: 2024-12-01 14:30:00

系统环境:
✓ 操作系统: Windows 11 (64位)
✓ Python版本: 3.9.7
✓ 内存: 16GB (可用: 12GB)
✓ 磁盘空间: 500GB (可用: 200GB)

依赖检查:
✓ opencv-python: 4.8.0
✓ pillow: 10.0.0
✓ numpy: 1.24.0
✗ pytesseract: 未安装

游戏连接:
✓ 游戏进程: gakumasu.exe (PID: 12345)
✓ 游戏窗口: 已找到
✗ 窗口分辨率: 1366x768 (推荐: 1920x1080)

配置文件:
✓ 主配置文件: config/ui_config.yaml
✓ 场景配置: 3个文件
✗ 模板文件: 缺少2个模板

建议修复:
1. 安装pytesseract: pip install pytesseract
2. 调整游戏分辨率到1920x1080
3. 更新缺失的模板文件
```

## 常见问题

### 1. 安装和启动问题

#### Q: 程序无法启动，提示模块导入错误

**症状：**
```
ModuleNotFoundError: No module named 'cv2'
```

**解决方案：**
```bash
# 安装缺失的依赖
pip install opencv-python
pip install -r requirements.txt

# 验证安装
python -c "import cv2; print(cv2.__version__)"
```

#### Q: 配置文件加载失败

**症状：**
```
ConfigError: Failed to load config file: config/ui_config.yaml
```

**解决方案：**
1. 检查配置文件是否存在
2. 验证YAML语法
3. 检查文件权限

```bash
# 验证配置文件语法
python -m yaml config/ui_config.yaml

# 重置为默认配置
python -m src.modules.ui.tools.config_reset
```

### 2. 游戏连接问题

#### Q: 无法检测到游戏窗口

**症状：**
- 程序提示"未找到游戏窗口"
- 游戏已启动但系统无法识别

**解决方案：**
1. **检查游戏进程**
   ```bash
   # 查看游戏进程
   tasklist | findstr gakumasu
   ```

2. **验证窗口标题**
   ```python
   from src.modules.ui.tools.window_finder import WindowFinder
   
   finder = WindowFinder()
   windows = finder.find_all_windows()
   for window in windows:
       print(f"窗口标题: {window.title}")
   ```

3. **以管理员权限运行**
   - 右键点击程序
   - 选择"以管理员身份运行"

#### Q: 游戏窗口分辨率不匹配

**症状：**
- UI元素识别失败
- 点击位置偏移

**解决方案：**
1. 调整游戏分辨率到1920x1080
2. 更新UI元素模板
3. 重新校准坐标

```bash
# 重新校准UI元素
python -m src.modules.ui.tools.element_calibrator
```

### 3. UI识别问题

#### Q: UI元素识别失败

**症状：**
- 按钮无法点击
- 输入框无法定位
- 文本识别错误

**解决方案：**
1. **调整识别参数**
   ```yaml
   # config/ui_config.yaml
   ui_elements:
     confidence_threshold: 0.7  # 降低阈值
     timeout: 10.0              # 增加超时时间
   ```

2. **更新模板图片**
   ```bash
   # 重新截取模板
   python -m src.modules.ui.tools.template_updater
   ```

3. **启用调试模式**
   ```bash
   python main.py --debug --save-screenshots
   ```

#### Q: 文本识别不准确

**症状：**
- OCR识别结果错误
- 中文字符识别失败

**解决方案：**
1. **安装中文语言包**
   ```bash
   # 下载中文训练数据
   # 将chi_sim.traineddata放到tessdata目录
   ```

2. **调整OCR参数**
   ```yaml
   perception:
     text_recognition:
       language: "chi_sim"
       confidence_threshold: 0.6
   ```

3. **图像预处理**
   ```python
   # 启用图像预处理
   perception:
     image_recognition:
       preprocessing: true
   ```

### 4. 性能问题

#### Q: 程序运行缓慢

**症状：**
- 操作响应延迟
- CPU使用率过高
- 内存占用过多

**解决方案：**
1. **启用性能优化**
   ```yaml
   performance:
     enable_caching: true
     rendering:
       enable_batching: true
       target_fps: 30  # 降低目标FPS
   ```

2. **调整并发设置**
   ```yaml
   automation_engine:
     max_concurrent_workflows: 3  # 减少并发数
   ```

3. **清理缓存**
   ```bash
   python -m src.modules.ui.tools.cache_cleaner
   ```

#### Q: 内存泄漏

**症状：**
- 内存使用持续增长
- 程序运行一段时间后变慢

**解决方案：**
1. **启用内存监控**
   ```bash
   python main.py --enable-memory-monitoring
   ```

2. **强制垃圾回收**
   ```python
   from src.modules.ui.optimization.memory_optimizer import MemoryOptimizer
   
   optimizer = MemoryOptimizer()
   optimizer.force_garbage_collection()
   ```

3. **重启程序**
   - 定期重启程序释放内存

## 错误代码

### 系统错误代码

| 错误代码 | 描述 | 解决方案 |
|---------|------|----------|
| UI-001 | UI元素未找到 | 检查模板文件，调整识别参数 |
| UI-002 | 元素交互失败 | 验证元素状态，增加重试次数 |
| UI-003 | 场景检测失败 | 更新场景配置，检查游戏状态 |
| UI-004 | 配置加载错误 | 验证配置文件语法和路径 |
| UI-005 | 权限不足 | 以管理员权限运行程序 |
| UI-006 | 内存不足 | 增加系统内存，启用内存优化 |
| UI-007 | 超时错误 | 增加超时时间，检查网络连接 |
| UI-008 | 依赖缺失 | 安装缺失的Python包 |

### 详细错误信息

#### UI-001: UI元素未找到

```
ElementNotFoundError: Button 'start_button' not found after 5.0 seconds
Location: src/modules/ui/elements/button.py:45
Confidence: 0.65 (threshold: 0.8)
Template: templates/start_button.png
```

**解决步骤：**
1. 检查模板文件是否存在
2. 降低confidence_threshold到0.6
3. 重新截取模板图片
4. 检查游戏界面是否正确

#### UI-003: 场景检测失败

```
SceneDetectionError: Unable to detect current scene
Detected elements: ['button_1', 'label_2']
Expected scene: TRAINING
Confidence: 0.45 (threshold: 0.7)
```

**解决步骤：**
1. 检查场景配置文件
2. 更新场景检测指标
3. 调整检测阈值
4. 验证游戏界面状态

## 性能问题

### 性能监控

```bash
# 启动性能监控
python -m src.modules.ui.tools.performance_monitor

# 生成性能报告
python -m src.modules.ui.tools.performance_reporter --duration 300
```

### 性能优化建议

1. **CPU优化**
   - 降低图像识别频率
   - 启用多线程处理
   - 使用GPU加速（如果可用）

2. **内存优化**
   - 启用缓存管理
   - 定期清理临时文件
   - 限制并发操作数量

3. **磁盘I/O优化**
   - 使用SSD存储
   - 减少日志输出
   - 压缩模板文件

## 日志分析

### 日志文件位置

```
logs/
├── ui_module.log          # 主日志文件
├── error.log              # 错误日志
├── performance.log        # 性能日志
├── debug.log              # 调试日志
└── archive/               # 历史日志归档
```

### 日志级别

- **DEBUG**: 详细调试信息
- **INFO**: 一般信息
- **WARNING**: 警告信息
- **ERROR**: 错误信息
- **CRITICAL**: 严重错误

### 日志分析工具

```bash
# 查看最新错误
python -m src.modules.ui.tools.log_analyzer --errors --last 24h

# 分析性能问题
python -m src.modules.ui.tools.log_analyzer --performance --slow-operations

# 生成日志报告
python -m src.modules.ui.tools.log_reporter --output report.html
```

### 常见日志模式

#### 1. 元素识别失败
```
2024-12-01 14:30:15 WARNING [Button] Element 'start_button' confidence 0.65 below threshold 0.8
2024-12-01 14:30:16 ERROR [Button] Failed to find element 'start_button' after 3 retries
```

#### 2. 内存使用警告
```
2024-12-01 14:35:20 WARNING [MemoryOptimizer] Memory usage 85% exceeds threshold 80%
2024-12-01 14:35:21 INFO [MemoryOptimizer] Triggering garbage collection
```

#### 3. 性能问题
```
2024-12-01 14:40:10 WARNING [PerformanceMonitor] Operation 'click_button' took 2.5s (threshold: 1.0s)
2024-12-01 14:40:11 INFO [RenderingOptimizer] FPS dropped to 25 (target: 60)
```

## 高级故障排除

### 1. 网络诊断

```bash
# 检查网络连接
python -m src.modules.ui.tools.network_diagnostic

# 测试API连接
python -m src.modules.ui.tools.api_tester
```

### 2. 系统资源监控

```python
from src.modules.ui.tools.system_monitor import SystemMonitor

monitor = SystemMonitor()
monitor.start_monitoring()

# 获取资源使用报告
report = monitor.get_resource_report()
print(f"CPU使用率: {report['cpu_percent']}%")
print(f"内存使用率: {report['memory_percent']}%")
```

### 3. 调试模式

```bash
# 启用详细调试
python main.py --debug --verbose --log-level DEBUG

# 启用性能分析
python main.py --profile --profile-output profile.stats

# 启用内存分析
python main.py --memory-profile --memory-output memory.prof
```

### 4. 远程诊断

```bash
# 生成诊断包
python -m src.modules.ui.tools.diagnostic_packager

# 上传诊断数据（可选）
python -m src.modules.ui.tools.diagnostic_uploader --package diagnostic.zip
```

## 获取帮助

### 1. 自助诊断

```bash
# 运行自助诊断向导
python -m src.modules.ui.tools.self_diagnostic_wizard
```

### 2. 社区支持

- **GitHub Issues**: 报告bug和获取帮助
- **讨论区**: 与其他用户交流经验
- **Wiki**: 查看社区贡献的解决方案

### 3. 专业支持

如果问题仍未解决，请联系技术支持：

- **邮件**: <EMAIL>
- **提供信息**:
  - 系统诊断报告
  - 错误日志文件
  - 问题复现步骤
  - 系统环境信息

### 4. 问题报告模板

```
问题描述:
[详细描述遇到的问题]

复现步骤:
1. [步骤1]
2. [步骤2]
3. [步骤3]

期望结果:
[描述期望的正常行为]

实际结果:
[描述实际发生的情况]

环境信息:
- 操作系统: Windows 11
- Python版本: 3.9.7
- 程序版本: 2.0.0
- 游戏版本: 1.2.3

错误日志:
[粘贴相关的错误日志]

附加信息:
[其他可能有用的信息]
```

通过遵循本指南，您应该能够解决大部分常见问题。如果问题持续存在，请不要犹豫寻求帮助。
