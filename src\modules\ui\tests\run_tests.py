"""
测试运行脚本
提供便捷的测试执行和报告功能
"""

import unittest
import sys
import time
import os
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))


class TestRunner:
    """测试运行器"""
    
    def __init__(self):
        self.test_results = {}
        self.start_time = None
        self.end_time = None
    
    def run_all_tests(self, verbosity: int = 2) -> Dict[str, Any]:
        """运行所有测试"""
        print("=" * 70)
        print("开始运行UI架构测试套件")
        print("=" * 70)
        
        self.start_time = time.time()
        
        # 发现并运行测试
        test_loader = unittest.TestLoader()
        test_suite = test_loader.discover(
            start_dir=Path(__file__).parent,
            pattern='test_*.py',
            top_level_dir=project_root
        )
        
        # 运行测试
        runner = unittest.TextTestRunner(
            verbosity=verbosity,
            stream=sys.stdout,
            buffer=True
        )
        
        result = runner.run(test_suite)
        
        self.end_time = time.time()
        
        # 生成测试报告
        self.test_results = {
            'tests_run': result.testsRun,
            'failures': len(result.failures),
            'errors': len(result.errors),
            'skipped': len(result.skipped) if hasattr(result, 'skipped') else 0,
            'success_rate': (result.testsRun - len(result.failures) - len(result.errors)) / max(1, result.testsRun),
            'duration': self.end_time - self.start_time,
            'failure_details': result.failures,
            'error_details': result.errors
        }
        
        self.print_summary()
        return self.test_results
    
    def run_specific_tests(self, test_modules: List[str], verbosity: int = 2) -> Dict[str, Any]:
        """运行指定的测试模块"""
        print(f"运行指定测试模块: {', '.join(test_modules)}")
        print("=" * 70)
        
        self.start_time = time.time()
        
        # 构建测试套件
        test_loader = unittest.TestLoader()
        test_suite = unittest.TestSuite()
        
        for module_name in test_modules:
            try:
                module = __import__(f"src.modules.ui.tests.{module_name}", fromlist=[module_name])
                suite = test_loader.loadTestsFromModule(module)
                test_suite.addTest(suite)
            except ImportError as e:
                print(f"警告: 无法导入测试模块 {module_name}: {e}")
        
        # 运行测试
        runner = unittest.TextTestRunner(
            verbosity=verbosity,
            stream=sys.stdout,
            buffer=True
        )
        
        result = runner.run(test_suite)
        
        self.end_time = time.time()
        
        # 生成测试报告
        self.test_results = {
            'tests_run': result.testsRun,
            'failures': len(result.failures),
            'errors': len(result.errors),
            'skipped': len(result.skipped) if hasattr(result, 'skipped') else 0,
            'success_rate': (result.testsRun - len(result.failures) - len(result.errors)) / max(1, result.testsRun),
            'duration': self.end_time - self.start_time,
            'failure_details': result.failures,
            'error_details': result.errors
        }
        
        self.print_summary()
        return self.test_results
    
    def run_performance_tests(self, verbosity: int = 2) -> Dict[str, Any]:
        """运行性能测试"""
        print("运行性能基准测试")
        print("=" * 70)

        return self.run_specific_tests(['test_integration', 'test_system_integration'], verbosity)

    def run_scene_tests(self, verbosity: int = 2) -> Dict[str, Any]:
        """运行场景测试"""
        print("运行场景功能测试")
        print("=" * 70)

        return self.run_specific_tests(['test_scenes', 'test_game_scenes'], verbosity)

    def run_integration_tests(self, verbosity: int = 2) -> Dict[str, Any]:
        """运行集成测试"""
        print("运行系统集成测试")
        print("=" * 70)

        return self.run_specific_tests(['test_integration', 'test_system_integration'], verbosity)
    
    def print_summary(self):
        """打印测试摘要"""
        print("\n" + "=" * 70)
        print("测试摘要")
        print("=" * 70)
        
        results = self.test_results
        
        print(f"总测试数: {results['tests_run']}")
        print(f"成功: {results['tests_run'] - results['failures'] - results['errors']}")
        print(f"失败: {results['failures']}")
        print(f"错误: {results['errors']}")
        print(f"跳过: {results['skipped']}")
        print(f"成功率: {results['success_rate']:.2%}")
        print(f"执行时间: {results['duration']:.2f}秒")
        
        # 打印失败详情
        if results['failures']:
            print(f"\n失败详情 ({results['failures']}个):")
            for i, (test, traceback) in enumerate(results['failure_details'], 1):
                print(f"\n{i}. {test}")
                print(f"   {traceback.split('AssertionError:')[-1].strip() if 'AssertionError:' in traceback else '详见完整输出'}")
        
        # 打印错误详情
        if results['errors']:
            print(f"\n错误详情 ({results['errors']}个):")
            for i, (test, traceback) in enumerate(results['error_details'], 1):
                print(f"\n{i}. {test}")
                print(f"   {traceback.split('Exception:')[-1].strip() if 'Exception:' in traceback else '详见完整输出'}")
        
        # 总体评估
        print(f"\n{'='*70}")
        if results['success_rate'] >= 0.95:
            print("✅ 测试结果: 优秀")
        elif results['success_rate'] >= 0.8:
            print("⚠️  测试结果: 良好")
        else:
            print("❌ 测试结果: 需要改进")
        print("=" * 70)
    
    def generate_html_report(self, output_file: str = "test_report.html"):
        """生成HTML测试报告"""
        if not self.test_results:
            print("没有测试结果可生成报告")
            return
        
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>UI架构测试报告</title>
    <meta charset="utf-8">
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
        .summary {{ margin: 20px 0; }}
        .success {{ color: green; }}
        .failure {{ color: red; }}
        .error {{ color: orange; }}
        .details {{ margin-top: 20px; }}
        .test-case {{ margin: 10px 0; padding: 10px; border-left: 3px solid #ccc; }}
        table {{ border-collapse: collapse; width: 100%; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>UI架构测试报告</h1>
        <p>生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    
    <div class="summary">
        <h2>测试摘要</h2>
        <table>
            <tr><th>指标</th><th>数值</th></tr>
            <tr><td>总测试数</td><td>{self.test_results['tests_run']}</td></tr>
            <tr><td>成功</td><td class="success">{self.test_results['tests_run'] - self.test_results['failures'] - self.test_results['errors']}</td></tr>
            <tr><td>失败</td><td class="failure">{self.test_results['failures']}</td></tr>
            <tr><td>错误</td><td class="error">{self.test_results['errors']}</td></tr>
            <tr><td>跳过</td><td>{self.test_results['skipped']}</td></tr>
            <tr><td>成功率</td><td>{self.test_results['success_rate']:.2%}</td></tr>
            <tr><td>执行时间</td><td>{self.test_results['duration']:.2f}秒</td></tr>
        </table>
    </div>
    
    <div class="details">
        <h2>详细结果</h2>
        """
        
        if self.test_results['failures']:
            html_content += "<h3>失败的测试</h3>"
            for test, traceback in self.test_results['failure_details']:
                html_content += f"""
                <div class="test-case failure">
                    <strong>{test}</strong>
                    <pre>{traceback}</pre>
                </div>
                """
        
        if self.test_results['errors']:
            html_content += "<h3>错误的测试</h3>"
            for test, traceback in self.test_results['error_details']:
                html_content += f"""
                <div class="test-case error">
                    <strong>{test}</strong>
                    <pre>{traceback}</pre>
                </div>
                """
        
        html_content += """
    </div>
</body>
</html>
        """
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"HTML报告已生成: {output_file}")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='UI架构测试运行器')
    parser.add_argument('--module', '-m', nargs='+', help='指定要运行的测试模块')
    parser.add_argument('--performance', '-p', action='store_true', help='只运行性能测试')
    parser.add_argument('--scenes', '-s', action='store_true', help='只运行场景测试')
    parser.add_argument('--integration', '-i', action='store_true', help='只运行集成测试')
    parser.add_argument('--verbosity', '-v', type=int, default=2, choices=[0, 1, 2], help='输出详细程度')
    parser.add_argument('--html-report', '-r', help='生成HTML报告的文件名')
    
    args = parser.parse_args()
    
    runner = TestRunner()
    
    try:
        if args.performance:
            results = runner.run_performance_tests(args.verbosity)
        elif args.scenes:
            results = runner.run_scene_tests(args.verbosity)
        elif args.integration:
            results = runner.run_integration_tests(args.verbosity)
        elif args.module:
            results = runner.run_specific_tests(args.module, args.verbosity)
        else:
            results = runner.run_all_tests(args.verbosity)
        
        # 生成HTML报告
        if args.html_report:
            runner.generate_html_report(args.html_report)
        
        # 返回适当的退出码
        if results['failures'] > 0 or results['errors'] > 0:
            sys.exit(1)
        else:
            sys.exit(0)
            
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(130)
    except Exception as e:
        print(f"测试运行器发生错误: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
