{"name": "gakumasu-bot-frontend", "version": "1.0.0", "description": "Gakumasu-Bot 前端用户界面", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.ts,.tsx --fix", "lint:style": "stylelint **/*.{vue,css,scss} --fix", "type-check": "vue-tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "analyze": "vite-bundle-analyzer", "clean": "rimraf dist node_modules/.vite"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "echarts": "^5.4.3", "vue-echarts": "^6.6.1", "dayjs": "^1.11.10", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "js-yaml": "^4.1.0", "monaco-editor": "^0.45.0", "@monaco-editor/loader": "^1.4.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.2", "vite": "^5.0.10", "typescript": "^5.3.3", "vue-tsc": "^1.8.25", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.2", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.2", "prettier": "^3.1.1", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.26.0", "vite-plugin-eslint": "^1.8.1", "sass": "^1.69.7", "terser": "^5.26.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}