"""
综合评估场景实现
处理育成过程中的综合评估、属性分析和成长建议
"""

import time
from typing import List, Dict, Any, Optional, Tuple
from enum import Enum

from .....core.data_structures import GameScene
from ...config.scene_config import SceneConfig
from ...config.ui_element_config import ButtonConfig, LabelConfig
from ..base_game_scene import BaseGameScene
from ...utils.performance_monitor import measure_block
from ...utils.scene_optimizer import get_scene_optimizer


class EvaluationCategory(Enum):
    """评估类别枚举"""
    OVERALL = "overall"            # 综合评估
    VOCAL = "vocal"               # 声乐评估
    DANCE = "dance"               # 舞蹈评估
    VISUAL = "visual"             # 视觉评估
    MENTAL = "mental"             # 精神评估
    GROWTH = "growth"             # 成长评估


class EvaluationGrade(Enum):
    """评估等级枚举"""
    S_PLUS = "S+"     # S+级
    S = "S"           # S级
    A = "A"           # A级
    B = "B"           # B级
    C = "C"           # C级
    D = "D"           # D级


class RecommendationType(Enum):
    """建议类型枚举"""
    TRAINING = "training"         # 训练建议
    STRATEGY = "strategy"         # 策略建议
    IMPROVEMENT = "improvement"   # 改进建议
    MAINTENANCE = "maintenance"   # 维持建议


class EvaluationScene(BaseGameScene):
    """综合评估场景类"""
    
    def __init__(self, config: SceneConfig, perception_module, action_controller):
        """
        初始化综合评估场景
        
        Args:
            config: 场景配置
            perception_module: 感知模块
            action_controller: 行动控制器
        """
        super().__init__(config, perception_module, action_controller)
        
        # 评估数据
        self._overall_grade: Optional[EvaluationGrade] = None
        self._category_grades: Dict[EvaluationCategory, EvaluationGrade] = {}
        self._evaluation_scores: Dict[str, int] = {}
        
        # 属性数据
        self._current_stats = {
            "vocal": 0, "dance": 0, "visual": 0, "mental": 0,
            "stamina": 100, "motivation": 3
        }
        self._stat_rankings = {
            "vocal": 0, "dance": 0, "visual": 0, "mental": 0
        }
        self._growth_rates = {
            "vocal": 0.0, "dance": 0.0, "visual": 0.0, "mental": 0.0
        }
        
        # 评估建议
        self._recommendations: List[Dict[str, Any]] = []
        self._improvement_areas: List[str] = []
        self._strengths: List[str] = []
        
        # 历史数据
        self._previous_evaluations: List[Dict[str, Any]] = []
        self._progress_trend = "stable"  # stable, improving, declining
        
        # 性能监控
        self.optimizer = get_scene_optimizer()
        
        self.scene_logger.info("综合评估场景初始化完成")
    
    def _create_scene_ui_elements(self):
        """创建评估场景特定的UI元素"""
        try:
            with measure_block("evaluation_ui_creation"):
                # 场景标识元素
                self._create_evaluation_indicators()
                
                # 评估结果显示
                self._create_evaluation_displays()
                
                # 属性分析显示
                self._create_stats_analysis()
                
                # 建议和推荐显示
                self._create_recommendation_displays()
                
                # 历史和趋势显示
                self._create_history_displays()
                
                # 控制按钮
                self._create_control_buttons()
            
            self.scene_logger.info(f"评估场景UI元素创建完成，共{len(self.ui_elements)}个元素")
            
        except Exception as e:
            self.scene_logger.error(f"评估场景UI元素创建失败: {e}")
            raise
    
    def _create_evaluation_indicators(self):
        """创建评估标识元素"""
        # 评估场景标题
        self.ui_elements["evaluation_title"] = self.ui_factory.create_enhanced_label(
            "evaluation_title",
            confidence_threshold=0.9,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
        
        # 综合评级显示
        self.ui_elements["overall_grade"] = self.ui_factory.create_enhanced_label(
            "overall_grade",
            confidence_threshold=0.9,
            text_recognition_enabled=True,
            ocr_language="en"
        )
        
        # 评估日期
        self.ui_elements["evaluation_date"] = self.ui_factory.create_label(
            "evaluation_date",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="en"
        )
        
        # 评估类型
        self.ui_elements["evaluation_type"] = self.ui_factory.create_label(
            "evaluation_type",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
    
    def _create_evaluation_displays(self):
        """创建评估结果显示"""
        # 各类别评级
        for category in EvaluationCategory:
            if category != EvaluationCategory.OVERALL:
                grade_label = f"{category.value}_grade"
                self.ui_elements[grade_label] = self.ui_factory.create_enhanced_label(
                    grade_label,
                    confidence_threshold=0.8,
                    text_recognition_enabled=True,
                    ocr_language="en"
                )
        
        # 评估得分显示
        self.ui_elements["total_score"] = self.ui_factory.create_enhanced_label(
            "total_score",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="en"
        )
        
        # 评估详情
        self.ui_elements["evaluation_details"] = self.ui_factory.create_enhanced_label(
            "evaluation_details",
            confidence_threshold=0.7,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
        
        # 评估摘要
        self.ui_elements["evaluation_summary"] = self.ui_factory.create_enhanced_label(
            "evaluation_summary",
            confidence_threshold=0.7,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
    
    def _create_stats_analysis(self):
        """创建属性分析显示"""
        # 当前属性值
        for stat in ["vocal", "dance", "visual", "mental"]:
            self.ui_elements[f"current_{stat}"] = self.ui_factory.create_enhanced_label(
                f"current_{stat}",
                confidence_threshold=0.8,
                text_recognition_enabled=True,
                ocr_language="en"
            )
        
        # 属性排名
        for stat in ["vocal", "dance", "visual", "mental"]:
            self.ui_elements[f"{stat}_ranking"] = self.ui_factory.create_label(
                f"{stat}_ranking",
                confidence_threshold=0.8,
                text_recognition_enabled=True,
                ocr_language="en"
            )
        
        # 成长率显示
        for stat in ["vocal", "dance", "visual", "mental"]:
            self.ui_elements[f"{stat}_growth_rate"] = self.ui_factory.create_enhanced_label(
                f"{stat}_growth_rate",
                confidence_threshold=0.8,
                text_recognition_enabled=True,
                ocr_language="en"
            )
        
        # 属性雷达图（如果有）
        self.ui_elements["stats_radar_chart"] = self.ui_factory.create_label(
            "stats_radar_chart",
            confidence_threshold=0.7,
            text_recognition_enabled=False
        )
        
        # 平衡度指标
        self.ui_elements["balance_indicator"] = self.ui_factory.create_label(
            "balance_indicator",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="en"
        )
    
    def _create_recommendation_displays(self):
        """创建建议和推荐显示"""
        # 改进建议标题
        self.ui_elements["recommendations_title"] = self.ui_factory.create_label(
            "recommendations_title",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
        
        # 具体建议（最多5条）
        for i in range(1, 6):
            self.ui_elements[f"recommendation_{i}"] = self.ui_factory.create_enhanced_label(
                f"recommendation_{i}",
                confidence_threshold=0.7,
                text_recognition_enabled=True,
                ocr_language="ja"
            )
        
        # 优势分析
        self.ui_elements["strengths_analysis"] = self.ui_factory.create_enhanced_label(
            "strengths_analysis",
            confidence_threshold=0.7,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
        
        # 改进重点
        self.ui_elements["improvement_focus"] = self.ui_factory.create_enhanced_label(
            "improvement_focus",
            confidence_threshold=0.7,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
        
        # 训练建议
        self.ui_elements["training_suggestions"] = self.ui_factory.create_enhanced_label(
            "training_suggestions",
            confidence_threshold=0.7,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
    
    def _create_history_displays(self):
        """创建历史和趋势显示"""
        # 进步趋势
        self.ui_elements["progress_trend"] = self.ui_factory.create_enhanced_label(
            "progress_trend",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
        
        # 历史评级
        self.ui_elements["previous_grades"] = self.ui_factory.create_label(
            "previous_grades",
            confidence_threshold=0.7,
            text_recognition_enabled=True,
            ocr_language="en"
        )
        
        # 成长曲线（如果有）
        self.ui_elements["growth_chart"] = self.ui_factory.create_label(
            "growth_chart",
            confidence_threshold=0.7,
            text_recognition_enabled=False
        )
        
        # 里程碑显示
        self.ui_elements["milestones"] = self.ui_factory.create_label(
            "milestones",
            confidence_threshold=0.7,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
    
    def _create_control_buttons(self):
        """创建控制按钮"""
        # 确认评估按钮
        self.ui_elements["confirm_evaluation_button"] = self.ui_factory.create_enhanced_button(
            "confirm_evaluation_button",
            confidence_threshold=0.9,
            timeout=5.0,
            verify_click_result=True
        )
        
        # 查看详细报告按钮
        self.ui_elements["detailed_report_button"] = self.ui_factory.create_button(
            "detailed_report_button",
            confidence_threshold=0.8,
            timeout=3.0
        )
        
        # 保存评估按钮
        self.ui_elements["save_evaluation_button"] = self.ui_factory.create_button(
            "save_evaluation_button",
            confidence_threshold=0.8,
            timeout=3.0
        )
        
        # 继续育成按钮
        self.ui_elements["continue_training_button"] = self.ui_factory.create_enhanced_button(
            "continue_training_button",
            confidence_threshold=0.9,
            timeout=5.0,
            expected_scene_after_click="produce_main",
            verify_click_result=True
        )
        
        # 分享评估按钮
        self.ui_elements["share_evaluation_button"] = self.ui_factory.create_button(
            "share_evaluation_button",
            confidence_threshold=0.8,
            timeout=3.0
        )
    
    def _get_critical_elements(self) -> List[str]:
        """获取评估场景的关键UI元素"""
        return [
            "evaluation_title",
            "overall_grade",
            "total_score",
            "confirm_evaluation_button",
            "continue_training_button"
        ]
    
    def _verify_scene_specific_state(self) -> bool:
        """验证评估场景特定状态"""
        try:
            # 验证评估标题可见
            title_element = self.get_ui_element("evaluation_title")
            if not title_element or not title_element.is_visible():
                self.scene_logger.debug("评估标题不可见")
                return False
            
            # 验证综合评级可见
            grade_element = self.get_ui_element("overall_grade")
            if not grade_element or not grade_element.is_visible():
                self.scene_logger.debug("综合评级不可见")
                return False
            
            # 验证控制按钮可见
            confirm_button = self.get_ui_element("confirm_evaluation_button")
            continue_button = self.get_ui_element("continue_training_button")
            
            if not ((confirm_button and confirm_button.is_visible()) or 
                   (continue_button and continue_button.is_visible())):
                self.scene_logger.debug("控制按钮不可见")
                return False
            
            return True
            
        except Exception as e:
            self.scene_logger.error(f"评估场景状态验证异常: {e}")
            return False
    
    def process_evaluation(self) -> bool:
        """处理评估结果"""
        try:
            self.scene_logger.info("开始处理评估结果")
            
            with measure_block("evaluation_processing"):
                # 读取综合评级
                if not self._read_overall_grade():
                    return False
                
                # 读取各类别评级
                if not self._read_category_grades():
                    return False
                
                # 读取属性数据
                if not self._read_stats_data():
                    return False
                
                # 读取建议和推荐
                if not self._read_recommendations():
                    return False
                
                # 分析趋势
                self._analyze_progress_trend()
                
                self.scene_logger.info("评估结果处理完成")
                return True
            
        except Exception as e:
            self.scene_logger.error(f"处理评估结果失败: {e}")
            return False
    
    def _read_overall_grade(self) -> bool:
        """读取综合评级"""
        try:
            grade_element = self.get_ui_element("overall_grade")
            if grade_element:
                grade_text = grade_element.read_text()
                if grade_text:
                    grade_text = grade_text.strip().upper()
                    for grade in EvaluationGrade:
                        if grade.value in grade_text:
                            self._overall_grade = grade
                            break
            
            # 读取总分
            score_element = self.get_ui_element("total_score")
            if score_element:
                score_text = score_element.read_text()
                if score_text:
                    import re
                    score_match = re.search(r'(\d+)/(\d+)', score_text)
                    if score_match:
                        self._evaluation_scores["total"] = int(score_match.group(1))
                        self._evaluation_scores["max"] = int(score_match.group(2))
            
            return True
            
        except Exception as e:
            self.scene_logger.error(f"读取综合评级失败: {e}")
            return False
    
    def _read_category_grades(self) -> bool:
        """读取各类别评级"""
        try:
            for category in EvaluationCategory:
                if category == EvaluationCategory.OVERALL:
                    continue
                
                grade_element = self.get_ui_element(f"{category.value}_grade")
                if grade_element:
                    grade_text = grade_element.read_text()
                    if grade_text:
                        grade_text = grade_text.strip().upper()
                        for grade in EvaluationGrade:
                            if grade.value in grade_text:
                                self._category_grades[category] = grade
                                break
            
            return True
            
        except Exception as e:
            self.scene_logger.error(f"读取类别评级失败: {e}")
            return False
    
    def _read_stats_data(self) -> bool:
        """读取属性数据"""
        try:
            # 读取当前属性值
            for stat in ["vocal", "dance", "visual", "mental"]:
                stat_element = self.get_ui_element(f"current_{stat}")
                if stat_element:
                    stat_text = stat_element.read_text()
                    if stat_text:
                        import re
                        stat_match = re.search(r'\d+', stat_text)
                        if stat_match:
                            self._current_stats[stat] = int(stat_match.group())
                
                # 读取属性排名
                ranking_element = self.get_ui_element(f"{stat}_ranking")
                if ranking_element:
                    ranking_text = ranking_element.read_text()
                    if ranking_text:
                        import re
                        ranking_match = re.search(r'\d+', ranking_text)
                        if ranking_match:
                            self._stat_rankings[stat] = int(ranking_match.group())
                
                # 读取成长率
                growth_element = self.get_ui_element(f"{stat}_growth_rate")
                if growth_element:
                    growth_text = growth_element.read_text()
                    if growth_text:
                        import re
                        growth_match = re.search(r'([+-]?\d+\.?\d*)%', growth_text)
                        if growth_match:
                            self._growth_rates[stat] = float(growth_match.group(1)) / 100.0
            
            return True
            
        except Exception as e:
            self.scene_logger.error(f"读取属性数据失败: {e}")
            return False
    
    def _read_recommendations(self) -> bool:
        """读取建议和推荐"""
        try:
            self._recommendations.clear()
            self._strengths.clear()
            self._improvement_areas.clear()
            
            # 读取具体建议
            for i in range(1, 6):
                rec_element = self.get_ui_element(f"recommendation_{i}")
                if rec_element and rec_element.is_visible():
                    rec_text = rec_element.read_text()
                    if rec_text:
                        self._recommendations.append({
                            "type": RecommendationType.IMPROVEMENT,
                            "content": rec_text.strip(),
                            "priority": i
                        })
            
            # 读取优势分析
            strengths_element = self.get_ui_element("strengths_analysis")
            if strengths_element:
                strengths_text = strengths_element.read_text()
                if strengths_text:
                    # 简单解析优势（实际可能需要更复杂的NLP）
                    self._strengths = [s.strip() for s in strengths_text.split('、') if s.strip()]
            
            # 读取改进重点
            focus_element = self.get_ui_element("improvement_focus")
            if focus_element:
                focus_text = focus_element.read_text()
                if focus_text:
                    self._improvement_areas = [a.strip() for a in focus_text.split('、') if a.strip()]
            
            return True
            
        except Exception as e:
            self.scene_logger.error(f"读取建议推荐失败: {e}")
            return False
    
    def _analyze_progress_trend(self):
        """分析进步趋势"""
        try:
            trend_element = self.get_ui_element("progress_trend")
            if trend_element:
                trend_text = trend_element.read_text()
                if trend_text:
                    trend_text_lower = trend_text.lower()
                    if "上昇" in trend_text or "improving" in trend_text_lower or "上升" in trend_text:
                        self._progress_trend = "improving"
                    elif "下降" in trend_text or "declining" in trend_text_lower:
                        self._progress_trend = "declining"
                    else:
                        self._progress_trend = "stable"
            
            # 基于成长率分析
            avg_growth = sum(self._growth_rates.values()) / len(self._growth_rates)
            if avg_growth > 0.05:  # 5%以上
                self._progress_trend = "improving"
            elif avg_growth < -0.05:  # -5%以下
                self._progress_trend = "declining"
            
        except Exception as e:
            self.scene_logger.error(f"分析进步趋势失败: {e}")
    
    def confirm_evaluation(self) -> bool:
        """确认评估结果"""
        try:
            self.scene_logger.info("确认评估结果")
            
            # 记录UI元素使用
            self.optimizer.record_ui_element_usage(GameScene.EVALUATION, "confirm_evaluation_button")
            
            if not self.interact_with_element("confirm_evaluation_button", "click"):
                self.scene_logger.error("点击确认评估按钮失败")
                return False
            
            time.sleep(1.0)
            self.scene_logger.info("评估结果确认成功")
            return True
            
        except Exception as e:
            self.scene_logger.error(f"确认评估结果失败: {e}")
            return False
    
    def save_evaluation(self) -> bool:
        """保存评估结果"""
        try:
            self.scene_logger.info("保存评估结果")
            
            if not self.interact_with_element("save_evaluation_button", "click"):
                self.scene_logger.error("点击保存评估按钮失败")
                return False
            
            time.sleep(1.0)
            self.scene_logger.info("评估结果保存成功")
            return True
            
        except Exception as e:
            self.scene_logger.error(f"保存评估结果失败: {e}")
            return False
    
    def continue_training(self) -> bool:
        """继续育成"""
        try:
            self.scene_logger.info("继续育成")
            
            if not self.interact_with_element("continue_training_button", "click"):
                self.scene_logger.error("点击继续育成按钮失败")
                return False
            
            time.sleep(2.0)
            self.scene_logger.info("成功继续育成")
            return True
            
        except Exception as e:
            self.scene_logger.error(f"继续育成失败: {e}")
            return False
    
    def get_evaluation_summary(self) -> Dict[str, Any]:
        """获取评估摘要"""
        return {
            "overall_grade": self._overall_grade.value if self._overall_grade else "unknown",
            "category_grades": {
                cat.value: grade.value for cat, grade in self._category_grades.items()
            },
            "evaluation_scores": self._evaluation_scores.copy(),
            "current_stats": self._current_stats.copy(),
            "stat_rankings": self._stat_rankings.copy(),
            "growth_rates": self._growth_rates.copy(),
            "progress_trend": self._progress_trend,
            "recommendations_count": len(self._recommendations),
            "strengths_count": len(self._strengths),
            "improvement_areas_count": len(self._improvement_areas),
            "scene_ready": self.is_scene_ready()
        }
    
    def get_top_recommendations(self, count: int = 3) -> List[Dict[str, Any]]:
        """获取前N条建议"""
        return sorted(self._recommendations, key=lambda x: x.get("priority", 999))[:count]
    
    def get_stat_balance_score(self) -> float:
        """获取属性平衡度评分"""
        if not self._current_stats:
            return 0.0
        
        stats_values = [self._current_stats[stat] for stat in ["vocal", "dance", "visual", "mental"]]
        avg_stat = sum(stats_values) / len(stats_values)
        
        if avg_stat == 0:
            return 1.0
        
        # 计算标准差
        variance = sum((stat - avg_stat) ** 2 for stat in stats_values) / len(stats_values)
        std_dev = variance ** 0.5
        
        # 平衡度评分（标准差越小，平衡度越高）
        balance_score = max(0.0, 1.0 - (std_dev / avg_stat))
        return balance_score
