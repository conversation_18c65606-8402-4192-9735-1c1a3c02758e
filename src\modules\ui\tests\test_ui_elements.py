"""
UI元素单元测试
测试各种UI元素的功能
"""

import unittest
import time
from unittest.mock import patch

from .test_utils import UITestCase, create_test_button_config, create_test_input_config, create_test_label_config
from .test_data import TEST_UI_CONFIGS, MOCK_OCR_RESULTS
from ..elements.buttons import Button, EnhancedButton
from ..elements.inputs import InputField, EnhancedInputField
from ..elements.labels import Label, EnhancedLabel
from ..elements.ui_element_factory import UIElementFactory, UIElementType


class TestButton(UITestCase):
    """按钮测试类"""
    
    def test_button_creation(self):
        """测试按钮创建"""
        config = create_test_button_config("test_button")
        button = Button(config, self.perception, self.action)
        
        self.assertEqual(button.config.template_name, "test_button")
        self.assertTrue(button.config.enabled)
        self.assertEqual(button.config.confidence_threshold, 0.8)
    
    def test_button_click_success(self):
        """测试按钮点击成功"""
        config = create_test_button_config("test_button")
        button = Button(config, self.perception, self.action)
        
        # 设置点击成功
        self.action.set_click_result("test_button", True)
        
        result = button.click()
        self.assertTrue(result)
        self.assert_element_clicked("test_button")
    
    def test_button_click_failure(self):
        """测试按钮点击失败"""
        config = create_test_button_config("test_button")
        button = Button(config, self.perception, self.action)
        
        # 设置点击失败
        self.action.set_click_result("test_button", False)
        
        result = button.click()
        self.assertFalse(result)
    
    def test_button_visibility(self):
        """测试按钮可见性检查"""
        config = create_test_button_config("test_button")
        button = Button(config, self.perception, self.action)
        
        # 模板已在setUp中设置，应该可见
        self.assertTrue(button.is_visible())
        
        # 移除模板，应该不可见
        self.perception.mock_templates.clear()
        self.assertFalse(button.is_visible())
    
    def test_button_position(self):
        """测试按钮位置获取"""
        config = create_test_button_config("test_button")
        button = Button(config, self.perception, self.action)
        
        position = button.get_position()
        self.assertIsNotNone(position)
        self.assertEqual(position.x, 150)  # 100 + 100/2 (中心点)
        self.assertEqual(position.y, 225)  # 200 + 50/2 (中心点)
    
    def test_enhanced_button_double_click(self):
        """测试增强按钮双击功能"""
        config = create_test_button_config("test_button", double_click_enabled=True)
        button = EnhancedButton(config, self.perception, self.action)
        
        # 模拟快速连续点击
        button._last_click_time = time.time() - 0.3  # 0.3秒前点击过
        
        result = button.click()
        self.assertTrue(result)
        
        # 检查是否执行了双击
        history = self.action.get_action_history()
        click_count = len([a for a in history if a['action'] == 'click'])
        self.assertEqual(click_count, 2)  # 双击应该有两次点击记录
    
    def test_button_performance_stats(self):
        """测试按钮性能统计"""
        config = create_test_button_config("test_button")
        button = Button(config, self.perception, self.action)
        
        # 执行几次点击
        for _ in range(3):
            button.click()
            time.sleep(0.01)
        
        stats = button.get_performance_stats()
        self.assertEqual(stats['total_operations'], 3)
        self.assertGreater(stats['success_rate'], 0)
        self.assertGreater(stats['average_duration'], 0)


class TestInputField(UITestCase):
    """输入框测试类"""
    
    def test_input_field_creation(self):
        """测试输入框创建"""
        config = create_test_input_config("test_input")
        input_field = InputField(config, self.perception, self.action)
        
        self.assertEqual(input_field.config.template_name, "test_input")
        self.assertTrue(input_field.input_config.clear_before_input)
        self.assertEqual(input_field.input_config.input_method, "direct")
    
    def test_input_text_success(self):
        """测试文本输入成功"""
        config = create_test_input_config("test_input")
        input_field = InputField(config, self.perception, self.action)
        
        test_text = "测试文本输入"
        result = input_field.input_text(test_text)
        
        self.assertTrue(result)
        self.assert_text_typed(test_text)
        self.assertEqual(input_field.get_last_input(), test_text)
    
    def test_input_field_clear(self):
        """测试输入框清空"""
        config = create_test_input_config("test_input")
        input_field = InputField(config, self.perception, self.action)
        
        # 先输入文本
        input_field.input_text("测试文本")
        
        # 清空
        result = input_field.clear()
        self.assertTrue(result)
        
        # 检查是否执行了全选和删除操作
        key_presses = self.action.input_simulator.get_key_presses()
        self.assertIn(['ctrl', 'a'], key_presses)
        self.assertIn('delete', key_presses)
    
    def test_input_field_focus(self):
        """测试输入框焦点管理"""
        config = create_test_input_config("test_input")
        input_field = InputField(config, self.perception, self.action)
        
        # 初始状态应该没有焦点
        self.assertFalse(input_field.is_focused())
        
        # 点击获得焦点
        input_field.click()
        self.assertTrue(input_field.is_focused())
        
        # 失去焦点
        input_field.lose_focus()
        self.assertFalse(input_field.is_focused())
    
    def test_enhanced_input_field_validation(self):
        """测试增强输入框验证功能"""
        config = create_test_input_config("test_input")
        input_field = EnhancedInputField(config, self.perception, self.action)
        
        # 测试有效输入
        result = input_field.input_with_validation("test123", r"^[a-z0-9]+$")
        self.assertTrue(result)
        
        # 测试无效输入
        result = input_field.input_with_validation("Test@123", r"^[a-z0-9]+$")
        self.assertFalse(result)
    
    def test_input_field_append_text(self):
        """测试文本追加功能"""
        config = create_test_input_config("test_input")
        input_field = EnhancedInputField(config, self.perception, self.action)
        
        # 先输入基础文本
        input_field.input_text("Hello")
        
        # 追加文本
        result = input_field.append_text(" World")
        self.assertTrue(result)
        
        # 检查是否执行了移动到末尾的操作
        key_presses = self.action.input_simulator.get_key_presses()
        self.assertIn(['ctrl', 'end'], key_presses)


class TestLabel(UITestCase):
    """标签测试类"""
    
    def test_label_creation(self):
        """测试标签创建"""
        config = create_test_label_config("test_label")
        label = Label(config, self.perception, self.action)
        
        self.assertEqual(label.config.template_name, "test_label")
        self.assertTrue(label.label_config.text_recognition_enabled)
        self.assertEqual(label.label_config.ocr_language, "ja")
    
    def test_label_read_text(self):
        """测试标签文本读取"""
        config = create_test_label_config("test_label")
        label = Label(config, self.perception, self.action)
        
        # 设置OCR结果
        region = {'x': 450, 'y': 585, 'width': 100, 'height': 30}
        self.perception.set_mock_ocr_result(region, "测试文本")
        
        text = label.read_text()
        self.assertEqual(text, "测试文本")
    
    def test_label_text_verification(self):
        """测试标签文本验证"""
        config = create_test_label_config("test_label")
        label = Label(config, self.perception, self.action)
        
        # 设置OCR结果
        region = {'x': 450, 'y': 585, 'width': 100, 'height': 30}
        self.perception.set_mock_ocr_result(region, "ボーカル")
        
        # 精确匹配
        self.assertTrue(label.verify_text("ボーカル", exact_match=True))
        self.assertFalse(label.verify_text("ダンス", exact_match=True))
        
        # 模糊匹配
        self.assertTrue(label.verify_text("ボー", exact_match=False))
        self.assertTrue(label.verify_text("カル", exact_match=False))
    
    def test_label_wait_for_text(self):
        """测试等待文本出现"""
        config = create_test_label_config("test_label")
        label = Label(config, self.perception, self.action)
        
        # 设置OCR结果
        region = {'x': 450, 'y': 585, 'width': 100, 'height': 30}
        self.perception.set_mock_ocr_result(region, "期待文本")
        
        # 等待文本出现
        result = label.wait_for_text("期待文本", timeout=2.0)
        self.assertTrue(result)
        
        # 等待不存在的文本
        result = label.wait_for_text("不存在文本", timeout=0.5)
        self.assertFalse(result)
    
    def test_enhanced_label_monitoring(self):
        """测试增强标签监控功能"""
        config = create_test_label_config("test_label")
        label = EnhancedLabel(config, self.perception, self.action)
        
        # 添加文本变化回调
        callback_called = []
        def text_change_callback(old_text, new_text):
            callback_called.append((old_text, new_text))
        
        label.add_text_change_callback(text_change_callback)
        
        # 启动监控
        label.start_text_monitoring(interval=0.1)
        
        # 模拟文本变化
        region = {'x': 450, 'y': 585, 'width': 100, 'height': 30}
        self.perception.set_mock_ocr_result(region, "初始文本")
        time.sleep(0.2)
        
        self.perception.set_mock_ocr_result(region, "变化文本")
        time.sleep(0.2)
        
        # 停止监控
        label.stop_text_monitoring()
        
        # 检查回调是否被调用
        self.assertGreater(len(callback_called), 0)


class TestUIElementFactory(UITestCase):
    """UI元素工厂测试类"""
    
    def test_factory_creation(self):
        """测试工厂创建"""
        factory = UIElementFactory(self.perception, self.action)
        
        self.assertIsNotNone(factory)
        self.assertEqual(len(factory.get_supported_types()), 9)  # 9种元素类型
    
    def test_create_button(self):
        """测试创建按钮"""
        factory = UIElementFactory(self.perception, self.action)
        
        button = factory.create_button("test_button", confidence_threshold=0.9)
        
        self.assertIsInstance(button, Button)
        self.assertEqual(button.config.template_name, "test_button")
        self.assertEqual(button.config.confidence_threshold, 0.9)
    
    def test_create_from_dict(self):
        """测试从字典创建元素"""
        factory = UIElementFactory(self.perception, self.action)
        
        element_config = TEST_UI_CONFIGS["test_button"]
        element = factory.create_element_from_dict(element_config)
        
        self.assertIsInstance(element, Button)
        self.assertEqual(element.config.template_name, "test_button")
    
    def test_register_custom_element(self):
        """测试注册自定义元素类型"""
        factory = UIElementFactory(self.perception, self.action)
        
        # 注册自定义创建器
        def custom_creator(perception, action, **kwargs):
            config = create_test_button_config("custom_element")
            return Button(config, perception, action)
        
        factory.register_custom_creator("custom_element", custom_creator)
        
        # 使用自定义创建器创建元素
        element = factory.create_custom_element("custom_element")
        
        self.assertIsInstance(element, Button)
        self.assertEqual(element.config.template_name, "custom_element")
    
    def test_factory_stats(self):
        """测试工厂统计"""
        factory = UIElementFactory(self.perception, self.action)
        
        # 创建几个元素
        factory.create_button("button1")
        factory.create_input_field("input1")
        factory.create_label("label1")
        
        stats = factory.get_factory_stats()
        
        self.assertEqual(stats['supported_types'], 9)
        self.assertEqual(stats['custom_creators'], 0)
        self.assertIsInstance(stats['type_list'], list)


if __name__ == '__main__':
    unittest.main()
