"""
剧本选择管理器
负责管理游戏剧本和难度的选择、配置和验证
"""

import os
import yaml
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path

from ...core.data_structures import (
    ScenarioType, DifficultyLevel, ScenarioInfo, 
    DifficultyInfo, ScenarioSelection, InvalidConfiguration
)
from ...utils.logger import get_logger


class ScenarioManager:
    """剧本管理器"""
    
    def __init__(self, config_path: str = None):
        """
        初始化剧本管理器
        
        Args:
            config_path: 配置文件路径，默认使用项目配置目录
        """
        self.logger = get_logger("ScenarioManager")
        
        # 设置配置路径
        if config_path is None:
            project_root = Path(__file__).parent.parent.parent.parent
            config_path = project_root / "config" / "scenarios"
        
        self.config_path = Path(config_path)
        self.config_path.mkdir(parents=True, exist_ok=True)
        
        # 数据存储
        self._scenarios: Dict[ScenarioType, ScenarioInfo] = {}
        self._difficulties: Dict[DifficultyLevel, DifficultyInfo] = {}
        self._current_selection: Optional[ScenarioSelection] = None
        
        # 加载配置
        self._load_configurations()
        
        self.logger.info(f"剧本管理器初始化完成，加载了 {len(self._scenarios)} 个剧本和 {len(self._difficulties)} 个难度")
    
    def _load_configurations(self):
        """加载配置文件"""
        try:
            # 加载剧本配置
            scenarios_file = self.config_path / "scenarios.yaml"
            if scenarios_file.exists():
                self._load_scenarios(scenarios_file)
            else:
                self._create_default_scenarios_config(scenarios_file)
                self._load_scenarios(scenarios_file)
            
            # 加载难度配置
            difficulties_file = self.config_path / "difficulties.yaml"
            if difficulties_file.exists():
                self._load_difficulties(difficulties_file)
            else:
                self._create_default_difficulties_config(difficulties_file)
                self._load_difficulties(difficulties_file)
                
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            raise InvalidConfiguration(f"配置文件加载失败: {e}")
    
    def _load_scenarios(self, config_file: Path):
        """加载剧本配置"""
        with open(config_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        for scenario_data in config.get('scenarios', []):
            try:
                scenario_info = ScenarioInfo.from_dict(scenario_data)
                self._scenarios[scenario_info.scenario_type] = scenario_info
                self.logger.debug(f"加载剧本: {scenario_info.name}")
            except Exception as e:
                self.logger.error(f"加载剧本失败 {scenario_data.get('name', 'unknown')}: {e}")
    
    def _load_difficulties(self, config_file: Path):
        """加载难度配置"""
        with open(config_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        for difficulty_data in config.get('difficulties', []):
            try:
                difficulty_info = DifficultyInfo.from_dict(difficulty_data)
                self._difficulties[difficulty_info.difficulty_level] = difficulty_info
                self.logger.debug(f"加载难度: {difficulty_info.name}")
            except Exception as e:
                self.logger.error(f"加载难度失败 {difficulty_data.get('name', 'unknown')}: {e}")
    
    def _create_default_scenarios_config(self, config_file: Path):
        """创建默认剧本配置文件"""
        default_config = {
            'scenarios': [
                {
                    'scenario_type': 'nia',
                    'name': 'nia_scenario',
                    'display_name': 'NIA剧本',
                    'description': 'NIA主题的育成剧本，注重平衡发展',
                    'special_attributes': {
                        'reward_multiplier': 1.0,
                        'experience_multiplier': 1.0,
                        'focus_stats': ['vocal', 'dance', 'visual', 'mental'],
                        'special_events': ['nia_special_1', 'nia_special_2']
                    },
                    'unlock_conditions': {
                        'min_level': 1,
                        'required_achievements': []
                    },
                    'rewards': {
                        'base_exp': 1000,
                        'bonus_items': ['nia_badge', 'memory_piece']
                    },
                    'is_available': True,
                    'version': '1.0.0'
                },
                {
                    'scenario_type': 'hajime',
                    'name': 'hajime_scenario',
                    'display_name': 'HAJIME剧本',
                    'description': 'HAJIME主题的育成剧本，注重技能提升',
                    'special_attributes': {
                        'reward_multiplier': 1.1,
                        'experience_multiplier': 1.05,
                        'focus_stats': ['vocal', 'dance'],
                        'special_events': ['hajime_special_1', 'hajime_special_2']
                    },
                    'unlock_conditions': {
                        'min_level': 5,
                        'required_achievements': ['complete_nia_scenario']
                    },
                    'rewards': {
                        'base_exp': 1200,
                        'bonus_items': ['hajime_badge', 'skill_book']
                    },
                    'is_available': True,
                    'version': '1.0.0'
                }
            ]
        }
        
        with open(config_file, 'w', encoding='utf-8') as f:
            yaml.dump(default_config, f, default_flow_style=False, allow_unicode=True)
        
        self.logger.info(f"创建默认剧本配置文件: {config_file}")
    
    def _create_default_difficulties_config(self, config_file: Path):
        """创建默认难度配置文件"""
        default_config = {
            'difficulties': [
                {
                    'difficulty_level': 'normal',
                    'name': 'normal',
                    'display_name': '普通',
                    'description': '标准难度，适合新手玩家',
                    'reward_multiplier': 1.0,
                    'experience_multiplier': 1.0,
                    'unlock_conditions': {
                        'min_level': 1
                    },
                    'special_effects': {
                        'stamina_cost_reduction': 0.0,
                        'failure_penalty_reduction': 0.1
                    },
                    'is_available': True,
                    'order': 1,
                    'color_theme': 'green'
                },
                {
                    'difficulty_level': 'master',
                    'name': 'master',
                    'display_name': '大师',
                    'description': '高难度，提供更多奖励和挑战',
                    'reward_multiplier': 1.5,
                    'experience_multiplier': 1.3,
                    'unlock_conditions': {
                        'min_level': 10,
                        'required_achievements': ['complete_normal_scenario']
                    },
                    'special_effects': {
                        'stamina_cost_increase': 0.2,
                        'failure_penalty_increase': 0.3,
                        'bonus_event_chance': 0.15
                    },
                    'is_available': True,
                    'order': 2,
                    'color_theme': 'red'
                }
            ]
        }
        
        with open(config_file, 'w', encoding='utf-8') as f:
            yaml.dump(default_config, f, default_flow_style=False, allow_unicode=True)
        
        self.logger.info(f"创建默认难度配置文件: {config_file}")
    
    def get_available_scenarios(self) -> List[ScenarioInfo]:
        """获取可用的剧本列表"""
        return [scenario for scenario in self._scenarios.values() if scenario.is_available]
    
    def get_available_difficulties(self) -> List[DifficultyInfo]:
        """获取可用的难度列表"""
        difficulties = [difficulty for difficulty in self._difficulties.values() if difficulty.is_available]
        return sorted(difficulties, key=lambda x: x.order)
    
    def get_scenario(self, scenario_type: ScenarioType) -> Optional[ScenarioInfo]:
        """获取指定类型的剧本信息"""
        return self._scenarios.get(scenario_type)
    
    def get_difficulty(self, difficulty_level: DifficultyLevel) -> Optional[DifficultyInfo]:
        """获取指定等级的难度信息"""
        return self._difficulties.get(difficulty_level)
    
    def validate_selection(self, scenario_type: ScenarioType, difficulty_level: DifficultyLevel) -> Tuple[bool, str]:
        """
        验证剧本和难度选择是否有效
        
        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        # 检查剧本是否存在和可用
        scenario = self.get_scenario(scenario_type)
        if not scenario:
            return False, f"剧本类型 {scenario_type.value} 不存在"
        if not scenario.is_available:
            return False, f"剧本 {scenario.name} 不可用"
        
        # 检查难度是否存在和可用
        difficulty = self.get_difficulty(difficulty_level)
        if not difficulty:
            return False, f"难度等级 {difficulty_level.value} 不存在"
        if not difficulty.is_available:
            return False, f"难度 {difficulty.name} 不可用"
        
        # 检查解锁条件（这里简化处理，实际应该检查用户进度）
        scenario_unlock = scenario.unlock_conditions
        difficulty_unlock = difficulty.unlock_conditions
        
        # 可以在这里添加更复杂的解锁条件检查逻辑
        
        return True, "选择有效"
    
    def create_selection(self, scenario_type: ScenarioType, difficulty_level: DifficultyLevel, 
                        custom_settings: Dict[str, Any] = None) -> ScenarioSelection:
        """
        创建剧本选择
        
        Args:
            scenario_type: 剧本类型
            difficulty_level: 难度等级
            custom_settings: 自定义设置
            
        Returns:
            ScenarioSelection: 剧本选择对象
            
        Raises:
            ValueError: 选择无效时抛出
        """
        # 验证选择
        is_valid, error_msg = self.validate_selection(scenario_type, difficulty_level)
        if not is_valid:
            raise ValueError(error_msg)
        
        # 获取剧本和难度信息
        scenario_info = self.get_scenario(scenario_type)
        difficulty_info = self.get_difficulty(difficulty_level)
        
        # 创建选择对象
        selection = ScenarioSelection(
            scenario_info=scenario_info,
            difficulty_info=difficulty_info,
            custom_settings=custom_settings or {}
        )
        
        # 保存当前选择
        self._current_selection = selection
        
        self.logger.info(f"创建剧本选择: {scenario_info.name} - {difficulty_info.name}")
        return selection
    
    def get_current_selection(self) -> Optional[ScenarioSelection]:
        """获取当前选择"""
        return self._current_selection
    
    def clear_selection(self):
        """清除当前选择"""
        self._current_selection = None
        self.logger.info("清除当前剧本选择")
    
    def add_scenario(self, scenario_info: ScenarioInfo):
        """添加新剧本"""
        self._scenarios[scenario_info.scenario_type] = scenario_info
        self.logger.info(f"添加剧本: {scenario_info.name}")
    
    def add_difficulty(self, difficulty_info: DifficultyInfo):
        """添加新难度"""
        self._difficulties[difficulty_info.difficulty_level] = difficulty_info
        self.logger.info(f"添加难度: {difficulty_info.name}")
    
    def get_selection_summary(self) -> Dict[str, Any]:
        """获取选择摘要信息"""
        if not self._current_selection:
            return {"has_selection": False}
        
        selection = self._current_selection
        return {
            "has_selection": True,
            "scenario": {
                "type": selection.scenario_info.scenario_type.value,
                "name": selection.scenario_info.display_name,
                "description": selection.scenario_info.description
            },
            "difficulty": {
                "level": selection.difficulty_info.difficulty_level.value,
                "name": selection.difficulty_info.display_name,
                "description": selection.difficulty_info.description
            },
            "multipliers": {
                "reward": selection.get_total_reward_multiplier(),
                "experience": selection.get_total_experience_multiplier()
            },
            "selected_time": selection.selected_time,
            "custom_settings": selection.custom_settings
        }
