# API参考文档

## 概述

本文档提供了Gakumasu-Bot UI模块的完整API参考，包括所有公共类、方法和接口的详细说明。

## 目录

- [核心基类](#核心基类)
- [UI元素类](#ui元素类)
- [游戏场景类](#游戏场景类)
- [管理器类](#管理器类)
- [配置类](#配置类)
- [工具类](#工具类)
- [异常类](#异常类)

## 核心基类

### BaseUIElement

基础UI元素抽象类，所有UI元素的父类。

```python
class BaseUIElement:
    """基础UI元素抽象类"""
    
    def __init__(self, element_id: str, config: UIElementConfig = None, **kwargs):
        """
        初始化UI元素
        
        Args:
            element_id: 元素唯一标识符
            config: 元素配置对象
            **kwargs: 其他配置参数
        """
    
    def is_visible(self) -> bool:
        """
        检查元素是否可见
        
        Returns:
            bool: 元素是否可见
        """
    
    def get_bounds(self) -> Tuple[int, int, int, int]:
        """
        获取元素边界
        
        Returns:
            Tuple[int, int, int, int]: (x, y, width, height)
        """
    
    def wait_for_element(self, timeout: float = None) -> bool:
        """
        等待元素出现
        
        Args:
            timeout: 超时时间（秒），None使用配置默认值
            
        Returns:
            bool: 是否在超时前找到元素
        """
    
    @abstractmethod
    def interact(self) -> bool:
        """
        与元素交互（抽象方法）
        
        Returns:
            bool: 交互是否成功
        """
```

### BaseGameScene

基础游戏场景抽象类，所有场景的父类。

```python
class BaseGameScene:
    """基础游戏场景抽象类"""
    
    def __init__(self, scene_type: GameScene, config: SceneConfig = None):
        """
        初始化游戏场景
        
        Args:
            scene_type: 场景类型枚举
            config: 场景配置对象
        """
    
    def is_scene_ready(self) -> bool:
        """
        检查场景是否就绪
        
        Returns:
            bool: 场景是否就绪
        """
    
    def activate(self) -> bool:
        """
        激活场景
        
        Returns:
            bool: 激活是否成功
        """
    
    def deactivate(self) -> None:
        """停用场景"""
    
    def get_ui_elements(self) -> Dict[str, BaseUIElement]:
        """
        获取场景中的UI元素
        
        Returns:
            Dict[str, BaseUIElement]: 元素ID到元素对象的映射
        """
    
    @abstractmethod
    def initialize_ui_elements(self) -> None:
        """初始化UI元素（抽象方法）"""
```

## UI元素类

### Button

按钮UI元素类。

```python
class Button(BaseUIElement):
    """按钮UI元素"""
    
    def __init__(self, element_id: str, confidence_threshold: float = 0.8, 
                 timeout: float = 5.0, retry_count: int = 3, 
                 verify_click_result: bool = False, **kwargs):
        """
        初始化按钮
        
        Args:
            element_id: 按钮ID
            confidence_threshold: 识别置信度阈值
            timeout: 操作超时时间
            retry_count: 重试次数
            verify_click_result: 是否验证点击结果
        """
    
    def click(self) -> bool:
        """
        点击按钮
        
        Returns:
            bool: 点击是否成功
        """
    
    def double_click(self) -> bool:
        """
        双击按钮
        
        Returns:
            bool: 双击是否成功
        """
    
    def right_click(self) -> bool:
        """
        右键点击按钮
        
        Returns:
            bool: 右键点击是否成功
        """
    
    def is_enabled(self) -> bool:
        """
        检查按钮是否启用
        
        Returns:
            bool: 按钮是否启用
        """
```

### InputField

输入框UI元素类。

```python
class InputField(BaseUIElement):
    """输入框UI元素"""
    
    def __init__(self, element_id: str, timeout: float = 5.0, 
                 verify_input_result: bool = True, **kwargs):
        """
        初始化输入框
        
        Args:
            element_id: 输入框ID
            timeout: 操作超时时间
            verify_input_result: 是否验证输入结果
        """
    
    def input_text(self, text: str, clear_first: bool = False) -> bool:
        """
        输入文本
        
        Args:
            text: 要输入的文本
            clear_first: 是否先清空现有内容
            
        Returns:
            bool: 输入是否成功
        """
    
    def get_text(self) -> str:
        """
        获取输入框文本
        
        Returns:
            str: 输入框当前文本
        """
    
    def clear(self) -> bool:
        """
        清空输入框
        
        Returns:
            bool: 清空是否成功
        """
    
    def is_editable(self) -> bool:
        """
        检查输入框是否可编辑
        
        Returns:
            bool: 输入框是否可编辑
        """
```

### Label

标签UI元素类。

```python
class Label(BaseUIElement):
    """标签UI元素"""
    
    def __init__(self, element_id: str, confidence_threshold: float = 0.7, 
                 text_recognition_enabled: bool = True, **kwargs):
        """
        初始化标签
        
        Args:
            element_id: 标签ID
            confidence_threshold: 识别置信度阈值
            text_recognition_enabled: 是否启用文本识别
        """
    
    def get_text(self) -> str:
        """
        获取标签文本
        
        Returns:
            str: 标签文本内容
        """
    
    def wait_for_text(self, expected_text: str, timeout: float = None) -> bool:
        """
        等待特定文本出现
        
        Args:
            expected_text: 期望的文本
            timeout: 超时时间
            
        Returns:
            bool: 是否在超时前出现期望文本
        """
    
    def text_contains(self, substring: str) -> bool:
        """
        检查文本是否包含子串
        
        Args:
            substring: 要检查的子串
            
        Returns:
            bool: 是否包含子串
        """
    
    def text_matches_pattern(self, pattern: str) -> bool:
        """
        检查文本是否匹配正则表达式
        
        Args:
            pattern: 正则表达式模式
            
        Returns:
            bool: 是否匹配模式
        """
```

## 游戏场景类

### TrainingScene

训练场景类。

```python
class TrainingScene(BaseGameScene):
    """训练场景"""
    
    def __init__(self, config: SceneConfig = None):
        """初始化训练场景"""
    
    def start_vocal_lesson(self) -> bool:
        """
        开始声乐课程
        
        Returns:
            bool: 是否成功开始
        """
    
    def start_dance_lesson(self) -> bool:
        """
        开始舞蹈课程
        
        Returns:
            bool: 是否成功开始
        """
    
    def start_visual_lesson(self) -> bool:
        """
        开始视觉课程
        
        Returns:
            bool: 是否成功开始
        """
    
    def start_mental_lesson(self) -> bool:
        """
        开始心理课程
        
        Returns:
            bool: 是否成功开始
        """
    
    def rest(self) -> bool:
        """
        休息
        
        Returns:
            bool: 是否成功休息
        """
    
    def go_outing(self) -> bool:
        """
        外出
        
        Returns:
            bool: 是否成功外出
        """
    
    def get_training_status(self) -> Dict[str, Any]:
        """
        获取训练状态
        
        Returns:
            Dict[str, Any]: 训练状态信息
        """
    
    def check_stamina(self) -> Dict[str, int]:
        """
        检查体力值
        
        Returns:
            Dict[str, int]: 体力信息 {"current": 当前值, "max": 最大值}
        """
```

### CompetitionScene

比赛场景类。

```python
class CompetitionScene(BaseGameScene):
    """比赛场景"""
    
    def __init__(self, config: SceneConfig = None):
        """初始化比赛场景"""
    
    def start_competition(self) -> bool:
        """
        开始比赛
        
        Returns:
            bool: 是否成功开始比赛
        """
    
    def get_competition_results(self) -> Dict[str, Any]:
        """
        获取比赛结果
        
        Returns:
            Dict[str, Any]: 比赛结果信息
        """
    
    def check_competition_status(self) -> Dict[str, str]:
        """
        检查比赛状态
        
        Returns:
            Dict[str, str]: 比赛状态信息
        """
```

## 管理器类

### SceneManager

场景管理器类。

```python
class SceneManager:
    """场景管理器"""
    
    def __init__(self):
        """初始化场景管理器"""
    
    def register_scene(self, scene_type: GameScene, scene: BaseGameScene) -> None:
        """
        注册场景
        
        Args:
            scene_type: 场景类型
            scene: 场景实例
        """
    
    def switch_to_scene(self, scene_type: GameScene, timeout: float = 30.0) -> bool:
        """
        切换到指定场景
        
        Args:
            scene_type: 目标场景类型
            timeout: 切换超时时间
            
        Returns:
            bool: 切换是否成功
        """
    
    def detect_current_scene(self) -> GameScene:
        """
        检测当前场景
        
        Returns:
            GameScene: 当前场景类型
        """
    
    def get_current_scene(self) -> Optional[BaseGameScene]:
        """
        获取当前场景实例
        
        Returns:
            Optional[BaseGameScene]: 当前场景实例
        """
    
    def get_scene_info(self) -> Dict[str, Any]:
        """
        获取场景信息
        
        Returns:
            Dict[str, Any]: 场景信息
        """
    
    def get_transition_history(self) -> List[Dict[str, Any]]:
        """
        获取场景转换历史
        
        Returns:
            List[Dict[str, Any]]: 转换历史记录
        """
```

### UIElementFactory

UI元素工厂类。

```python
class UIElementFactory:
    """UI元素工厂"""
    
    @staticmethod
    def create_element(element_type: str, element_id: str, **kwargs) -> BaseUIElement:
        """
        创建UI元素
        
        Args:
            element_type: 元素类型
            element_id: 元素ID
            **kwargs: 其他参数
            
        Returns:
            BaseUIElement: 创建的UI元素实例
            
        Raises:
            ValueError: 不支持的元素类型
        """
    
    @staticmethod
    def register_element_type(element_type: str, element_class: type) -> None:
        """
        注册元素类型
        
        Args:
            element_type: 元素类型名称
            element_class: 元素类
        """
    
    @staticmethod
    def get_supported_types() -> List[str]:
        """
        获取支持的元素类型
        
        Returns:
            List[str]: 支持的元素类型列表
        """
```

## 配置类

### UIElementConfig

UI元素配置类。

```python
@dataclass
class UIElementConfig:
    """UI元素配置"""
    
    confidence_threshold: float = 0.8
    timeout: float = 5.0
    retry_count: int = 3
    cache_duration: float = 1.0
    debug_mode: bool = False
    screenshot_on_failure: bool = True
    
    def validate(self) -> bool:
        """
        验证配置有效性
        
        Returns:
            bool: 配置是否有效
        """
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典
        
        Returns:
            Dict[str, Any]: 配置字典
        """
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UIElementConfig':
        """
        从字典创建配置
        
        Args:
            data: 配置字典
            
        Returns:
            UIElementConfig: 配置实例
        """
```

### SceneConfig

场景配置类。

```python
@dataclass
class SceneConfig:
    """场景配置"""
    
    scene_id: str
    timeout: float = 10.0
    retry_count: int = 3
    auto_detect: bool = True
    transition_delay: float = 1.0
    
    def validate(self) -> bool:
        """
        验证配置有效性
        
        Returns:
            bool: 配置是否有效
        """
```

## 工具类

### PerformanceMonitor

性能监控器类。

```python
class PerformanceMonitor:
    """性能监控器"""
    
    @staticmethod
    def measure_time(func: Callable) -> Callable:
        """
        测量函数执行时间的装饰器
        
        Args:
            func: 要测量的函数
            
        Returns:
            Callable: 装饰后的函数
        """
    
    @staticmethod
    def get_memory_usage() -> Dict[str, int]:
        """
        获取内存使用情况
        
        Returns:
            Dict[str, int]: 内存使用信息
        """
    
    @staticmethod
    def get_performance_stats() -> Dict[str, Any]:
        """
        获取性能统计
        
        Returns:
            Dict[str, Any]: 性能统计信息
        """
```

## 异常类

### UIElementError

UI元素相关异常。

```python
class UIElementError(Exception):
    """UI元素异常基类"""
    pass

class ElementNotFoundError(UIElementError):
    """元素未找到异常"""
    pass

class ElementNotVisibleError(UIElementError):
    """元素不可见异常"""
    pass

class InteractionFailedError(UIElementError):
    """交互失败异常"""
    pass
```

### SceneError

场景相关异常。

```python
class SceneError(Exception):
    """场景异常基类"""
    pass

class SceneNotReadyError(SceneError):
    """场景未就绪异常"""
    pass

class SceneTransitionError(SceneError):
    """场景转换异常"""
    pass
```

## 使用示例

### 基本UI操作

```python
from src.modules.ui.elements.button import Button
from src.modules.ui.elements.input_field import InputField

# 创建按钮并点击
button = Button("start_button", confidence_threshold=0.9)
if button.is_visible():
    success = button.click()
    print(f"点击结果: {success}")

# 创建输入框并输入文本
input_field = InputField("name_input")
success = input_field.input_text("测试文本", clear_first=True)
print(f"输入结果: {success}")
```

### 场景管理

```python
from src.modules.ui.scenes.scene_manager import SceneManager
from src.modules.ui.scenes.training_scene import TrainingScene
from src.core.data_structures import GameScene

# 创建场景管理器
scene_manager = SceneManager()

# 注册训练场景
training_scene = TrainingScene()
scene_manager.register_scene(GameScene.TRAINING, training_scene)

# 切换到训练场景
success = scene_manager.switch_to_scene(GameScene.TRAINING)
if success:
    # 开始声乐课程
    training_scene.start_vocal_lesson()
```

### 配置使用

```python
from src.modules.ui.config.ui_element_config import UIElementConfig
from src.modules.ui.elements.button import Button

# 创建自定义配置
config = UIElementConfig(
    confidence_threshold=0.95,
    timeout=10.0,
    retry_count=5,
    debug_mode=True
)

# 使用配置创建按钮
button = Button("custom_button", config)
```
