"""
感知模块主类
整合屏幕捕获、场景识别、模板匹配等功能
"""

import time
import cv2
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import re
import os

from ...utils.logger import get_logger
from ...core.data_structures import (
    GameState, GameScene, Card, CardType, CardRarity,
    GameUIElementNotFound
)
from .screen_capture import ScreenCapture, ScreenCaptureError
from .scene_recognizer import SceneRecognizer
from .template_matcher import TemplateMatcher, MatchResult

# OCR相关导入
try:
    import easyocr
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False


class PerceptionModule:
    """感知模块主类"""
    
    def __init__(self,
                 game_window_title: str = "gakumas",
                 templates_dir: str = "assets/templates",
                 language: str = "ja",
                 enable_ocr: bool = True):
        """
        初始化感知模块

        Args:
            game_window_title: 游戏窗口标题
            templates_dir: 模板图片目录
            language: 语言设置 ('ja' 或 'cn')
            enable_ocr: 是否启用OCR功能
        """
        self.logger = get_logger("PerceptionModule")
        self.language = language
        self.enable_ocr = enable_ocr and OCR_AVAILABLE

        # 初始化子模块
        self.screen_capture = ScreenCapture(game_window_title)
        self.scene_recognizer = SceneRecognizer(templates_dir)
        self.template_matcher = TemplateMatcher(templates_dir)

        # 初始化OCR
        self.ocr_reader = None
        if self.enable_ocr:
            try:
                # 根据语言设置选择OCR语言
                ocr_languages = ['ja', 'en'] if language == 'ja' else ['ch_sim', 'en']
                self.ocr_reader = easyocr.Reader(ocr_languages, gpu=False, verbose=False)
                self.logger.info(f"OCR初始化成功，支持语言: {ocr_languages}")
            except Exception as e:
                self.logger.warning(f"OCR初始化失败: {e}")
                self.enable_ocr = False
        else:
            if not OCR_AVAILABLE:
                self.logger.warning("EasyOCR未安装，OCR功能不可用")

        # 状态缓存
        self._last_screenshot = None
        self._last_game_state = None
        self._last_update_time = 0

        # 配置参数
        self.screenshot_cache_duration = 0.5  # 截图缓存时间（秒）
        self.debug_mode = False
        self.debug_screenshots_dir = "logs/debug_screenshots"

        # OCR配置参数
        self.ocr_confidence_threshold = 0.5  # OCR置信度阈值
        self.ocr_text_regions = self._init_ocr_regions()  # OCR文本区域定义

        self.logger.info(f"感知模块初始化完成，语言: {language}, OCR: {self.enable_ocr}")
    
    def get_game_state(self, force_refresh: bool = False) -> GameState:
        """
        获取当前游戏状态
        
        Args:
            force_refresh: 是否强制刷新状态
            
        Returns:
            当前游戏状态
            
        Raises:
            ScreenCaptureError: 屏幕捕获失败
            GameUIElementNotFound: UI元素未找到
        """
        current_time = time.time()
        
        # 检查是否需要更新状态
        if (not force_refresh and 
            self._last_game_state and 
            current_time - self._last_update_time < self.screenshot_cache_duration):
            return self._last_game_state
        
        try:
            # 捕获屏幕
            screenshot = self.screen_capture.capture_screen()
            if screenshot is None:
                raise ScreenCaptureError("无法捕获游戏屏幕")
            
            self._last_screenshot = screenshot
            
            # 保存调试截图
            if self.debug_mode:
                self._save_debug_screenshot(screenshot)
            
            # 识别场景
            current_scene = self.scene_recognizer.recognize_scene(screenshot)
            
            # 创建基础游戏状态
            game_state = GameState(
                current_scene=current_scene,
                current_language=self.language,
                timestamp=datetime.now()
            )
            
            # 根据场景解析具体状态
            if current_scene == GameScene.PRODUCE_MAIN:
                self._parse_produce_main_state(screenshot, game_state)
            elif current_scene == GameScene.PRODUCE_BATTLE:
                self._parse_produce_battle_state(screenshot, game_state)
            elif current_scene == GameScene.PRODUCE_EXAM:
                self._parse_produce_exam_state(screenshot, game_state)
            elif current_scene == GameScene.MAIN_MENU:
                self._parse_main_menu_state(screenshot, game_state)
            
            # 获取窗口信息
            window_rect = self.screen_capture.get_window_rect()
            if window_rect:
                game_state.screen_resolution = (window_rect["width"], window_rect["height"])
            
            # 缓存状态
            self._last_game_state = game_state
            self._last_update_time = current_time
            
            self.logger.debug(f"游戏状态更新: {current_scene.value}")
            return game_state
            
        except Exception as e:
            self.logger.error(f"获取游戏状态失败: {e}")
            raise
    
    def _parse_produce_main_state(self, screenshot: np.ndarray, game_state: GameState):
        """
        解析育成主界面状态
        
        Args:
            screenshot: 游戏截图
            game_state: 游戏状态对象
        """
        try:
            # 识别体力值
            stamina = self._extract_stamina_value(screenshot)
            if stamina is not None:
                game_state.stamina = stamina
            
            # 识别元气值
            vigor = self._extract_vigor_value(screenshot)
            if vigor is not None:
                game_state.vigor = vigor
            
            # 识别当前周数
            week = self._extract_week_number(screenshot)
            if week is not None:
                game_state.current_week = week
            
            # 识别可用的行动选项
            ui_elements = self.scene_recognizer.detect_ui_elements(
                screenshot, ["lesson_buttons", "rest_button", "outing_button"]
            )
            game_state.ui_elements = {name: self._match_result_to_dict(match) 
                                    for name, match in ui_elements.items()}
            
        except Exception as e:
            self.logger.warning(f"解析育成主界面状态失败: {e}")
    
    def _parse_produce_battle_state(self, screenshot: np.ndarray, game_state: GameState):
        """
        解析育成战斗界面状态
        
        Args:
            screenshot: 游戏截图
            game_state: 游戏状态对象
        """
        try:
            # 识别当前分数
            score = self._extract_score_value(screenshot)
            if score is not None:
                game_state.score = score
            
            # 识别手牌（这里简化处理，实际需要OCR识别卡牌名称）
            hand_cards = self._extract_hand_cards(screenshot)
            game_state.hand = hand_cards
            
            # 识别牌组和弃牌堆数量
            deck_size = self._extract_deck_size(screenshot)
            if deck_size is not None:
                game_state.deck_size = deck_size
            
            discard_size = self._extract_discard_size(screenshot)
            if discard_size is not None:
                game_state.discard_size = discard_size
            
        except Exception as e:
            self.logger.warning(f"解析育成战斗界面状态失败: {e}")
    
    def _parse_produce_exam_state(self, screenshot: np.ndarray, game_state: GameState):
        """
        解析考试界面状态
        
        Args:
            screenshot: 游戏截图
            game_state: 游戏状态对象
        """
        try:
            # 考试界面的状态解析
            score = self._extract_score_value(screenshot)
            if score is not None:
                game_state.score = score
            
            # 识别考试相关UI元素
            ui_elements = self.scene_recognizer.detect_ui_elements(
                screenshot, ["exam_cards", "exam_score", "turn_indicator"]
            )
            game_state.ui_elements = {name: self._match_result_to_dict(match) 
                                    for name, match in ui_elements.items()}
            
        except Exception as e:
            self.logger.warning(f"解析考试界面状态失败: {e}")
    
    def _parse_main_menu_state(self, screenshot: np.ndarray, game_state: GameState):
        """
        解析主菜单状态
        
        Args:
            screenshot: 游戏截图
            game_state: 游戏状态对象
        """
        try:
            # 识别主菜单的可用选项
            ui_elements = self.scene_recognizer.detect_ui_elements(
                screenshot, ["produce_button", "part_time_job_button", "daily_tasks_button"]
            )
            game_state.ui_elements = {name: self._match_result_to_dict(match) 
                                    for name, match in ui_elements.items()}
            
        except Exception as e:
            self.logger.warning(f"解析主菜单状态失败: {e}")
    
    def _extract_stamina_value(self, screenshot: np.ndarray) -> Optional[int]:
        """
        从截图中提取体力值

        Args:
            screenshot: 游戏截图

        Returns:
            体力值，如果提取失败返回None
        """
        try:
            if self.enable_ocr and "stamina_text" in self.ocr_text_regions:
                # 使用OCR提取体力值
                region = self.ocr_text_regions["stamina_text"]
                text = self.extract_text_from_region(screenshot, region)

                # 从文本中提取数字
                numbers = re.findall(r'\d+', text)
                if numbers:
                    return int(numbers[0])

            # 如果OCR失败，尝试模板匹配方法
            # 这里可以添加基于模板匹配的体力识别逻辑

            # 暂时返回默认值
            return 100

        except Exception as e:
            self.logger.warning(f"提取体力值失败: {e}")
            return None
    
    def _extract_vigor_value(self, screenshot: np.ndarray) -> Optional[int]:
        """
        从截图中提取元气值

        Args:
            screenshot: 游戏截图

        Returns:
            元气值，如果提取失败返回None
        """
        try:
            if self.enable_ocr and "vigor_text" in self.ocr_text_regions:
                # 使用OCR提取元气值
                region = self.ocr_text_regions["vigor_text"]
                text = self.extract_text_from_region(screenshot, region)

                # 从文本中提取数字
                numbers = re.findall(r'\d+', text)
                if numbers:
                    return int(numbers[0])

            # 如果OCR失败，尝试模板匹配方法
            # 这里可以添加基于模板匹配的元气识别逻辑

            # 暂时返回默认值
            return 80

        except Exception as e:
            self.logger.warning(f"提取元气值失败: {e}")
            return None
    
    def _extract_week_number(self, screenshot: np.ndarray) -> Optional[int]:
        """
        从截图中提取当前周数

        Args:
            screenshot: 游戏截图

        Returns:
            周数，如果提取失败返回None
        """
        try:
            if self.enable_ocr and "week_text" in self.ocr_text_regions:
                # 使用OCR提取周数
                region = self.ocr_text_regions["week_text"]
                text = self.extract_text_from_region(screenshot, region)

                # 从文本中提取数字（寻找周数模式）
                week_match = re.search(r'(\d+)週|Week\s*(\d+)|第(\d+)周', text)
                if week_match:
                    # 获取匹配的数字
                    week_num = week_match.group(1) or week_match.group(2) or week_match.group(3)
                    return int(week_num)

                # 如果没有找到周数模式，尝试提取纯数字
                numbers = re.findall(r'\d+', text)
                if numbers:
                    return int(numbers[0])

            # 暂时返回默认值
            return 1

        except Exception as e:
            self.logger.warning(f"提取周数失败: {e}")
            return None
    
    def _extract_score_value(self, screenshot: np.ndarray) -> Optional[int]:
        """
        从截图中提取分数值

        Args:
            screenshot: 游戏截图

        Returns:
            分数值，如果提取失败返回None
        """
        try:
            if self.enable_ocr and "score_text" in self.ocr_text_regions:
                # 使用OCR提取分数值
                region = self.ocr_text_regions["score_text"]
                text = self.extract_text_from_region(screenshot, region)

                # 从文本中提取数字（分数通常是较大的数字）
                numbers = re.findall(r'\d+', text)
                if numbers:
                    # 选择最大的数字作为分数
                    scores = [int(num) for num in numbers]
                    return max(scores)

            # 暂时返回默认值
            return 5000

        except Exception as e:
            self.logger.warning(f"提取分数值失败: {e}")
            return None
    
    def _extract_hand_cards(self, screenshot: np.ndarray) -> List[Card]:
        """
        从截图中提取手牌信息
        
        Args:
            screenshot: 游戏截图
            
        Returns:
            手牌列表
        """
        # 这里需要实现卡牌识别
        # 暂时返回模拟数据
        return []
    
    def _extract_deck_size(self, screenshot: np.ndarray) -> Optional[int]:
        """
        从截图中提取牌组剩余数量
        
        Args:
            screenshot: 游戏截图
            
        Returns:
            牌组剩余数量
        """
        # 这里需要实现OCR识别
        # 暂时返回模拟值
        return 20
    
    def _extract_discard_size(self, screenshot: np.ndarray) -> Optional[int]:
        """
        从截图中提取弃牌堆数量
        
        Args:
            screenshot: 游戏截图
            
        Returns:
            弃牌堆数量
        """
        # 这里需要实现OCR识别
        # 暂时返回模拟值
        return 5
    
    def _match_result_to_dict(self, match_result: MatchResult) -> Dict[str, Any]:
        """
        将匹配结果转换为字典
        
        Args:
            match_result: 匹配结果
            
        Returns:
            匹配结果字典
        """
        return {
            "x": match_result.x,
            "y": match_result.y,
            "width": match_result.width,
            "height": match_result.height,
            "center_x": match_result.center_x,
            "center_y": match_result.center_y,
            "confidence": match_result.confidence
        }
    
    def _save_debug_screenshot(self, screenshot: np.ndarray):
        """
        保存调试截图
        
        Args:
            screenshot: 截图数组
        """
        try:
            from pathlib import Path
            debug_dir = Path(self.debug_screenshots_dir)
            debug_dir.mkdir(parents=True, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
            filename = debug_dir / f"debug_{timestamp}.png"
            
            cv2.imwrite(str(filename), screenshot)
            
        except Exception as e:
            self.logger.debug(f"保存调试截图失败: {e}")
    
    def get_current_scene(self, image: Optional[np.ndarray] = None) -> GameScene:
        """
        获取当前场景
        
        Args:
            image: 可选的图像，如果不提供则捕获新截图
            
        Returns:
            当前场景
        """
        if image is None:
            image = self.screen_capture.capture_screen()
            if image is None:
                return GameScene.UNKNOWN
        
        return self.scene_recognizer.recognize_scene(image)
    
    def find_ui_element(self, template_name: str, 
                       region: Optional[Tuple[int, int, int, int]] = None) -> Optional[MatchResult]:
        """
        查找UI元素
        
        Args:
            template_name: 模板名称
            region: 搜索区域 (x, y, width, height)
            
        Returns:
            匹配结果，如果未找到返回None
        """
        if region:
            screenshot = self.screen_capture.capture_region(region[0], region[1], region[2], region[3])
        else:
            screenshot = self.screen_capture.capture_screen()
        
        if screenshot is None:
            return None
        
        return self.template_matcher.match_template(screenshot, template_name)
    
    def wait_for_scene(self, target_scene: GameScene, timeout: float = 30.0, 
                      check_interval: float = 1.0) -> bool:
        """
        等待指定场景出现
        
        Args:
            target_scene: 目标场景
            timeout: 超时时间（秒）
            check_interval: 检查间隔（秒）
            
        Returns:
            是否成功等到目标场景
        """
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                current_scene = self.get_current_scene()
                if current_scene == target_scene:
                    self.logger.info(f"成功等到目标场景: {target_scene.value}")
                    return True
                
                time.sleep(check_interval)
                
            except Exception as e:
                self.logger.warning(f"等待场景时出错: {e}")
                time.sleep(check_interval)
        
        self.logger.warning(f"等待场景超时: {target_scene.value}")
        return False
    
    def get_module_info(self) -> Dict[str, Any]:
        """
        获取模块信息
        
        Returns:
            模块信息字典
        """
        return {
            "language": self.language,
            "debug_mode": self.debug_mode,
            "screenshot_cache_duration": self.screenshot_cache_duration,
            "last_update_time": self._last_update_time,
            "ocr_enabled": self.enable_ocr,
            "screen_capture_info": self.screen_capture.get_window_info(),
            "scene_recognizer_info": self.scene_recognizer.get_recognition_info(),
            "template_matcher_info": self.template_matcher.get_template_info()
        }

    def _init_ocr_regions(self) -> Dict[str, Tuple[int, int, int, int]]:
        """
        初始化OCR文本区域定义

        Returns:
            文本区域字典 {区域名: (x, y, width, height)}
        """
        # 基于1920x1080分辨率的相对坐标
        # 实际使用时会根据窗口大小进行缩放
        return {
            "stamina_text": (100, 50, 150, 30),      # 体力数值区域
            "vigor_text": (100, 90, 150, 30),        # 元气数值区域
            "week_text": (800, 50, 100, 30),         # 周数区域
            "score_text": (800, 90, 150, 30),        # 分数区域
            "event_title": (400, 200, 400, 50),      # 事件标题区域
            "event_content": (300, 300, 600, 200),   # 事件内容区域
            "card_name": (50, 800, 200, 40),         # 卡牌名称区域
            "card_cost": (50, 840, 50, 30),          # 卡牌消耗区域
            "dialog_text": (200, 400, 800, 300),     # 对话文本区域
        }

    def extract_text_from_region(self, image: np.ndarray, region: Tuple[int, int, int, int],
                                preprocess: bool = True) -> str:
        """
        从指定区域提取文本

        Args:
            image: 输入图像
            region: 区域坐标 (x, y, width, height)
            preprocess: 是否进行预处理

        Returns:
            提取的文本
        """
        if not self.enable_ocr or not self.ocr_reader:
            self.logger.warning("OCR功能未启用或未初始化")
            return ""

        try:
            # 提取ROI
            x, y, w, h = region
            roi = image[y:y+h, x:x+w]

            if roi.size == 0:
                return ""

            # 预处理提高识别率
            if preprocess:
                roi = self._preprocess_for_ocr(roi)

            # OCR识别
            results = self.ocr_reader.readtext(roi)

            # 处理结果
            text = self._process_ocr_results(results)

            self.logger.debug(f"OCR识别结果: {text}")
            return text

        except Exception as e:
            self.logger.error(f"OCR文本提取失败: {e}")
            return ""

    def extract_game_text(self, image: np.ndarray, regions: Dict[str, Tuple[int, int, int, int]]) -> Dict[str, str]:
        """
        从多个区域提取游戏文本

        Args:
            image: 输入图像
            regions: 区域字典 {区域名: (x, y, width, height)}

        Returns:
            文本结果字典 {区域名: 文本内容}
        """
        results = {}

        for name, region in regions.items():
            text = self.extract_text_from_region(image, region)
            results[name] = text

        return results

    def _preprocess_for_ocr(self, roi: np.ndarray) -> np.ndarray:
        """
        OCR预处理

        Args:
            roi: 输入ROI图像

        Returns:
            预处理后的图像
        """
        try:
            # 转换为灰度图
            if len(roi.shape) == 3:
                gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
            else:
                gray = roi.copy()

            # 高斯模糊去噪
            blurred = cv2.GaussianBlur(gray, (3, 3), 0)

            # 自适应阈值二值化
            binary = cv2.adaptiveThreshold(
                blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                cv2.THRESH_BINARY, 11, 2
            )

            # 形态学操作去除噪点
            kernel = np.ones((2, 2), np.uint8)
            cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)

            return cleaned

        except Exception as e:
            self.logger.error(f"OCR预处理失败: {e}")
            return roi

    def _process_ocr_results(self, results: List) -> str:
        """
        处理OCR识别结果

        Args:
            results: EasyOCR识别结果

        Returns:
            清理后的文本
        """
        if not results:
            return ""

        # 过滤低置信度结果
        filtered_results = [
            result for result in results
            if len(result) >= 3 and result[2] >= self.ocr_confidence_threshold
        ]

        if not filtered_results:
            return ""

        # 提取文本并合并
        texts = [result[1] for result in filtered_results]
        combined_text = " ".join(texts)

        # 清理文本
        cleaned_text = self._clean_ocr_text(combined_text)

        return cleaned_text

    def _clean_ocr_text(self, text: str) -> str:
        """
        清理OCR识别的文本

        Args:
            text: 原始文本

        Returns:
            清理后的文本
        """
        if not text:
            return ""

        # 移除多余空格
        cleaned = re.sub(r'\s+', ' ', text.strip())

        # 移除特殊字符（保留日文、中文、英文、数字）
        if self.language == 'ja':
            # 保留日文字符
            cleaned = re.sub(r'[^\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF\w\s]', '', cleaned)
        else:
            # 保留中文字符
            cleaned = re.sub(r'[^\u4E00-\u9FAF\w\s]', '', cleaned)

        return cleaned.strip()
