"""
游戏启动器
负责自动启动DMM Player和学园偶像大师游戏
"""

import os
import time
import subprocess
from pathlib import Path
from typing import Optional, Dict, Any

from ...utils.logger import get_logger
from ...core.data_structures import GakumasuBotException
from .input_simulator import InputSimulator


class GameLaunchError(GakumasuBotException):
    """游戏启动错误"""
    pass


class GameLauncher:
    """游戏启动器类"""
    
    def __init__(self, dmm_player_path: str, game_icon_template: str = "assets/templates/dmm_gakumasu_icon.png"):
        """
        初始化游戏启动器
        
        Args:
            dmm_player_path: DMM Player可执行文件路径
            game_icon_template: 游戏图标模板路径
        """
        self.logger = get_logger("GameLauncher")
        self.dmm_player_path = Path(dmm_player_path)
        self.game_icon_template = game_icon_template
        
        # 初始化输入模拟器
        self.input_simulator = InputSimulator()
        
        # 启动配置
        self.launch_timeout = 60.0      # 启动超时时间（秒）
        self.dmm_startup_delay = 10.0   # DMM启动等待时间
        self.game_startup_delay = 15.0  # 游戏启动等待时间
        self.retry_attempts = 3         # 重试次数
        self.retry_delay = 5.0          # 重试间隔
        
        # 验证DMM Player路径
        if not self.dmm_player_path.exists():
            raise GameLaunchError(f"DMM Player路径不存在: {self.dmm_player_path}")
        
        self.logger.info(f"游戏启动器初始化完成，DMM路径: {self.dmm_player_path}")
    
    def is_dmm_running(self) -> bool:
        """
        检查DMM Player是否正在运行
        
        Returns:
            DMM Player是否运行中
        """
        try:
            # 使用tasklist命令检查进程
            result = subprocess.run(
                ['tasklist', '/FI', 'IMAGENAME eq DMMGamePlayer.exe'],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            is_running = 'DMMGamePlayer.exe' in result.stdout
            self.logger.debug(f"DMM Player运行状态: {is_running}")
            return is_running
            
        except Exception as e:
            self.logger.warning(f"检查DMM运行状态失败: {e}")
            return False
    
    def is_game_running(self, game_window_title: str = "gakumas") -> bool:
        """
        检查游戏是否正在运行
        
        Args:
            game_window_title: 游戏窗口标题
            
        Returns:
            游戏是否运行中
        """
        try:
            import win32gui
            
            def enum_windows_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd):
                    window_title = win32gui.GetWindowText(hwnd)
                    if game_window_title in window_title:
                        windows.append((hwnd, window_title))
                return True
            
            windows = []
            win32gui.EnumWindows(enum_windows_callback, windows)
            
            is_running = len(windows) > 0
            self.logger.debug(f"游戏运行状态: {is_running}")
            return is_running
            
        except Exception as e:
            self.logger.warning(f"检查游戏运行状态失败: {e}")
            return False
    
    def launch_dmm_player(self) -> bool:
        """
        启动DMM Player
        
        Returns:
            是否启动成功
        """
        try:
            if self.is_dmm_running():
                self.logger.info("DMM Player已在运行")
                return True
            
            self.logger.info("正在启动DMM Player...")
            
            # 启动DMM Player
            subprocess.Popen([str(self.dmm_player_path)], shell=True)
            
            # 等待DMM Player启动
            self.logger.info(f"等待DMM Player启动 ({self.dmm_startup_delay}秒)...")
            time.sleep(self.dmm_startup_delay)
            
            # 验证启动结果
            start_time = time.time()
            while time.time() - start_time < self.launch_timeout:
                if self.is_dmm_running():
                    self.logger.info("DMM Player启动成功")
                    return True
                
                time.sleep(2.0)
            
            raise GameLaunchError("DMM Player启动超时")
            
        except Exception as e:
            self.logger.error(f"启动DMM Player失败: {e}")
            raise GameLaunchError(f"启动DMM Player失败: {e}")
    
    def find_game_icon_in_dmm(self) -> Optional[tuple]:
        """
        在DMM Player中查找游戏图标
        
        Returns:
            游戏图标位置坐标，如果未找到返回None
        """
        try:
            # 这里需要集成感知模块来查找游戏图标
            # 暂时返回模拟位置，实际实现需要模板匹配
            from ..perception.template_matcher import TemplateMatcher
            from ..perception.screen_capture import ScreenCapture
            
            # 创建屏幕捕获器和模板匹配器
            screen_capture = ScreenCapture("DMM GAME PLAYER")
            template_matcher = TemplateMatcher()
            
            # 捕获DMM Player窗口
            screenshot = screen_capture.capture_screen()
            if screenshot is None:
                self.logger.warning("无法捕获DMM Player窗口")
                return None
            
            # 查找游戏图标
            template_name = Path(self.game_icon_template).stem
            match_result = template_matcher.match_template(
                screenshot, template_name, confidence_threshold=0.7
            )
            
            if match_result:
                # 返回图标中心位置
                center_x = match_result.center_x
                center_y = match_result.center_y
                
                # 转换为屏幕绝对坐标
                window_rect = screen_capture.get_window_rect()
                if window_rect:
                    abs_x = window_rect["left"] + center_x
                    abs_y = window_rect["top"] + center_y
                    
                    self.logger.info(f"找到游戏图标位置: ({abs_x}, {abs_y})")
                    return (abs_x, abs_y)
            
            self.logger.warning("未找到游戏图标")
            return None
            
        except Exception as e:
            self.logger.error(f"查找游戏图标失败: {e}")
            return None
    
    def launch_game_from_dmm(self) -> bool:
        """
        从DMM Player启动游戏
        
        Returns:
            是否启动成功
        """
        try:
            self.logger.info("正在从DMM Player启动游戏...")
            
            # 查找游戏图标
            icon_position = self.find_game_icon_in_dmm()
            if not icon_position:
                # 如果找不到图标，尝试使用默认位置（需要根据实际情况调整）
                self.logger.warning("未找到游戏图标，使用默认位置")
                # 这里可以设置一个默认的游戏图标位置
                # icon_position = (800, 400)  # 示例位置
                raise GameLaunchError("无法找到游戏图标")
            
            # 双击游戏图标
            self.logger.info(f"双击游戏图标位置: {icon_position}")
            success = self.input_simulator.double_click(icon_position[0], icon_position[1])
            
            if not success:
                raise GameLaunchError("双击游戏图标失败")
            
            # 等待游戏启动
            self.logger.info(f"等待游戏启动 ({self.game_startup_delay}秒)...")
            time.sleep(self.game_startup_delay)
            
            return True
            
        except Exception as e:
            self.logger.error(f"从DMM启动游戏失败: {e}")
            raise GameLaunchError(f"从DMM启动游戏失败: {e}")
    
    def wait_for_game_ready(self, game_window_title: str = "gakumas") -> bool:
        """
        等待游戏完全启动并准备就绪
        
        Args:
            game_window_title: 游戏窗口标题
            
        Returns:
            游戏是否准备就绪
        """
        try:
            self.logger.info("等待游戏准备就绪...")
            
            start_time = time.time()
            while time.time() - start_time < self.launch_timeout:
                if self.is_game_running(game_window_title):
                    # 游戏窗口已出现，再等待一段时间确保完全加载
                    self.logger.info("游戏窗口已出现，等待完全加载...")
                    time.sleep(5.0)
                    
                    # 可以在这里添加更多的游戏就绪检查
                    # 例如检查主菜单是否出现
                    
                    self.logger.info("游戏启动完成")
                    return True
                
                time.sleep(2.0)
            
            raise GameLaunchError("等待游戏启动超时")
            
        except Exception as e:
            self.logger.error(f"等待游戏就绪失败: {e}")
            raise GameLaunchError(f"等待游戏就绪失败: {e}")
    
    def launch_game_complete(self, game_window_title: str = "gakumas") -> bool:
        """
        完整的游戏启动流程
        
        Args:
            game_window_title: 游戏窗口标题
            
        Returns:
            是否启动成功
        """
        for attempt in range(self.retry_attempts):
            try:
                self.logger.info(f"开始游戏启动流程 (尝试 {attempt + 1}/{self.retry_attempts})")
                
                # 检查游戏是否已经在运行
                if self.is_game_running(game_window_title):
                    self.logger.info("游戏已在运行")
                    return True
                
                # 步骤1: 启动DMM Player
                if not self.launch_dmm_player():
                    raise GameLaunchError("DMM Player启动失败")
                
                # 步骤2: 从DMM启动游戏
                if not self.launch_game_from_dmm():
                    raise GameLaunchError("从DMM启动游戏失败")
                
                # 步骤3: 等待游戏准备就绪
                if not self.wait_for_game_ready(game_window_title):
                    raise GameLaunchError("游戏启动超时")
                
                self.logger.info("游戏启动流程完成")
                return True
                
            except Exception as e:
                self.logger.warning(f"启动尝试 {attempt + 1} 失败: {e}")
                
                if attempt < self.retry_attempts - 1:
                    self.logger.info(f"等待 {self.retry_delay} 秒后重试...")
                    time.sleep(self.retry_delay)
                else:
                    self.logger.error("所有启动尝试均失败")
                    raise GameLaunchError(f"游戏启动失败，已尝试 {self.retry_attempts} 次")
        
        return False
    
    def close_game(self, game_window_title: str = "gakumas") -> bool:
        """
        关闭游戏
        
        Args:
            game_window_title: 游戏窗口标题
            
        Returns:
            是否关闭成功
        """
        try:
            if not self.is_game_running(game_window_title):
                self.logger.info("游戏未在运行")
                return True
            
            self.logger.info("正在关闭游戏...")
            
            # 尝试通过Alt+F4关闭游戏
            self.input_simulator.key_press(['alt', 'f4'])
            time.sleep(3.0)
            
            # 检查是否成功关闭
            if not self.is_game_running(game_window_title):
                self.logger.info("游戏已关闭")
                return True
            
            # 如果Alt+F4无效，尝试强制结束进程
            self.logger.warning("尝试强制结束游戏进程...")
            subprocess.run(['taskkill', '/F', '/IM', 'gakumasu.exe'], 
                          capture_output=True, timeout=10)
            
            time.sleep(2.0)
            return not self.is_game_running(game_window_title)
            
        except Exception as e:
            self.logger.error(f"关闭游戏失败: {e}")
            return False
    
    def get_launcher_info(self) -> Dict[str, Any]:
        """
        获取启动器信息
        
        Returns:
            启动器配置信息
        """
        return {
            "dmm_player_path": str(self.dmm_player_path),
            "game_icon_template": self.game_icon_template,
            "launch_timeout": self.launch_timeout,
            "dmm_startup_delay": self.dmm_startup_delay,
            "game_startup_delay": self.game_startup_delay,
            "retry_attempts": self.retry_attempts,
            "retry_delay": self.retry_delay,
            "dmm_running": self.is_dmm_running(),
            "game_running": self.is_game_running()
        }
