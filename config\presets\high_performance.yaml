# Gakumasu-Bo<PERSON> 高性能配置预设
# 适用于高端硬件配置，追求最快响应速度
# 系统要求: 16GB+ 内存, 8核+ CPU, NVIDIA GPU

system:
  log_level: INFO
  language: ja
  screen_resolution: [1920, 1080]
  game_window_title: "gakumas"

dmm:
  dmm_player_path: ""  # 需要用户配置
  launch_timeout: 45
  auto_launch: true

performance:
  screenshot_interval: 0.2        # 高频截图
  action_delay_min: 0.02         # 最小延迟
  action_delay_max: 0.05         # 最大延迟
  decision_timeout: 15.0         # 快速决策
  enable_gpu_acceleration: true   # 启用GPU加速
  max_worker_threads: 8          # 多线程处理
  memory_cache_size: 512         # 大内存缓存

template_matching:
  confidence_threshold: 0.9       # 高精度匹配
  enable_multi_scale: true
  scale_range: [0.8, 1.2]
  scale_step: 0.05               # 精细尺度步长
  enable_template_cache: true
  cache_expiry_time: 600

ocr:
  enable_ocr: true
  languages: ['ja', 'en']
  confidence_threshold: 0.8       # 高OCR精度
  use_gpu: true                  # GPU加速OCR
  preprocessing:
    enable_denoising: true
    enable_contrast_enhancement: true
    enable_binarization: true

ai:
  enable_mcts: true
  mcts_iterations: 2000          # 高迭代次数
  mcts_timeout: 15.0            # 充足思考时间
  mcts_exploration_constant: 1.414
  heuristic_weights:
    score: 1.2
    stamina: 1.0
    vigor: 0.8
    card_synergy: 0.6
    risk_factor: 0.4
  enable_decision_cache: true
  decision_cache_size: 2000
  enable_online_learning: true

error_handling:
  max_retries: 2                 # 快速失败
  retry_interval: 1.0
  enable_auto_recovery: true
  recovery_strategy: "restart"
  save_error_screenshots: true

debug:
  enable_debug_mode: false
  save_debug_screenshots: false
  enable_verbose_logging: false
