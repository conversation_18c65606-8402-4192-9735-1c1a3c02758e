"""
测试决策模块
"""

import pytest
import time
from unittest.mock import Mock, patch, MagicMock
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.modules.decision import (
    DecisionModule, HeuristicEvaluator, MCTSEngine, EventHandler,
    EvaluationResult, MCTSResult, EventDecision, EventType
)
from src.core.data_structures import (
    GameState, Card, Action, ActionType, GameScene, CardType, CardRarity, UserStrategy
)


class TestEvaluationResult:
    """测试EvaluationResult类"""
    
    def test_evaluation_result_creation(self):
        """测试EvaluationResult创建"""
        result = EvaluationResult(
            total_score=0.8,
            criteria_scores={"score": 0.9, "efficiency": 0.7},
            confidence=0.85,
            reasoning="测试评估"
        )
        
        assert result.total_score == 0.8
        assert result.criteria_scores["score"] == 0.9
        assert result.confidence == 0.85
        assert result.reasoning == "测试评估"
        assert result.metadata == {}
    
    def test_evaluation_result_with_metadata(self):
        """测试带元数据的EvaluationResult"""
        metadata = {"card_id": "test_001", "card_type": "vocal"}
        result = EvaluationResult(
            total_score=0.7,
            criteria_scores={},
            confidence=0.6,
            reasoning="测试",
            metadata=metadata
        )
        
        assert result.metadata == metadata


class TestHeuristicEvaluator:
    """测试HeuristicEvaluator类"""
    
    def setup_method(self):
        """测试前准备"""
        self.user_strategy = UserStrategy()
        self.evaluator = HeuristicEvaluator(self.user_strategy)
    
    def test_heuristic_evaluator_init(self):
        """测试HeuristicEvaluator初始化"""
        assert self.evaluator.user_strategy == self.user_strategy
        assert len(self.evaluator.evaluation_weights) > 0
        assert len(self.evaluator.rarity_base_scores) > 0
    
    def test_evaluate_card(self):
        """测试卡牌评估"""
        card = Card(
            card_id="test_001",
            name_jp="テストカード",
            card_type=CardType.VOCAL,
            rarity=CardRarity.SR,
            cost=2
        )
        
        game_state = GameState(
            current_scene=GameScene.PRODUCE_BATTLE,
            stamina=80,
            vigor=70
        )
        
        result = self.evaluator.evaluate_card(card, game_state)
        
        assert isinstance(result, EvaluationResult)
        assert 0.0 <= result.total_score <= 1.0
        assert 0.0 <= result.confidence <= 1.0
        assert result.reasoning is not None
        assert "card_id" in result.metadata
    
    def test_evaluate_game_state(self):
        """测试游戏状态评估"""
        game_state = GameState(
            current_scene=GameScene.PRODUCE_MAIN,
            stamina=60,
            vigor=80,
            current_week=10
        )
        
        result = self.evaluator.evaluate_game_state(game_state)
        
        assert isinstance(result, EvaluationResult)
        assert 0.0 <= result.total_score <= 1.0
        assert result.confidence > 0.0
        assert "scene" in result.metadata
    
    def test_evaluate_action_sequence(self):
        """测试行动序列评估"""
        actions = [
            Action(ActionType.CLICK, (100, 200), "点击1"),
            Action(ActionType.KEYPRESS, "space", "按键")
        ]
        
        game_state = GameState(current_scene=GameScene.PRODUCE_MAIN)
        
        result = self.evaluator.evaluate_action_sequence(actions, game_state)
        
        assert isinstance(result, EvaluationResult)
        assert result.metadata["action_count"] == 2
    
    def test_evaluate_empty_action_sequence(self):
        """测试空行动序列评估"""
        game_state = GameState(current_scene=GameScene.PRODUCE_MAIN)
        
        result = self.evaluator.evaluate_action_sequence([], game_state)
        
        assert result.total_score == 0.0
        assert result.confidence == 1.0
        assert "空行动序列" in result.reasoning
    
    def test_get_evaluator_info(self):
        """测试获取评分器信息"""
        info = self.evaluator.get_evaluator_info()
        
        assert "evaluation_weights" in info
        assert "card_type_weights" in info
        assert "rarity_base_scores" in info
        assert "user_strategy_target" in info


class TestEventHandler:
    """测试EventHandler类"""
    
    def setup_method(self):
        """测试前准备"""
        self.user_strategy = UserStrategy()
        self.handler = EventHandler(self.user_strategy)
    
    def test_event_handler_init(self):
        """测试EventHandler初始化"""
        assert self.handler.user_strategy == self.user_strategy
        assert len(self.handler.event_patterns) > 0
        assert len(self.handler.event_strategies) > 0
    
    def test_identify_event_by_scene(self):
        """测试基于场景的事件识别"""
        game_state = GameState(current_scene=GameScene.PRODUCE_MAIN)
        
        event_type = self.handler.identify_event(game_state)
        
        assert event_type == EventType.LESSON_CHOICE
    
    def test_identify_event_by_text(self):
        """测试基于文本的事件识别"""
        game_state = GameState(current_scene=GameScene.UNKNOWN)
        screen_text = "レッスンを選択してください"
        
        event_type = self.handler.identify_event(game_state, screen_text)
        
        assert event_type == EventType.LESSON_CHOICE
    
    def test_handle_lesson_choice_event(self):
        """测试课程选择事件处理"""
        game_state = GameState(
            current_scene=GameScene.PRODUCE_MAIN,
            stamina=80
        )
        
        decision = self.handler.handle_event(EventType.LESSON_CHOICE, game_state)
        
        assert isinstance(decision, EventDecision)
        assert decision.event_type == EventType.LESSON_CHOICE
        assert decision.recommended_action is not None
        assert 0.0 <= decision.confidence <= 1.0
    
    def test_handle_rest_decision_low_stamina(self):
        """测试低体力时的休息决策"""
        game_state = GameState(
            current_scene=GameScene.PRODUCE_MAIN,
            stamina=15,  # 低体力
            vigor=50
        )
        
        decision = self.handler.handle_event(EventType.REST_DECISION, game_state)
        
        assert decision.event_type == EventType.REST_DECISION
        assert "休息" in decision.reasoning
        assert decision.confidence > 0.8
    
    def test_handle_unknown_event(self):
        """测试未知事件处理"""
        game_state = GameState(current_scene=GameScene.UNKNOWN)
        
        decision = self.handler.handle_event(EventType.UNKNOWN_EVENT, game_state)
        
        assert decision.event_type == EventType.UNKNOWN_EVENT
        assert decision.confidence < 0.5
        assert len(decision.alternative_actions) > 0
    
    def test_get_event_statistics(self):
        """测试获取事件统计"""
        # 处理一些事件
        game_state = GameState(current_scene=GameScene.PRODUCE_MAIN)
        self.handler.handle_event(EventType.LESSON_CHOICE, game_state)
        self.handler.handle_event(EventType.LESSON_CHOICE, game_state)
        
        stats = self.handler.get_event_statistics()
        
        assert stats["total_events_processed"] == 2
        assert stats["event_type_counts"]["lesson_choice"] == 2
    
    def test_reset_statistics(self):
        """测试重置统计信息"""
        game_state = GameState(current_scene=GameScene.PRODUCE_MAIN)
        self.handler.handle_event(EventType.LESSON_CHOICE, game_state)
        
        self.handler.reset_statistics()
        
        stats = self.handler.get_event_statistics()
        assert stats["total_events_processed"] == 0


class TestMCTSEngine:
    """测试MCTSEngine类"""
    
    def setup_method(self):
        """测试前准备"""
        self.evaluator = HeuristicEvaluator()
        self.engine = MCTSEngine(self.evaluator)
    
    def test_mcts_engine_init(self):
        """测试MCTSEngine初始化"""
        assert self.engine.heuristic_evaluator == self.evaluator
        assert self.engine.max_iterations == 1000
        assert self.engine.max_time == 10.0
    
    def test_mcts_search_simple(self):
        """测试简单MCTS搜索"""
        game_state = GameState(
            current_scene=GameScene.PRODUCE_MAIN,
            stamina=80,
            vigor=70
        )
        
        actions = [
            Action(ActionType.CLICK, (200, 300), "声乐课程"),
            Action(ActionType.CLICK, (300, 300), "舞蹈课程")
        ]
        
        # 限制搜索时间和迭代次数以加快测试
        result = self.engine.search(
            game_state, 
            actions,
            max_iterations=10,
            max_time=1.0
        )
        
        assert isinstance(result, MCTSResult)
        assert result.best_action in actions or result.best_action is None
        assert result.iterations <= 10
        assert result.search_time <= 2.0  # 允许一些误差
        assert 0.0 <= result.confidence <= 1.0
    
    def test_mcts_search_empty_actions(self):
        """测试空行动列表的MCTS搜索"""
        game_state = GameState(current_scene=GameScene.PRODUCE_MAIN)
        
        result = self.engine.search(game_state, [], max_iterations=5, max_time=0.5)
        
        assert result.best_action is None
        assert result.best_score == 0.0
    
    def test_mcts_configure(self):
        """测试MCTS引擎配置"""
        self.engine.configure(
            max_iterations=500,
            max_time=5.0,
            exploration_constant=1.5
        )
        
        assert self.engine.max_iterations == 500
        assert self.engine.max_time == 5.0
        assert self.engine.exploration_constant == 1.5
    
    def test_mcts_reset_statistics(self):
        """测试重置MCTS统计"""
        # 执行一次搜索
        game_state = GameState(current_scene=GameScene.PRODUCE_MAIN)
        actions = [Action(ActionType.CLICK, (100, 100), "测试")]
        self.engine.search(game_state, actions, max_iterations=5, max_time=0.5)
        
        self.engine.reset_statistics()
        
        info = self.engine.get_engine_info()
        assert info["total_searches"] == 0
        assert info["total_search_time"] == 0.0


class TestDecisionModule:
    """测试DecisionModule类"""
    
    def setup_method(self):
        """测试前准备"""
        self.user_strategy = UserStrategy()
        self.decision_module = DecisionModule(self.user_strategy)
    
    def test_decision_module_init(self):
        """测试DecisionModule初始化"""
        assert self.decision_module.user_strategy == self.user_strategy
        assert isinstance(self.decision_module.heuristic_evaluator, HeuristicEvaluator)
        assert isinstance(self.decision_module.mcts_engine, MCTSEngine)
        assert isinstance(self.decision_module.event_handler, EventHandler)
    
    def test_make_decision_simple(self):
        """测试简单决策"""
        game_state = GameState(
            current_scene=GameScene.PRODUCE_MAIN,
            stamina=80,
            vigor=70
        )
        
        actions = [
            Action(ActionType.CLICK, (200, 300), "声乐课程"),
            Action(ActionType.CLICK, (500, 300), "休息")
        ]
        
        chosen_action = self.decision_module.make_decision(game_state, actions)
        
        assert chosen_action in actions
        assert len(self.decision_module.decision_history) == 1
    
    def test_make_decision_no_actions(self):
        """测试无可用行动的决策"""
        game_state = GameState(current_scene=GameScene.PRODUCE_MAIN)
        
        chosen_action = self.decision_module.make_decision(game_state, [])
        
        # 应该生成默认行动
        assert chosen_action is not None
        assert chosen_action.action_type in [ActionType.CLICK, ActionType.KEYPRESS]
    
    def test_make_decision_invalid_state(self):
        """测试无效状态的决策"""
        # 创建无效状态
        game_state = GameState(
            current_scene=GameScene.UNKNOWN,
            stamina=-10  # 无效的体力值
        )
        
        chosen_action = self.decision_module.make_decision(game_state)
        
        # 应该返回安全的默认行动
        assert chosen_action is not None
    
    def test_evaluate_game_state(self):
        """测试游戏状态评估"""
        game_state = GameState(
            current_scene=GameScene.PRODUCE_MAIN,
            stamina=60,
            vigor=80
        )
        
        result = self.decision_module.evaluate_game_state(game_state)
        
        assert isinstance(result, EvaluationResult)
        assert 0.0 <= result.total_score <= 1.0
    
    def test_evaluate_card(self):
        """测试卡牌评估"""
        card = Card(
            card_id="test_001",
            name_jp="テストカード",
            card_type=CardType.VOCAL,
            rarity=CardRarity.R
        )
        
        game_state = GameState(current_scene=GameScene.PRODUCE_BATTLE)
        
        result = self.decision_module.evaluate_card(card, game_state)
        
        assert isinstance(result, EvaluationResult)
        assert result.metadata["card_id"] == "test_001"
    
    def test_configure_decision_module(self):
        """测试决策模块配置"""
        self.decision_module.configure(
            decision_mode="heuristic_only",
            enable_mcts=False,
            mcts_time_limit=3.0,
            confidence_threshold=0.8
        )
        
        assert self.decision_module.decision_mode == "heuristic_only"
        assert self.decision_module.enable_mcts is False
        assert self.decision_module.mcts_time_limit == 3.0
        assert self.decision_module.confidence_threshold == 0.8
    
    def test_get_decision_statistics(self):
        """测试获取决策统计"""
        # 执行一些决策
        game_state = GameState(current_scene=GameScene.PRODUCE_MAIN)
        actions = [Action(ActionType.CLICK, (100, 100), "测试")]
        
        self.decision_module.make_decision(game_state, actions)
        self.decision_module.make_decision(game_state, actions)
        
        stats = self.decision_module.get_decision_statistics()
        
        assert stats["total_decisions"] == 2
        assert stats["average_decision_time"] > 0.0
        assert stats["decision_history_size"] == 2
    
    def test_get_recent_decisions(self):
        """测试获取最近决策"""
        game_state = GameState(current_scene=GameScene.PRODUCE_MAIN)
        actions = [Action(ActionType.CLICK, (100, 100), "测试")]
        
        self.decision_module.make_decision(game_state, actions)
        
        recent = self.decision_module.get_recent_decisions(limit=5)
        
        assert len(recent) == 1
        assert "timestamp" in recent[0]
        assert "action_description" in recent[0]
    
    def test_reset_statistics(self):
        """测试重置统计信息"""
        game_state = GameState(current_scene=GameScene.PRODUCE_MAIN)
        actions = [Action(ActionType.CLICK, (100, 100), "测试")]
        
        self.decision_module.make_decision(game_state, actions)
        
        self.decision_module.reset_statistics()
        
        stats = self.decision_module.get_decision_statistics()
        assert stats["total_decisions"] == 0
        assert len(self.decision_module.decision_history) == 0
    
    def test_get_module_info(self):
        """测试获取模块信息"""
        info = self.decision_module.get_module_info()
        
        assert "decision_mode" in info
        assert "enable_mcts" in info
        assert "statistics" in info
        assert "heuristic_evaluator_info" in info
        assert "mcts_engine_info" in info
        assert "event_handler_info" in info


if __name__ == "__main__":
    pytest.main([__file__])
