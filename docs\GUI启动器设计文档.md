# Gakumasu-Bot GUI启动器设计文档

**版本：** 1.0  
**日期：** 2025年7月26日  
**项目阶段：** GUI一键启动功能开发

## 一、设计概述

### 1.1 项目目标
基于现有 Gakumasu-Bot 项目架构，设计并实现一个简洁高效的 GUI 启动器 (`gui.py`)，提供一键启动前后端服务的便捷体验。

### 1.2 核心功能
- **一键启动**：同时启动后端服务（FastAPI）和前端开发服务器（Vue.js）
- **自动浏览器**：启动完成后自动打开默认浏览器并导航到前端应用
- **进程管理**：统一管理前后端进程的生命周期
- **优雅关闭**：支持 Ctrl+C 优雅关闭所有服务
- **错误处理**：完善的错误处理和日志输出
- **端口检测**：自动检测端口占用并处理冲突

### 1.3 技术要求
- 使用 Python 标准库 `subprocess` 管理进程
- 集成现有的 Scheduler、StateManager、ConfigManager 模块
- 支持 WebSocket 实时通信
- 区分开发环境和生产环境启动方式

## 二、架构设计

### 2.1 整体架构

```mermaid
graph TD
    A[gui.py 启动器] --> B[后端服务管理]
    A --> C[前端服务管理]
    A --> D[浏览器控制]
    A --> E[进程监控]
    
    B --> F[FastAPI 服务]
    B --> G[WebSocket 服务]
    B --> H[核心模块集成]
    
    C --> I[Vue.js 开发服务器]
    C --> J[静态文件服务]
    
    D --> K[自动打开浏览器]
    D --> L[URL 导航]
    
    E --> M[进程状态监控]
    E --> N[优雅关闭处理]
```

### 2.2 核心组件设计

#### 2.2.1 GUILauncher 主类
```python
class GUILauncher:
    """GUI启动器主类"""
    
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.config = {}
        self.logger = None
        
    def start(self, mode='development'):
        """启动所有服务"""
        
    def stop(self):
        """停止所有服务"""
        
    def check_ports(self):
        """检查端口占用"""
        
    def open_browser(self):
        """打开浏览器"""
```

#### 2.2.2 服务配置
```python
DEFAULT_CONFIG = {
    'backend': {
        'host': '127.0.0.1',
        'port': 8000,
        'module': 'src.web.main:app',
        'reload': True
    },
    'frontend': {
        'host': '127.0.0.1', 
        'port': 3000,
        'command': 'npm run dev',
        'working_dir': 'frontend'
    },
    'browser': {
        'auto_open': True,
        'url': 'http://localhost:3000',
        'delay': 3  # 启动后等待时间
    }
}
```

### 2.3 启动流程设计

```mermaid
sequenceDiagram
    participant U as 用户
    participant G as GUI启动器
    participant B as 后端服务
    participant F as 前端服务
    participant BR as 浏览器
    
    U->>G: 运行 python gui.py
    G->>G: 初始化配置和日志
    G->>G: 检查端口占用
    G->>B: 启动 FastAPI 服务
    B-->>G: 服务启动确认
    G->>F: 启动 Vue.js 开发服务器
    F-->>G: 服务启动确认
    G->>G: 等待服务就绪
    G->>BR: 自动打开浏览器
    BR-->>U: 显示前端界面
    
    Note over G: 监控进程状态
    
    U->>G: Ctrl+C 退出
    G->>F: 优雅关闭前端服务
    G->>B: 优雅关闭后端服务
    G-->>U: 退出完成
```

## 三、详细实现方案

### 3.1 端口管理策略

#### 3.1.1 端口检测
```python
def is_port_in_use(port: int, host: str = 'localhost') -> bool:
    """检查端口是否被占用"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        try:
            s.bind((host, port))
            return False
        except OSError:
            return True

def find_available_port(start_port: int, max_attempts: int = 10) -> int:
    """查找可用端口"""
    for i in range(max_attempts):
        port = start_port + i
        if not is_port_in_use(port):
            return port
    raise RuntimeError(f"无法找到可用端口 (起始端口: {start_port})")
```

#### 3.1.2 动态端口分配
- 如果默认端口被占用，自动分配下一个可用端口
- 更新前端代理配置以匹配后端端口
- 在日志中明确显示实际使用的端口

### 3.2 进程管理策略

#### 3.2.1 后端服务启动
```python
def start_backend(self) -> subprocess.Popen:
    """启动后端服务"""
    cmd = [
        'uvicorn',
        self.config['backend']['module'],
        '--host', self.config['backend']['host'],
        '--port', str(self.config['backend']['port']),
        '--reload' if self.config['backend']['reload'] else '--no-reload'
    ]
    
    return subprocess.Popen(
        cmd,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
        bufsize=1,
        universal_newlines=True
    )
```

#### 3.2.2 前端服务启动
```python
def start_frontend(self) -> subprocess.Popen:
    """启动前端服务"""
    return subprocess.Popen(
        self.config['frontend']['command'].split(),
        cwd=self.config['frontend']['working_dir'],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
        shell=True
    )
```

### 3.3 健康检查机制

#### 3.3.1 服务就绪检测
```python
def wait_for_service(self, url: str, timeout: int = 30) -> bool:
    """等待服务就绪"""
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            response = requests.get(url, timeout=1)
            if response.status_code == 200:
                return True
        except requests.RequestException:
            pass
        time.sleep(1)
    return False
```

#### 3.3.2 进程状态监控
```python
def monitor_processes(self):
    """监控进程状态"""
    while self.running:
        if self.backend_process and self.backend_process.poll() is not None:
            self.logger.error("后端服务意外退出")
            break
        if self.frontend_process and self.frontend_process.poll() is not None:
            self.logger.error("前端服务意外退出")
            break
        time.sleep(1)
```

### 3.4 集成现有模块

#### 3.4.1 与现有架构的兼容性
- **无侵入式集成**：不修改现有的 Scheduler、StateManager、ConfigManager
- **配置复用**：使用现有的配置文件结构
- **日志统一**：集成现有的日志系统

#### 3.4.2 WebSocket 支持
- 确保后端 WebSocket 服务正常启动
- 前端代理配置正确转发 WebSocket 连接
- 实时状态更新功能正常工作

## 四、错误处理和日志

### 4.1 错误处理策略
- **端口冲突**：自动分配可用端口并更新配置
- **依赖缺失**：检查必要的依赖并提供安装提示
- **服务启动失败**：详细的错误信息和解决建议
- **进程异常退出**：自动重启或优雅关闭

### 4.2 日志输出
```python
# 日志格式示例
2025-07-26 10:30:00 [INFO] GUI启动器初始化完成
2025-07-26 10:30:01 [INFO] 检查端口占用: 8000 (可用), 3000 (可用)
2025-07-26 10:30:02 [INFO] 启动后端服务: uvicorn src.web.main:app --host 127.0.0.1 --port 8000 --reload
2025-07-26 10:30:05 [INFO] 后端服务就绪: http://127.0.0.1:8000
2025-07-26 10:30:06 [INFO] 启动前端服务: npm run dev
2025-07-26 10:30:10 [INFO] 前端服务就绪: http://127.0.0.1:3000
2025-07-26 10:30:13 [INFO] 自动打开浏览器: http://127.0.0.1:3000
2025-07-26 10:30:13 [INFO] 所有服务启动完成，按 Ctrl+C 退出
```

## 五、使用方式

### 5.1 基本使用
```bash
# 开发模式启动（默认）
python gui.py

# 生产模式启动
python gui.py --prod

# 指定端口启动
python gui.py --backend-port 8080 --frontend-port 3001

# 不自动打开浏览器
python gui.py --no-browser
```

### 5.2 命令行参数
- `--mode`: 启动模式 (development/production)
- `--backend-port`: 后端端口
- `--frontend-port`: 前端端口
- `--no-browser`: 不自动打开浏览器
- `--config`: 指定配置文件路径
- `--log-level`: 日志级别

## 六、测试验证

### 6.1 功能测试
- [ ] 基本启动流程测试
- [ ] 端口冲突处理测试
- [ ] 优雅关闭测试
- [ ] 错误处理测试
- [ ] 浏览器自动打开测试

### 6.2 集成测试
- [ ] 与现有模块的兼容性测试
- [ ] WebSocket 连接测试
- [ ] 前后端通信测试
- [ ] 配置文件加载测试

## 七、后续扩展

### 7.1 功能增强
- 图形化界面（可选）
- 服务状态实时显示
- 配置文件热重载
- 多环境配置支持

### 7.2 性能优化
- 并行启动优化
- 内存使用监控
- 启动时间优化
- 资源清理优化
