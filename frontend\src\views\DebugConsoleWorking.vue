<template>
  <div class="debug-console-working">
    <div class="header">
      <h1>🔧 调试控制台 - 工作版本</h1>
      <div class="status-indicators">
        <el-tag :type="backendStatus ? 'success' : 'danger'" size="small">
          后端: {{ backendStatus ? '已连接' : '未连接' }}
        </el-tag>
        <el-tag type="info" size="small">
          前端: 正常运行
        </el-tag>
      </div>
    </div>

    <el-container class="main-container">
      <!-- 左侧导航 -->
      <el-aside width="250px" class="sidebar">
        <el-menu
          :default-active="activeTab"
          class="sidebar-menu"
          @select="handleTabSelect"
        >
          <el-menu-item index="connection-test">
            <el-icon><Link /></el-icon>
            <span>连接测试</span>
          </el-menu-item>
          <el-menu-item index="system-status">
            <el-icon><Monitor /></el-icon>
            <span>系统状态</span>
          </el-menu-item>
          <el-menu-item index="api-test">
            <el-icon><Tools /></el-icon>
            <span>API测试</span>
          </el-menu-item>
          <el-menu-item index="logs">
            <el-icon><Document /></el-icon>
            <span>日志查看</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 主内容区 -->
      <el-main class="content-area">
        <!-- 连接测试面板 -->
        <div v-show="activeTab === 'connection-test'" class="tab-panel">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>🔗 连接测试</span>
                <el-button type="primary" @click="testAllConnections" :loading="testing">
                  测试所有连接
                </el-button>
              </div>
            </template>
            
            <div class="connection-tests">
              <el-space direction="vertical" style="width: 100%">
                <el-button @click="testFrontend" :loading="testing">
                  测试前端服务 (localhost:3000)
                </el-button>
                <el-button @click="testBackendHealth" :loading="testing">
                  测试后端健康检查 (localhost:8000/health)
                </el-button>
                <el-button @click="testBackendAPI" :loading="testing">
                  测试后端API (localhost:8000/api/v1/status)
                </el-button>
                
                <div v-if="testResults.length > 0" class="test-results">
                  <h4>测试结果：</h4>
                  <div v-for="result in testResults" :key="result.id" class="result-item">
                    <el-tag :type="result.success ? 'success' : 'danger'" size="small">
                      {{ result.success ? '✅' : '❌' }}
                    </el-tag>
                    <span class="result-text">{{ result.message }}</span>
                    <span class="result-time">{{ formatTime(result.timestamp) }}</span>
                  </div>
                </div>
              </el-space>
            </div>
          </el-card>
        </div>

        <!-- 系统状态面板 -->
        <div v-show="activeTab === 'system-status'" class="tab-panel">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>📊 系统状态</span>
                <el-button type="primary" @click="refreshSystemStatus" :loading="loading.systemStatus">
                  刷新状态
                </el-button>
              </div>
            </template>
            
            <div v-if="systemStatus" class="system-info">
              <el-descriptions :column="2" border>
                <el-descriptions-item label="系统状态">
                  <el-tag :type="getStatusType(systemStatus.status)">
                    {{ systemStatus.status || '未知' }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="运行时间">
                  {{ formatUptime(systemStatus.uptime) }}
                </el-descriptions-item>
                <el-descriptions-item label="调度器状态">
                  <el-tag :type="getStatusType(systemStatus.scheduler_status)">
                    {{ systemStatus.scheduler_status || '未知' }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="活跃任务">
                  {{ systemStatus.active_tasks || 0 }}
                </el-descriptions-item>
                <el-descriptions-item label="已完成任务">
                  {{ systemStatus.completed_tasks || 0 }}
                </el-descriptions-item>
                <el-descriptions-item label="失败任务">
                  {{ systemStatus.failed_tasks || 0 }}
                </el-descriptions-item>
              </el-descriptions>
              
              <div v-if="systemStatus.current_game_state" class="game-state">
                <h4>游戏状态信息：</h4>
                <el-descriptions :column="2" size="small">
                  <el-descriptions-item label="当前场景">
                    {{ systemStatus.current_game_state.current_scene || '未知' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="体力值">
                    {{ systemStatus.current_game_state.stamina || 0 }}
                  </el-descriptions-item>
                  <el-descriptions-item label="元气值">
                    {{ systemStatus.current_game_state.vigor || 0 }}
                  </el-descriptions-item>
                  <el-descriptions-item label="当前分数">
                    {{ systemStatus.current_game_state.score || 0 }}
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </div>
            <div v-else class="no-data">
              <el-empty description="暂无系统状态数据，请点击刷新状态" />
            </div>
          </el-card>
        </div>

        <!-- API测试面板 -->
        <div v-show="activeTab === 'api-test'" class="tab-panel">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>🧪 API测试</span>
              </div>
            </template>
            
            <div class="api-tests">
              <el-space direction="vertical" style="width: 100%">
                <el-button type="primary" @click="testAPI('/health', 'GET')" :loading="testing">
                  GET /health - 健康检查
                </el-button>
                <el-button type="success" @click="testAPI('/api/v1/status', 'GET')" :loading="testing">
                  GET /api/v1/status - 系统状态
                </el-button>
                <el-button type="warning" @click="testAPI('/api/v1/config', 'GET')" :loading="testing">
                  GET /api/v1/config - 获取配置
                </el-button>
                <el-button type="info" @click="testAPI('/api/v1/screenshot/history', 'GET')" :loading="testing">
                  GET /api/v1/screenshot/history - 截图历史
                </el-button>
              </el-space>
            </div>
          </el-card>
        </div>

        <!-- 日志面板 -->
        <div v-show="activeTab === 'logs'" class="tab-panel">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>📝 操作日志</span>
                <el-button @click="clearLogs" size="small">清空日志</el-button>
              </div>
            </template>
            
            <div class="logs-container">
              <div v-if="logs.length === 0" class="no-logs">
                <el-empty description="暂无日志记录" />
              </div>
              <div v-else class="logs-list">
                <div v-for="log in logs" :key="log.id" class="log-item">
                  <el-tag :type="getLogType(log.level)" size="small">{{ log.level }}</el-tag>
                  <span class="log-message">{{ log.message }}</span>
                  <span class="log-time">{{ formatTime(log.timestamp) }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Link, Monitor, Tools, Document } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const activeTab = ref('connection-test')
const backendStatus = ref(false)
const testing = ref(false)
const testResults = ref([])
const systemStatus = ref(null)
const logs = ref([])

const loading = ref({
  systemStatus: false
})

// 方法
const handleTabSelect = (key) => {
  activeTab.value = key
  addLog('INFO', `切换到 ${getTabName(key)} 面板`)
}

const getTabName = (key) => {
  const names = {
    'connection-test': '连接测试',
    'system-status': '系统状态',
    'api-test': 'API测试',
    'logs': '日志查看'
  }
  return names[key] || key
}

const addLog = (level, message) => {
  logs.value.unshift({
    id: Date.now(),
    level,
    message,
    timestamp: new Date()
  })
  
  // 限制日志数量
  if (logs.value.length > 100) {
    logs.value = logs.value.slice(0, 100)
  }
}

const testFrontend = async () => {
  testing.value = true
  try {
    const response = await fetch('http://localhost:3000/')
    const success = response.ok
    testResults.value.unshift({
      id: Date.now(),
      success,
      message: success ? '前端服务连接成功' : `前端服务连接失败: ${response.status}`,
      timestamp: new Date()
    })
    addLog(success ? 'INFO' : 'ERROR', success ? '前端服务测试通过' : '前端服务测试失败')
  } catch (error) {
    testResults.value.unshift({
      id: Date.now(),
      success: false,
      message: `前端服务连接错误: ${error.message}`,
      timestamp: new Date()
    })
    addLog('ERROR', `前端服务测试异常: ${error.message}`)
  } finally {
    testing.value = false
  }
}

const testBackendHealth = async () => {
  testing.value = true
  try {
    const response = await fetch('http://localhost:8000/health')
    const success = response.ok
    if (success) {
      const data = await response.json()
      testResults.value.unshift({
        id: Date.now(),
        success: true,
        message: `后端健康检查成功: ${data.status}`,
        timestamp: new Date()
      })
      backendStatus.value = true
      addLog('INFO', '后端健康检查通过')
    } else {
      testResults.value.unshift({
        id: Date.now(),
        success: false,
        message: `后端健康检查失败: ${response.status}`,
        timestamp: new Date()
      })
      addLog('ERROR', '后端健康检查失败')
    }
  } catch (error) {
    testResults.value.unshift({
      id: Date.now(),
      success: false,
      message: `后端健康检查错误: ${error.message}`,
      timestamp: new Date()
    })
    backendStatus.value = false
    addLog('ERROR', `后端健康检查异常: ${error.message}`)
  } finally {
    testing.value = false
  }
}

const testBackendAPI = async () => {
  testing.value = true
  try {
    const response = await fetch('http://localhost:8000/api/v1/status')
    const success = response.ok
    if (success) {
      const data = await response.json()
      testResults.value.unshift({
        id: Date.now(),
        success: true,
        message: `后端API测试成功，系统状态: ${data.status}`,
        timestamp: new Date()
      })
      addLog('INFO', '后端API测试通过')
    } else {
      testResults.value.unshift({
        id: Date.now(),
        success: false,
        message: `后端API测试失败: ${response.status}`,
        timestamp: new Date()
      })
      addLog('ERROR', '后端API测试失败')
    }
  } catch (error) {
    testResults.value.unshift({
      id: Date.now(),
      success: false,
      message: `后端API测试错误: ${error.message}`,
      timestamp: new Date()
    })
    addLog('ERROR', `后端API测试异常: ${error.message}`)
  } finally {
    testing.value = false
  }
}

const testAllConnections = async () => {
  addLog('INFO', '开始执行全面连接测试')
  await testFrontend()
  await testBackendHealth()
  await testBackendAPI()
  addLog('INFO', '全面连接测试完成')
}

const refreshSystemStatus = async () => {
  loading.value.systemStatus = true
  addLog('INFO', '开始刷新系统状态')
  try {
    const response = await fetch('http://localhost:8000/api/v1/status')
    if (response.ok) {
      systemStatus.value = await response.json()
      ElMessage.success('系统状态刷新成功')
      addLog('INFO', '系统状态刷新成功')
    } else {
      ElMessage.error('系统状态刷新失败')
      addLog('ERROR', `系统状态刷新失败: ${response.status}`)
    }
  } catch (error) {
    ElMessage.error('API连接失败')
    addLog('ERROR', `系统状态刷新异常: ${error.message}`)
  } finally {
    loading.value.systemStatus = false
  }
}

const testAPI = async (endpoint, method = 'GET') => {
  testing.value = true
  const url = `http://localhost:8000${endpoint}`
  addLog('INFO', `测试API: ${method} ${endpoint}`)
  
  try {
    const response = await fetch(url, { method })
    const success = response.ok
    
    if (success) {
      const data = await response.json()
      testResults.value.unshift({
        id: Date.now(),
        success: true,
        message: `${method} ${endpoint} - 成功`,
        timestamp: new Date()
      })
      addLog('INFO', `API测试成功: ${method} ${endpoint}`)
    } else {
      testResults.value.unshift({
        id: Date.now(),
        success: false,
        message: `${method} ${endpoint} - 失败: ${response.status}`,
        timestamp: new Date()
      })
      addLog('ERROR', `API测试失败: ${method} ${endpoint} - ${response.status}`)
    }
  } catch (error) {
    testResults.value.unshift({
      id: Date.now(),
      success: false,
      message: `${method} ${endpoint} - 错误: ${error.message}`,
      timestamp: new Date()
    })
    addLog('ERROR', `API测试异常: ${method} ${endpoint} - ${error.message}`)
  } finally {
    testing.value = false
  }
}

const clearLogs = () => {
  logs.value = []
  addLog('INFO', '日志已清空')
}

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString()
}

const formatUptime = (seconds) => {
  if (!seconds) return '0秒'
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟${secs}秒`
  } else {
    return `${secs}秒`
  }
}

const getStatusType = (status) => {
  if (status === '运行中') return 'success'
  if (status === '已停止') return 'danger'
  return 'info'
}

const getLogType = (level) => {
  const types = {
    'INFO': 'info',
    'ERROR': 'danger',
    'WARNING': 'warning',
    'SUCCESS': 'success'
  }
  return types[level] || 'info'
}

// 生命周期
onMounted(async () => {
  addLog('INFO', '调试控制台已启动')
  // 自动测试后端连接
  await testBackendHealth()
})
</script>

<style scoped>
.debug-console-working {
  height: 100vh;
  background: #f5f5f5;
}

.header {
  background: white;
  padding: 20px;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header h1 {
  margin: 0;
  color: #409eff;
  font-size: 24px;
}

.status-indicators {
  display: flex;
  gap: 10px;
}

.main-container {
  height: calc(100vh - 80px);
}

.sidebar {
  background: white;
  border-right: 1px solid #e6e6e6;
}

.sidebar-menu {
  border-right: none;
}

.content-area {
  padding: 20px;
}

.tab-panel {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.connection-tests, .api-tests {
  padding: 20px 0;
}

.test-results {
  margin-top: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 8px 0;
  padding: 8px;
  background: white;
  border-radius: 4px;
  border-left: 3px solid #e6e6e6;
}

.result-text {
  flex: 1;
  font-size: 14px;
}

.result-time {
  font-size: 12px;
  color: #999;
}

.system-info {
  padding: 20px 0;
}

.game-state {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e6e6e6;
}

.no-data {
  padding: 40px 0;
  text-align: center;
}

.logs-container {
  max-height: 500px;
  overflow-y: auto;
}

.logs-list {
  padding: 10px 0;
}

.log-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 5px 0;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 4px;
}

.log-message {
  flex: 1;
  font-size: 14px;
}

.log-time {
  font-size: 12px;
  color: #999;
}

.no-logs {
  padding: 40px 0;
  text-align: center;
}
</style>
