"""
场景测试
测试游戏场景识别、管理和转换功能
"""

import unittest
import time
from unittest.mock import Mock, patch, MagicMock

from .base_test import SceneTestCase
from src.modules.ui.scenes.base_game_scene import BaseGameScene
from src.modules.ui.scenes.scene_manager import SceneManager
from src.modules.ui.scenes.training_scene import TrainingScene
from src.modules.ui.scenes.competition_scene import CompetitionScene
from src.modules.ui.config.scene_config import SceneConfig
from src.core.data_structures import GameScene


class TestBaseGameScene(SceneTestCase):
    """基础游戏场景测试"""
    
    def setUp(self):
        super().setUp()
        self.config = SceneConfig(
            scene_id="test_scene",
            timeout=10.0,
            retry_count=3
        )
    
    def test_scene_creation(self):
        """测试场景创建"""
        scene = BaseGameScene(GameScene.TRAINING, self.config)
        
        self.assertEqual(scene.scene_type, GameScene.TRAINING)
        self.assertEqual(scene.config, self.config)
        self.assertIsNotNone(scene.logger)
        self.assertFalse(scene._is_active)
    
    def test_scene_detection(self):
        """测试场景检测"""
        scene = BaseGameScene(GameScene.TRAINING, self.config)
        
        # Mock场景检测成功
        self.mock_perception.return_value.detect_scene.return_value = GameScene.TRAINING
        
        result = scene.is_scene_ready()
        self.assertTrue(result)
    
    def test_scene_detection_failure(self):
        """测试场景检测失败"""
        scene = BaseGameScene(GameScene.TRAINING, self.config)
        
        # Mock场景检测失败
        self.mock_perception.return_value.detect_scene.return_value = GameScene.UNKNOWN
        
        result = scene.is_scene_ready()
        self.assertFalse(result)
    
    def test_wait_for_scene_ready(self):
        """测试等待场景就绪"""
        scene = BaseGameScene(GameScene.TRAINING, self.config)
        
        # Mock场景立即就绪
        self.mock_perception.return_value.detect_scene.return_value = GameScene.TRAINING
        
        start_time = time.time()
        result = scene.wait_for_scene_ready()
        end_time = time.time()
        
        self.assertTrue(result)
        self.assertLess(end_time - start_time, 1.0)
    
    def test_wait_for_scene_timeout(self):
        """测试等待场景超时"""
        scene = BaseGameScene(GameScene.TRAINING, SceneConfig(timeout=1.0))
        
        # Mock场景不就绪
        self.mock_perception.return_value.detect_scene.return_value = GameScene.UNKNOWN
        
        start_time = time.time()
        result = scene.wait_for_scene_ready()
        end_time = time.time()
        
        self.assertFalse(result)
        self.assertGreaterEqual(end_time - start_time, 1.0)
    
    def test_scene_activation(self):
        """测试场景激活"""
        scene = BaseGameScene(GameScene.TRAINING, self.config)
        
        # Mock场景检测和UI元素
        self.mock_perception.return_value.detect_scene.return_value = GameScene.TRAINING
        
        result = scene.activate()
        self.assertTrue(result)
        self.assertTrue(scene._is_active)
    
    def test_scene_deactivation(self):
        """测试场景停用"""
        scene = BaseGameScene(GameScene.TRAINING, self.config)
        scene._is_active = True
        
        scene.deactivate()
        self.assertFalse(scene._is_active)


class TestTrainingScene(SceneTestCase):
    """训练场景测试"""
    
    def setUp(self):
        super().setUp()
        self.training_scene = TrainingScene(self.config)
    
    def test_training_scene_creation(self):
        """测试训练场景创建"""
        self.assertEqual(self.training_scene.scene_type, GameScene.TRAINING)
        self.assertIsNotNone(self.training_scene.ui_elements)
    
    def test_start_vocal_lesson(self):
        """测试开始声乐课程"""
        # Mock UI元素和操作
        mock_vocal_button = self.create_mock_ui_element("vocal_lesson_button")
        self.training_scene.ui_elements["vocal_lesson_button"] = mock_vocal_button
        
        mock_vocal_button.click.return_value = True
        self.mock_perception.return_value.detect_scene.return_value = GameScene.TRAINING
        
        result = self.training_scene.start_vocal_lesson()
        self.assertTrue(result)
        mock_vocal_button.click.assert_called_once()
    
    def test_start_dance_lesson(self):
        """测试开始舞蹈课程"""
        mock_dance_button = self.create_mock_ui_element("dance_lesson_button")
        self.training_scene.ui_elements["dance_lesson_button"] = mock_dance_button
        
        mock_dance_button.click.return_value = True
        self.mock_perception.return_value.detect_scene.return_value = GameScene.TRAINING
        
        result = self.training_scene.start_dance_lesson()
        self.assertTrue(result)
        mock_dance_button.click.assert_called_once()
    
    def test_get_training_status(self):
        """测试获取训练状态"""
        # Mock状态标签
        mock_status_label = self.create_mock_ui_element("status_label", "label")
        mock_status_label.get_text.return_value = "训练中"
        self.training_scene.ui_elements["status_label"] = mock_status_label
        
        status = self.training_scene.get_training_status()
        self.assertEqual(status["status"], "训练中")
    
    def test_check_stamina(self):
        """测试检查体力"""
        mock_stamina_label = self.create_mock_ui_element("stamina_label", "label")
        mock_stamina_label.get_text.return_value = "75/100"
        self.training_scene.ui_elements["stamina_label"] = mock_stamina_label
        
        stamina = self.training_scene.check_stamina()
        self.assertEqual(stamina["current"], 75)
        self.assertEqual(stamina["max"], 100)
    
    def test_rest_action(self):
        """测试休息行动"""
        mock_rest_button = self.create_mock_ui_element("rest_button")
        self.training_scene.ui_elements["rest_button"] = mock_rest_button
        
        mock_rest_button.click.return_value = True
        
        result = self.training_scene.rest()
        self.assertTrue(result)
        mock_rest_button.click.assert_called_once()


class TestCompetitionScene(SceneTestCase):
    """比赛场景测试"""
    
    def setUp(self):
        super().setUp()
        self.competition_scene = CompetitionScene(self.config)
    
    def test_competition_scene_creation(self):
        """测试比赛场景创建"""
        self.assertEqual(self.competition_scene.scene_type, GameScene.COMPETITION)
        self.assertIsNotNone(self.competition_scene.ui_elements)
    
    def test_start_competition(self):
        """测试开始比赛"""
        mock_start_button = self.create_mock_ui_element("start_competition_button")
        self.competition_scene.ui_elements["start_competition_button"] = mock_start_button
        
        mock_start_button.click.return_value = True
        self.mock_perception.return_value.detect_scene.return_value = GameScene.COMPETITION
        
        result = self.competition_scene.start_competition()
        self.assertTrue(result)
        mock_start_button.click.assert_called_once()
    
    def test_get_competition_results(self):
        """测试获取比赛结果"""
        mock_result_label = self.create_mock_ui_element("result_label", "label")
        mock_result_label.get_text.return_value = "第1名"
        self.competition_scene.ui_elements["result_label"] = mock_result_label
        
        results = self.competition_scene.get_competition_results()
        self.assertEqual(results["rank"], "第1名")
    
    def test_check_competition_status(self):
        """测试检查比赛状态"""
        mock_status_label = self.create_mock_ui_element("competition_status_label", "label")
        mock_status_label.get_text.return_value = "比赛进行中"
        self.competition_scene.ui_elements["competition_status_label"] = mock_status_label
        
        status = self.competition_scene.check_competition_status()
        self.assertEqual(status["status"], "比赛进行中")


class TestSceneManager(SceneTestCase):
    """场景管理器测试"""
    
    def setUp(self):
        super().setUp()
        self.scene_manager = SceneManager()
    
    def test_scene_manager_creation(self):
        """测试场景管理器创建"""
        self.assertIsNotNone(self.scene_manager.logger)
        self.assertIsNone(self.scene_manager.current_scene)
        self.assertIsInstance(self.scene_manager.registered_scenes, dict)
    
    def test_register_scene(self):
        """测试注册场景"""
        scene = self.create_mock_scene("training")
        
        self.scene_manager.register_scene(GameScene.TRAINING, scene)
        
        self.assertIn(GameScene.TRAINING, self.scene_manager.registered_scenes)
        self.assertEqual(self.scene_manager.registered_scenes[GameScene.TRAINING], scene)
    
    def test_detect_current_scene(self):
        """测试检测当前场景"""
        # Mock场景检测
        self.mock_perception.return_value.detect_scene.return_value = GameScene.TRAINING
        
        detected_scene = self.scene_manager.detect_current_scene()
        self.assertEqual(detected_scene, GameScene.TRAINING)
    
    def test_switch_to_scene_success(self):
        """测试切换场景成功"""
        # 注册场景
        training_scene = self.create_mock_scene("training", ready=True)
        self.scene_manager.register_scene(GameScene.TRAINING, training_scene)
        
        # Mock场景检测
        self.mock_perception.return_value.detect_scene.return_value = GameScene.TRAINING
        
        start_time = time.time()
        result = self.scene_manager.switch_to_scene(GameScene.TRAINING)
        end_time = time.time()
        
        self.assertTrue(result)
        self.assertEqual(self.scene_manager.current_scene, training_scene)
        
        # 验证场景转换性能
        self.assert_scene_transition("unknown", "training", end_time - start_time)
    
    def test_switch_to_scene_failure(self):
        """测试切换场景失败"""
        # 注册场景但设置为不就绪
        training_scene = self.create_mock_scene("training", ready=False)
        self.scene_manager.register_scene(GameScene.TRAINING, training_scene)
        
        result = self.scene_manager.switch_to_scene(GameScene.TRAINING)
        self.assertFalse(result)
        self.assertIsNone(self.scene_manager.current_scene)
    
    def test_switch_to_unregistered_scene(self):
        """测试切换到未注册的场景"""
        result = self.scene_manager.switch_to_scene(GameScene.COMPETITION)
        self.assertFalse(result)
    
    def test_get_scene_info(self):
        """测试获取场景信息"""
        training_scene = self.create_mock_scene("training", state="active")
        self.scene_manager.register_scene(GameScene.TRAINING, training_scene)
        self.scene_manager.current_scene = training_scene
        
        info = self.scene_manager.get_scene_info()
        
        self.assertIn("current_scene", info)
        self.assertIn("registered_scenes", info)
        self.assertEqual(info["current_scene"], "training")
    
    def test_scene_transition_history(self):
        """测试场景转换历史"""
        # 注册多个场景
        training_scene = self.create_mock_scene("training", ready=True)
        competition_scene = self.create_mock_scene("competition", ready=True)
        
        self.scene_manager.register_scene(GameScene.TRAINING, training_scene)
        self.scene_manager.register_scene(GameScene.COMPETITION, competition_scene)
        
        # Mock场景检测
        self.mock_perception.return_value.detect_scene.side_effect = [
            GameScene.TRAINING, GameScene.COMPETITION
        ]
        
        # 执行场景转换
        self.scene_manager.switch_to_scene(GameScene.TRAINING)
        self.scene_manager.switch_to_scene(GameScene.COMPETITION)
        
        # 检查历史记录
        history = self.scene_manager.get_transition_history()
        self.assertEqual(len(history), 2)
        self.assertEqual(history[0]["to_scene"], GameScene.TRAINING)
        self.assertEqual(history[1]["to_scene"], GameScene.COMPETITION)


class TestSceneIntegration(SceneTestCase):
    """场景集成测试"""
    
    def test_complete_training_workflow(self):
        """测试完整的训练工作流"""
        # 创建训练场景
        training_scene = TrainingScene(self.config)
        
        # Mock UI元素
        mock_vocal_button = self.create_mock_ui_element("vocal_lesson_button")
        mock_status_label = self.create_mock_ui_element("status_label", "label")
        mock_stamina_label = self.create_mock_ui_element("stamina_label", "label")
        
        training_scene.ui_elements.update({
            "vocal_lesson_button": mock_vocal_button,
            "status_label": mock_status_label,
            "stamina_label": mock_stamina_label
        })
        
        # Mock返回值
        mock_vocal_button.click.return_value = True
        mock_status_label.get_text.return_value = "训练完成"
        mock_stamina_label.get_text.return_value = "50/100"
        
        # Mock场景检测
        self.mock_perception.return_value.detect_scene.return_value = GameScene.TRAINING
        
        # 定义测试步骤
        steps = [
            {
                'name': '激活训练场景',
                'function': training_scene.activate,
                'expected': True
            },
            {
                'name': '开始声乐课程',
                'function': training_scene.start_vocal_lesson,
                'expected': True
            },
            {
                'name': '检查训练状态',
                'function': training_scene.get_training_status,
                'expected': {"status": "训练完成"}
            },
            {
                'name': '检查体力',
                'function': training_scene.check_stamina,
                'expected': {"current": 50, "max": 100}
            }
        ]
        
        # 运行集成测试
        results = self.run_integration_test("完整训练工作流", steps)
        
        # 验证所有步骤都成功
        for result in results:
            self.assertTrue(result['success'], f"步骤失败: {result['step']}")
    
    def test_scene_manager_workflow(self):
        """测试场景管理器工作流"""
        scene_manager = SceneManager()
        
        # 创建和注册场景
        training_scene = self.create_mock_scene("training", ready=True)
        competition_scene = self.create_mock_scene("competition", ready=True)
        
        scene_manager.register_scene(GameScene.TRAINING, training_scene)
        scene_manager.register_scene(GameScene.COMPETITION, competition_scene)
        
        # Mock场景检测
        self.mock_perception.return_value.detect_scene.side_effect = [
            GameScene.TRAINING, GameScene.COMPETITION, GameScene.TRAINING
        ]
        
        # 定义测试步骤
        steps = [
            {
                'name': '切换到训练场景',
                'function': scene_manager.switch_to_scene,
                'args': [GameScene.TRAINING],
                'expected': True
            },
            {
                'name': '切换到比赛场景',
                'function': scene_manager.switch_to_scene,
                'args': [GameScene.COMPETITION],
                'expected': True
            },
            {
                'name': '切换回训练场景',
                'function': scene_manager.switch_to_scene,
                'args': [GameScene.TRAINING],
                'expected': True
            }
        ]
        
        # 运行集成测试
        results = self.run_integration_test("场景管理器工作流", steps)
        
        # 验证转换历史
        history = scene_manager.get_transition_history()
        self.assertEqual(len(history), 3)


if __name__ == '__main__':
    unittest.main()
