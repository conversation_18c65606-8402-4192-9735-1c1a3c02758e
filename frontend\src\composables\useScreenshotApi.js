/**
 * 截图API组合式函数
 * 提供所有截图相关的API调用方法
 */

import { ref } from 'vue'

const API_BASE = '/api/v1'

export function useScreenshotApi() {
  const loading = ref(false)
  const error = ref(null)

  /**
   * 通用API请求方法
   */
  async function apiRequest(url, options = {}) {
    try {
      loading.value = true
      error.value = null

      const response = await fetch(`${API_BASE}${url}`, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        },
        ...options
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`)
      }

      // 如果是下载请求，返回blob
      if (options.responseType === 'blob') {
        return await response.blob()
      }

      // 如果响应为空，返回null
      const contentType = response.headers.get('content-type')
      if (!contentType || !contentType.includes('application/json')) {
        return null
      }

      return await response.json()

    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 执行截图
   */
  async function captureScreenshot(request) {
    const response = await apiRequest('/screenshot/capture', {
      method: 'POST',
      body: JSON.stringify(request)
    })
    return response
  }

  /**
   * 获取截图预览
   */
  async function getScreenshotPreview() {
    const blob = await apiRequest('/screenshot/preview', {
      responseType: 'blob'
    })
    return URL.createObjectURL(blob)
  }

  /**
   * 获取截图历史记录
   */
  async function getScreenshotHistory(limit = 100) {
    const response = await apiRequest(`/screenshot/history?limit=${limit}`)
    return response
  }

  /**
   * 删除截图
   */
  async function deleteScreenshot(screenshotId) {
    const response = await apiRequest(`/screenshot/${screenshotId}`, {
      method: 'DELETE'
    })
    return response?.success || false
  }

  /**
   * 批量删除截图
   */
  async function batchDeleteScreenshots(screenshotIds) {
    const response = await apiRequest('/screenshot/batch-delete', {
      method: 'POST',
      body: JSON.stringify({ ids: screenshotIds })
    })
    return response
  }

  /**
   * 下载截图
   */
  async function downloadScreenshot(screenshotId) {
    try {
      const response = await fetch(`${API_BASE}/screenshot/${screenshotId}/download`)
      
      if (!response.ok) {
        throw new Error(`下载失败: ${response.statusText}`)
      }

      const blob = await response.blob()
      const contentDisposition = response.headers.get('Content-Disposition')
      
      // 从Content-Disposition头中提取文件名
      let filename = 'screenshot.png'
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
        if (filenameMatch && filenameMatch[1]) {
          filename = filenameMatch[1].replace(/['"]/g, '')
        }
      }

      // 创建下载链接
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = filename
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      return true

    } catch (err) {
      error.value = err.message
      throw err
    }
  }

  /**
   * 获取截图统计信息
   */
  async function getScreenshotStats() {
    const response = await apiRequest('/screenshot/stats')
    return response
  }

  /**
   * 获取截图配置
   */
  async function getScreenshotConfig() {
    const response = await apiRequest('/screenshot/config')
    return response
  }

  /**
   * 更新截图配置
   */
  async function updateScreenshotConfig(config) {
    const response = await apiRequest('/screenshot/config', {
      method: 'POST',
      body: JSON.stringify(config)
    })
    return response
  }

  /**
   * 获取WebSocket统计信息
   */
  async function getWebSocketStats() {
    const response = await apiRequest('/websocket/stats')
    return response?.data || {}
  }

  /**
   * 获取系统状态
   */
  async function getSystemStatus() {
    const response = await apiRequest('/status')
    return response
  }

  /**
   * 系统控制
   */
  async function controlSystem(action, parameters = {}) {
    const response = await apiRequest('/control', {
      method: 'POST',
      body: JSON.stringify({ action, parameters })
    })
    return response
  }

  /**
   * 健康检查
   */
  async function healthCheck() {
    const response = await apiRequest('/health')
    return response
  }

  return {
    // 状态
    loading,
    error,

    // 截图相关
    captureScreenshot,
    getScreenshotPreview,
    getScreenshotHistory,
    deleteScreenshot,
    batchDeleteScreenshots,
    downloadScreenshot,
    getScreenshotStats,
    getScreenshotConfig,
    updateScreenshotConfig,

    // 系统相关
    getWebSocketStats,
    getSystemStatus,
    controlSystem,
    healthCheck,

    // 工具方法
    apiRequest
  }
}
