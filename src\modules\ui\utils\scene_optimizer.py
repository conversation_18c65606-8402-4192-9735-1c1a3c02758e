"""
场景性能优化器
提供场景级别的性能优化功能
"""

import time
from typing import Dict, List, Any, Optional, Set
from collections import defaultdict
import threading

from ....utils.logger import get_logger
from ....core.data_structures import GameScene
from .performance_monitor import get_performance_monitor, measure_block


class SceneOptimizer:
    """场景性能优化器"""
    
    def __init__(self):
        """初始化场景优化器"""
        self.logger = get_logger("SceneOptimizer")
        self.performance_monitor = get_performance_monitor()
        
        # 优化配置
        self._optimization_enabled = True
        self._cache_optimization = True
        self._preload_optimization = True
        self._memory_optimization = True
        
        # 场景使用统计
        self._scene_usage_stats: Dict[GameScene, Dict[str, Any]] = defaultdict(lambda: {
            'access_count': 0,
            'total_load_time': 0.0,
            'avg_load_time': 0.0,
            'last_access_time': 0.0,
            'ui_element_usage': defaultdict(int),
            'navigation_patterns': defaultdict(int)
        })
        
        # 优化建议
        self._optimization_suggestions: List[Dict[str, Any]] = []
        
        # 线程锁
        self._lock = threading.RLock()
        
        self.logger.info("场景优化器初始化完成")
    
    def record_scene_access(self, scene_type: GameScene, load_time: float = 0.0):
        """
        记录场景访问
        
        Args:
            scene_type: 场景类型
            load_time: 加载时间
        """
        if not self._optimization_enabled:
            return
        
        try:
            with self._lock:
                stats = self._scene_usage_stats[scene_type]
                stats['access_count'] += 1
                stats['last_access_time'] = time.time()
                
                if load_time > 0:
                    stats['total_load_time'] += load_time
                    stats['avg_load_time'] = stats['total_load_time'] / stats['access_count']
                
                self.logger.debug(f"记录场景访问: {scene_type.value}, 访问次数: {stats['access_count']}")
                
        except Exception as e:
            self.logger.error(f"记录场景访问失败: {e}")
    
    def record_ui_element_usage(self, scene_type: GameScene, element_name: str):
        """
        记录UI元素使用
        
        Args:
            scene_type: 场景类型
            element_name: 元素名称
        """
        if not self._optimization_enabled:
            return
        
        try:
            with self._lock:
                stats = self._scene_usage_stats[scene_type]
                stats['ui_element_usage'][element_name] += 1
                
                self.logger.debug(f"记录UI元素使用: {scene_type.value}.{element_name}")
                
        except Exception as e:
            self.logger.error(f"记录UI元素使用失败: {e}")
    
    def record_navigation_pattern(self, from_scene: GameScene, to_scene: GameScene):
        """
        记录导航模式
        
        Args:
            from_scene: 源场景
            to_scene: 目标场景
        """
        if not self._optimization_enabled:
            return
        
        try:
            with self._lock:
                pattern_key = f"{from_scene.value}->{to_scene.value}"
                stats = self._scene_usage_stats[from_scene]
                stats['navigation_patterns'][pattern_key] += 1
                
                self.logger.debug(f"记录导航模式: {pattern_key}")
                
        except Exception as e:
            self.logger.error(f"记录导航模式失败: {e}")
    
    def get_hot_scenes(self, top_n: int = 5) -> List[GameScene]:
        """
        获取热门场景
        
        Args:
            top_n: 返回前N个热门场景
            
        Returns:
            热门场景列表
        """
        try:
            with self._lock:
                # 按访问次数排序
                sorted_scenes = sorted(
                    self._scene_usage_stats.items(),
                    key=lambda x: x[1]['access_count'],
                    reverse=True
                )
                
                return [scene for scene, _ in sorted_scenes[:top_n]]
                
        except Exception as e:
            self.logger.error(f"获取热门场景失败: {e}")
            return []
    
    def get_slow_loading_scenes(self, threshold: float = 2.0) -> List[GameScene]:
        """
        获取加载缓慢的场景
        
        Args:
            threshold: 加载时间阈值（秒）
            
        Returns:
            加载缓慢的场景列表
        """
        try:
            with self._lock:
                slow_scenes = []
                
                for scene_type, stats in self._scene_usage_stats.items():
                    avg_load_time = stats.get('avg_load_time', 0.0)
                    if avg_load_time > threshold:
                        slow_scenes.append(scene_type)
                
                return slow_scenes
                
        except Exception as e:
            self.logger.error(f"获取加载缓慢场景失败: {e}")
            return []
    
    def get_unused_ui_elements(self, scene_type: GameScene, threshold: int = 0) -> List[str]:
        """
        获取未使用的UI元素
        
        Args:
            scene_type: 场景类型
            threshold: 使用次数阈值
            
        Returns:
            未使用的UI元素列表
        """
        try:
            with self._lock:
                stats = self._scene_usage_stats.get(scene_type, {})
                ui_usage = stats.get('ui_element_usage', {})
                
                # 这里需要从场景配置中获取所有UI元素
                # 简化实现，返回使用次数低于阈值的元素
                unused_elements = [
                    element for element, count in ui_usage.items()
                    if count <= threshold
                ]
                
                return unused_elements
                
        except Exception as e:
            self.logger.error(f"获取未使用UI元素失败: {e}")
            return []
    
    def generate_optimization_suggestions(self) -> List[Dict[str, Any]]:
        """
        生成优化建议
        
        Returns:
            优化建议列表
        """
        try:
            suggestions = []
            
            # 建议预加载热门场景
            hot_scenes = self.get_hot_scenes(3)
            if hot_scenes:
                suggestions.append({
                    'type': 'preload',
                    'priority': 'high',
                    'description': f'建议预加载热门场景: {[s.value for s in hot_scenes]}',
                    'scenes': hot_scenes,
                    'estimated_benefit': 'high'
                })
            
            # 建议优化加载缓慢的场景
            slow_scenes = self.get_slow_loading_scenes(1.5)
            if slow_scenes:
                suggestions.append({
                    'type': 'performance',
                    'priority': 'medium',
                    'description': f'建议优化加载缓慢的场景: {[s.value for s in slow_scenes]}',
                    'scenes': slow_scenes,
                    'estimated_benefit': 'medium'
                })
            
            # 建议缓存优化
            with self._lock:
                total_accesses = sum(
                    stats['access_count'] 
                    for stats in self._scene_usage_stats.values()
                )
                
                if total_accesses > 50:
                    suggestions.append({
                        'type': 'cache',
                        'priority': 'medium',
                        'description': '建议启用更积极的缓存策略',
                        'estimated_benefit': 'medium'
                    })
            
            # 建议内存优化
            unused_elements_count = 0
            for scene_type in self._scene_usage_stats.keys():
                unused_elements_count += len(self.get_unused_ui_elements(scene_type))
            
            if unused_elements_count > 10:
                suggestions.append({
                    'type': 'memory',
                    'priority': 'low',
                    'description': f'发现{unused_elements_count}个未使用的UI元素，建议延迟加载',
                    'estimated_benefit': 'low'
                })
            
            self._optimization_suggestions = suggestions
            return suggestions
            
        except Exception as e:
            self.logger.error(f"生成优化建议失败: {e}")
            return []
    
    def apply_optimization(self, suggestion: Dict[str, Any]) -> bool:
        """
        应用优化建议
        
        Args:
            suggestion: 优化建议
            
        Returns:
            是否应用成功
        """
        try:
            suggestion_type = suggestion.get('type')
            
            if suggestion_type == 'preload':
                return self._apply_preload_optimization(suggestion)
            elif suggestion_type == 'performance':
                return self._apply_performance_optimization(suggestion)
            elif suggestion_type == 'cache':
                return self._apply_cache_optimization(suggestion)
            elif suggestion_type == 'memory':
                return self._apply_memory_optimization(suggestion)
            else:
                self.logger.warning(f"未知的优化类型: {suggestion_type}")
                return False
                
        except Exception as e:
            self.logger.error(f"应用优化建议失败: {e}")
            return False
    
    def _apply_preload_optimization(self, suggestion: Dict[str, Any]) -> bool:
        """应用预加载优化"""
        try:
            scenes = suggestion.get('scenes', [])
            self.logger.info(f"应用预加载优化: {[s.value for s in scenes]}")
            
            # 这里可以与SceneFactory集成，实现预加载
            # 简化实现，只记录日志
            return True
            
        except Exception as e:
            self.logger.error(f"应用预加载优化失败: {e}")
            return False
    
    def _apply_performance_optimization(self, suggestion: Dict[str, Any]) -> bool:
        """应用性能优化"""
        try:
            scenes = suggestion.get('scenes', [])
            self.logger.info(f"应用性能优化: {[s.value for s in scenes]}")
            
            # 这里可以实现具体的性能优化策略
            # 例如：调整缓存策略、优化UI元素创建等
            return True
            
        except Exception as e:
            self.logger.error(f"应用性能优化失败: {e}")
            return False
    
    def _apply_cache_optimization(self, suggestion: Dict[str, Any]) -> bool:
        """应用缓存优化"""
        try:
            self.logger.info("应用缓存优化")
            self._cache_optimization = True
            return True
            
        except Exception as e:
            self.logger.error(f"应用缓存优化失败: {e}")
            return False
    
    def _apply_memory_optimization(self, suggestion: Dict[str, Any]) -> bool:
        """应用内存优化"""
        try:
            self.logger.info("应用内存优化")
            self._memory_optimization = True
            return True
            
        except Exception as e:
            self.logger.error(f"应用内存优化失败: {e}")
            return False
    
    def get_optimization_report(self) -> Dict[str, Any]:
        """
        获取优化报告
        
        Returns:
            优化报告
        """
        try:
            with self._lock:
                # 计算总体统计
                total_accesses = sum(
                    stats['access_count'] 
                    for stats in self._scene_usage_stats.values()
                )
                
                total_load_time = sum(
                    stats['total_load_time'] 
                    for stats in self._scene_usage_stats.values()
                )
                
                avg_load_time = total_load_time / max(total_accesses, 1)
                
                # 生成报告
                report = {
                    'summary': {
                        'total_scene_accesses': total_accesses,
                        'total_load_time': total_load_time,
                        'average_load_time': avg_load_time,
                        'unique_scenes_accessed': len(self._scene_usage_stats),
                        'optimization_enabled': self._optimization_enabled
                    },
                    'hot_scenes': [s.value for s in self.get_hot_scenes()],
                    'slow_scenes': [s.value for s in self.get_slow_loading_scenes()],
                    'optimization_suggestions': self.generate_optimization_suggestions(),
                    'scene_details': {
                        scene.value: stats.copy() 
                        for scene, stats in self._scene_usage_stats.items()
                    }
                }
                
                return report
                
        except Exception as e:
            self.logger.error(f"获取优化报告失败: {e}")
            return {}
    
    def reset_stats(self):
        """重置统计数据"""
        try:
            with self._lock:
                self._scene_usage_stats.clear()
                self._optimization_suggestions.clear()
                
                self.logger.info("场景优化器统计数据已重置")
                
        except Exception as e:
            self.logger.error(f"重置统计数据失败: {e}")
    
    def set_optimization_enabled(self, enabled: bool):
        """设置优化状态"""
        self._optimization_enabled = enabled
        self.logger.info(f"场景优化器{'启用' if enabled else '禁用'}")


# 全局场景优化器实例
_global_optimizer: Optional[SceneOptimizer] = None


def get_scene_optimizer() -> SceneOptimizer:
    """获取全局场景优化器实例"""
    global _global_optimizer
    if _global_optimizer is None:
        _global_optimizer = SceneOptimizer()
    return _global_optimizer
