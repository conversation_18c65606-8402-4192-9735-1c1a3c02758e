# Gakumasu-Bot 完整配置文件示例
# 此文件包含所有可配置的参数及其说明
# 复制此文件为 settings.yaml 并根据需要修改

# ============================================================================
# 系统基础配置
# ============================================================================
system:
  # 日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL
  log_level: INFO
  
  # 游戏语言: ja (日语), cn (中文)
  language: ja
  
  # 屏幕分辨率 [宽度, 高度]
  screen_resolution: [1920, 1080]
  
  # 游戏窗口标题（用于窗口识别）
  game_window_title: "gakumas"
  
  # 工作目录配置
  working_directory: "."
  
  # 临时文件目录
  temp_directory: "temp"

# ============================================================================
# DMM Player 配置
# ============================================================================
dmm:
  # DMM Player 可执行文件路径（必须配置）
  dmm_player_path: "C:/Users/<USER>/AppData/Local/DMM Game Player/DMMGamePlayer.exe"
  
  # 游戏图标模板文件路径
  game_icon_template: "assets/templates/dmm_gakumasu_icon.png"
  
  # 启动超时时间（秒）
  launch_timeout: 60
  
  # 启动后等待时间（秒）
  post_launch_wait: 10
  
  # 是否自动启动游戏
  auto_launch: true

# ============================================================================
# 性能优化配置
# ============================================================================
performance:
  # 屏幕截图间隔（秒）
  screenshot_interval: 0.5
  
  # 操作延迟范围（秒）
  action_delay_min: 0.05
  action_delay_max: 0.15
  
  # 决策超时时间（秒）
  decision_timeout: 30.0
  
  # 是否启用GPU加速
  enable_gpu_acceleration: false
  
  # 最大并发线程数
  max_worker_threads: 4
  
  # 内存缓存大小（MB）
  memory_cache_size: 256
  
  # 是否启用性能监控
  enable_performance_monitoring: true

# ============================================================================
# 计算机视觉配置
# ============================================================================
template_matching:
  # 模板文件目录
  template_directory: "assets/templates"
  
  # 匹配置信度阈值
  confidence_threshold: 0.8
  
  # 是否启用多尺度匹配
  enable_multi_scale: true
  
  # 尺度范围 [最小, 最大]
  scale_range: [0.8, 1.2]
  
  # 尺度步长
  scale_step: 0.1
  
  # 匹配方法: TM_CCOEFF_NORMED, TM_CCORR_NORMED, TM_SQDIFF_NORMED
  matching_method: "TM_CCOEFF_NORMED"
  
  # 是否启用模板缓存
  enable_template_cache: true
  
  # 缓存过期时间（秒）
  cache_expiry_time: 300

# ============================================================================
# OCR 识别配置
# ============================================================================
ocr:
  # 是否启用OCR功能
  enable_ocr: true
  
  # 支持的语言列表
  languages: ['ja', 'en']
  
  # OCR置信度阈值
  confidence_threshold: 0.7
  
  # 是否使用GPU加速
  use_gpu: false
  
  # 文本预处理选项
  preprocessing:
    enable_denoising: true
    enable_contrast_enhancement: true
    enable_binarization: true
  
  # OCR引擎设置
  engine_settings:
    # 文本检测模式: 0=方向和脚本检测, 1=自动页面分割
    page_segmentation_mode: 1
    
    # 字符白名单（为空则不限制）
    character_whitelist: ""

# ============================================================================
# AI 决策配置
# ============================================================================
ai:
  # 是否启用MCTS算法
  enable_mcts: true
  
  # MCTS迭代次数
  mcts_iterations: 1000
  
  # MCTS超时时间（秒）
  mcts_timeout: 10.0
  
  # MCTS探索参数
  mcts_exploration_constant: 1.414
  
  # 启发式权重配置
  heuristic_weights:
    score: 1.0          # 分数权重
    stamina: 0.8        # 体力权重
    vigor: 0.6          # 元气权重
    card_synergy: 0.4   # 卡牌协同权重
    risk_factor: 0.3    # 风险因子权重
  
  # 决策缓存设置
  enable_decision_cache: true
  decision_cache_size: 1000
  
  # 学习率设置
  learning_rate: 0.01
  
  # 是否启用在线学习
  enable_online_learning: false

# ============================================================================
# 错误处理和恢复配置
# ============================================================================
error_handling:
  # 最大重试次数
  max_retries: 3
  
  # 重试间隔（秒）
  retry_interval: 2.0
  
  # 是否启用自动恢复
  enable_auto_recovery: true
  
  # 恢复策略: restart, skip, abort
  recovery_strategy: "restart"
  
  # 错误日志详细程度: minimal, normal, verbose
  error_log_level: "normal"
  
  # 是否保存错误截图
  save_error_screenshots: true
  
  # 错误截图保存目录
  error_screenshot_directory: "logs/error_screenshots"

# ============================================================================
# 调试和开发配置
# ============================================================================
debug:
  # 是否启用调试模式
  enable_debug_mode: false
  
  # 是否保存调试截图
  save_debug_screenshots: false
  
  # 调试截图保存目录
  debug_screenshot_directory: "logs/debug_screenshots"
  
  # 是否启用详细日志
  enable_verbose_logging: false
  
  # 是否显示匹配结果可视化
  show_match_visualization: false
  
  # 调试信息输出间隔（秒）
  debug_info_interval: 5.0

# ============================================================================
# 网络和API配置
# ============================================================================
network:
  # Web API服务端口
  api_port: 8000
  
  # 是否启用CORS
  enable_cors: true
  
  # 允许的源域名
  allowed_origins: ["http://localhost:3000", "http://127.0.0.1:3000"]
  
  # API请求超时时间（秒）
  api_timeout: 30.0
  
  # WebSocket连接超时时间（秒）
  websocket_timeout: 60.0

# ============================================================================
# 安全和隐私配置
# ============================================================================
security:
  # 是否启用API认证
  enable_api_auth: false
  
  # API密钥（如果启用认证）
  api_key: ""
  
  # 是否记录敏感信息
  log_sensitive_data: false
  
  # 数据加密密钥
  encryption_key: ""
  
  # 是否启用数据匿名化
  enable_data_anonymization: true

# ============================================================================
# 文件路径配置
# ============================================================================
paths:
  # 配置文件目录
  config_directory: "config"
  
  # 数据文件目录
  data_directory: "data"
  
  # 资源文件目录
  assets_directory: "assets"
  
  # 日志文件目录
  logs_directory: "logs"
  
  # 临时文件目录
  temp_directory: "temp"
  
  # 备份文件目录
  backup_directory: "backup"

# ============================================================================
# 实验性功能配置
# ============================================================================
experimental:
  # 是否启用实验性功能
  enable_experimental_features: false
  
  # 实验性AI算法
  enable_experimental_ai: false
  
  # 实验性UI识别
  enable_experimental_ui_detection: false
  
  # 实验性性能优化
  enable_experimental_performance_optimizations: false
