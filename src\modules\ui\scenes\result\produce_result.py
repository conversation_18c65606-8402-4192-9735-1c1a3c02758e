"""
育成结果场景实现
处理育成完成后的最终结果展示、成就统计和历史记录保存
"""

import time
from typing import List, Dict, Any, Optional, Tuple
from enum import Enum
from datetime import datetime

from .....core.data_structures import GameScene
from ...config.scene_config import SceneConfig
from ...config.ui_element_config import ButtonConfig, LabelConfig
from ..base_game_scene import BaseGameScene
from ...utils.performance_monitor import measure_block
from ...utils.scene_optimizer import get_scene_optimizer


class ProduceResultType(Enum):
    """育成结果类型枚举"""
    SUCCESS = "success"        # 成功完成
    FAILURE = "failure"        # 失败
    TIMEOUT = "timeout"        # 超时
    INTERRUPTED = "interrupted" # 中断


class AchievementLevel(Enum):
    """成就等级枚举"""
    LEGENDARY = "legendary"    # 传说级
    EPIC = "epic"             # 史诗级
    RARE = "rare"             # 稀有级
    COMMON = "common"         # 普通级


class StatCategory(Enum):
    """统计类别枚举"""
    FINAL_STATS = "final_stats"        # 最终属性
    GROWTH_STATS = "growth_stats"      # 成长统计
    ACTIVITY_STATS = "activity_stats"  # 活动统计
    ACHIEVEMENT_STATS = "achievement_stats"  # 成就统计


class ProduceResultScene(BaseGameScene):
    """育成结果场景类"""
    
    def __init__(self, config: SceneConfig, perception_module, action_controller):
        """
        初始化育成结果场景
        
        Args:
            config: 场景配置
            perception_module: 感知模块
            action_controller: 行动控制器
        """
        super().__init__(config, perception_module, action_controller)
        
        # 结果数据
        self._result_type: Optional[ProduceResultType] = None
        self._final_rank = "C"
        self._total_score = 0
        self._completion_rate = 0.0
        
        # 最终属性
        self._final_stats = {
            "vocal": 0, "dance": 0, "visual": 0, "mental": 0,
            "stamina": 100, "motivation": 3, "level": 1
        }
        
        # 成长统计
        self._growth_stats = {
            "total_lessons": 0,
            "total_rest": 0,
            "total_outings": 0,
            "total_battles": 0,
            "total_exams": 0,
            "days_completed": 0,
            "events_triggered": 0
        }
        
        # 成就数据
        self._achievements: List[Dict[str, Any]] = []
        self._milestones: List[Dict[str, Any]] = []
        self._records: List[Dict[str, Any]] = []
        
        # 历史数据
        self._produce_history = {
            "start_date": None,
            "end_date": None,
            "duration_days": 0,
            "idol_name": "",
            "strategy_used": "",
            "final_evaluation": ""
        }
        
        # 比较数据
        self._comparison_data = {
            "previous_best": {},
            "average_performance": {},
            "ranking_position": 0
        }
        
        # 性能监控
        self.optimizer = get_scene_optimizer()
        
        self.scene_logger.info("育成结果场景初始化完成")
    
    def _create_scene_ui_elements(self):
        """创建结果场景特定的UI元素"""
        try:
            with measure_block("result_ui_creation"):
                # 场景标识元素
                self._create_result_indicators()
                
                # 结果展示元素
                self._create_result_displays()
                
                # 属性展示元素
                self._create_stats_displays()
                
                # 成就展示元素
                self._create_achievement_displays()
                
                # 统计信息元素
                self._create_statistics_displays()
                
                # 历史记录元素
                self._create_history_displays()
                
                # 控制按钮
                self._create_control_buttons()
            
            self.scene_logger.info(f"结果场景UI元素创建完成，共{len(self.ui_elements)}个元素")
            
        except Exception as e:
            self.scene_logger.error(f"结果场景UI元素创建失败: {e}")
            raise
    
    def _create_result_indicators(self):
        """创建结果标识元素"""
        # 结果场景标题
        self.ui_elements["result_title"] = self.ui_factory.create_enhanced_label(
            "result_title",
            confidence_threshold=0.9,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
        
        # 最终评级显示
        self.ui_elements["final_rank"] = self.ui_factory.create_enhanced_label(
            "final_rank",
            confidence_threshold=0.9,
            text_recognition_enabled=True,
            ocr_language="en"
        )
        
        # 总分显示
        self.ui_elements["total_score"] = self.ui_factory.create_enhanced_label(
            "total_score",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="en"
        )
        
        # 完成度显示
        self.ui_elements["completion_rate"] = self.ui_factory.create_enhanced_label(
            "completion_rate",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="en"
        )
        
        # 结果类型指示器
        self.ui_elements["result_type_indicator"] = self.ui_factory.create_label(
            "result_type_indicator",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
    
    def _create_result_displays(self):
        """创建结果展示元素"""
        # 偶像最终形象
        self.ui_elements["final_idol_image"] = self.ui_factory.create_label(
            "final_idol_image",
            confidence_threshold=0.8,
            text_recognition_enabled=False
        )
        
        # 结果摘要
        self.ui_elements["result_summary"] = self.ui_factory.create_enhanced_label(
            "result_summary",
            confidence_threshold=0.7,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
        
        # 评价文本
        self.ui_elements["evaluation_text"] = self.ui_factory.create_enhanced_label(
            "evaluation_text",
            confidence_threshold=0.7,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
        
        # 特殊称号（如果有）
        self.ui_elements["special_title"] = self.ui_factory.create_label(
            "special_title",
            confidence_threshold=0.7,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
    
    def _create_stats_displays(self):
        """创建属性展示元素"""
        # 最终属性值
        for stat in ["vocal", "dance", "visual", "mental"]:
            self.ui_elements[f"final_{stat}"] = self.ui_factory.create_enhanced_label(
                f"final_{stat}",
                confidence_threshold=0.8,
                text_recognition_enabled=True,
                ocr_language="en"
            )
        
        # 最终等级
        self.ui_elements["final_level"] = self.ui_factory.create_enhanced_label(
            "final_level",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="en"
        )
        
        # 属性总和
        self.ui_elements["total_stats"] = self.ui_factory.create_enhanced_label(
            "total_stats",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="en"
        )
        
        # 属性平衡度
        self.ui_elements["stats_balance"] = self.ui_factory.create_label(
            "stats_balance",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="en"
        )
        
        # 属性雷达图
        self.ui_elements["stats_radar"] = self.ui_factory.create_label(
            "stats_radar",
            confidence_threshold=0.7,
            text_recognition_enabled=False
        )
    
    def _create_achievement_displays(self):
        """创建成就展示元素"""
        # 成就标题
        self.ui_elements["achievements_title"] = self.ui_factory.create_label(
            "achievements_title",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
        
        # 成就列表（最多显示5个）
        for i in range(1, 6):
            self.ui_elements[f"achievement_{i}"] = self.ui_factory.create_enhanced_label(
                f"achievement_{i}",
                confidence_threshold=0.7,
                text_recognition_enabled=True,
                ocr_language="ja"
            )
        
        # 里程碑显示
        self.ui_elements["milestones_display"] = self.ui_factory.create_enhanced_label(
            "milestones_display",
            confidence_threshold=0.7,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
        
        # 新记录显示
        self.ui_elements["new_records"] = self.ui_factory.create_enhanced_label(
            "new_records",
            confidence_threshold=0.7,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
        
        # 成就总数
        self.ui_elements["achievement_count"] = self.ui_factory.create_label(
            "achievement_count",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="en"
        )
    
    def _create_statistics_displays(self):
        """创建统计信息元素"""
        # 活动统计
        activity_stats = ["total_lessons", "total_rest", "total_outings", "total_battles", "total_exams"]
        for stat in activity_stats:
            self.ui_elements[stat] = self.ui_factory.create_enhanced_label(
                stat,
                confidence_threshold=0.8,
                text_recognition_enabled=True,
                ocr_language="en"
            )
        
        # 时间统计
        self.ui_elements["days_completed"] = self.ui_factory.create_enhanced_label(
            "days_completed",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="en"
        )
        
        self.ui_elements["events_triggered"] = self.ui_factory.create_label(
            "events_triggered",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="en"
        )
        
        # 效率统计
        self.ui_elements["efficiency_rating"] = self.ui_factory.create_label(
            "efficiency_rating",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="en"
        )
    
    def _create_history_displays(self):
        """创建历史记录元素"""
        # 育成历史
        self.ui_elements["produce_duration"] = self.ui_factory.create_enhanced_label(
            "produce_duration",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="en"
        )
        
        # 偶像名称
        self.ui_elements["idol_name"] = self.ui_factory.create_enhanced_label(
            "idol_name",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
        
        # 使用策略
        self.ui_elements["strategy_used"] = self.ui_factory.create_label(
            "strategy_used",
            confidence_threshold=0.7,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
        
        # 排名信息
        self.ui_elements["ranking_info"] = self.ui_factory.create_enhanced_label(
            "ranking_info",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="en"
        )
    
    def _create_control_buttons(self):
        """创建控制按钮"""
        # 确认结果按钮
        self.ui_elements["confirm_result_button"] = self.ui_factory.create_enhanced_button(
            "confirm_result_button",
            confidence_threshold=0.9,
            timeout=5.0,
            verify_click_result=True
        )
        
        # 保存记录按钮
        self.ui_elements["save_record_button"] = self.ui_factory.create_enhanced_button(
            "save_record_button",
            confidence_threshold=0.9,
            timeout=5.0,
            verify_click_result=True
        )
        
        # 查看详细报告按钮
        self.ui_elements["detailed_report_button"] = self.ui_factory.create_button(
            "detailed_report_button",
            confidence_threshold=0.8,
            timeout=3.0
        )
        
        # 分享结果按钮
        self.ui_elements["share_result_button"] = self.ui_factory.create_button(
            "share_result_button",
            confidence_threshold=0.8,
            timeout=3.0
        )
        
        # 重新开始按钮
        self.ui_elements["restart_button"] = self.ui_factory.create_enhanced_button(
            "restart_button",
            confidence_threshold=0.8,
            timeout=5.0,
            expected_scene_after_click="main_menu",
            verify_click_result=True
        )
        
        # 继续游戏按钮
        self.ui_elements["continue_game_button"] = self.ui_factory.create_enhanced_button(
            "continue_game_button",
            confidence_threshold=0.9,
            timeout=5.0,
            expected_scene_after_click="main_menu",
            verify_click_result=True
        )
    
    def _get_critical_elements(self) -> List[str]:
        """获取结果场景的关键UI元素"""
        return [
            "result_title",
            "final_rank",
            "total_score",
            "confirm_result_button",
            "continue_game_button"
        ]
    
    def _verify_scene_specific_state(self) -> bool:
        """验证结果场景特定状态"""
        try:
            # 验证结果标题可见
            title_element = self.get_ui_element("result_title")
            if not title_element or not title_element.is_visible():
                self.scene_logger.debug("结果标题不可见")
                return False
            
            # 验证最终评级可见
            rank_element = self.get_ui_element("final_rank")
            if not rank_element or not rank_element.is_visible():
                self.scene_logger.debug("最终评级不可见")
                return False
            
            # 验证控制按钮可见
            confirm_button = self.get_ui_element("confirm_result_button")
            continue_button = self.get_ui_element("continue_game_button")
            
            if not ((confirm_button and confirm_button.is_visible()) or 
                   (continue_button and continue_button.is_visible())):
                self.scene_logger.debug("控制按钮不可见")
                return False
            
            return True
            
        except Exception as e:
            self.scene_logger.error(f"结果场景状态验证异常: {e}")
            return False
    
    def process_result_data(self) -> bool:
        """处理结果数据"""
        try:
            self.scene_logger.info("开始处理结果数据")
            
            with measure_block("result_data_processing"):
                # 读取基本结果信息
                if not self._read_basic_result_info():
                    return False
                
                # 读取最终属性
                if not self._read_final_stats():
                    return False
                
                # 读取成长统计
                if not self._read_growth_statistics():
                    return False
                
                # 读取成就信息
                if not self._read_achievements():
                    return False
                
                # 读取历史信息
                if not self._read_history_info():
                    return False
                
                # 计算比较数据
                self._calculate_comparison_data()
                
                self.scene_logger.info("结果数据处理完成")
                return True
            
        except Exception as e:
            self.scene_logger.error(f"处理结果数据失败: {e}")
            return False
    
    def _read_basic_result_info(self) -> bool:
        """读取基本结果信息"""
        try:
            # 读取最终评级
            rank_element = self.get_ui_element("final_rank")
            if rank_element:
                rank_text = rank_element.read_text()
                if rank_text:
                    self._final_rank = rank_text.strip()
            
            # 读取总分
            score_element = self.get_ui_element("total_score")
            if score_element:
                score_text = score_element.read_text()
                if score_text:
                    import re
                    score_match = re.search(r'\d+', score_text)
                    if score_match:
                        self._total_score = int(score_match.group())
            
            # 读取完成度
            completion_element = self.get_ui_element("completion_rate")
            if completion_element:
                completion_text = completion_element.read_text()
                if completion_text:
                    import re
                    completion_match = re.search(r'(\d+\.?\d*)%', completion_text)
                    if completion_match:
                        self._completion_rate = float(completion_match.group(1)) / 100.0
            
            # 判断结果类型
            if self._completion_rate >= 1.0:
                self._result_type = ProduceResultType.SUCCESS
            elif self._completion_rate >= 0.5:
                self._result_type = ProduceResultType.TIMEOUT
            else:
                self._result_type = ProduceResultType.FAILURE
            
            return True
            
        except Exception as e:
            self.scene_logger.error(f"读取基本结果信息失败: {e}")
            return False
    
    def _read_final_stats(self) -> bool:
        """读取最终属性"""
        try:
            # 读取各项属性
            for stat in ["vocal", "dance", "visual", "mental"]:
                stat_element = self.get_ui_element(f"final_{stat}")
                if stat_element:
                    stat_text = stat_element.read_text()
                    if stat_text:
                        import re
                        stat_match = re.search(r'\d+', stat_text)
                        if stat_match:
                            self._final_stats[stat] = int(stat_match.group())
            
            # 读取最终等级
            level_element = self.get_ui_element("final_level")
            if level_element:
                level_text = level_element.read_text()
                if level_text:
                    import re
                    level_match = re.search(r'\d+', level_text)
                    if level_match:
                        self._final_stats["level"] = int(level_match.group())
            
            return True
            
        except Exception as e:
            self.scene_logger.error(f"读取最终属性失败: {e}")
            return False
    
    def _read_growth_statistics(self) -> bool:
        """读取成长统计"""
        try:
            # 读取活动统计
            activity_stats = ["total_lessons", "total_rest", "total_outings", "total_battles", "total_exams"]
            for stat in activity_stats:
                stat_element = self.get_ui_element(stat)
                if stat_element:
                    stat_text = stat_element.read_text()
                    if stat_text:
                        import re
                        stat_match = re.search(r'\d+', stat_text)
                        if stat_match:
                            self._growth_stats[stat] = int(stat_match.group())
            
            # 读取时间统计
            days_element = self.get_ui_element("days_completed")
            if days_element:
                days_text = days_element.read_text()
                if days_text:
                    import re
                    days_match = re.search(r'\d+', days_text)
                    if days_match:
                        self._growth_stats["days_completed"] = int(days_match.group())
            
            events_element = self.get_ui_element("events_triggered")
            if events_element:
                events_text = events_element.read_text()
                if events_text:
                    import re
                    events_match = re.search(r'\d+', events_text)
                    if events_match:
                        self._growth_stats["events_triggered"] = int(events_match.group())
            
            return True
            
        except Exception as e:
            self.scene_logger.error(f"读取成长统计失败: {e}")
            return False
    
    def _read_achievements(self) -> bool:
        """读取成就信息"""
        try:
            self._achievements.clear()
            
            # 读取成就列表
            for i in range(1, 6):
                achievement_element = self.get_ui_element(f"achievement_{i}")
                if achievement_element and achievement_element.is_visible():
                    achievement_text = achievement_element.read_text()
                    if achievement_text:
                        self._achievements.append({
                            "name": achievement_text.strip(),
                            "level": AchievementLevel.COMMON,  # 默认等级
                            "timestamp": datetime.now().isoformat()
                        })
            
            # 读取成就总数
            count_element = self.get_ui_element("achievement_count")
            if count_element:
                count_text = count_element.read_text()
                if count_text:
                    import re
                    count_match = re.search(r'\d+', count_text)
                    if count_match:
                        total_achievements = int(count_match.group())
                        # 如果显示的成就少于总数，说明有更多成就
                        if len(self._achievements) < total_achievements:
                            self._achievements.append({
                                "name": f"其他{total_achievements - len(self._achievements)}个成就",
                                "level": AchievementLevel.COMMON,
                                "timestamp": datetime.now().isoformat()
                            })
            
            return True
            
        except Exception as e:
            self.scene_logger.error(f"读取成就信息失败: {e}")
            return False
    
    def _read_history_info(self) -> bool:
        """读取历史信息"""
        try:
            # 读取偶像名称
            idol_element = self.get_ui_element("idol_name")
            if idol_element:
                idol_text = idol_element.read_text()
                if idol_text:
                    self._produce_history["idol_name"] = idol_text.strip()
            
            # 读取育成时长
            duration_element = self.get_ui_element("produce_duration")
            if duration_element:
                duration_text = duration_element.read_text()
                if duration_text:
                    import re
                    duration_match = re.search(r'(\d+)', duration_text)
                    if duration_match:
                        self._produce_history["duration_days"] = int(duration_match.group(1))
            
            # 读取使用策略
            strategy_element = self.get_ui_element("strategy_used")
            if strategy_element:
                strategy_text = strategy_element.read_text()
                if strategy_text:
                    self._produce_history["strategy_used"] = strategy_text.strip()
            
            # 设置时间戳
            self._produce_history["end_date"] = datetime.now().isoformat()
            
            return True
            
        except Exception as e:
            self.scene_logger.error(f"读取历史信息失败: {e}")
            return False
    
    def _calculate_comparison_data(self):
        """计算比较数据"""
        try:
            # 简化实现：基于当前数据计算一些比较指标
            total_stats = sum(self._final_stats[stat] for stat in ["vocal", "dance", "visual", "mental"])
            
            self._comparison_data = {
                "total_stats": total_stats,
                "average_stat": total_stats / 4,
                "completion_rate": self._completion_rate,
                "efficiency_score": self._total_score / max(self._growth_stats["days_completed"], 1)
            }
            
        except Exception as e:
            self.scene_logger.error(f"计算比较数据失败: {e}")
    
    def confirm_result(self) -> bool:
        """确认结果"""
        try:
            self.scene_logger.info("确认育成结果")
            
            # 记录UI元素使用
            self.optimizer.record_ui_element_usage(GameScene.PRODUCE_RESULT, "confirm_result_button")
            
            if not self.interact_with_element("confirm_result_button", "click"):
                self.scene_logger.error("点击确认结果按钮失败")
                return False
            
            time.sleep(1.0)
            self.scene_logger.info("育成结果确认成功")
            return True
            
        except Exception as e:
            self.scene_logger.error(f"确认结果失败: {e}")
            return False
    
    def save_record(self) -> bool:
        """保存记录"""
        try:
            self.scene_logger.info("保存育成记录")
            
            if not self.interact_with_element("save_record_button", "click"):
                self.scene_logger.error("点击保存记录按钮失败")
                return False
            
            time.sleep(2.0)
            self.scene_logger.info("育成记录保存成功")
            return True
            
        except Exception as e:
            self.scene_logger.error(f"保存记录失败: {e}")
            return False
    
    def continue_game(self) -> bool:
        """继续游戏"""
        try:
            self.scene_logger.info("继续游戏")
            
            if not self.interact_with_element("continue_game_button", "click"):
                self.scene_logger.error("点击继续游戏按钮失败")
                return False
            
            time.sleep(2.0)
            self.scene_logger.info("成功返回主菜单")
            return True
            
        except Exception as e:
            self.scene_logger.error(f"继续游戏失败: {e}")
            return False
    
    def restart_produce(self) -> bool:
        """重新开始育成"""
        try:
            self.scene_logger.info("重新开始育成")
            
            if not self.interact_with_element("restart_button", "click"):
                self.scene_logger.error("点击重新开始按钮失败")
                return False
            
            time.sleep(2.0)
            self.scene_logger.info("重新开始育成成功")
            return True
            
        except Exception as e:
            self.scene_logger.error(f"重新开始育成失败: {e}")
            return False
    
    def get_result_summary(self) -> Dict[str, Any]:
        """获取结果摘要"""
        return {
            "result_type": self._result_type.value if self._result_type else "unknown",
            "final_rank": self._final_rank,
            "total_score": self._total_score,
            "completion_rate": self._completion_rate,
            "final_stats": self._final_stats.copy(),
            "growth_stats": self._growth_stats.copy(),
            "achievements_count": len(self._achievements),
            "produce_history": self._produce_history.copy(),
            "comparison_data": self._comparison_data.copy(),
            "scene_ready": self.is_scene_ready()
        }
    
    def get_achievements(self) -> List[Dict[str, Any]]:
        """获取成就列表"""
        return self._achievements.copy()
    
    def get_final_stats_total(self) -> int:
        """获取最终属性总和"""
        return sum(self._final_stats[stat] for stat in ["vocal", "dance", "visual", "mental"])
    
    def is_successful_produce(self) -> bool:
        """检查是否成功育成"""
        return self._result_type == ProduceResultType.SUCCESS and self._completion_rate >= 0.8
    
    def get_efficiency_rating(self) -> str:
        """获取效率评级"""
        if self._growth_stats["days_completed"] == 0:
            return "N/A"
        
        efficiency = self._total_score / self._growth_stats["days_completed"]
        
        if efficiency >= 100:
            return "S"
        elif efficiency >= 80:
            return "A"
        elif efficiency >= 60:
            return "B"
        elif efficiency >= 40:
            return "C"
        else:
            return "D"
