"""
测试服务器是否重新加载了我们的更改
"""

import requests
import json
import time


def test_server_reload():
    """测试服务器重新加载"""
    print("🧪 测试服务器是否重新加载了我们的更改")
    print("=" * 50)
    
    # 等待服务器重新加载
    print("⏳ 等待服务器重新加载...")
    time.sleep(5)
    
    # 检查服务器状态
    try:
        health_response = requests.get("http://localhost:8000/health", timeout=5)
        if health_response.status_code != 200:
            print("❌ 服务器未运行")
            return False
        print("✅ 服务器运行正常")
    except:
        print("❌ 无法连接到服务器")
        return False
    
    # 发送截图请求
    test_data = {
        "config": {
            "mode": "window",
            "format": "png",
            "quality": 90,
            "save_to_disk": True,
            "filename_prefix": "reload_test"
        }
    }
    
    print(f"\n📡 发送截图请求...")
    print(f"📝 如果看到 '🚨 [CRITICAL] API端点被调用了！！！ - 版本2'，说明服务器重新加载了")
    print(f"📝 如果没有看到，说明服务器没有重新加载我们的更改")
    
    try:
        response = requests.post(
            "http://localhost:8000/api/v1/screenshot/capture",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"\n📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API调用成功!")
            return True
        else:
            print(f"❌ API调用失败")
            print(f"📋 错误响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False


if __name__ == "__main__":
    test_server_reload()
