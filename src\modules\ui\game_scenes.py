"""
游戏场景类实现
包含各种游戏场景的面向对象封装
"""

import time
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any

from ...utils.logger import get_logger
from ...core.data_structures import GameScene
from .base_ui_element import BaseUIElement
from .game_buttons import (
    ProduceButton, PartTimeJobButton, DailyTasksButton,
    IdolSelectionButton, SupportCardButton, StartProduceButton,
    LessonButton, RestButton, OutingButton
)


class BaseScene(ABC):
    """场景抽象基类"""
    
    def __init__(self, 
                 scene_type: GameScene,
                 perception_module,
                 action_controller,
                 config: Optional[Dict[str, Any]] = None):
        """
        初始化场景
        
        Args:
            scene_type: 场景类型
            perception_module: 感知模块
            action_controller: 行动控制器
            config: 场景配置
        """
        self.scene_type = scene_type
        self.perception = perception_module
        self.action = action_controller
        self.config = config or {}
        self.ui_elements: Dict[str, BaseUIElement] = {}
        self.logger = get_logger(f"Scene.{self.__class__.__name__}")
        
        # 初始化UI元素
        self._init_ui_elements()
    
    @abstractmethod
    def _init_ui_elements(self):
        """初始化UI元素（抽象方法）"""
        pass
    
    def is_current_scene(self) -> bool:
        """
        检查是否为当前场景
        
        Returns:
            是否为当前场景
        """
        try:
            game_state = self.perception.get_game_state()
            return game_state.current_scene == self.scene_type
        except Exception as e:
            self.logger.error(f"检查当前场景失败: {e}")
            return False
    
    def get_ui_element(self, name: str) -> Optional[BaseUIElement]:
        """
        获取UI元素
        
        Args:
            name: UI元素名称
            
        Returns:
            UI元素实例，如果不存在返回None
        """
        return self.ui_elements.get(name)
    
    def wait_for_scene(self, timeout: float = 15.0) -> bool:
        """
        等待场景出现
        
        Args:
            timeout: 等待超时时间
            
        Returns:
            是否在超时前出现
        """
        start_time = time.time()
        while time.time() - start_time < timeout:
            if self.is_current_scene():
                return True
            time.sleep(0.5)
        
        return False
    
    def get_all_visible_elements(self) -> Dict[str, BaseUIElement]:
        """
        获取所有可见的UI元素
        
        Returns:
            可见UI元素字典
        """
        visible_elements = {}
        for name, element in self.ui_elements.items():
            if element.is_visible():
                visible_elements[name] = element
        return visible_elements
    
    def wait_for_any_element_visible(self, element_names: List[str], timeout: float = 10.0) -> Optional[str]:
        """
        等待任意一个UI元素变为可见
        
        Args:
            element_names: UI元素名称列表
            timeout: 等待超时时间
            
        Returns:
            第一个变为可见的元素名称，如果超时返回None
        """
        start_time = time.time()
        while time.time() - start_time < timeout:
            for name in element_names:
                element = self.get_ui_element(name)
                if element and element.is_visible():
                    return name
            time.sleep(0.5)
        
        return None


class MainMenuScene(BaseScene):
    """主菜单场景类"""
    
    def __init__(self, perception_module, action_controller, config: Optional[Dict] = None):
        super().__init__(GameScene.MAIN_MENU, perception_module, action_controller, config)
    
    def _init_ui_elements(self):
        """初始化主菜单UI元素"""
        # 育成按钮
        self.produce_button = ProduceButton(
            self.perception, 
            self.action,
            **self.config.get('produce_button', {})
        )
        self.ui_elements['produce_button'] = self.produce_button
        
        # 打工按钮
        self.part_time_job_button = PartTimeJobButton(
            self.perception,
            self.action,
            **self.config.get('part_time_job_button', {})
        )
        self.ui_elements['part_time_job_button'] = self.part_time_job_button
        
        # 日常任务按钮
        self.daily_tasks_button = DailyTasksButton(
            self.perception,
            self.action,
            **self.config.get('daily_tasks_button', {})
        )
        self.ui_elements['daily_tasks_button'] = self.daily_tasks_button
    
    def navigate_to_produce(self) -> bool:
        """
        导航到育成准备界面
        
        Returns:
            是否导航成功
        """
        self.logger.info("开始导航到育成准备界面")
        
        # 确保当前在主菜单
        if not self.is_current_scene():
            self.logger.error("当前不在主菜单场景")
            return False
        
        # 点击育成按钮
        return self.produce_button.click()
    
    def navigate_to_part_time_job(self) -> bool:
        """导航到打工界面"""
        self.logger.info("开始导航到打工界面")
        
        if not self.is_current_scene():
            self.logger.error("当前不在主菜单场景")
            return False
        
        return self.part_time_job_button.click()
    
    def navigate_to_daily_tasks(self) -> bool:
        """导航到日常任务界面"""
        self.logger.info("开始导航到日常任务界面")
        
        if not self.is_current_scene():
            self.logger.error("当前不在主菜单场景")
            return False
        
        return self.daily_tasks_button.click()


class ProduceSetupScene(BaseScene):
    """育成准备场景类"""
    
    def __init__(self, perception_module, action_controller, config: Optional[Dict] = None):
        super().__init__(GameScene.PRODUCE_SETUP, perception_module, action_controller, config)
    
    def _init_ui_elements(self):
        """初始化育成准备界面UI元素"""
        # 偶像选择按钮
        self.idol_selection_button = IdolSelectionButton(
            self.perception,
            self.action,
            **self.config.get('idol_selection_button', {})
        )
        self.ui_elements['idol_selection_button'] = self.idol_selection_button
        
        # 支援卡选择按钮
        self.support_card_button = SupportCardButton(
            self.perception,
            self.action,
            **self.config.get('support_card_button', {})
        )
        self.ui_elements['support_card_button'] = self.support_card_button
        
        # 开始育成按钮
        self.start_produce_button = StartProduceButton(
            self.perception,
            self.action,
            **self.config.get('start_produce_button', {})
        )
        self.ui_elements['start_produce_button'] = self.start_produce_button
    
    def select_idol(self, idol_name: Optional[str] = None) -> bool:
        """
        选择偶像
        
        Args:
            idol_name: 偶像名称，如果为None则使用默认选择
            
        Returns:
            是否选择成功
        """
        self.logger.info(f"选择偶像: {idol_name or '默认'}")
        
        if not self.is_current_scene():
            self.logger.error("当前不在育成准备场景")
            return False
        
        # 点击偶像选择按钮
        if not self.idol_selection_button.click():
            return False
        
        # TODO: 实现具体的偶像选择逻辑
        # 这里可能需要额外的UI元素类来处理偶像列表
        
        return True
    
    def select_support_cards(self, card_names: Optional[List[str]] = None) -> bool:
        """
        选择支援卡
        
        Args:
            card_names: 支援卡名称列表
            
        Returns:
            是否选择成功
        """
        self.logger.info(f"选择支援卡: {card_names or '默认配置'}")
        
        if not self.is_current_scene():
            self.logger.error("当前不在育成准备场景")
            return False
        
        # 点击支援卡选择按钮
        if not self.support_card_button.click():
            return False
        
        # TODO: 实现具体的支援卡选择逻辑
        
        return True
    
    def start_produce(self) -> bool:
        """
        开始育成
        
        Returns:
            是否成功开始育成
        """
        self.logger.info("开始育成")
        
        if not self.is_current_scene():
            self.logger.error("当前不在育成准备场景")
            return False
        
        return self.start_produce_button.click()


class ProduceMainScene(BaseScene):
    """育成主界面场景类"""
    
    def __init__(self, perception_module, action_controller, config: Optional[Dict] = None):
        super().__init__(GameScene.PRODUCE_MAIN, perception_module, action_controller, config)
    
    def _init_ui_elements(self):
        """初始化育成主界面UI元素"""
        # 课程按钮
        lesson_types = ['vocal', 'dance', 'visual', 'mental']
        for lesson_type in lesson_types:
            button = LessonButton(
                lesson_type,
                self.perception,
                self.action,
                **self.config.get(f'{lesson_type}_lesson_button', {})
            )
            self.ui_elements[f'{lesson_type}_lesson_button'] = button
        
        # 休息按钮
        self.rest_button = RestButton(
            self.perception,
            self.action,
            **self.config.get('rest_button', {})
        )
        self.ui_elements['rest_button'] = self.rest_button
        
        # 外出按钮
        self.outing_button = OutingButton(
            self.perception,
            self.action,
            **self.config.get('outing_button', {})
        )
        self.ui_elements['outing_button'] = self.outing_button
    
    def take_lesson(self, lesson_type: str) -> bool:
        """
        上课
        
        Args:
            lesson_type: 课程类型 (vocal, dance, visual, mental)
            
        Returns:
            是否成功上课
        """
        self.logger.info(f"选择{lesson_type}课程")
        
        if not self.is_current_scene():
            self.logger.error("当前不在育成主界面")
            return False
        
        button_name = f'{lesson_type}_lesson_button'
        lesson_button = self.get_ui_element(button_name)
        
        if not lesson_button:
            self.logger.error(f"未找到{lesson_type}课程按钮")
            return False
        
        return lesson_button.click()
    
    def take_rest(self) -> bool:
        """
        休息
        
        Returns:
            是否成功休息
        """
        self.logger.info("选择休息")
        
        if not self.is_current_scene():
            self.logger.error("当前不在育成主界面")
            return False
        
        return self.rest_button.click()
    
    def go_outing(self) -> bool:
        """
        外出
        
        Returns:
            是否成功外出
        """
        self.logger.info("选择外出")
        
        if not self.is_current_scene():
            self.logger.error("当前不在育成主界面")
            return False
        
        return self.outing_button.click()
    
    def get_available_actions(self) -> List[str]:
        """
        获取当前可用的行动选项
        
        Returns:
            可用行动选项列表
        """
        available_actions = []
        
        # 检查所有UI元素的可见性
        for name, element in self.ui_elements.items():
            if element.is_visible():
                available_actions.append(name)
        
        return available_actions
