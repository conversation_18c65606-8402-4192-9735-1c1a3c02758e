"""
场景配置类
使用Dataclass实现场景配置数据结构
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any
from enum import Enum

from ....core.data_structures import GameScene


class NavigationStrategy(Enum):
    """导航策略枚举"""
    DIRECT = "direct"  # 直接导航
    STEP_BY_STEP = "step_by_step"  # 分步导航
    SMART = "smart"  # 智能导航（自动选择最优路径）


@dataclass
class NavigationStep:
    """导航步骤配置"""
    action_type: str  # click, wait, verify
    target: str  # UI元素名称或场景名称
    timeout: float = 5.0
    retry_count: int = 3
    optional: bool = False  # 是否为可选步骤
    description: str = ""


@dataclass
class NavigationConfig:
    """导航配置类"""
    strategy: NavigationStrategy = NavigationStrategy.SMART
    max_navigation_time: float = 30.0
    steps: List[NavigationStep] = field(default_factory=list)
    fallback_enabled: bool = True
    fallback_steps: List[NavigationStep] = field(default_factory=list)
    
    # 验证设置
    verify_each_step: bool = True
    final_verification: bool = True
    verification_timeout: float = 3.0


@dataclass
class SceneConfig:
    """场景配置类 - 使用Dataclass"""
    scene_type: GameScene
    scene_name: str
    
    # UI元素配置
    ui_elements: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    
    # 场景识别配置
    scene_indicators: List[str] = field(default_factory=list)  # 场景识别模板
    recognition_confidence: float = 0.8
    recognition_timeout: float = 10.0
    
    # 导航配置
    navigation: Optional[NavigationConfig] = None
    
    # 性能配置
    cache_enabled: bool = True
    preload_ui_elements: bool = False  # 是否预加载UI元素
    
    # 错误处理配置
    error_recovery_enabled: bool = True
    max_error_retry: int = 3
    error_recovery_delay: float = 1.0

    def __post_init__(self):
        """配置验证"""
        if not (0 < self.recognition_confidence <= 1.0):
            raise ValueError("recognition_confidence must be between 0 and 1")
        
        if self.recognition_timeout <= 0:
            raise ValueError("recognition_timeout must be positive")
        
        if not self.scene_indicators:
            raise ValueError("scene_indicators cannot be empty")
        
        # 如果没有导航配置，创建默认配置
        if self.navigation is None:
            self.navigation = NavigationConfig()

    def get_ui_element_config(self, element_name: str) -> Optional[Dict[str, Any]]:
        """获取UI元素配置"""
        return self.ui_elements.get(element_name)

    def add_ui_element_config(self, element_name: str, config: Dict[str, Any]):
        """添加UI元素配置"""
        self.ui_elements[element_name] = config

    def remove_ui_element_config(self, element_name: str) -> bool:
        """移除UI元素配置"""
        if element_name in self.ui_elements:
            del self.ui_elements[element_name]
            return True
        return False

    def get_scene_indicator_templates(self) -> List[str]:
        """获取场景识别模板列表"""
        return self.scene_indicators.copy()

    def add_scene_indicator(self, template_name: str):
        """添加场景识别模板"""
        if template_name not in self.scene_indicators:
            self.scene_indicators.append(template_name)

    def remove_scene_indicator(self, template_name: str) -> bool:
        """移除场景识别模板"""
        if template_name in self.scene_indicators:
            self.scene_indicators.remove(template_name)
            return True
        return False


@dataclass
class SceneTransitionConfig:
    """场景转换配置"""
    from_scene: GameScene
    to_scene: GameScene
    navigation_steps: List[NavigationStep]
    estimated_time: float = 5.0
    success_rate: float = 0.95  # 预期成功率
    
    # 条件检查
    preconditions: List[str] = field(default_factory=list)  # 前置条件
    postconditions: List[str] = field(default_factory=list)  # 后置条件
    
    def __post_init__(self):
        """验证配置"""
        if self.from_scene == self.to_scene:
            raise ValueError("from_scene and to_scene cannot be the same")
        
        if not self.navigation_steps:
            raise ValueError("navigation_steps cannot be empty")
        
        if not (0 < self.success_rate <= 1.0):
            raise ValueError("success_rate must be between 0 and 1")


# 预定义的场景配置模板
DEFAULT_SCENE_CONFIGS = {
    GameScene.MAIN_MENU: SceneConfig(
        scene_type=GameScene.MAIN_MENU,
        scene_name="主菜单",
        scene_indicators=["main_menu_logo", "main_menu_bg"],
        ui_elements={
            "produce_button": {
                "template_name": "produce_button",
                "position": {"x": 500, "y": 400},
                "confidence_threshold": 0.8
            },
            "part_time_job_button": {
                "template_name": "part_time_job_button", 
                "position": {"x": 300, "y": 600},
                "confidence_threshold": 0.8
            },
            "daily_tasks_button": {
                "template_name": "daily_tasks_button",
                "position": {"x": 700, "y": 600}, 
                "confidence_threshold": 0.8
            }
        }
    ),
    
    GameScene.PRODUCE_SETUP: SceneConfig(
        scene_type=GameScene.PRODUCE_SETUP,
        scene_name="育成准备",
        scene_indicators=["produce_setup_title", "idol_selection_area"],
        ui_elements={
            "idol_selection_button": {
                "template_name": "idol_selection_button",
                "position": {"x": 300, "y": 500},
                "confidence_threshold": 0.8
            },
            "support_card_button": {
                "template_name": "support_card_button",
                "position": {"x": 600, "y": 500},
                "confidence_threshold": 0.8
            },
            "start_produce_button": {
                "template_name": "start_produce_button",
                "position": {"x": 960, "y": 800},
                "confidence_threshold": 0.8
            }
        }
    ),
    
    GameScene.PRODUCE_MAIN: SceneConfig(
        scene_type=GameScene.PRODUCE_MAIN,
        scene_name="育成主界面",
        scene_indicators=["produce_main_ui", "lesson_area"],
        ui_elements={
            "vocal_lesson_button": {
                "template_name": "vocal_lesson_button",
                "confidence_threshold": 0.8
            },
            "dance_lesson_button": {
                "template_name": "dance_lesson_button", 
                "confidence_threshold": 0.8
            },
            "visual_lesson_button": {
                "template_name": "visual_lesson_button",
                "confidence_threshold": 0.8
            },
            "mental_lesson_button": {
                "template_name": "mental_lesson_button",
                "confidence_threshold": 0.8
            },
            "rest_button": {
                "template_name": "rest_button",
                "confidence_threshold": 0.8
            },
            "outing_button": {
                "template_name": "outing_button",
                "confidence_threshold": 0.8
            }
        }
    )
}
