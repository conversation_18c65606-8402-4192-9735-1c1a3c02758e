"""
启发式评分系统
负责评估游戏状态、卡牌价值和行动选择的启发式算法
"""

import math
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

from ...utils.logger import get_logger
from ...core.data_structures import (
    GameState, Card, Action, UserStrategy, GameScene,
    CardType, CardRarity
)


class EvaluationCriteria(Enum):
    """评估标准枚举"""
    SCORE_POTENTIAL = "score_potential"      # 得分潜力
    RESOURCE_EFFICIENCY = "resource_efficiency"  # 资源效率
    SYNERGY_BONUS = "synergy_bonus"         # 协同奖励
    RISK_ASSESSMENT = "risk_assessment"     # 风险评估
    LONG_TERM_VALUE = "long_term_value"     # 长期价值


@dataclass
class EvaluationResult:
    """评估结果数据结构"""
    total_score: float                      # 总评分
    criteria_scores: Dict[str, float]       # 各项标准得分
    confidence: float                       # 置信度
    reasoning: str                          # 评估理由
    metadata: Dict[str, Any] = None         # 额外元数据
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class HeuristicEvaluator:
    """启发式评分器类"""
    
    def __init__(self, user_strategy: Optional[UserStrategy] = None):
        """
        初始化启发式评分器
        
        Args:
            user_strategy: 用户策略配置
        """
        self.logger = get_logger("HeuristicEvaluator")
        self.user_strategy = user_strategy or UserStrategy()
        
        # 评估权重配置
        self.evaluation_weights = {
            EvaluationCriteria.SCORE_POTENTIAL: 1.0,
            EvaluationCriteria.RESOURCE_EFFICIENCY: 0.8,
            EvaluationCriteria.SYNERGY_BONUS: 0.6,
            EvaluationCriteria.RISK_ASSESSMENT: 0.7,
            EvaluationCriteria.LONG_TERM_VALUE: 0.5
        }
        
        # 卡牌类型权重（基于用户策略）
        self.card_type_weights = self.user_strategy.produce_goal.priority_weights
        
        # 稀有度基础分数
        self.rarity_base_scores = {
            CardRarity.N: 1.0,
            CardRarity.R: 1.5,
            CardRarity.SR: 2.5,
            CardRarity.SSR: 4.0
        }
        
        self.logger.info("启发式评分器初始化完成")
    
    def evaluate_card(self, card: Card, game_state: GameState) -> EvaluationResult:
        """
        评估单张卡牌的价值
        
        Args:
            card: 要评估的卡牌
            game_state: 当前游戏状态
            
        Returns:
            卡牌评估结果
        """
        try:
            criteria_scores = {}
            
            # 1. 得分潜力评估
            criteria_scores[EvaluationCriteria.SCORE_POTENTIAL.value] = \
                self._evaluate_score_potential(card, game_state)
            
            # 2. 资源效率评估
            criteria_scores[EvaluationCriteria.RESOURCE_EFFICIENCY.value] = \
                self._evaluate_resource_efficiency(card, game_state)
            
            # 3. 协同奖励评估
            criteria_scores[EvaluationCriteria.SYNERGY_BONUS.value] = \
                self._evaluate_synergy_bonus(card, game_state)
            
            # 4. 风险评估
            criteria_scores[EvaluationCriteria.RISK_ASSESSMENT.value] = \
                self._evaluate_risk_assessment(card, game_state)
            
            # 5. 长期价值评估
            criteria_scores[EvaluationCriteria.LONG_TERM_VALUE.value] = \
                self._evaluate_long_term_value(card, game_state)
            
            # 计算加权总分
            total_score = self._calculate_weighted_score(criteria_scores)
            
            # 计算置信度
            confidence = self._calculate_confidence(criteria_scores, game_state)
            
            # 生成评估理由
            reasoning = self._generate_reasoning(card, criteria_scores)
            
            result = EvaluationResult(
                total_score=total_score,
                criteria_scores=criteria_scores,
                confidence=confidence,
                reasoning=reasoning,
                metadata={
                    "card_id": card.card_id,
                    "card_name": card.name_jp,
                    "card_type": card.card_type.value,
                    "card_rarity": card.rarity.value
                }
            )
            
            self.logger.debug(f"卡牌评估完成: {card.name_jp}, 总分: {total_score:.2f}")
            return result
            
        except Exception as e:
            self.logger.error(f"卡牌评估失败: {card.name_jp}, 错误: {e}")
            # 返回默认评估结果
            return EvaluationResult(
                total_score=0.5,
                criteria_scores={},
                confidence=0.1,
                reasoning="评估失败，使用默认分数"
            )
    
    def evaluate_action_sequence(self, actions: List[Action], 
                                game_state: GameState) -> EvaluationResult:
        """
        评估行动序列的价值
        
        Args:
            actions: 行动序列
            game_state: 当前游戏状态
            
        Returns:
            行动序列评估结果
        """
        try:
            if not actions:
                return EvaluationResult(
                    total_score=0.0,
                    criteria_scores={},
                    confidence=1.0,
                    reasoning="空行动序列"
                )
            
            criteria_scores = {}
            
            # 评估行动序列的各个方面
            criteria_scores["efficiency"] = self._evaluate_sequence_efficiency(actions)
            criteria_scores["safety"] = self._evaluate_sequence_safety(actions, game_state)
            criteria_scores["goal_alignment"] = self._evaluate_goal_alignment(actions, game_state)
            
            # 计算总分
            total_score = sum(criteria_scores.values()) / len(criteria_scores)
            
            # 计算置信度
            confidence = min(1.0, total_score + 0.2)
            
            reasoning = f"行动序列包含{len(actions)}个操作，效率: {criteria_scores['efficiency']:.2f}"
            
            return EvaluationResult(
                total_score=total_score,
                criteria_scores=criteria_scores,
                confidence=confidence,
                reasoning=reasoning,
                metadata={"action_count": len(actions)}
            )
            
        except Exception as e:
            self.logger.error(f"行动序列评估失败: {e}")
            return EvaluationResult(
                total_score=0.3,
                criteria_scores={},
                confidence=0.1,
                reasoning="行动序列评估失败"
            )
    
    def evaluate_game_state(self, game_state: GameState) -> EvaluationResult:
        """
        评估当前游戏状态的价值
        
        Args:
            game_state: 游戏状态
            
        Returns:
            游戏状态评估结果
        """
        try:
            criteria_scores = {}
            
            # 1. 资源状态评估
            criteria_scores["resource_status"] = self._evaluate_resource_status(game_state)
            
            # 2. 进度评估
            criteria_scores["progress_status"] = self._evaluate_progress_status(game_state)
            
            # 3. 手牌质量评估
            criteria_scores["hand_quality"] = self._evaluate_hand_quality(game_state)
            
            # 4. 场景适应性评估
            criteria_scores["scene_adaptability"] = self._evaluate_scene_adaptability(game_state)
            
            # 计算总分
            total_score = sum(criteria_scores.values()) / len(criteria_scores)
            
            # 计算置信度
            confidence = 0.8 if game_state.is_valid() else 0.3
            
            reasoning = f"游戏状态评估: 场景={game_state.current_scene.value}, 体力={game_state.stamina}"
            
            return EvaluationResult(
                total_score=total_score,
                criteria_scores=criteria_scores,
                confidence=confidence,
                reasoning=reasoning,
                metadata={
                    "scene": game_state.current_scene.value,
                    "week": game_state.current_week,
                    "stamina": game_state.stamina
                }
            )
            
        except Exception as e:
            self.logger.error(f"游戏状态评估失败: {e}")
            return EvaluationResult(
                total_score=0.5,
                criteria_scores={},
                confidence=0.2,
                reasoning="游戏状态评估失败"
            )
    
    def _evaluate_score_potential(self, card: Card, game_state: GameState) -> float:
        """评估卡牌的得分潜力"""
        base_score = 0.5
        
        # 基于稀有度的基础分数
        if card.rarity in self.rarity_base_scores:
            base_score = self.rarity_base_scores[card.rarity] / 4.0
        
        # 基于卡牌类型的权重调整
        if card.card_type in [CardType.VOCAL, CardType.DANCE, CardType.VISUAL]:
            type_name = card.card_type.value.lower()
            if type_name in self.card_type_weights:
                base_score *= self.card_type_weights[type_name]
        
        # 基于卡牌效果的分数调整
        for effect in card.effects:
            if effect.type == "add_score":
                base_score += min(effect.value / 1000.0, 0.5)
        
        return min(base_score, 1.0)
    
    def _evaluate_resource_efficiency(self, card: Card, game_state: GameState) -> float:
        """评估卡牌的资源效率"""
        if card.cost <= 0:
            return 1.0
        
        # 计算效果价值与成本的比率
        effect_value = 0.0
        for effect in card.effects:
            if effect.type == "add_score":
                effect_value += effect.value / 100.0
            elif effect.type in ["add_vocal", "add_dance", "add_visual", "add_mental"]:
                effect_value += effect.value / 10.0
        
        efficiency = effect_value / card.cost if card.cost > 0 else effect_value
        return min(efficiency / 2.0, 1.0)
    
    def _evaluate_synergy_bonus(self, card: Card, game_state: GameState) -> float:
        """评估卡牌的协同奖励"""
        synergy_score = 0.5
        
        # 检查与手牌的协同效果
        same_type_count = sum(1 for hand_card in game_state.hand 
                             if hand_card.card_type == card.card_type)
        
        if same_type_count > 0:
            synergy_score += min(same_type_count * 0.1, 0.3)
        
        # 检查特殊协同效果
        for effect in card.effects:
            if "synergy" in effect.type or "combo" in effect.type:
                synergy_score += 0.2
        
        return min(synergy_score, 1.0)
    
    def _evaluate_risk_assessment(self, card: Card, game_state: GameState) -> float:
        """评估卡牌使用的风险"""
        risk_score = 0.8  # 默认低风险
        
        # 体力不足的风险
        if game_state.stamina < card.cost * 10:
            risk_score -= 0.3
        
        # 高成本卡牌的风险
        if card.cost > 3:
            risk_score -= 0.2
        
        # 基于用户风险偏好调整
        risk_aversion = self.user_strategy.behavior.risk_aversion
        risk_score = risk_score * (1 - risk_aversion) + 0.5 * risk_aversion
        
        return max(risk_score, 0.0)
    
    def _evaluate_long_term_value(self, card: Card, game_state: GameState) -> float:
        """评估卡牌的长期价值"""
        long_term_score = 0.5
        
        # 持续效果的长期价值
        for effect in card.effects:
            if effect.duration and effect.duration > 1:
                long_term_score += min(effect.duration * 0.1, 0.3)
        
        # 基于当前周数的价值调整
        if game_state.current_week > 0:
            weeks_remaining = max(52 - game_state.current_week, 1)
            long_term_multiplier = min(weeks_remaining / 52.0, 1.0)
            long_term_score *= long_term_multiplier
        
        return min(long_term_score, 1.0)
    
    def _calculate_weighted_score(self, criteria_scores: Dict[str, float]) -> float:
        """计算加权总分"""
        total_weighted_score = 0.0
        total_weight = 0.0
        
        for criteria_name, score in criteria_scores.items():
            # 查找对应的权重
            weight = 1.0
            for criteria_enum in EvaluationCriteria:
                if criteria_enum.value == criteria_name:
                    weight = self.evaluation_weights.get(criteria_enum, 1.0)
                    break
            
            total_weighted_score += score * weight
            total_weight += weight
        
        return total_weighted_score / total_weight if total_weight > 0 else 0.0
    
    def _calculate_confidence(self, criteria_scores: Dict[str, float], 
                            game_state: GameState) -> float:
        """计算评估置信度"""
        base_confidence = 0.7
        
        # 基于游戏状态的有效性调整置信度
        if game_state.is_valid():
            base_confidence += 0.2
        
        # 基于评估标准的一致性调整置信度
        if criteria_scores:
            score_variance = self._calculate_variance(list(criteria_scores.values()))
            consistency_bonus = max(0, 0.1 - score_variance)
            base_confidence += consistency_bonus
        
        return min(base_confidence, 1.0)
    
    def _calculate_variance(self, scores: List[float]) -> float:
        """计算分数方差"""
        if not scores:
            return 0.0
        
        mean = sum(scores) / len(scores)
        variance = sum((score - mean) ** 2 for score in scores) / len(scores)
        return variance
    
    def _generate_reasoning(self, card: Card, criteria_scores: Dict[str, float]) -> str:
        """生成评估理由"""
        reasoning_parts = [f"卡牌 {card.name_jp} ({card.rarity.value})"]
        
        # 找出最高分的评估标准
        if criteria_scores:
            best_criteria = max(criteria_scores.items(), key=lambda x: x[1])
            reasoning_parts.append(f"最佳表现: {best_criteria[0]} ({best_criteria[1]:.2f})")
        
        return " - ".join(reasoning_parts)
    
    def _evaluate_sequence_efficiency(self, actions: List[Action]) -> float:
        """评估行动序列效率"""
        if not actions:
            return 0.0
        
        # 基于行动数量的效率评估
        efficiency = 1.0 - min(len(actions) / 10.0, 0.5)
        
        # 检查重复行动
        action_types = [action.action_type for action in actions]
        unique_types = len(set(action_types))
        diversity_bonus = unique_types / len(action_types) * 0.2
        
        return min(efficiency + diversity_bonus, 1.0)
    
    def _evaluate_sequence_safety(self, actions: List[Action], game_state: GameState) -> float:
        """评估行动序列安全性"""
        safety_score = 0.8
        
        # 检查高风险操作
        for action in actions:
            if action.retry_count > 5:
                safety_score -= 0.1
        
        return max(safety_score, 0.0)
    
    def _evaluate_goal_alignment(self, actions: List[Action], game_state: GameState) -> float:
        """评估行动序列与目标的一致性"""
        # 基于用户策略目标评估
        goal_type = self.user_strategy.produce_goal.target
        
        if goal_type == "high_score":
            return 0.8  # 假设大多数行动都有助于得分
        elif goal_type == "true_end":
            return 0.7  # 需要更复杂的评估逻辑
        else:
            return 0.6  # 默认对齐度
    
    def _evaluate_resource_status(self, game_state: GameState) -> float:
        """评估资源状态"""
        stamina_ratio = game_state.stamina / 100.0 if game_state.stamina <= 100 else 1.0
        vigor_ratio = game_state.vigor / 100.0 if game_state.vigor <= 100 else 1.0
        
        return (stamina_ratio + vigor_ratio) / 2.0
    
    def _evaluate_progress_status(self, game_state: GameState) -> float:
        """评估进度状态"""
        if game_state.current_week <= 0:
            return 0.5
        
        # 假设52周为完整育成周期
        progress_ratio = min(game_state.current_week / 52.0, 1.0)
        return progress_ratio
    
    def _evaluate_hand_quality(self, game_state: GameState) -> float:
        """评估手牌质量"""
        if not game_state.hand:
            return 0.3
        
        total_quality = 0.0
        for card in game_state.hand:
            card_eval = self.evaluate_card(card, game_state)
            total_quality += card_eval.total_score
        
        return total_quality / len(game_state.hand)
    
    def _evaluate_scene_adaptability(self, game_state: GameState) -> float:
        """评估场景适应性"""
        scene_scores = {
            GameScene.MAIN_MENU: 0.5,
            GameScene.PRODUCE_MAIN: 0.8,
            GameScene.PRODUCE_BATTLE: 0.9,
            GameScene.PRODUCE_EXAM: 0.9,
            GameScene.PRODUCE_RESULT: 0.6,
            GameScene.UNKNOWN: 0.2
        }
        
        return scene_scores.get(game_state.current_scene, 0.5)
    
    def get_evaluator_info(self) -> Dict[str, Any]:
        """获取评分器信息"""
        return {
            "evaluation_weights": {k.value: v for k, v in self.evaluation_weights.items()},
            "card_type_weights": self.card_type_weights,
            "rarity_base_scores": {k.value: v for k, v in self.rarity_base_scores.items()},
            "user_strategy_target": self.user_strategy.produce_goal.target,
            "risk_aversion": self.user_strategy.behavior.risk_aversion
        }
