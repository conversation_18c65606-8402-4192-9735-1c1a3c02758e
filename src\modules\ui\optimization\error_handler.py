"""
错误处理器
提供全局错误处理、错误恢复和用户友好的错误提示功能
"""

import time
import traceback
import threading
from typing import Dict, Any, Optional, List, Callable, Union
from enum import Enum
from dataclasses import dataclass, field
from collections import defaultdict, deque

from ....utils.logger import get_logger


class ErrorLevel(Enum):
    """错误级别枚举"""
    DEBUG = "debug"               # 调试信息
    INFO = "info"                 # 信息
    WARNING = "warning"           # 警告
    ERROR = "error"               # 错误
    CRITICAL = "critical"         # 严重错误
    FATAL = "fatal"               # 致命错误


class ErrorCategory(Enum):
    """错误类别枚举"""
    UI_ERROR = "ui_error"         # UI错误
    NETWORK_ERROR = "network_error" # 网络错误
    SYSTEM_ERROR = "system_error" # 系统错误
    VALIDATION_ERROR = "validation_error" # 验证错误
    PERMISSION_ERROR = "permission_error" # 权限错误
    TIMEOUT_ERROR = "timeout_error" # 超时错误
    UNKNOWN_ERROR = "unknown_error" # 未知错误


class RecoveryStrategy(Enum):
    """恢复策略枚举"""
    IGNORE = "ignore"             # 忽略错误
    RETRY = "retry"               # 重试
    FALLBACK = "fallback"         # 回退
    RESTART = "restart"           # 重启
    USER_INTERVENTION = "user_intervention" # 用户干预
    GRACEFUL_SHUTDOWN = "graceful_shutdown" # 优雅关闭


@dataclass
class ErrorReport:
    """错误报告"""
    id: str
    level: ErrorLevel
    category: ErrorCategory
    message: str
    exception: Optional[Exception] = None
    traceback_info: Optional[str] = None
    context: Dict[str, Any] = field(default_factory=dict)
    timestamp: float = field(default_factory=time.time)
    source_module: Optional[str] = None
    source_function: Optional[str] = None
    recovery_strategy: Optional[RecoveryStrategy] = None
    recovery_attempts: int = 0
    resolved: bool = False
    user_notified: bool = False


@dataclass
class ErrorPattern:
    """错误模式"""
    pattern_id: str
    error_signature: str
    category: ErrorCategory
    recovery_strategy: RecoveryStrategy
    max_retries: int = 3
    retry_delay: float = 1.0
    description: str = ""
    custom_handler: Optional[Callable] = None


class ErrorHandler:
    """错误处理器"""
    
    def __init__(self, max_error_history: int = 1000):
        """
        初始化错误处理器
        
        Args:
            max_error_history: 最大错误历史记录数
        """
        self.max_error_history = max_error_history
        self.logger = get_logger("ErrorHandler")
        
        # 错误存储
        self._error_reports: deque = deque(maxlen=max_error_history)
        self._error_patterns: Dict[str, ErrorPattern] = {}
        self._error_stats: Dict[ErrorCategory, int] = defaultdict(int)
        
        # 恢复处理器
        self._recovery_handlers: Dict[RecoveryStrategy, Callable] = {}
        self._custom_handlers: Dict[str, Callable] = {}
        
        # 通知系统
        self._notification_handlers: List[Callable] = []
        self._suppressed_errors: set = set()
        
        # 线程安全
        self._lock = threading.RLock()
        
        # 配置
        self.enable_auto_recovery = True
        self.enable_user_notification = True
        self.max_retry_attempts = 3
        self.retry_delay = 1.0
        
        # 注册默认恢复处理器
        self._register_default_handlers()
        
        # 注册默认错误模式
        self._register_default_patterns()
        
        self.logger.info("错误处理器初始化完成")
    
    def handle_error(self, error: Union[Exception, str], level: ErrorLevel = ErrorLevel.ERROR,
                    category: ErrorCategory = ErrorCategory.UNKNOWN_ERROR,
                    context: Dict[str, Any] = None, source_info: Dict[str, str] = None) -> str:
        """
        处理错误
        
        Args:
            error: 错误对象或错误消息
            level: 错误级别
            category: 错误类别
            context: 错误上下文
            source_info: 源信息（module, function等）
            
        Returns:
            错误报告ID
        """
        try:
            with self._lock:
                # 创建错误报告
                error_report = self._create_error_report(
                    error, level, category, context, source_info
                )
                
                # 存储错误报告
                self._error_reports.append(error_report)
                self._error_stats[category] += 1
                
                # 查找匹配的错误模式
                pattern = self._find_matching_pattern(error_report)
                if pattern:
                    error_report.recovery_strategy = pattern.recovery_strategy
                
                # 执行恢复策略
                if self.enable_auto_recovery and error_report.recovery_strategy:
                    self._execute_recovery_strategy(error_report, pattern)
                
                # 用户通知
                if self.enable_user_notification and not error_report.user_notified:
                    self._notify_user(error_report)
                
                # 记录日志
                self._log_error(error_report)
                
                return error_report.id
                
        except Exception as e:
            # 错误处理器本身出错，使用基础日志
            self.logger.critical(f"错误处理器异常: {e}")
            return ""
    
    def _create_error_report(self, error: Union[Exception, str], level: ErrorLevel,
                           category: ErrorCategory, context: Dict[str, Any],
                           source_info: Dict[str, str]) -> ErrorReport:
        """创建错误报告"""
        error_id = f"error_{int(time.time() * 1000000)}"
        
        # 处理异常信息
        if isinstance(error, Exception):
            message = str(error)
            exception = error
            traceback_info = traceback.format_exc()
        else:
            message = str(error)
            exception = None
            traceback_info = None
        
        # 源信息
        source_module = source_info.get("module") if source_info else None
        source_function = source_info.get("function") if source_info else None
        
        return ErrorReport(
            id=error_id,
            level=level,
            category=category,
            message=message,
            exception=exception,
            traceback_info=traceback_info,
            context=context or {},
            source_module=source_module,
            source_function=source_function
        )
    
    def _find_matching_pattern(self, error_report: ErrorReport) -> Optional[ErrorPattern]:
        """查找匹配的错误模式"""
        error_signature = self._generate_error_signature(error_report)
        
        # 精确匹配
        if error_signature in self._error_patterns:
            return self._error_patterns[error_signature]
        
        # 模糊匹配
        for pattern in self._error_patterns.values():
            if self._matches_pattern(error_report, pattern):
                return pattern
        
        return None
    
    def _generate_error_signature(self, error_report: ErrorReport) -> str:
        """生成错误签名"""
        signature_parts = [
            error_report.category.value,
            error_report.level.value
        ]
        
        if error_report.exception:
            signature_parts.append(type(error_report.exception).__name__)
        
        # 添加关键词
        message_lower = error_report.message.lower()
        keywords = ["timeout", "connection", "permission", "not found", "invalid"]
        for keyword in keywords:
            if keyword in message_lower:
                signature_parts.append(keyword)
        
        return "_".join(signature_parts)
    
    def _matches_pattern(self, error_report: ErrorReport, pattern: ErrorPattern) -> bool:
        """检查错误是否匹配模式"""
        # 简化的模式匹配
        signature = self._generate_error_signature(error_report)
        return pattern.error_signature in signature or signature in pattern.error_signature
    
    def _execute_recovery_strategy(self, error_report: ErrorReport, pattern: Optional[ErrorPattern]):
        """执行恢复策略"""
        try:
            strategy = error_report.recovery_strategy
            
            if strategy in self._recovery_handlers:
                handler = self._recovery_handlers[strategy]
                success = handler(error_report, pattern)
                
                if success:
                    error_report.resolved = True
                    self.logger.info(f"错误恢复成功: {error_report.id}")
                else:
                    error_report.recovery_attempts += 1
                    
                    # 检查是否需要重试
                    max_retries = pattern.max_retries if pattern else self.max_retry_attempts
                    if (strategy == RecoveryStrategy.RETRY and 
                        error_report.recovery_attempts < max_retries):
                        
                        # 延迟重试
                        delay = pattern.retry_delay if pattern else self.retry_delay
                        threading.Timer(delay, lambda: self._execute_recovery_strategy(error_report, pattern)).start()
            
            elif pattern and pattern.custom_handler:
                # 使用自定义处理器
                try:
                    success = pattern.custom_handler(error_report)
                    if success:
                        error_report.resolved = True
                except Exception as e:
                    self.logger.error(f"自定义错误处理器执行失败: {e}")
            
        except Exception as e:
            self.logger.error(f"执行恢复策略失败: {e}")
    
    def _notify_user(self, error_report: ErrorReport):
        """通知用户"""
        try:
            # 检查是否被抑制
            if error_report.id in self._suppressed_errors:
                return
            
            # 生成用户友好的消息
            user_message = self._generate_user_message(error_report)
            
            # 调用通知处理器
            for handler in self._notification_handlers:
                try:
                    handler(error_report, user_message)
                except Exception as e:
                    self.logger.error(f"通知处理器执行失败: {e}")
            
            error_report.user_notified = True
            
        except Exception as e:
            self.logger.error(f"用户通知失败: {e}")
    
    def _generate_user_message(self, error_report: ErrorReport) -> str:
        """生成用户友好的错误消息"""
        if error_report.category == ErrorCategory.NETWORK_ERROR:
            return "网络连接出现问题，请检查网络设置后重试。"
        elif error_report.category == ErrorCategory.PERMISSION_ERROR:
            return "权限不足，请检查相关权限设置。"
        elif error_report.category == ErrorCategory.TIMEOUT_ERROR:
            return "操作超时，请稍后重试。"
        elif error_report.category == ErrorCategory.VALIDATION_ERROR:
            return "输入数据有误，请检查后重新输入。"
        elif error_report.category == ErrorCategory.UI_ERROR:
            return "界面操作出现问题，正在尝试恢复。"
        else:
            return "系统出现异常，正在处理中。"
    
    def _log_error(self, error_report: ErrorReport):
        """记录错误日志"""
        log_message = f"[{error_report.category.value}] {error_report.message}"
        
        if error_report.level == ErrorLevel.DEBUG:
            self.logger.debug(log_message)
        elif error_report.level == ErrorLevel.INFO:
            self.logger.info(log_message)
        elif error_report.level == ErrorLevel.WARNING:
            self.logger.warning(log_message)
        elif error_report.level == ErrorLevel.ERROR:
            self.logger.error(log_message)
        elif error_report.level in [ErrorLevel.CRITICAL, ErrorLevel.FATAL]:
            self.logger.critical(log_message)
        
        # 记录详细信息
        if error_report.traceback_info:
            self.logger.debug(f"Traceback for {error_report.id}:\n{error_report.traceback_info}")
    
    def _register_default_handlers(self):
        """注册默认恢复处理器"""
        self._recovery_handlers[RecoveryStrategy.IGNORE] = self._handle_ignore
        self._recovery_handlers[RecoveryStrategy.RETRY] = self._handle_retry
        self._recovery_handlers[RecoveryStrategy.FALLBACK] = self._handle_fallback
        self._recovery_handlers[RecoveryStrategy.RESTART] = self._handle_restart
        self._recovery_handlers[RecoveryStrategy.USER_INTERVENTION] = self._handle_user_intervention
        self._recovery_handlers[RecoveryStrategy.GRACEFUL_SHUTDOWN] = self._handle_graceful_shutdown
    
    def _register_default_patterns(self):
        """注册默认错误模式"""
        # 网络超时错误
        self.register_error_pattern(ErrorPattern(
            pattern_id="network_timeout",
            error_signature="network_error_timeout",
            category=ErrorCategory.NETWORK_ERROR,
            recovery_strategy=RecoveryStrategy.RETRY,
            max_retries=3,
            retry_delay=2.0,
            description="网络超时错误"
        ))
        
        # UI元素未找到错误
        self.register_error_pattern(ErrorPattern(
            pattern_id="ui_element_not_found",
            error_signature="ui_error_not_found",
            category=ErrorCategory.UI_ERROR,
            recovery_strategy=RecoveryStrategy.FALLBACK,
            max_retries=2,
            retry_delay=1.0,
            description="UI元素未找到"
        ))
        
        # 权限错误
        self.register_error_pattern(ErrorPattern(
            pattern_id="permission_denied",
            error_signature="permission_error",
            category=ErrorCategory.PERMISSION_ERROR,
            recovery_strategy=RecoveryStrategy.USER_INTERVENTION,
            description="权限被拒绝"
        ))
    
    def _handle_ignore(self, error_report: ErrorReport, pattern: Optional[ErrorPattern]) -> bool:
        """处理忽略策略"""
        self.logger.debug(f"忽略错误: {error_report.id}")
        return True
    
    def _handle_retry(self, error_report: ErrorReport, pattern: Optional[ErrorPattern]) -> bool:
        """处理重试策略"""
        self.logger.info(f"重试错误: {error_report.id} (第{error_report.recovery_attempts + 1}次)")
        # 实际的重试逻辑需要在具体的上下文中实现
        return False  # 返回False表示需要继续重试
    
    def _handle_fallback(self, error_report: ErrorReport, pattern: Optional[ErrorPattern]) -> bool:
        """处理回退策略"""
        self.logger.info(f"回退处理错误: {error_report.id}")
        # 实际的回退逻辑需要在具体的上下文中实现
        return True
    
    def _handle_restart(self, error_report: ErrorReport, pattern: Optional[ErrorPattern]) -> bool:
        """处理重启策略"""
        self.logger.warning(f"重启处理错误: {error_report.id}")
        # 实际的重启逻辑需要在具体的上下文中实现
        return True
    
    def _handle_user_intervention(self, error_report: ErrorReport, pattern: Optional[ErrorPattern]) -> bool:
        """处理用户干预策略"""
        self.logger.info(f"需要用户干预: {error_report.id}")
        # 等待用户操作
        return False
    
    def _handle_graceful_shutdown(self, error_report: ErrorReport, pattern: Optional[ErrorPattern]) -> bool:
        """处理优雅关闭策略"""
        self.logger.critical(f"执行优雅关闭: {error_report.id}")
        # 实际的关闭逻辑需要在具体的上下文中实现
        return True
    
    def register_error_pattern(self, pattern: ErrorPattern):
        """注册错误模式"""
        self._error_patterns[pattern.pattern_id] = pattern
        self.logger.debug(f"注册错误模式: {pattern.pattern_id}")
    
    def register_recovery_handler(self, strategy: RecoveryStrategy, handler: Callable):
        """注册恢复处理器"""
        self._recovery_handlers[strategy] = handler
        self.logger.debug(f"注册恢复处理器: {strategy.value}")
    
    def register_notification_handler(self, handler: Callable):
        """注册通知处理器"""
        self._notification_handlers.append(handler)
        self.logger.debug("注册通知处理器")
    
    def suppress_error(self, error_id: str):
        """抑制错误通知"""
        self._suppressed_errors.add(error_id)
    
    def resolve_error(self, error_id: str, resolution_note: str = ""):
        """手动解决错误"""
        with self._lock:
            for error_report in self._error_reports:
                if error_report.id == error_id:
                    error_report.resolved = True
                    if resolution_note:
                        error_report.context["resolution_note"] = resolution_note
                    self.logger.info(f"手动解决错误: {error_id}")
                    return True
        return False
    
    def get_error_report(self, error_id: str) -> Optional[ErrorReport]:
        """获取错误报告"""
        with self._lock:
            for error_report in self._error_reports:
                if error_report.id == error_id:
                    return error_report
        return None
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计"""
        with self._lock:
            total_errors = len(self._error_reports)
            resolved_errors = sum(1 for report in self._error_reports if report.resolved)
            
            # 按级别统计
            level_stats = defaultdict(int)
            for report in self._error_reports:
                level_stats[report.level.value] += 1
            
            # 按类别统计
            category_stats = dict(self._error_stats)
            
            # 最近错误
            recent_errors = list(self._error_reports)[-10:] if self._error_reports else []
            
            return {
                "total_errors": total_errors,
                "resolved_errors": resolved_errors,
                "unresolved_errors": total_errors - resolved_errors,
                "resolution_rate": resolved_errors / total_errors if total_errors > 0 else 0,
                "level_statistics": dict(level_stats),
                "category_statistics": category_stats,
                "registered_patterns": len(self._error_patterns),
                "recent_errors": [
                    {
                        "id": report.id,
                        "level": report.level.value,
                        "category": report.category.value,
                        "message": report.message,
                        "timestamp": report.timestamp,
                        "resolved": report.resolved
                    }
                    for report in recent_errors
                ]
            }
    
    def clear_resolved_errors(self):
        """清除已解决的错误"""
        with self._lock:
            unresolved = [report for report in self._error_reports if not report.resolved]
            self._error_reports.clear()
            self._error_reports.extend(unresolved)
            
            self.logger.info(f"清除已解决错误，剩余{len(unresolved)}个未解决错误")
    
    def export_error_report(self, format_type: str = "json") -> Any:
        """导出错误报告"""
        try:
            with self._lock:
                if format_type == "json":
                    import json
                    reports_data = []
                    
                    for report in self._error_reports:
                        report_data = {
                            "id": report.id,
                            "level": report.level.value,
                            "category": report.category.value,
                            "message": report.message,
                            "timestamp": report.timestamp,
                            "source_module": report.source_module,
                            "source_function": report.source_function,
                            "resolved": report.resolved,
                            "recovery_attempts": report.recovery_attempts,
                            "context": report.context
                        }
                        
                        if report.traceback_info:
                            report_data["traceback"] = report.traceback_info
                        
                        reports_data.append(report_data)
                    
                    return json.dumps(reports_data, indent=2, ensure_ascii=False)
                
                elif format_type == "csv":
                    import csv
                    import io
                    
                    output = io.StringIO()
                    writer = csv.writer(output)
                    
                    # 写入表头
                    writer.writerow([
                        "ID", "Level", "Category", "Message", "Timestamp", 
                        "Source Module", "Source Function", "Resolved", "Recovery Attempts"
                    ])
                    
                    # 写入数据
                    for report in self._error_reports:
                        writer.writerow([
                            report.id,
                            report.level.value,
                            report.category.value,
                            report.message,
                            time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(report.timestamp)),
                            report.source_module or "",
                            report.source_function or "",
                            report.resolved,
                            report.recovery_attempts
                        ])
                    
                    return output.getvalue()
                
                return None
                
        except Exception as e:
            self.logger.error(f"导出错误报告失败: {e}")
            return None
