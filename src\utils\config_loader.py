"""
Gakumasu-Bot 配置加载器
负责加载和管理系统配置和用户策略
"""

import os
import json
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import asdict

from ..core.data_structures import UserStrategy, InvalidConfiguration
from .logger import get_logger


class ConfigLoader:
    """配置加载器类"""
    
    def __init__(self, config_dir: str = "config", data_dir: str = "data"):
        self.config_dir = Path(config_dir)
        self.data_dir = Path(data_dir)
        self.logger = get_logger("ConfigLoader")
        
        # 配置缓存
        self._settings_cache: Optional[Dict[str, Any]] = None
        self._user_strategy_cache: Optional[UserStrategy] = None
        self._cards_cache: Optional[Dict[str, Any]] = None
        self._events_cache: Optional[Dict[str, Any]] = None
    
    def load_settings(self, force_reload: bool = False) -> Dict[str, Any]:
        """
        加载系统设置
        
        Args:
            force_reload: 是否强制重新加载
            
        Returns:
            系统设置字典
            
        Raises:
            InvalidConfiguration: 配置文件无效时抛出
        """
        if self._settings_cache is not None and not force_reload:
            return self._settings_cache
        
        settings_file = self.config_dir / "settings.yaml"
        
        if not settings_file.exists():
            self.logger.warning(f"设置文件不存在: {settings_file}")
            # 返回默认设置
            self._settings_cache = self._get_default_settings()
            return self._settings_cache
        
        try:
            with open(settings_file, 'r', encoding='utf-8') as f:
                settings = yaml.safe_load(f)
            
            # 验证必要的配置项
            self._validate_settings(settings)
            
            # 合并默认设置
            default_settings = self._get_default_settings()
            merged_settings = {**default_settings, **settings}
            
            self._settings_cache = merged_settings
            self.logger.info(f"成功加载设置文件: {settings_file}")
            
            return merged_settings
            
        except yaml.YAMLError as e:
            raise InvalidConfiguration(f"YAML格式错误: {e}")
        except Exception as e:
            raise InvalidConfiguration(f"加载设置文件失败: {e}")
    
    def load_user_strategy(self, force_reload: bool = False) -> UserStrategy:
        """
        加载用户策略配置
        
        Args:
            force_reload: 是否强制重新加载
            
        Returns:
            用户策略对象
        """
        if self._user_strategy_cache is not None and not force_reload:
            return self._user_strategy_cache
        
        strategy_file = self.config_dir / "user_strategy.yaml"
        
        if not strategy_file.exists():
            self.logger.warning(f"用户策略文件不存在: {strategy_file}")
            # 返回默认策略
            self._user_strategy_cache = UserStrategy()
            return self._user_strategy_cache
        
        try:
            with open(strategy_file, 'r', encoding='utf-8') as f:
                strategy_data = yaml.safe_load(f)
            
            self._user_strategy_cache = UserStrategy.from_dict(strategy_data)
            self.logger.info(f"成功加载用户策略: {strategy_file}")
            
            return self._user_strategy_cache
            
        except Exception as e:
            self.logger.error(f"加载用户策略失败: {e}")
            # 返回默认策略
            self._user_strategy_cache = UserStrategy()
            return self._user_strategy_cache
    
    def save_user_strategy(self, strategy: UserStrategy) -> bool:
        """
        保存用户策略配置
        
        Args:
            strategy: 用户策略对象
            
        Returns:
            是否保存成功
        """
        strategy_file = self.config_dir / "user_strategy.yaml"
        
        try:
            # 确保配置目录存在
            self.config_dir.mkdir(exist_ok=True)
            
            with open(strategy_file, 'w', encoding='utf-8') as f:
                yaml.dump(strategy.to_dict(), f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
            
            # 更新缓存
            self._user_strategy_cache = strategy
            self.logger.info(f"成功保存用户策略: {strategy_file}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"保存用户策略失败: {e}")
            return False
    
    def load_cards_data(self, force_reload: bool = False) -> Dict[str, Any]:
        """
        加载卡牌数据
        
        Args:
            force_reload: 是否强制重新加载
            
        Returns:
            卡牌数据字典
        """
        if self._cards_cache is not None and not force_reload:
            return self._cards_cache
        
        cards_file = self.data_dir / "cards.json"
        
        if not cards_file.exists():
            self.logger.warning(f"卡牌数据文件不存在: {cards_file}")
            self._cards_cache = {}
            return self._cards_cache
        
        try:
            with open(cards_file, 'r', encoding='utf-8') as f:
                cards_data = json.load(f)
            
            self._cards_cache = cards_data
            self.logger.info(f"成功加载卡牌数据: {cards_file}")
            
            return cards_data
            
        except Exception as e:
            self.logger.error(f"加载卡牌数据失败: {e}")
            self._cards_cache = {}
            return self._cards_cache
    
    def load_events_data(self, force_reload: bool = False) -> Dict[str, Any]:
        """
        加载事件数据
        
        Args:
            force_reload: 是否强制重新加载
            
        Returns:
            事件数据字典
        """
        if self._events_cache is not None and not force_reload:
            return self._events_cache
        
        events_file = self.data_dir / "events.json"
        
        if not events_file.exists():
            self.logger.warning(f"事件数据文件不存在: {events_file}")
            self._events_cache = {}
            return self._events_cache
        
        try:
            with open(events_file, 'r', encoding='utf-8') as f:
                events_data = json.load(f)
            
            self._events_cache = events_data
            self.logger.info(f"成功加载事件数据: {events_file}")
            
            return events_data
            
        except Exception as e:
            self.logger.error(f"加载事件数据失败: {e}")
            self._events_cache = {}
            return self._events_cache
    
    def _get_default_settings(self) -> Dict[str, Any]:
        """获取默认系统设置"""
        return {
            "system": {
                "log_level": "INFO",
                "language": "ja",  # 默认日语
                "screen_resolution": [1920, 1080],
                "game_window_title": "gakumas"
            },
            "dmm": {
                "dmm_player_path": "",  # 需要用户配置
                "game_icon_template": "assets/templates/dmm_gakumasu_icon.png",
                "launch_timeout": 60
            },
            "performance": {
                "screenshot_interval": 0.5,
                "action_delay_min": 0.05,
                "action_delay_max": 0.15,
                "decision_timeout": 30.0
            },
            "ai": {
                "enable_mcts": True,
                "mcts_iterations": 1000,
                "mcts_timeout": 10.0,
                "heuristic_weights": {
                    "score": 1.0,
                    "stamina": 0.8,
                    "vigor": 0.6,
                    "card_synergy": 0.4
                }
            },
            "background_mode": {
                "enabled": True,
                "capture_method": "auto",
                "input_method": "auto",
                "performance": {
                    "capture_interval": 0.5,
                    "cache_window_handle": True,
                    "monitor_window_state": True,
                    "state_check_interval": 1.0
                },
                "compatibility": {
                    "fallback_to_foreground": True,
                    "auto_bring_to_front": False,
                    "retry_on_failure": True,
                    "max_retry_attempts": 3
                },
                "debug": {
                    "log_capture_stats": False,
                    "log_input_stats": False,
                    "save_debug_screenshots": False
                }
            }
        }
    
    def _validate_settings(self, settings: Dict[str, Any]) -> None:
        """
        验证设置配置的有效性
        
        Args:
            settings: 设置字典
            
        Raises:
            InvalidConfiguration: 配置无效时抛出
        """
        # 检查必要的配置项
        if "dmm" not in settings:
            raise InvalidConfiguration("缺少DMM配置节")
        
        dmm_config = settings["dmm"]
        if "dmm_player_path" not in dmm_config or not dmm_config["dmm_player_path"]:
            raise InvalidConfiguration("必须配置dmm_player_path")
        
        # 检查DMM Player路径是否存在
        dmm_path = Path(dmm_config["dmm_player_path"])
        if not dmm_path.exists():
            raise InvalidConfiguration(f"DMM Player路径不存在: {dmm_path}")
    
    def clear_cache(self):
        """清除所有缓存"""
        self._settings_cache = None
        self._user_strategy_cache = None
        self._cards_cache = None
        self._events_cache = None
        self.logger.info("配置缓存已清除")
