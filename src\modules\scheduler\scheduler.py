"""
调度器主类
整合任务管理、状态管理、配置管理，提供统一的调度接口
"""

import time
import threading
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, Future

from ...utils.logger import get_logger
from ...core.data_structures import GameState, Action, UserStrategy, GakumasuBotException
from ..perception import PerceptionModule
from ..decision import DecisionModule
from ..action import ActionController
from .task_manager import TaskManager, Task, TaskStatus, TaskPriority
from .state_manager import StateManager
from .config_manager import ConfigManager


class SchedulerError(GakumasuBotException):
    """调度器错误"""
    pass


class Scheduler:
    """调度器主类"""
    
    def __init__(self, dmm_player_path: str = "C:/DMM GAME PLAYER/DMMGamePlayer.exe"):
        """
        初始化调度器
        
        Args:
            dmm_player_path: DMM Player路径
        """
        self.logger = get_logger("Scheduler")
        
        # 初始化管理器
        self.task_manager = TaskManager()
        self.state_manager = StateManager()
        self.config_manager = ConfigManager()
        
        # 初始化核心模块
        self.perception_module: Optional[PerceptionModule] = None
        self.decision_module: Optional[DecisionModule] = None
        self.action_controller: Optional[ActionController] = None
        
        # DMM Player路径
        self.dmm_player_path = dmm_player_path
        
        # 调度状态
        self.is_running = False
        self.is_paused = False
        self.scheduler_thread: Optional[threading.Thread] = None
        self.executor = ThreadPoolExecutor(max_workers=5)
        
        # 调度配置
        self.task_execution_interval = 1.0  # 任务执行检查间隔
        self.state_save_interval = 300.0    # 状态保存间隔
        self.config_reload_interval = 600.0  # 配置重载间隔
        
        # 定时器
        self.last_state_save = datetime.now()
        self.last_config_reload = datetime.now()
        
        # 统计信息
        self.total_tasks_executed = 0
        self.total_execution_time = 0.0
        self.start_time: Optional[datetime] = None
        
        # 错误处理
        self.max_consecutive_errors = 5
        self.consecutive_errors = 0
        
        self.logger.info("调度器初始化完成")
    
    def initialize_modules(self) -> bool:
        """
        初始化核心模块
        
        Returns:
            是否初始化成功
        """
        try:
            self.logger.info("开始初始化核心模块")
            
            # 加载配置
            user_strategy = self.config_manager.load_user_config()
            system_config = self.config_manager.load_system_config()
            
            # 初始化感知模块
            if system_config.get("modules", {}).get("perception", {}).get("enabled", True):
                self.perception_module = PerceptionModule()
                self.logger.info("感知模块初始化完成")
            
            # 初始化决策模块
            if system_config.get("modules", {}).get("decision", {}).get("enabled", True):
                self.decision_module = DecisionModule(user_strategy)
                
                # 应用决策模块配置
                decision_config = system_config.get("modules", {}).get("decision", {})
                if "default_mode" in decision_config:
                    self.decision_module.configure(
                        decision_mode=decision_config["default_mode"],
                        enable_mcts=system_config.get("mcts_enabled", True),
                        mcts_time_limit=system_config.get("mcts_time_limit", 5.0),
                        confidence_threshold=system_config.get("decision_confidence_threshold", 0.6)
                    )
                
                self.logger.info("决策模块初始化完成")
            
            # 初始化行动模块
            if system_config.get("modules", {}).get("action", {}).get("enabled", True):
                self.action_controller = ActionController(
                    dmm_player_path=self.dmm_player_path,
                    perception_module=self.perception_module
                )
                
                # 应用行动模块配置
                action_config = system_config.get("modules", {}).get("action", {})
                if action_config.get("auto_recovery", True):
                    self.action_controller.enable_auto_recovery = True
                
                self.logger.info("行动模块初始化完成")
            
            # 验证模块初始化
            if not self._validate_modules():
                raise SchedulerError("核心模块验证失败")
            
            self.logger.info("所有核心模块初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"初始化核心模块失败: {e}")
            return False
    
    def start(self) -> bool:
        """
        启动调度器
        
        Returns:
            是否启动成功
        """
        try:
            if self.is_running:
                self.logger.warning("调度器已在运行")
                return True
            
            # 初始化模块
            if not self.initialize_modules():
                raise SchedulerError("模块初始化失败")
            
            # 启动调度线程
            self.is_running = True
            self.is_paused = False
            self.start_time = datetime.now()
            self.consecutive_errors = 0
            
            self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
            self.scheduler_thread.start()
            
            self.logger.info("调度器启动成功")
            return True
            
        except Exception as e:
            self.logger.error(f"启动调度器失败: {e}")
            self.is_running = False
            return False
    
    def stop(self) -> bool:
        """
        停止调度器
        
        Returns:
            是否停止成功
        """
        try:
            if not self.is_running:
                self.logger.warning("调度器未在运行")
                return True
            
            self.logger.info("正在停止调度器...")
            
            # 停止调度循环
            self.is_running = False
            
            # 等待调度线程结束
            if self.scheduler_thread and self.scheduler_thread.is_alive():
                self.scheduler_thread.join(timeout=10.0)
            
            # 关闭线程池
            self.executor.shutdown(wait=True)
            
            # 保存当前状态
            if self.perception_module:
                current_state = self.perception_module.get_current_game_state()
                if current_state:
                    self.state_manager.save_current_state(current_state)
            
            self.logger.info("调度器已停止")
            return True
            
        except Exception as e:
            self.logger.error(f"停止调度器失败: {e}")
            return False
    
    def pause(self) -> bool:
        """暂停调度器"""
        if self.is_running:
            self.is_paused = True
            self.logger.info("调度器已暂停")
            return True
        return False
    
    def resume(self) -> bool:
        """恢复调度器"""
        if self.is_running and self.is_paused:
            self.is_paused = False
            self.logger.info("调度器已恢复")
            return True
        return False
    
    def schedule_task(self, name: str, execute_func: Callable,
                     description: str = "", priority: TaskPriority = TaskPriority.NORMAL,
                     scheduled_at: Optional[datetime] = None,
                     dependencies: Optional[List[str]] = None,
                     **kwargs) -> str:
        """
        调度新任务
        
        Args:
            name: 任务名称
            execute_func: 执行函数
            description: 任务描述
            priority: 任务优先级
            scheduled_at: 调度时间
            dependencies: 依赖任务
            **kwargs: 其他参数
            
        Returns:
            任务ID
        """
        return self.task_manager.create_task(
            name=name,
            execute_func=execute_func,
            description=description,
            priority=priority,
            scheduled_at=scheduled_at,
            dependencies=dependencies,
            **kwargs
        )
    
    def schedule_game_action(self, action: Action, 
                           priority: TaskPriority = TaskPriority.NORMAL,
                           scheduled_at: Optional[datetime] = None) -> str:
        """
        调度游戏行动任务
        
        Args:
            action: 游戏行动
            priority: 任务优先级
            scheduled_at: 调度时间
            
        Returns:
            任务ID
        """
        def execute_action():
            if self.action_controller:
                return self.action_controller.execute_action(action)
            return False
        
        return self.schedule_task(
            name=f"执行行动: {action.description}",
            execute_func=execute_action,
            description=f"执行游戏行动: {action.action_type.value}",
            priority=priority,
            scheduled_at=scheduled_at
        )
    
    def schedule_decision_task(self, priority: TaskPriority = TaskPriority.HIGH) -> str:
        """
        调度决策任务
        
        Args:
            priority: 任务优先级
            
        Returns:
            任务ID
        """
        def make_decision():
            if not self.perception_module or not self.decision_module:
                return None
            
            # 获取当前游戏状态
            game_state = self.perception_module.get_current_game_state()
            if not game_state:
                return None
            
            # 做出决策
            decision = self.decision_module.make_decision(game_state)
            
            # 如果有决策结果，调度执行任务
            if decision:
                self.schedule_game_action(decision, TaskPriority.HIGH)
            
            return decision
        
        return self.schedule_task(
            name="智能决策",
            execute_func=make_decision,
            description="基于当前游戏状态进行智能决策",
            priority=priority
        )
    
    def schedule_periodic_task(self, name: str, execute_func: Callable,
                              interval: float, description: str = "",
                              priority: TaskPriority = TaskPriority.NORMAL) -> List[str]:
        """
        调度周期性任务
        
        Args:
            name: 任务名称
            execute_func: 执行函数
            interval: 执行间隔（秒）
            description: 任务描述
            priority: 任务优先级
            
        Returns:
            任务ID列表
        """
        task_ids = []
        
        # 创建多个任务实例
        for i in range(10):  # 创建10个周期实例
            scheduled_time = datetime.now() + timedelta(seconds=interval * i)
            task_id = self.schedule_task(
                name=f"{name} #{i+1}",
                execute_func=execute_func,
                description=description,
                priority=priority,
                scheduled_at=scheduled_time
            )
            task_ids.append(task_id)
        
        return task_ids
    
    def get_current_game_state(self) -> Optional[GameState]:
        """获取当前游戏状态"""
        if self.perception_module:
            return self.perception_module.get_current_game_state()
        return self.state_manager.load_current_state()
    
    def save_current_state(self) -> bool:
        """保存当前游戏状态"""
        try:
            current_state = self.get_current_game_state()
            if current_state:
                return self.state_manager.save_current_state(current_state)
            return False
        except Exception as e:
            self.logger.error(f"保存当前状态失败: {e}")
            return False
    
    def create_state_snapshot(self, snapshot_id: Optional[str] = None) -> Optional[str]:
        """创建状态快照"""
        try:
            current_state = self.get_current_game_state()
            if current_state:
                return self.state_manager.create_snapshot(current_state, snapshot_id)
            return None
        except Exception as e:
            self.logger.error(f"创建状态快照失败: {e}")
            return None
    
    def _scheduler_loop(self):
        """调度器主循环"""
        self.logger.info("调度器主循环开始")
        
        while self.is_running:
            try:
                if not self.is_paused:
                    # 执行任务
                    self._execute_pending_tasks()
                    
                    # 定期维护
                    self._perform_maintenance()
                
                # 等待下一次检查
                time.sleep(self.task_execution_interval)
                
            except Exception as e:
                self.consecutive_errors += 1
                self.logger.error(f"调度器循环错误 ({self.consecutive_errors}): {e}")
                
                # 如果连续错误过多，暂停调度器
                if self.consecutive_errors >= self.max_consecutive_errors:
                    self.logger.critical("连续错误过多，暂停调度器")
                    self.is_paused = True
                    self.consecutive_errors = 0
                
                time.sleep(5.0)  # 错误后等待更长时间
        
        self.logger.info("调度器主循环结束")
    
    def _execute_pending_tasks(self):
        """执行待处理任务"""
        # 获取下一个任务
        task = self.task_manager.get_next_task()
        if not task:
            return
        
        # 开始执行任务
        if not self.task_manager.start_task(task.task_id):
            return
        
        # 提交任务到线程池
        future = self.executor.submit(self._execute_task, task)
        
        # 记录执行
        self.total_tasks_executed += 1
    
    def _execute_task(self, task: Task):
        """执行单个任务"""
        start_time = time.time()
        
        try:
            self.logger.debug(f"开始执行任务: {task.name}")
            
            # 执行任务函数
            if task.execute_func:
                result = task.execute_func(*task.execute_args, **task.execute_kwargs)
                self.task_manager.complete_task(task.task_id, result)
            else:
                self.task_manager.complete_task(task.task_id)
            
            # 重置错误计数
            self.consecutive_errors = 0
            
        except Exception as e:
            error_msg = f"任务执行失败: {e}"
            self.logger.error(error_msg)
            self.task_manager.fail_task(task.task_id, error_msg)
        
        finally:
            execution_time = time.time() - start_time
            self.total_execution_time += execution_time
            self.logger.debug(f"任务执行完成: {task.name}, 耗时: {execution_time:.2f}s")
    
    def _perform_maintenance(self):
        """执行定期维护"""
        now = datetime.now()
        
        # 状态保存
        if (now - self.last_state_save).total_seconds() >= self.state_save_interval:
            self.save_current_state()
            self.last_state_save = now
        
        # 配置重载
        if (now - self.last_config_reload).total_seconds() >= self.config_reload_interval:
            self._reload_configurations()
            self.last_config_reload = now
        
        # 清理任务
        self.task_manager.cleanup_completed_tasks()
        
        # 清理状态快照
        self.state_manager.cleanup_old_snapshots()
    
    def _reload_configurations(self):
        """重新加载配置"""
        try:
            # 重新加载用户策略
            user_strategy = self.config_manager.load_user_config()
            if self.decision_module and user_strategy:
                self.decision_module.user_strategy = user_strategy
            
            # 重新加载系统配置
            system_config = self.config_manager.load_system_config()
            self._apply_system_config(system_config)
            
            self.logger.debug("配置重新加载完成")
            
        except Exception as e:
            self.logger.error(f"重新加载配置失败: {e}")
    
    def _apply_system_config(self, config: Dict[str, Any]):
        """应用系统配置"""
        try:
            # 更新调度器配置
            scheduler_config = config.get("modules", {}).get("scheduler", {})
            if "task_timeout" in scheduler_config:
                # 应用任务超时配置
                pass
            
            # 更新决策模块配置
            if self.decision_module:
                decision_config = config.get("modules", {}).get("decision", {})
                if decision_config:
                    self.decision_module.configure(
                        enable_mcts=config.get("mcts_enabled", True),
                        mcts_time_limit=config.get("mcts_time_limit", 5.0),
                        confidence_threshold=config.get("decision_confidence_threshold", 0.6)
                    )
            
        except Exception as e:
            self.logger.error(f"应用系统配置失败: {e}")
    
    def _validate_modules(self) -> bool:
        """验证模块初始化"""
        required_modules = 0
        initialized_modules = 0
        
        if self.config_manager.get_config_value("modules.perception.enabled", True):
            required_modules += 1
            if self.perception_module:
                initialized_modules += 1
        
        if self.config_manager.get_config_value("modules.decision.enabled", True):
            required_modules += 1
            if self.decision_module:
                initialized_modules += 1
        
        if self.config_manager.get_config_value("modules.action.enabled", True):
            required_modules += 1
            if self.action_controller:
                initialized_modules += 1
        
        return initialized_modules >= required_modules
    
    def get_scheduler_status(self) -> Dict[str, Any]:
        """获取调度器状态"""
        uptime = (datetime.now() - self.start_time).total_seconds() if self.start_time else 0
        avg_execution_time = (self.total_execution_time / max(self.total_tasks_executed, 1))
        
        return {
            "is_running": self.is_running,
            "is_paused": self.is_paused,
            "uptime_seconds": uptime,
            "total_tasks_executed": self.total_tasks_executed,
            "total_execution_time": self.total_execution_time,
            "average_execution_time": avg_execution_time,
            "consecutive_errors": self.consecutive_errors,
            "task_manager_stats": self.task_manager.get_manager_statistics(),
            "state_manager_stats": self.state_manager.get_manager_statistics(),
            "config_manager_stats": self.config_manager.get_manager_statistics()
        }
    
    def get_scheduler_info(self) -> Dict[str, Any]:
        """获取调度器信息"""
        return {
            "status": self.get_scheduler_status(),
            "modules": {
                "perception": self.perception_module is not None,
                "decision": self.decision_module is not None,
                "action": self.action_controller is not None
            },
            "task_manager": self.task_manager.get_manager_info(),
            "state_manager": self.state_manager.get_manager_info(),
            "config_manager": self.config_manager.get_manager_info()
        }
