"""
操作验证器
负责验证操作执行的结果，确保操作达到预期效果
"""

import time
from typing import Optional, Callable, Dict, Any, List
from enum import Enum

from ...utils.logger import get_logger
from ...core.data_structures import Action, GameScene, GakumasuBotException


class VerificationResult(Enum):
    """验证结果枚举"""
    SUCCESS = "success"          # 验证成功
    FAILED = "failed"           # 验证失败
    TIMEOUT = "timeout"         # 验证超时
    ERROR = "error"             # 验证错误


class VerificationError(GakumasuBotException):
    """验证错误"""
    pass


class ActionVerifier:
    """操作验证器类"""
    
    def __init__(self, perception_module=None):
        """
        初始化操作验证器
        
        Args:
            perception_module: 感知模块实例，用于获取游戏状态
        """
        self.logger = get_logger("ActionVerifier")
        self.perception_module = perception_module
        
        # 验证配置
        self.default_timeout = 10.0      # 默认验证超时时间
        self.check_interval = 0.5        # 检查间隔
        self.max_retries = 3             # 最大重试次数
        
        # 预定义验证规则
        self.verification_rules = self._init_verification_rules()
        
        self.logger.info("操作验证器初始化完成")
    
    def _init_verification_rules(self) -> Dict[str, Dict[str, Any]]:
        """
        初始化验证规则
        
        Returns:
            验证规则字典
        """
        return {
            "scene_change": {
                "description": "场景切换验证",
                "timeout": 15.0,
                "check_interval": 1.0
            },
            "ui_element_appear": {
                "description": "UI元素出现验证",
                "timeout": 5.0,
                "check_interval": 0.5
            },
            "ui_element_disappear": {
                "description": "UI元素消失验证",
                "timeout": 5.0,
                "check_interval": 0.5
            },
            "value_change": {
                "description": "数值变化验证",
                "timeout": 3.0,
                "check_interval": 0.3
            },
            "window_focus": {
                "description": "窗口焦点验证",
                "timeout": 2.0,
                "check_interval": 0.2
            }
        }
    
    def verify_scene_change(self, expected_scene: GameScene, timeout: Optional[float] = None) -> VerificationResult:
        """
        验证场景是否切换到预期场景
        
        Args:
            expected_scene: 预期的场景
            timeout: 验证超时时间
            
        Returns:
            验证结果
        """
        if not self.perception_module:
            self.logger.warning("感知模块未初始化，无法验证场景切换")
            return VerificationResult.ERROR
        
        if timeout is None:
            timeout = self.verification_rules["scene_change"]["timeout"]
        
        check_interval = self.verification_rules["scene_change"]["check_interval"]
        
        try:
            self.logger.debug(f"开始验证场景切换到: {expected_scene.value}")
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    current_scene = self.perception_module.get_current_scene()
                    
                    if current_scene == expected_scene:
                        self.logger.info(f"场景切换验证成功: {expected_scene.value}")
                        return VerificationResult.SUCCESS
                    
                    self.logger.debug(f"当前场景: {current_scene.value}, 等待切换到: {expected_scene.value}")
                    time.sleep(check_interval)
                    
                except Exception as e:
                    self.logger.debug(f"场景检查异常: {e}")
                    time.sleep(check_interval)
            
            self.logger.warning(f"场景切换验证超时: {expected_scene.value}")
            return VerificationResult.TIMEOUT
            
        except Exception as e:
            self.logger.error(f"场景切换验证错误: {e}")
            return VerificationResult.ERROR
    
    def verify_ui_element_present(self, template_name: str, timeout: Optional[float] = None) -> VerificationResult:
        """
        验证UI元素是否出现
        
        Args:
            template_name: 模板名称
            timeout: 验证超时时间
            
        Returns:
            验证结果
        """
        if not self.perception_module:
            self.logger.warning("感知模块未初始化，无法验证UI元素")
            return VerificationResult.ERROR
        
        if timeout is None:
            timeout = self.verification_rules["ui_element_appear"]["timeout"]
        
        check_interval = self.verification_rules["ui_element_appear"]["check_interval"]
        
        try:
            self.logger.debug(f"开始验证UI元素出现: {template_name}")
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    match_result = self.perception_module.find_ui_element(template_name)
                    
                    if match_result and match_result.confidence > 0.7:
                        self.logger.info(f"UI元素验证成功: {template_name}")
                        return VerificationResult.SUCCESS
                    
                    time.sleep(check_interval)
                    
                except Exception as e:
                    self.logger.debug(f"UI元素检查异常: {e}")
                    time.sleep(check_interval)
            
            self.logger.warning(f"UI元素验证超时: {template_name}")
            return VerificationResult.TIMEOUT
            
        except Exception as e:
            self.logger.error(f"UI元素验证错误: {e}")
            return VerificationResult.ERROR
    
    def verify_ui_element_absent(self, template_name: str, timeout: Optional[float] = None) -> VerificationResult:
        """
        验证UI元素是否消失
        
        Args:
            template_name: 模板名称
            timeout: 验证超时时间
            
        Returns:
            验证结果
        """
        if not self.perception_module:
            self.logger.warning("感知模块未初始化，无法验证UI元素")
            return VerificationResult.ERROR
        
        if timeout is None:
            timeout = self.verification_rules["ui_element_disappear"]["timeout"]
        
        check_interval = self.verification_rules["ui_element_disappear"]["check_interval"]
        
        try:
            self.logger.debug(f"开始验证UI元素消失: {template_name}")
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    match_result = self.perception_module.find_ui_element(template_name)
                    
                    if not match_result or match_result.confidence < 0.5:
                        self.logger.info(f"UI元素消失验证成功: {template_name}")
                        return VerificationResult.SUCCESS
                    
                    time.sleep(check_interval)
                    
                except Exception as e:
                    self.logger.debug(f"UI元素检查异常: {e}")
                    time.sleep(check_interval)
            
            self.logger.warning(f"UI元素消失验证超时: {template_name}")
            return VerificationResult.TIMEOUT
            
        except Exception as e:
            self.logger.error(f"UI元素消失验证错误: {e}")
            return VerificationResult.ERROR
    
    def verify_custom_condition(self, condition_func: Callable[[], bool], 
                               description: str = "自定义条件",
                               timeout: Optional[float] = None) -> VerificationResult:
        """
        验证自定义条件
        
        Args:
            condition_func: 条件检查函数，返回True表示条件满足
            description: 条件描述
            timeout: 验证超时时间
            
        Returns:
            验证结果
        """
        if timeout is None:
            timeout = self.default_timeout
        
        try:
            self.logger.debug(f"开始验证自定义条件: {description}")
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    if condition_func():
                        self.logger.info(f"自定义条件验证成功: {description}")
                        return VerificationResult.SUCCESS
                    
                    time.sleep(self.check_interval)
                    
                except Exception as e:
                    self.logger.debug(f"条件检查异常: {e}")
                    time.sleep(self.check_interval)
            
            self.logger.warning(f"自定义条件验证超时: {description}")
            return VerificationResult.TIMEOUT
            
        except Exception as e:
            self.logger.error(f"自定义条件验证错误: {e}")
            return VerificationResult.ERROR
    
    def verify_window_focus(self, window_title: str, timeout: Optional[float] = None) -> VerificationResult:
        """
        验证窗口是否获得焦点
        
        Args:
            window_title: 窗口标题
            timeout: 验证超时时间
            
        Returns:
            验证结果
        """
        if timeout is None:
            timeout = self.verification_rules["window_focus"]["timeout"]
        
        check_interval = self.verification_rules["window_focus"]["check_interval"]
        
        try:
            import win32gui
            
            self.logger.debug(f"开始验证窗口焦点: {window_title}")
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    foreground_window = win32gui.GetForegroundWindow()
                    current_title = win32gui.GetWindowText(foreground_window)
                    
                    if window_title in current_title:
                        self.logger.info(f"窗口焦点验证成功: {window_title}")
                        return VerificationResult.SUCCESS
                    
                    time.sleep(check_interval)
                    
                except Exception as e:
                    self.logger.debug(f"窗口焦点检查异常: {e}")
                    time.sleep(check_interval)
            
            self.logger.warning(f"窗口焦点验证超时: {window_title}")
            return VerificationResult.TIMEOUT
            
        except Exception as e:
            self.logger.error(f"窗口焦点验证错误: {e}")
            return VerificationResult.ERROR
    
    def verify_action_with_retries(self, action: Action, 
                                  verification_func: Callable[[], VerificationResult],
                                  max_retries: Optional[int] = None) -> bool:
        """
        带重试的操作验证
        
        Args:
            action: 要执行的操作
            verification_func: 验证函数
            max_retries: 最大重试次数
            
        Returns:
            是否最终验证成功
        """
        if max_retries is None:
            max_retries = self.max_retries
        
        for attempt in range(max_retries + 1):  # +1 因为第一次不算重试
            try:
                self.logger.debug(f"执行操作 (尝试 {attempt + 1}/{max_retries + 1}): {action.description}")
                
                # 执行操作（这里需要输入模拟器）
                # action_success = self.input_simulator.execute_action(action)
                # if not action_success:
                #     continue
                
                # 验证结果
                result = verification_func()
                
                if result == VerificationResult.SUCCESS:
                    self.logger.info(f"操作验证成功: {action.description}")
                    return True
                elif result == VerificationResult.TIMEOUT:
                    self.logger.warning(f"操作验证超时 (尝试 {attempt + 1}): {action.description}")
                elif result == VerificationResult.FAILED:
                    self.logger.warning(f"操作验证失败 (尝试 {attempt + 1}): {action.description}")
                else:
                    self.logger.error(f"操作验证错误 (尝试 {attempt + 1}): {action.description}")
                
                if attempt < max_retries:
                    self.logger.info(f"等待后重试...")
                    time.sleep(1.0)
                
            except Exception as e:
                self.logger.error(f"操作执行异常 (尝试 {attempt + 1}): {e}")
                if attempt < max_retries:
                    time.sleep(1.0)
        
        self.logger.error(f"操作验证最终失败: {action.description}")
        return False
    
    def create_scene_verification(self, expected_scene: GameScene) -> Callable[[], VerificationResult]:
        """
        创建场景验证函数
        
        Args:
            expected_scene: 预期场景
            
        Returns:
            验证函数
        """
        def verify():
            return self.verify_scene_change(expected_scene)
        return verify
    
    def create_ui_element_verification(self, template_name: str, should_present: bool = True) -> Callable[[], VerificationResult]:
        """
        创建UI元素验证函数
        
        Args:
            template_name: 模板名称
            should_present: 是否应该出现
            
        Returns:
            验证函数
        """
        def verify():
            if should_present:
                return self.verify_ui_element_present(template_name)
            else:
                return self.verify_ui_element_absent(template_name)
        return verify
    
    def get_verifier_info(self) -> Dict[str, Any]:
        """
        获取验证器信息
        
        Returns:
            验证器配置信息
        """
        return {
            "default_timeout": self.default_timeout,
            "check_interval": self.check_interval,
            "max_retries": self.max_retries,
            "verification_rules": self.verification_rules,
            "perception_module_available": self.perception_module is not None
        }
