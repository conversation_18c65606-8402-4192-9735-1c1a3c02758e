"""
后台运行模式管理器
统一管理后台运行模式的启用/禁用和协调各模块
"""

import time
from typing import Dict, Any, Optional, Callable
from threading import Lock
from datetime import datetime

from ..utils.logger import get_logger
from ..core.data_structures import GakumasuBotException
from .perception.enhanced_screen_capture import EnhancedScreenCapture, BackgroundCaptureMethod, WindowState
from .action.background_input_simulator import BackgroundInputSimulator, BackgroundInputMethod


class BackgroundModeError(GakumasuBotException):
    """后台模式错误"""
    pass


class BackgroundModeManager:
    """后台运行模式管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化后台模式管理器
        
        Args:
            config: 后台模式配置
        """
        self.logger = get_logger("BackgroundModeManager")
        self.config = config
        self._lock = Lock()
        
        # 状态管理
        self.is_enabled = config.get("enabled", True)
        self.is_active = False
        self.last_activity_time = time.time()
        
        # 模块实例
        self.screen_capture: Optional[EnhancedScreenCapture] = None
        self.input_simulator: Optional[BackgroundInputSimulator] = None
        
        # 性能监控
        self.performance_stats = {
            "total_operations": 0,
            "background_operations": 0,
            "foreground_operations": 0,
            "failed_operations": 0,
            "average_response_time": 0.0
        }
        
        # 事件监听器
        self.event_listeners = {
            "window_state_changed": [],
            "mode_switched": [],
            "error_occurred": []
        }
        
        self.logger.info(f"后台模式管理器初始化完成，启用状态: {self.is_enabled}")
    
    def initialize_modules(self, game_window_title: str = "gakumas"):
        """
        初始化后台模式相关模块
        
        Args:
            game_window_title: 游戏窗口标题
        """
        try:
            with self._lock:
                # 解析配置
                capture_method_str = self.config.get("capture_method", "auto")
                input_method_str = self.config.get("input_method", "auto")
                
                # 转换枚举
                capture_method = BackgroundCaptureMethod(capture_method_str)
                input_method = BackgroundInputMethod(input_method_str)
                
                # 初始化增强屏幕捕获器
                self.screen_capture = EnhancedScreenCapture(
                    game_window_title=game_window_title,
                    enable_background_mode=self.is_enabled,
                    background_capture_method=capture_method
                )
                
                # 获取窗口句柄
                window_handle = self.screen_capture.find_game_window()
                
                # 初始化后台输入模拟器
                self.input_simulator = BackgroundInputSimulator(
                    target_window_handle=window_handle,
                    enable_background_mode=self.is_enabled,
                    background_input_method=input_method
                )
                
                self.is_active = True
                self.logger.info("后台模式模块初始化完成")
                
        except Exception as e:
            self.logger.error(f"后台模式模块初始化失败: {e}")
            raise BackgroundModeError(f"后台模式模块初始化失败: {e}")
    
    def enable_background_mode(self):
        """启用后台模式"""
        with self._lock:
            self.is_enabled = True
            if self.screen_capture:
                self.screen_capture.enable_background_mode = True
            if self.input_simulator:
                self.input_simulator.enable_background_mode = True
            
            self._notify_event("mode_switched", {"enabled": True})
            self.logger.info("后台模式已启用")
    
    def disable_background_mode(self):
        """禁用后台模式"""
        with self._lock:
            self.is_enabled = False
            if self.screen_capture:
                self.screen_capture.enable_background_mode = False
            if self.input_simulator:
                self.input_simulator.enable_background_mode = False
            
            self._notify_event("mode_switched", {"enabled": False})
            self.logger.info("后台模式已禁用")
    
    def get_window_state(self) -> Optional[WindowState]:
        """获取当前窗口状态"""
        if self.screen_capture:
            return self.screen_capture.get_window_state()
        return None
    
    def is_background_operation_needed(self) -> bool:
        """判断是否需要后台操作"""
        if not self.is_enabled or not self.is_active:
            return False
        
        window_state = self.get_window_state()
        return window_state in [WindowState.VISIBLE, WindowState.MINIMIZED]
    
    def capture_screen(self, region: Optional[Dict[str, int]] = None) -> Optional[Any]:
        """
        智能屏幕捕获
        
        Args:
            region: 捕获区域
            
        Returns:
            捕获的图像
        """
        start_time = time.time()
        
        try:
            if not self.screen_capture:
                raise BackgroundModeError("屏幕捕获器未初始化")
            
            result = self.screen_capture.capture_screen(region)
            
            # 更新统计
            self._update_operation_stats(start_time, True)
            self.last_activity_time = time.time()
            
            return result
            
        except Exception as e:
            self._update_operation_stats(start_time, False)
            self._notify_event("error_occurred", {"error": str(e), "operation": "capture_screen"})
            raise
    
    def simulate_click(self, x: int, y: int, button: str = "left") -> bool:
        """
        智能鼠标点击
        
        Args:
            x: X坐标
            y: Y坐标
            button: 鼠标按键
            
        Returns:
            是否成功
        """
        start_time = time.time()
        
        try:
            if not self.input_simulator:
                raise BackgroundModeError("输入模拟器未初始化")
            
            result = self.input_simulator.click(x, y, button)
            
            # 更新统计
            self._update_operation_stats(start_time, result)
            self.last_activity_time = time.time()
            
            return result
            
        except Exception as e:
            self._update_operation_stats(start_time, False)
            self._notify_event("error_occurred", {"error": str(e), "operation": "simulate_click"})
            return False
    
    def simulate_key_press(self, key: str, duration: Optional[float] = None) -> bool:
        """
        智能按键操作
        
        Args:
            key: 按键名称
            duration: 按键持续时间
            
        Returns:
            是否成功
        """
        start_time = time.time()
        
        try:
            if not self.input_simulator:
                raise BackgroundModeError("输入模拟器未初始化")
            
            result = self.input_simulator.key_press(key, duration)
            
            # 更新统计
            self._update_operation_stats(start_time, result)
            self.last_activity_time = time.time()
            
            return result
            
        except Exception as e:
            self._update_operation_stats(start_time, False)
            self._notify_event("error_occurred", {"error": str(e), "operation": "simulate_key_press"})
            return False
    
    def refresh_window_handle(self) -> bool:
        """刷新窗口句柄"""
        try:
            if not self.screen_capture:
                return False
            
            # 强制刷新窗口句柄
            window_handle = self.screen_capture.find_game_window(force_refresh=True)
            
            if window_handle and self.input_simulator:
                self.input_simulator.set_target_window(window_handle)
                self.logger.info(f"窗口句柄已刷新: {window_handle}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"刷新窗口句柄失败: {e}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """获取后台模式状态"""
        status = {
            "enabled": self.is_enabled,
            "active": self.is_active,
            "window_state": self.get_window_state().value if self.get_window_state() else "unknown",
            "background_operation_needed": self.is_background_operation_needed(),
            "last_activity_time": self.last_activity_time,
            "performance_stats": self.performance_stats.copy()
        }
        
        # 添加模块状态
        if self.screen_capture:
            status["screen_capture"] = self.screen_capture.get_capture_stats()
        
        if self.input_simulator:
            status["input_simulator"] = self.input_simulator.get_input_stats()
        
        return status
    
    def _update_operation_stats(self, start_time: float, success: bool):
        """更新操作统计"""
        operation_time = time.time() - start_time
        
        self.performance_stats["total_operations"] += 1
        
        if success:
            if self.is_background_operation_needed():
                self.performance_stats["background_operations"] += 1
            else:
                self.performance_stats["foreground_operations"] += 1
        else:
            self.performance_stats["failed_operations"] += 1
        
        # 更新平均响应时间
        total_ops = self.performance_stats["total_operations"]
        current_avg = self.performance_stats["average_response_time"]
        self.performance_stats["average_response_time"] = (
            (current_avg * (total_ops - 1) + operation_time) / total_ops
        )
    
    def add_event_listener(self, event_type: str, listener: Callable):
        """添加事件监听器"""
        if event_type in self.event_listeners:
            self.event_listeners[event_type].append(listener)
    
    def remove_event_listener(self, event_type: str, listener: Callable):
        """移除事件监听器"""
        if event_type in self.event_listeners and listener in self.event_listeners[event_type]:
            self.event_listeners[event_type].remove(listener)
    
    def _notify_event(self, event_type: str, data: Dict[str, Any]):
        """通知事件监听器"""
        if event_type in self.event_listeners:
            for listener in self.event_listeners[event_type]:
                try:
                    listener(data)
                except Exception as e:
                    self.logger.error(f"事件监听器执行失败: {e}")
    
    def cleanup(self):
        """清理资源"""
        with self._lock:
            self.is_active = False
            
            if self.screen_capture:
                # 清理屏幕捕获器资源
                if hasattr(self.screen_capture, 'mss_instance') and self.screen_capture.mss_instance:
                    self.screen_capture.mss_instance.close()
            
            self.logger.info("后台模式管理器资源已清理")
