"""
行动控制器
统一管理所有游戏操作，包括输入模拟、游戏启动、操作验证等
"""

import time
from typing import Optional, List, Dict, Any, Callable
from datetime import datetime

from ...utils.logger import get_logger
from ...core.data_structures import Action, ActionType, GameScene, GakumasuBotException
from .input_simulator import InputSimulator, InputSimulationError
from .game_launcher import GameLauncher, GameLaunchError
from .action_verifier import ActionVerifier, VerificationResult


class ActionControllerError(GakumasuBotException):
    """行动控制器错误"""
    pass


class ActionController:
    """行动控制器主类"""
    
    def __init__(self, 
                 dmm_player_path: str,
                 perception_module=None,
                 game_window_title: str = "gakumas"):
        """
        初始化行动控制器
        
        Args:
            dmm_player_path: DMM Player路径
            perception_module: 感知模块实例
            game_window_title: 游戏窗口标题
        """
        self.logger = get_logger("ActionController")
        self.perception_module = perception_module
        self.game_window_title = game_window_title
        
        # 初始化子模块
        self.input_simulator = InputSimulator()
        self.game_launcher = GameLauncher(dmm_player_path)
        self.action_verifier = ActionVerifier(perception_module)
        
        # 操作历史记录
        self.action_history: List[Dict[str, Any]] = []
        self.max_history_size = 100
        
        # 错误恢复配置
        self.enable_auto_recovery = True
        self.recovery_attempts = 3
        self.recovery_delay = 2.0
        
        # 安全配置
        self.enable_safety_checks = True
        self.operation_delay_range = (0.1, 0.3)  # 操作间随机延迟范围
        
        self.logger.info("行动控制器初始化完成")
    
    def execute_action(self, action: Action, verify: bool = True) -> bool:
        """
        执行单个操作
        
        Args:
            action: 要执行的操作
            verify: 是否进行验证
            
        Returns:
            是否执行成功
        """
        start_time = datetime.now()
        
        try:
            self.logger.info(f"开始执行操作: {action.description}")
            
            # 安全检查
            if self.enable_safety_checks and not self._safety_check(action):
                raise ActionControllerError("安全检查失败")
            
            # 执行操作
            success = self.input_simulator.execute_action(action)
            
            if not success:
                raise ActionControllerError("输入模拟执行失败")
            
            # 记录操作历史
            self._record_action(action, success, start_time)
            
            # 验证操作结果
            if verify and action.verify_func:
                verification_result = action.verify_func()
                if verification_result != VerificationResult.SUCCESS:
                    self.logger.warning(f"操作验证失败: {action.description}")
                    success = False
            
            if success:
                self.logger.info(f"操作执行成功: {action.description}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"操作执行失败: {action.description}, 错误: {e}")
            self._record_action(action, False, start_time, str(e))
            
            # 尝试错误恢复
            if self.enable_auto_recovery:
                return self._attempt_recovery(action, e)
            
            return False
    
    def execute_action_sequence(self, actions: List[Action], 
                              stop_on_failure: bool = True) -> bool:
        """
        执行操作序列
        
        Args:
            actions: 操作列表
            stop_on_failure: 是否在失败时停止
            
        Returns:
            是否全部执行成功
        """
        self.logger.info(f"开始执行操作序列，共 {len(actions)} 个操作")
        
        success_count = 0
        
        for i, action in enumerate(actions):
            try:
                self.logger.debug(f"执行操作 {i+1}/{len(actions)}: {action.description}")
                
                success = self.execute_action(action)
                
                if success:
                    success_count += 1
                else:
                    self.logger.warning(f"操作 {i+1} 执行失败: {action.description}")
                    if stop_on_failure:
                        self.logger.error("操作序列因失败而停止")
                        break
                
                # 操作间延迟
                if i < len(actions) - 1:  # 不是最后一个操作
                    delay = self._get_random_delay()
                    time.sleep(delay)
                
            except Exception as e:
                self.logger.error(f"操作序列执行异常: {e}")
                if stop_on_failure:
                    break
        
        success_rate = success_count / len(actions) if actions else 0
        self.logger.info(f"操作序列执行完成，成功率: {success_rate:.1%} ({success_count}/{len(actions)})")
        
        return success_count == len(actions)
    
    def click_ui_element(self, template_name: str, 
                        confidence_threshold: float = 0.8,
                        timeout: float = 5.0) -> bool:
        """
        点击UI元素
        
        Args:
            template_name: 模板名称
            confidence_threshold: 置信度阈值
            timeout: 查找超时时间
            
        Returns:
            是否点击成功
        """
        if not self.perception_module:
            raise ActionControllerError("感知模块未初始化，无法定位UI元素")
        
        try:
            self.logger.info(f"查找并点击UI元素: {template_name}")
            
            # 查找UI元素
            start_time = time.time()
            match_result = None
            
            while time.time() - start_time < timeout:
                match_result = self.perception_module.find_ui_element(template_name)
                
                if match_result and match_result.confidence >= confidence_threshold:
                    break
                
                time.sleep(0.5)
            
            if not match_result or match_result.confidence < confidence_threshold:
                raise ActionControllerError(f"未找到UI元素: {template_name}")
            
            # 创建点击操作
            click_action = Action(
                action_type=ActionType.CLICK,
                target=(match_result.center_x, match_result.center_y),
                description=f"点击UI元素: {template_name}"
            )
            
            # 执行点击
            return self.execute_action(click_action)
            
        except Exception as e:
            self.logger.error(f"点击UI元素失败: {template_name}, 错误: {e}")
            return False
    
    def wait_for_scene_and_click(self, target_scene: GameScene, 
                                template_name: str,
                                scene_timeout: float = 15.0,
                                click_timeout: float = 5.0) -> bool:
        """
        等待场景出现并点击指定元素
        
        Args:
            target_scene: 目标场景
            template_name: 要点击的模板名称
            scene_timeout: 场景等待超时时间
            click_timeout: 点击查找超时时间
            
        Returns:
            是否成功
        """
        try:
            self.logger.info(f"等待场景 {target_scene.value} 并点击 {template_name}")
            
            # 等待场景出现
            if not self.perception_module:
                raise ActionControllerError("感知模块未初始化")
            
            scene_appeared = self.perception_module.wait_for_scene(
                target_scene, timeout=scene_timeout
            )
            
            if not scene_appeared:
                raise ActionControllerError(f"等待场景超时: {target_scene.value}")
            
            # 点击UI元素
            return self.click_ui_element(template_name, timeout=click_timeout)
            
        except Exception as e:
            self.logger.error(f"等待场景并点击失败: {e}")
            return False
    
    def launch_game(self) -> bool:
        """
        启动游戏
        
        Returns:
            是否启动成功
        """
        try:
            return self.game_launcher.launch_game_complete(self.game_window_title)
        except GameLaunchError as e:
            self.logger.error(f"游戏启动失败: {e}")
            return False
    
    def close_game(self) -> bool:
        """
        关闭游戏
        
        Returns:
            是否关闭成功
        """
        try:
            return self.game_launcher.close_game(self.game_window_title)
        except Exception as e:
            self.logger.error(f"游戏关闭失败: {e}")
            return False
    
    def ensure_game_focus(self) -> bool:
        """
        确保游戏窗口获得焦点
        
        Returns:
            是否成功
        """
        try:
            if not self.perception_module:
                return False
            
            # 检查游戏窗口是否已经有焦点
            if self.perception_module.screen_capture.is_game_window_active():
                return True
            
            # 尝试将游戏窗口置于前台
            success = self.perception_module.screen_capture.bring_game_window_to_front()
            
            if success:
                # 验证焦点切换
                time.sleep(1.0)
                return self.perception_module.screen_capture.is_game_window_active()
            
            return False
            
        except Exception as e:
            self.logger.error(f"确保游戏焦点失败: {e}")
            return False
    
    def _safety_check(self, action: Action) -> bool:
        """
        安全检查
        
        Args:
            action: 要检查的操作
            
        Returns:
            是否通过安全检查
        """
        try:
            # 检查操作类型是否安全
            if action.action_type == ActionType.CLICK:
                if isinstance(action.target, (tuple, list)) and len(action.target) == 2:
                    x, y = action.target
                    # 检查点击位置是否在安全范围内
                    if not self.input_simulator.is_position_safe(x, y):
                        self.logger.warning(f"点击位置不安全: ({x}, {y})")
                        return False
            
            # 检查游戏窗口是否存在
            if self.perception_module:
                window_rect = self.perception_module.screen_capture.get_window_rect()
                if not window_rect:
                    self.logger.warning("游戏窗口不存在")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"安全检查异常: {e}")
            return False
    
    def _record_action(self, action: Action, success: bool, 
                      start_time: datetime, error_msg: str = "") -> None:
        """
        记录操作历史
        
        Args:
            action: 操作对象
            success: 是否成功
            start_time: 开始时间
            error_msg: 错误信息
        """
        record = {
            "timestamp": start_time.isoformat(),
            "action_type": action.action_type.value,
            "description": action.description,
            "target": str(action.target),
            "success": success,
            "duration": (datetime.now() - start_time).total_seconds(),
            "error_msg": error_msg
        }
        
        self.action_history.append(record)
        
        # 限制历史记录大小
        if len(self.action_history) > self.max_history_size:
            self.action_history.pop(0)
    
    def _attempt_recovery(self, failed_action: Action, error: Exception) -> bool:
        """
        尝试错误恢复
        
        Args:
            failed_action: 失败的操作
            error: 错误信息
            
        Returns:
            是否恢复成功
        """
        self.logger.info(f"尝试错误恢复: {failed_action.description}")
        
        for attempt in range(self.recovery_attempts):
            try:
                time.sleep(self.recovery_delay)
                
                # 确保游戏窗口焦点
                self.ensure_game_focus()
                
                # 重新执行操作
                success = self.input_simulator.execute_action(failed_action)
                
                if success:
                    self.logger.info(f"错误恢复成功 (尝试 {attempt + 1})")
                    return True
                
            except Exception as e:
                self.logger.debug(f"恢复尝试 {attempt + 1} 失败: {e}")
        
        self.logger.warning("错误恢复失败")
        return False
    
    def _get_random_delay(self) -> float:
        """
        获取随机延迟时间
        
        Returns:
            延迟时间（秒）
        """
        import random
        return random.uniform(*self.operation_delay_range)
    
    def get_action_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取操作历史
        
        Args:
            limit: 返回记录数量限制
            
        Returns:
            操作历史列表
        """
        return self.action_history[-limit:] if self.action_history else []
    
    def get_controller_info(self) -> Dict[str, Any]:
        """
        获取控制器信息
        
        Returns:
            控制器配置信息
        """
        return {
            "game_window_title": self.game_window_title,
            "enable_auto_recovery": self.enable_auto_recovery,
            "recovery_attempts": self.recovery_attempts,
            "recovery_delay": self.recovery_delay,
            "enable_safety_checks": self.enable_safety_checks,
            "operation_delay_range": self.operation_delay_range,
            "action_history_size": len(self.action_history),
            "max_history_size": self.max_history_size,
            "input_simulator_info": self.input_simulator.get_simulator_info(),
            "game_launcher_info": self.game_launcher.get_launcher_info(),
            "action_verifier_info": self.action_verifier.get_verifier_info()
        }
