# Gakumasu-Bot 稳定性优先配置预设
# 适用于长时间运行，追求最高稳定性
# 系统要求: 8GB+ 内存, 4核+ CPU

system:
  log_level: INFO
  language: ja
  screen_resolution: [1920, 1080]
  game_window_title: "gakumas"

dmm:
  dmm_player_path: ""  # 需要用户配置
  launch_timeout: 90
  auto_launch: true
  post_launch_wait: 15

performance:
  screenshot_interval: 0.8        # 较长间隔，减少资源占用
  action_delay_min: 0.1          # 较长延迟，模拟人类操作
  action_delay_max: 0.3
  decision_timeout: 45.0         # 充足的决策时间
  enable_gpu_acceleration: false  # 禁用GPU，避免驱动问题
  max_worker_threads: 2          # 保守的线程数
  memory_cache_size: 128         # 适中的缓存

template_matching:
  confidence_threshold: 0.75      # 较低阈值，提高匹配成功率
  enable_multi_scale: true
  scale_range: [0.9, 1.1]        # 保守的尺度范围
  scale_step: 0.1
  enable_template_cache: true
  cache_expiry_time: 300

ocr:
  enable_ocr: true
  languages: ['ja', 'en']
  confidence_threshold: 0.6       # 较低OCR阈值
  use_gpu: false
  preprocessing:
    enable_denoising: true
    enable_contrast_enhancement: true
    enable_binarization: false    # 禁用可能不稳定的处理

ai:
  enable_mcts: true
  mcts_iterations: 800           # 适中的迭代次数
  mcts_timeout: 12.0
  mcts_exploration_constant: 1.0  # 保守的探索参数
  heuristic_weights:
    score: 1.0
    stamina: 1.2               # 更重视体力管理
    vigor: 0.8
    card_synergy: 0.4
    risk_factor: 0.6           # 更重视风险控制
  enable_decision_cache: true
  decision_cache_size: 500
  enable_online_learning: false  # 禁用学习，保持稳定

error_handling:
  max_retries: 5                 # 更多重试次数
  retry_interval: 3.0           # 更长重试间隔
  enable_auto_recovery: true
  recovery_strategy: "restart"
  error_log_level: "verbose"
  save_error_screenshots: true

debug:
  enable_debug_mode: false
  save_debug_screenshots: false
  enable_verbose_logging: false
  debug_info_interval: 10.0
