"""
游戏场景基类
为具体的游戏场景提供通用功能和模板方法
"""

import time
from typing import Dict, List, Optional, Any
from abc import abstractmethod

from ....utils.logger import get_logger
from ....core.data_structures import GameScene
from ..base.base_scene import BaseScene
from ..config.scene_config import SceneConfig
from ..elements.ui_element_factory import UIElementFactory


class BaseGameScene(BaseScene):
    """游戏场景基类 - 为具体游戏场景提供通用功能"""
    
    def __init__(self, config: SceneConfig, perception_module, action_controller):
        """
        初始化游戏场景
        
        Args:
            config: 场景配置
            perception_module: 感知模块
            action_controller: 行动控制器
        """
        super().__init__(config, perception_module, action_controller)
        
        # UI元素工厂
        self.ui_factory = UIElementFactory(perception_module, action_controller)
        
        # 场景特定状态
        self._scene_ready = False
        self._last_interaction_time = 0.0
        self._interaction_count = 0
        
        # 场景验证相关
        self._verification_attempts = 0
        self._max_verification_attempts = 3
        
        # 初始化场景特定的日志记录器
        self.scene_logger = get_logger(f"Scene.{self.__class__.__name__}")
    
    def _init_ui_elements(self):
        """初始化UI元素（实现基类的抽象方法）"""
        try:
            self.scene_logger.info(f"初始化场景UI元素: {self.config.scene_name}")
            
            # 调用子类的具体初始化方法
            self._create_scene_ui_elements()
            
            # 验证关键UI元素
            if self._validate_critical_elements():
                self._scene_ready = True
                self.scene_logger.info(f"场景UI元素初始化完成: {len(self.ui_elements)}个元素")
            else:
                self.scene_logger.warning("关键UI元素验证失败")
                
        except Exception as e:
            self.scene_logger.error(f"UI元素初始化失败: {e}")
            self._scene_ready = False
    
    @abstractmethod
    def _create_scene_ui_elements(self):
        """创建场景特定的UI元素（子类实现）"""
        pass
    
    @abstractmethod
    def _get_critical_elements(self) -> List[str]:
        """获取关键UI元素列表（子类实现）"""
        pass
    
    def _validate_critical_elements(self) -> bool:
        """验证关键UI元素是否存在"""
        critical_elements = self._get_critical_elements()
        
        for element_name in critical_elements:
            if element_name not in self.ui_elements:
                self.scene_logger.error(f"缺少关键UI元素: {element_name}")
                return False
        
        return True
    
    def is_scene_ready(self) -> bool:
        """检查场景是否准备就绪"""
        return self._scene_ready
    
    def wait_for_scene_ready(self, timeout: float = 15.0) -> bool:
        """等待场景准备就绪"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if self.is_current_scene() and self.is_scene_ready():
                return True
            
            # 如果场景未准备就绪，尝试重新初始化
            if self.is_current_scene() and not self.is_scene_ready():
                self._init_ui_elements()
            
            time.sleep(0.5)
        
        return False
    
    def interact_with_element(self, element_name: str, action: str = "click", **kwargs) -> bool:
        """
        与UI元素交互
        
        Args:
            element_name: 元素名称
            action: 交互动作
            **kwargs: 额外参数
            
        Returns:
            是否交互成功
        """
        try:
            element = self.get_ui_element(element_name)
            if not element:
                self.scene_logger.error(f"未找到UI元素: {element_name}")
                return False
            
            # 记录交互
            self._last_interaction_time = time.time()
            self._interaction_count += 1
            
            # 执行交互
            if action == "click":
                result = element.click()
            elif action == "input" and hasattr(element, 'input_text'):
                text = kwargs.get('text', '')
                result = element.input_text(text)
            elif action == "read" and hasattr(element, 'read_text'):
                text = element.read_text()
                result = text is not None
                if result:
                    kwargs['result_text'] = text
            else:
                self.scene_logger.error(f"不支持的交互动作: {action}")
                return False
            
            if result:
                self.scene_logger.debug(f"成功与元素交互: {element_name} - {action}")
            else:
                self.scene_logger.warning(f"元素交互失败: {element_name} - {action}")
            
            return result
            
        except Exception as e:
            self.scene_logger.error(f"元素交互异常: {element_name} - {action}, 错误: {e}")
            return False
    
    def verify_scene_state(self) -> bool:
        """验证场景状态"""
        try:
            self._verification_attempts += 1
            
            # 基础验证：场景是否为当前场景
            if not self.is_current_scene():
                self.scene_logger.debug("场景状态验证失败: 不是当前场景")
                return False
            
            # 验证关键元素可见性
            critical_elements = self._get_critical_elements()
            for element_name in critical_elements:
                element = self.get_ui_element(element_name)
                if element and not element.is_visible():
                    self.scene_logger.debug(f"场景状态验证失败: 关键元素不可见 - {element_name}")
                    return False
            
            # 调用子类的具体验证逻辑
            if not self._verify_scene_specific_state():
                return False
            
            self.scene_logger.debug("场景状态验证成功")
            return True
            
        except Exception as e:
            self.scene_logger.error(f"场景状态验证异常: {e}")
            return False
    
    def _verify_scene_specific_state(self) -> bool:
        """验证场景特定状态（子类可重写）"""
        return True
    
    def _execute_navigation(self, target_scene: GameScene, timeout: float) -> bool:
        """执行导航逻辑（重写基类方法）"""
        try:
            self.scene_logger.info(f"开始导航: {self.config.scene_name} -> {target_scene.value}")
            
            # 检查是否有预定义的导航路径
            navigation_method = getattr(self, f'_navigate_to_{target_scene.value.lower()}', None)
            if navigation_method:
                return navigation_method(timeout)
            
            # 使用通用导航逻辑
            return self._execute_generic_navigation(target_scene, timeout)
            
        except Exception as e:
            self.scene_logger.error(f"导航执行失败: {e}")
            return False
    
    def _execute_generic_navigation(self, target_scene: GameScene, timeout: float) -> bool:
        """执行通用导航逻辑"""
        try:
            # 使用配置中的导航步骤
            if self.config.navigation and self.config.navigation.steps:
                return self._execute_navigation_steps(self.config.navigation.steps, timeout)
            
            # 如果没有配置导航步骤，尝试直接导航
            self.scene_logger.warning(f"没有配置导航步骤，尝试直接导航到: {target_scene.value}")
            return False
            
        except Exception as e:
            self.scene_logger.error(f"通用导航执行失败: {e}")
            return False
    
    def _execute_navigation_steps(self, steps, timeout: float) -> bool:
        """执行导航步骤"""
        try:
            start_time = time.time()
            
            for i, step in enumerate(steps):
                remaining_time = timeout - (time.time() - start_time)
                if remaining_time <= 0:
                    self.scene_logger.error("导航超时")
                    return False
                
                step_timeout = min(remaining_time, step.timeout)
                
                if step.action_type == "click":
                    success = self.interact_with_element(step.target, "click")
                elif step.action_type == "wait":
                    success = self._wait_for_element(step.target, step_timeout)
                elif step.action_type == "verify":
                    success = self._verify_element_visible(step.target)
                else:
                    self.scene_logger.error(f"不支持的导航步骤类型: {step.action_type}")
                    success = False
                
                if not success and not step.optional:
                    self.scene_logger.error(f"导航步骤失败: {step.description}")
                    return False
                
                # 步骤间延迟
                time.sleep(0.2)
            
            return True
            
        except Exception as e:
            self.scene_logger.error(f"导航步骤执行失败: {e}")
            return False
    
    def _wait_for_element(self, element_name: str, timeout: float) -> bool:
        """等待元素出现"""
        element = self.get_ui_element(element_name)
        if not element:
            return False
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            if element.is_visible():
                return True
            time.sleep(0.1)
        
        return False
    
    def _verify_element_visible(self, element_name: str) -> bool:
        """验证元素可见"""
        element = self.get_ui_element(element_name)
        return element is not None and element.is_visible()
    
    def get_scene_statistics(self) -> Dict[str, Any]:
        """获取场景统计信息"""
        base_stats = self.get_scene_info()
        
        scene_stats = {
            'scene_ready': self._scene_ready,
            'last_interaction_time': self._last_interaction_time,
            'interaction_count': self._interaction_count,
            'verification_attempts': self._verification_attempts,
            'critical_elements': self._get_critical_elements(),
            'critical_elements_status': {
                name: self.get_ui_element(name).is_visible() if self.get_ui_element(name) else False
                for name in self._get_critical_elements()
            }
        }
        
        return {**base_stats, **scene_stats}
    
    def reset_scene_state(self):
        """重置场景状态"""
        self._scene_ready = False
        self._last_interaction_time = 0.0
        self._interaction_count = 0
        self._verification_attempts = 0
        
        # 清理缓存
        self.clear_cache()
        
        # 重新初始化UI元素
        self._init_ui_elements()
        
        self.scene_logger.info("场景状态已重置")
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"{self.__class__.__name__}(scene='{self.config.scene_name}', ready={self._scene_ready})"
