"""
直接测试API端点
通过HTTP请求测试截图API
"""

import requests
import json
import time


def test_screenshot_api():
    """测试截图API"""
    print("🧪 直接测试截图API...")
    
    # API端点
    base_url = "http://localhost:8000"
    api_url = f"{base_url}/api/v1/screenshot/capture"
    
    # 测试配置 - 使用字符串值
    test_config = {
        "config": {
            "mode": "window",
            "format": "png", 
            "quality": 90,
            "save_to_disk": True,
            "filename_prefix": "api_direct_test"
        }
    }
    
    try:
        print(f"📡 发送请求到: {api_url}")
        print(f"📋 请求数据: {json.dumps(test_config, indent=2)}")
        
        # 发送POST请求
        response = requests.post(
            api_url,
            json=test_config,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📊 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ API调用成功!")
            print(f"📋 响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ API调用失败!")
            print(f"📋 错误响应: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败 - 请确保服务器正在运行")
        return False
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False


def test_health_check():
    """测试健康检查"""
    print("\n🧪 测试健康检查...")
    
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        
        if response.status_code == 200:
            print("✅ 服务器健康检查通过")
            print(f"📋 健康状态: {response.json()}")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False


def test_screenshot_stats():
    """测试统计信息API"""
    print("\n🧪 测试统计信息API...")
    
    try:
        response = requests.get("http://localhost:8000/api/v1/screenshot/stats", timeout=10)
        
        if response.status_code == 200:
            stats = response.json()
            print("✅ 统计信息获取成功")
            print(f"📊 统计数据: {json.dumps(stats, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ 统计信息获取失败: {response.status_code}")
            print(f"📋 错误响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 统计信息获取异常: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始直接API测试")
    print("=" * 50)
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(2)
    
    # 执行测试
    tests = [
        ("健康检查", test_health_check),
        ("统计信息API", test_screenshot_stats),
        ("截图API", test_screenshot_api)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📊 直接API测试结果:")
    
    passed_count = 0
    total_count = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed_count += 1
    
    print(f"\n总体结果: {passed_count}/{total_count} 通过 ({passed_count/total_count*100:.1f}%)")
    
    if passed_count >= total_count * 0.8:
        print("🎉 API测试基本通过！")
        return True
    else:
        print("⚠️ API测试存在问题")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
