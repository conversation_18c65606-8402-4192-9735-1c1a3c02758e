# 调试控制台设计文档

## 文档信息

| **项目名称** | Gakumasu-Bot 调试控制台 |
| **文档版本** | V1.0 |
| **创建日期** | 2025-08-05 |
| **创建人** | AI Assistant |
| **文档类型** | 功能设计文档 |

## 1. 项目现状分析

### 1.1 架构解耦程度评估

**结论：项目具有极高的模块解耦程度，完全支持调试控制台开发**

**分析依据：**

1. **四层架构设计**：
   - 用户界面层：Web UI、GUI启动器、命令行界面
   - API服务层：FastAPI Web服务，提供完整的REST API和WebSocket
   - 核心业务层：感知、决策、行动、调度四大模块独立运行
   - 基础设施层：日志、配置、数据存储

2. **模块独立性**：
   - 各模块通过标准化接口通信
   - 核心业务逻辑与界面层完全分离
   - 支持多种用户界面（Web、GUI、CLI）同时存在

3. **接口完整性**：
   - 系统管理接口：健康检查、状态查询、启动/停止/重启
   - 配置管理接口：获取/更新/重载配置
   - 游戏控制接口：游戏状态查询、启动游戏、执行操作、获取截图
   - WebSocket事件：实时状态更新、游戏状态变化、操作结果、错误事件

### 1.2 现有API接口分析

**结论：现有API接口完全满足调试控制台需求，无需额外开发后端功能**

**可用接口清单：**

1. **系统状态监控**：
   - `GET /api/v1/status` - 获取系统状态
   - `GET /health` - 健康检查
   - `WebSocket /ws` - 实时状态更新

2. **系统控制**：
   - `POST /api/v1/control` - 系统控制（启动/停止/重启/暂停/恢复）
   - `POST /api/v1/tasks` - 创建任务
   - `GET /api/v1/tasks` - 获取任务列表

3. **配置管理**：
   - `GET /api/v1/config` - 获取配置
   - `POST /api/v1/config` - 更新配置

4. **游戏功能测试**：
   - `GET /api/v1/screenshot/preview` - 获取截图预览
   - `POST /api/v1/screenshot/capture` - 执行截图
   - `GET /api/v1/screenshot/history` - 获取截图历史

### 1.3 技术可行性分析

**结论：基于现有技术栈，实现调试控制台的最佳方案是集成到现有Vue.js前端**

**技术优势：**

1. **现有技术栈完整**：
   - 前端：Vue.js 3.4.0+ + Element Plus + Vite
   - 后端：FastAPI + WebSocket
   - 已有组合式API：useWebSocket、useScreenshotApi

2. **开发效率高**：
   - 复用现有UI组件和样式
   - 利用现有API接口，无需后端开发
   - 集成到现有导航系统

3. **用户体验一致**：
   - 统一的界面风格
   - 一致的交互模式
   - 无需额外部署

## 2. 功能需求设计

### 2.1 核心功能模块

#### 2.1.1 系统状态监控面板
- **实时系统状态**：显示四大模块（感知、决策、行动、调度）运行状态
- **性能指标**：CPU、内存使用率，屏幕捕获延迟、决策时间等
- **连接状态**：WebSocket连接状态，API响应时间
- **任务统计**：活跃任务数、已完成任务数、失败任务数

#### 2.1.2 游戏状态监控
- **当前游戏场景**：实时显示识别到的游戏场景
- **GameState详情**：体力、分数、手牌、偶像属性等详细信息
- **实时截图**：显示当前游戏画面
- **场景识别结果**：识别置信度、匹配模板信息

#### 2.1.3 功能测试区域
- **感知模块测试**：
  * 手动触发屏幕捕获
  * 场景识别测试
  * 模板匹配验证
- **决策模块测试**：
  * 模拟决策过程
  * MCTS算法结果展示
  * 启发式评分详情
- **行动模块测试**：
  * 执行指定操作
  * 操作验证结果
  * 输入模拟测试
- **调度模块测试**：
  * 任务调度状态
  * 配置管理测试
  * 状态持久化验证

#### 2.1.4 日志和调试工具
- **实时日志流**：显示系统日志，支持自动滚动
- **日志级别过滤**：DEBUG、INFO、WARNING、ERROR、CRITICAL
- **错误追踪**：堆栈信息显示，错误统计
- **调试截图查看器**：查看调试过程中的截图

#### 2.1.5 配置管理界面
- **动态配置修改**：实时修改系统配置参数
- **配置验证**：参数有效性检查
- **预设配置**：快速切换预设配置方案
- **配置导入导出**：配置文件的备份和恢复

### 2.2 用户界面设计

#### 2.2.1 布局结构
```
┌─────────────────────────────────────────────────────────┐
│                    顶部导航栏                            │
├─────────────────────────────────────────────────────────┤
│  侧边栏    │                主内容区域                   │
│           │  ┌─────────────────────────────────────────┐ │
│  - 系统状态 │  │            系统状态监控面板              │ │
│  - 游戏监控 │  └─────────────────────────────────────────┘ │
│  - 功能测试 │  ┌─────────────────────────────────────────┐ │
│  - 日志查看 │  │            游戏状态监控区域              │ │
│  - 配置管理 │  └─────────────────────────────────────────┘ │
│           │  ┌─────────────────────────────────────────┐ │
│           │  │            功能测试控制台                │ │
│           │  └─────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

#### 2.2.2 组件设计
- **StatusCard**：状态卡片组件，显示各种状态信息
- **GameStateViewer**：游戏状态查看器
- **TestPanel**：功能测试面板
- **LogViewer**：日志查看器
- **ConfigEditor**：配置编辑器

## 3. 技术实现方案

### 3.1 前端实现方案

#### 3.1.1 路由配置
在 `frontend/src/router/index.js` 中添加调试控制台路由：

```javascript
{
  path: '/debug-console',
  name: 'DebugConsole',
  component: () => import('../views/DebugConsole.vue'),
  meta: {
    title: '调试控制台',
    icon: 'Tools',
    description: '系统调试和功能测试工具'
  }
}
```

#### 3.1.2 主要组件结构
- `DebugConsole.vue` - 主页面组件
- `components/debug/SystemStatusPanel.vue` - 系统状态面板
- `components/debug/GameStateMonitor.vue` - 游戏状态监控
- `components/debug/FunctionTestPanel.vue` - 功能测试面板
- `components/debug/LogViewer.vue` - 日志查看器
- `components/debug/ConfigManager.vue` - 配置管理器

#### 3.1.3 API集成
利用现有的组合式API：
- `useWebSocket.js` - WebSocket连接管理
- `useApi.js` - HTTP API调用（需要创建）
- `useDebugConsole.js` - 调试控制台专用API（需要创建）

### 3.2 后端扩展（如需要）

基于分析，现有API接口已经完全满足需求，无需额外开发后端功能。如果需要扩展，可以在以下方面：

1. **调试专用API**：
   - `GET /api/v1/debug/modules` - 获取模块详细状态
   - `POST /api/v1/debug/test` - 执行功能测试
   - `GET /api/v1/debug/logs` - 获取日志数据

2. **WebSocket事件扩展**：
   - `debug_test_result` - 测试结果事件
   - `module_status_change` - 模块状态变化事件

## 4. 实施计划

### 4.1 开发阶段

**阶段1：基础框架搭建（预计1天）**
1. 创建DebugConsole.vue主页面
2. 设计基础布局和导航
3. 集成到现有路由系统

**阶段2：系统状态监控（预计1天）**
1. 实现SystemStatusPanel组件
2. 集成WebSocket实时更新
3. 显示系统性能指标

**阶段3：游戏状态监控（预计1天）**
1. 实现GameStateMonitor组件
2. 集成截图预览功能
3. 显示GameState详细信息

**阶段4：功能测试面板（预计2天）**
1. 实现各模块测试功能
2. 集成API调用和结果显示
3. 添加测试结果可视化

**阶段5：日志和配置管理（预计1天）**
1. 实现LogViewer组件
2. 实现ConfigManager组件
3. 添加配置验证和保存功能

**阶段6：优化和测试（预计1天）**
1. 界面优化和响应式设计
2. 功能测试和bug修复
3. 性能优化

### 4.2 测试计划

1. **单元测试**：各组件功能测试
2. **集成测试**：API接口集成测试
3. **用户体验测试**：界面交互测试
4. **性能测试**：实时更新性能测试

### 4.3 部署计划

1. **开发环境测试**：本地开发环境验证
2. **集成测试**：与现有系统集成测试
3. **文档更新**：更新用户手册和技术文档

## 5. 预期效果

### 5.1 功能效果
- 提供完整的系统调试能力
- 实时监控系统运行状态
- 方便开发者测试各模块功能
- 提升开发和维护效率

### 5.2 技术效果
- 充分利用现有技术栈
- 无需额外后端开发
- 保持系统架构一致性
- 提供良好的用户体验

### 5.3 维护效果
- 降低系统调试难度
- 提高问题定位效率
- 简化配置管理流程
- 增强系统可观测性

---

**下一步行动**：
1. 确认设计方案
2. 开始实施阶段1：基础框架搭建
3. 逐步实现各功能模块
4. 持续测试和优化
