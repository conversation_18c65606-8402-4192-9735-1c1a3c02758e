# 剧本配置文件
# 定义游戏中可用的剧本类型和属性

scenarios:
  # NIA剧本配置
  - scenario_type: "nia"
    name: "nia_scenario"
    display_name: "NIA剧本"
    description: "NIA主题的育成剧本，注重平衡发展，适合新手玩家体验完整的育成流程"
    
    # 特殊属性
    special_attributes:
      reward_multiplier: 1.0          # 奖励倍率
      experience_multiplier: 1.0      # 经验倍率
      focus_stats:                    # 重点属性
        - "vocal"
        - "dance"
        - "visual"
        - "mental"
      special_events:                 # 特殊事件
        - "nia_special_1"
        - "nia_special_2"
        - "nia_ending_event"
      event_probability: 0.15         # 特殊事件概率
      stamina_efficiency: 1.0         # 体力效率
      skill_growth_rate: 1.0          # 技能成长率
    
    # 解锁条件
    unlock_conditions:
      min_level: 1                    # 最低等级要求
      required_achievements: []       # 必需成就
      min_play_time: 0               # 最低游戏时间（小时）
    
    # 奖励设置
    rewards:
      base_exp: 1000                  # 基础经验
      bonus_items:                    # 奖励物品
        - "nia_badge"
        - "memory_piece"
        - "training_manual"
      completion_rewards:             # 完成奖励
        - type: "currency"
          amount: 5000
        - type: "item"
          item_id: "nia_trophy"
          amount: 1
    
    # 可用性和版本
    is_available: true
    version: "1.0.0"

  # HAJIME剧本配置
  - scenario_type: "hajime"
    name: "hajime_scenario"
    display_name: "HAJIME剧本"
    description: "HAJIME主题的育成剧本，注重技能提升和高级策略，适合有经验的玩家"
    
    # 特殊属性
    special_attributes:
      reward_multiplier: 1.1          # 更高的奖励倍率
      experience_multiplier: 1.05     # 略高的经验倍率
      focus_stats:                    # 重点属性（更专注）
        - "vocal"
        - "dance"
      special_events:                 # 特殊事件
        - "hajime_special_1"
        - "hajime_special_2"
        - "hajime_challenge_event"
        - "hajime_ending_event"
      event_probability: 0.20         # 更高的特殊事件概率
      stamina_efficiency: 0.95        # 略低的体力效率（更有挑战性）
      skill_growth_rate: 1.15         # 更高的技能成长率
      advanced_mechanics: true        # 启用高级机制
    
    # 解锁条件
    unlock_conditions:
      min_level: 5                    # 更高的等级要求
      required_achievements:          # 需要完成NIA剧本
        - "complete_nia_scenario"
      min_play_time: 10              # 最低游戏时间要求
    
    # 奖励设置
    rewards:
      base_exp: 1200                  # 更高的基础经验
      bonus_items:                    # 更好的奖励物品
        - "hajime_badge"
        - "skill_book"
        - "advanced_training_manual"
        - "rare_memory_piece"
      completion_rewards:             # 完成奖励
        - type: "currency"
          amount: 8000
        - type: "item"
          item_id: "hajime_trophy"
          amount: 1
        - type: "item"
          item_id: "master_certificate"
          amount: 1
    
    # 可用性和版本
    is_available: true
    version: "1.0.0"

# 全局剧本设置
global_settings:
  # 默认设置
  default_language: "ja"
  enable_auto_save: true
  save_interval: 300                  # 自动保存间隔（秒）
  
  # 性能设置
  max_concurrent_events: 3
  event_processing_timeout: 30.0
  
  # 调试设置
  debug_mode: false
  log_events: true
  detailed_logging: false

# 扩展配置（为未来新剧本预留）
extension_scenarios:
  # 示例：未来可能的剧本类型
  # - scenario_type: "future_scenario"
  #   name: "future_scenario"
  #   display_name: "未来剧本"
  #   description: "未来版本的剧本"
  #   is_available: false
  #   version: "2.0.0"
