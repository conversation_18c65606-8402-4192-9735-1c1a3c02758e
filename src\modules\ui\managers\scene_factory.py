"""
场景工厂类
使用传统Class管理复杂场景创建逻辑
"""

import time
from typing import Dict, Type, Optional, Any, Callable

from ....utils.logger import get_logger
from ....core.data_structures import GameScene
from ..base.base_scene import BaseScene
from ..config.scene_config import SceneConfig, DEFAULT_SCENE_CONFIGS
from .config_loader import UIConfigLoader


class SceneFactory:
    """场景工厂类 - 使用传统Class管理复杂创建逻辑"""
    
    def __init__(self, perception_module, action_controller, config_loader: UIConfigLoader):
        """
        初始化场景工厂
        
        Args:
            perception_module: 感知模块
            action_controller: 行动控制器
            config_loader: 配置加载器
        """
        self.perception = perception_module
        self.action = action_controller
        self.config_loader = config_loader
        self.logger = get_logger("SceneFactory")
        
        # 场景类映射
        self._scene_classes: Dict[GameScene, Type[BaseScene]] = {}
        
        # 场景创建统计
        self._creation_stats = {
            'total_created': 0,
            'creation_times': {},
            'creation_errors': {}
        }
        
        # 自定义创建器
        self._custom_creators: Dict[GameScene, Callable] = {}
        
        # 初始化默认场景类
        self._register_default_scenes()
    
    def _register_default_scenes(self):
        """注册默认场景类"""
        # 这里需要导入具体的场景类
        # 为了避免循环导入，我们延迟导入
        try:
            from ..scenes.main_menu import MainMenuScene
            from ..scenes.produce_setup import ProduceSetupScene
            from ..scenes.produce_main import ProduceMainScene

            self._scene_classes.update({
                GameScene.MAIN_MENU: MainMenuScene,
                GameScene.PRODUCE_SETUP: ProduceSetupScene,
                GameScene.PRODUCE_MAIN: ProduceMainScene,
            })

            self.logger.info(f"默认场景类注册完成，共注册{len(self._scene_classes)}个场景类")

            # 记录注册的场景类型
            for scene_type, scene_class in self._scene_classes.items():
                self.logger.debug(f"注册场景: {scene_type.value} -> {scene_class.__name__}")

        except ImportError as e:
            self.logger.warning(f"部分场景类导入失败: {e}")
            # 尝试单独导入每个场景类，提供更详细的错误信息
            self._register_scenes_individually()

    def _register_scenes_individually(self):
        """单独注册每个场景类，提供详细的错误信息"""
        scene_imports = [
            (GameScene.MAIN_MENU, "..scenes.main_menu", "MainMenuScene"),
            (GameScene.PRODUCE_SETUP, "..scenes.produce_setup", "ProduceSetupScene"),
            (GameScene.PRODUCE_MAIN, "..scenes.produce_main", "ProduceMainScene"),
        ]

        for scene_type, module_path, class_name in scene_imports:
            try:
                module = __import__(module_path, fromlist=[class_name])
                scene_class = getattr(module, class_name)
                self._scene_classes[scene_type] = scene_class
                self.logger.info(f"成功注册场景: {scene_type.value} -> {class_name}")
            except ImportError as e:
                self.logger.error(f"导入场景类失败: {class_name} from {module_path}, 错误: {e}")
            except AttributeError as e:
                self.logger.error(f"场景类不存在: {class_name} in {module_path}, 错误: {e}")
            except Exception as e:
                self.logger.error(f"注册场景类时发生未知错误: {class_name}, 错误: {e}")

    def register_all_available_scenes(self):
        """注册所有可用的场景类（包括扩展场景）"""
        try:
            # 注册核心场景
            self._register_default_scenes()

            # 尝试注册扩展场景
            self._register_extended_scenes()

            self.logger.info(f"场景注册完成，总共注册了{len(self._scene_classes)}个场景类")

        except Exception as e:
            self.logger.error(f"注册所有场景类失败: {e}")

    def _register_extended_scenes(self):
        """注册扩展场景类"""
        extended_scene_imports = [
            # 可以在这里添加更多场景类
            # (GameScene.PRODUCE_BATTLE, "..scenes.produce_battle", "ProduceBattleScene"),
            # (GameScene.PRODUCE_EXAM, "..scenes.produce_exam", "ProduceExamScene"),
            # (GameScene.PRODUCE_RESULT, "..scenes.produce_result", "ProduceResultScene"),
        ]

        for scene_type, module_path, class_name in extended_scene_imports:
            try:
                module = __import__(module_path, fromlist=[class_name])
                scene_class = getattr(module, class_name)
                self._scene_classes[scene_type] = scene_class
                self.logger.info(f"成功注册扩展场景: {scene_type.value} -> {class_name}")
            except ImportError:
                self.logger.debug(f"扩展场景类不可用: {class_name}")
            except Exception as e:
                self.logger.warning(f"注册扩展场景类失败: {class_name}, 错误: {e}")

    def get_registered_scenes_info(self) -> Dict[str, Any]:
        """获取已注册场景的详细信息"""
        scenes_info = {}

        for scene_type, scene_class in self._scene_classes.items():
            scenes_info[scene_type.value] = {
                'scene_type': scene_type.value,
                'class_name': scene_class.__name__,
                'module': scene_class.__module__,
                'is_available': True
            }

        # 添加未注册的场景信息
        all_scenes = set(GameScene)
        registered_scenes = set(self._scene_classes.keys())
        unregistered_scenes = all_scenes - registered_scenes

        for scene_type in unregistered_scenes:
            scenes_info[scene_type.value] = {
                'scene_type': scene_type.value,
                'class_name': None,
                'module': None,
                'is_available': False
            }

        return scenes_info
    
    def create_scene(self, scene_type: GameScene, 
                    config: Optional[SceneConfig] = None) -> Optional[BaseScene]:
        """
        创建场景实例
        
        Args:
            scene_type: 场景类型
            config: 场景配置，如果为None则从配置文件加载
            
        Returns:
            场景实例，如果不支持该场景类型返回None
        """
        start_time = time.time()
        
        try:
            self.logger.debug(f"创建场景: {scene_type.value}")
            
            # 检查自定义创建器
            if scene_type in self._custom_creators:
                return self._create_with_custom_creator(scene_type, config)
            
            # 获取场景类
            scene_class = self._scene_classes.get(scene_type)
            if not scene_class:
                self.logger.error(f"不支持的场景类型: {scene_type.value}")
                self._record_creation_error(scene_type, "不支持的场景类型")
                return None
            
            # 获取或创建配置
            if config is None:
                config = self._load_scene_config(scene_type)
                if config is None:
                    return None
            
            # 创建场景实例
            scene = scene_class(config, self.perception, self.action)
            
            # 记录创建统计
            creation_time = time.time() - start_time
            self._record_creation_success(scene_type, creation_time)
            
            self.logger.info(f"成功创建场景: {scene_type.value}, 耗时: {creation_time:.3f}秒")
            return scene
            
        except Exception as e:
            creation_time = time.time() - start_time
            self.logger.error(f"创建场景失败: {scene_type.value}, 错误: {e}, 耗时: {creation_time:.3f}秒")
            self._record_creation_error(scene_type, str(e))
            return None
    
    def _create_with_custom_creator(self, scene_type: GameScene, 
                                   config: Optional[SceneConfig]) -> Optional[BaseScene]:
        """使用自定义创建器创建场景"""
        try:
            creator = self._custom_creators[scene_type]
            scene = creator(scene_type, config, self.perception, self.action)
            self.logger.info(f"使用自定义创建器创建场景: {scene_type.value}")
            return scene
            
        except Exception as e:
            self.logger.error(f"自定义创建器执行失败: {scene_type.value}, 错误: {e}")
            return None
    
    def _load_scene_config(self, scene_type: GameScene) -> Optional[SceneConfig]:
        """加载场景配置"""
        try:
            # 首先尝试从配置文件加载
            config = self.config_loader.load_scene_config(scene_type)
            if config:
                return config
            
            # 如果配置文件中没有，使用默认配置
            default_config = DEFAULT_SCENE_CONFIGS.get(scene_type)
            if default_config:
                self.logger.info(f"使用默认配置创建场景: {scene_type.value}")
                return default_config
            
            # 创建最基本的配置
            basic_config = SceneConfig(
                scene_type=scene_type,
                scene_name=scene_type.value,
                scene_indicators=[f"{scene_type.value}_indicator"]
            )
            
            self.logger.warning(f"使用基本配置创建场景: {scene_type.value}")
            return basic_config
            
        except Exception as e:
            self.logger.error(f"加载场景配置失败: {scene_type.value}, 错误: {e}")
            return None
    
    def register_scene_class(self, scene_type: GameScene, scene_class: Type[BaseScene]):
        """
        注册场景类
        
        Args:
            scene_type: 场景类型
            scene_class: 场景类
        """
        self._scene_classes[scene_type] = scene_class
        self.logger.info(f"注册场景类: {scene_type.value} -> {scene_class.__name__}")
    
    def register_custom_creator(self, scene_type: GameScene, creator: Callable):
        """
        注册自定义场景创建器
        
        Args:
            scene_type: 场景类型
            creator: 创建器函数
        """
        self._custom_creators[scene_type] = creator
        self.logger.info(f"注册自定义场景创建器: {scene_type.value}")
    
    def unregister_scene_class(self, scene_type: GameScene) -> bool:
        """
        注销场景类
        
        Args:
            scene_type: 场景类型
            
        Returns:
            是否成功注销
        """
        if scene_type in self._scene_classes:
            del self._scene_classes[scene_type]
            self.logger.info(f"注销场景类: {scene_type.value}")
            return True
        return False
    
    def get_supported_scenes(self) -> list:
        """获取支持的场景类型列表"""
        return list(self._scene_classes.keys())
    
    def is_scene_supported(self, scene_type: GameScene) -> bool:
        """检查是否支持指定场景类型"""
        return scene_type in self._scene_classes or scene_type in self._custom_creators
    
    def _record_creation_success(self, scene_type: GameScene, creation_time: float):
        """记录创建成功统计"""
        self._creation_stats['total_created'] += 1
        
        if scene_type not in self._creation_stats['creation_times']:
            self._creation_stats['creation_times'][scene_type] = []
        
        self._creation_stats['creation_times'][scene_type].append(creation_time)
        
        # 保持最近50次记录
        if len(self._creation_stats['creation_times'][scene_type]) > 50:
            self._creation_stats['creation_times'][scene_type] = \
                self._creation_stats['creation_times'][scene_type][-50:]
    
    def _record_creation_error(self, scene_type: GameScene, error_message: str):
        """记录创建错误统计"""
        if scene_type not in self._creation_stats['creation_errors']:
            self._creation_stats['creation_errors'][scene_type] = []
        
        self._creation_stats['creation_errors'][scene_type].append({
            'error': error_message,
            'timestamp': time.time()
        })
        
        # 保持最近20次错误记录
        if len(self._creation_stats['creation_errors'][scene_type]) > 20:
            self._creation_stats['creation_errors'][scene_type] = \
                self._creation_stats['creation_errors'][scene_type][-20:]
    
    def get_creation_stats(self) -> Dict[str, Any]:
        """获取创建统计信息"""
        stats = {
            'total_created': self._creation_stats['total_created'],
            'supported_scenes': len(self._scene_classes),
            'custom_creators': len(self._custom_creators),
            'scene_stats': {}
        }
        
        # 计算每个场景的统计
        for scene_type, times in self._creation_stats['creation_times'].items():
            if times:
                stats['scene_stats'][scene_type.value] = {
                    'total_created': len(times),
                    'average_time': sum(times) / len(times),
                    'min_time': min(times),
                    'max_time': max(times),
                    'error_count': len(self._creation_stats['creation_errors'].get(scene_type, []))
                }
        
        return stats
    
    def reset_stats(self):
        """重置统计信息"""
        self._creation_stats = {
            'total_created': 0,
            'creation_times': {},
            'creation_errors': {}
        }
        self.logger.info("场景工厂统计信息已重置")


class EnhancedSceneFactory(SceneFactory):
    """增强场景工厂 - 提供更多高级功能"""
    
    def __init__(self, perception_module, action_controller, config_loader: UIConfigLoader):
        super().__init__(perception_module, action_controller, config_loader)
        
        # 增强功能
        self._scene_cache: Dict[GameScene, BaseScene] = {}
        self._cache_enabled = True
        self._preload_scenes: set = set()
        
        # 性能监控
        self._performance_monitor = True
        self._creation_threshold = 1.0  # 创建时间阈值（秒）
    
    def create_scene(self, scene_type: GameScene, 
                    config: Optional[SceneConfig] = None,
                    use_cache: bool = True) -> Optional[BaseScene]:
        """
        创建场景实例（支持缓存）
        
        Args:
            scene_type: 场景类型
            config: 场景配置
            use_cache: 是否使用缓存
            
        Returns:
            场景实例
        """
        # 检查缓存
        if use_cache and self._cache_enabled and scene_type in self._scene_cache:
            self.logger.debug(f"从缓存获取场景: {scene_type.value}")
            return self._scene_cache[scene_type]
        
        # 创建新场景
        scene = super().create_scene(scene_type, config)
        
        # 添加到缓存
        if scene and use_cache and self._cache_enabled:
            self._scene_cache[scene_type] = scene
        
        return scene
    
    def preload_scenes(self, scene_types: list):
        """预加载场景"""
        self.logger.info(f"预加载场景: {[s.value for s in scene_types]}")
        
        for scene_type in scene_types:
            if scene_type not in self._scene_cache:
                scene = self.create_scene(scene_type, use_cache=True)
                if scene:
                    self._preload_scenes.add(scene_type)
    
    def clear_cache(self):
        """清理场景缓存"""
        self._scene_cache.clear()
        self._preload_scenes.clear()
        self.logger.info("场景缓存已清理")
    
    def set_cache_enabled(self, enabled: bool):
        """设置缓存启用状态"""
        self._cache_enabled = enabled
        if not enabled:
            self.clear_cache()
        self.logger.info(f"场景缓存{'启用' if enabled else '禁用'}")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        return {
            'cache_enabled': self._cache_enabled,
            'cached_scenes': len(self._scene_cache),
            'preloaded_scenes': len(self._preload_scenes),
            'cached_scene_types': [s.value for s in self._scene_cache.keys()],
            'preloaded_scene_types': [s.value for s in self._preload_scenes]
        }
