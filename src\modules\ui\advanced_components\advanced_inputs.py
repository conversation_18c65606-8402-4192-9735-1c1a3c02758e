"""
高级输入组件
提供滑块、日期时间、颜色选择器等高级输入控件
"""

import time
from typing import List, Dict, Any, Optional, Union, Tuple
from enum import Enum
from dataclasses import dataclass, field

from ....utils.logger import get_logger
from ..elements.base_ui_element import BaseUIElement
from ..config.ui_element_config import UIElementConfig
from ..utils.performance_monitor import measure_block


class InputValidationResult(Enum):
    """输入验证结果枚举"""
    VALID = "valid"
    INVALID = "invalid"
    WARNING = "warning"


@dataclass
class InputRange:
    """输入范围"""
    min_value: Union[int, float]
    max_value: Union[int, float]
    step: Union[int, float] = 1


@dataclass
class ValidationResult:
    """验证结果"""
    result: InputValidationResult
    message: str = ""
    suggestions: List[str] = field(default_factory=list)


class AdvancedInputBase(BaseUIElement):
    """高级输入组件基类"""
    
    def __init__(self, element_id: str, config: UIElementConfig = None, **kwargs):
        super().__init__(element_id, config, **kwargs)
        self.logger = get_logger(f"AdvancedInput.{self.__class__.__name__}")
        
        # 输入状态
        self._value: Any = None
        self._previous_value: Any = None
        self._is_dirty = False
        self._validation_result: Optional[ValidationResult] = None
        
        # 事件回调
        self.on_value_change: Optional[callable] = None
        self.on_validation: Optional[callable] = None
        self.on_focus: Optional[callable] = None
        self.on_blur: Optional[callable] = None
    
    def set_value(self, value: Any) -> bool:
        """设置输入值"""
        try:
            if self._validate_value(value):
                self._previous_value = self._value
                self._value = value
                self._is_dirty = True
                
                # 触发值变化事件
                if self.on_value_change:
                    self.on_value_change(value, self._previous_value)
                
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"设置值失败: {e}")
            return False
    
    def get_value(self) -> Any:
        """获取输入值"""
        return self._value
    
    def _validate_value(self, value: Any) -> bool:
        """验证输入值（子类重写）"""
        return True
    
    def validate(self) -> ValidationResult:
        """验证当前值"""
        if self._validate_value(self._value):
            result = ValidationResult(InputValidationResult.VALID)
        else:
            result = ValidationResult(InputValidationResult.INVALID, "输入值无效")
        
        self._validation_result = result
        
        if self.on_validation:
            self.on_validation(result)
        
        return result
    
    def is_dirty(self) -> bool:
        """检查是否有未保存的更改"""
        return self._is_dirty
    
    def mark_clean(self):
        """标记为已保存"""
        self._is_dirty = False
    
    def reset(self):
        """重置到初始状态"""
        self._value = None
        self._previous_value = None
        self._is_dirty = False
        self._validation_result = None


class SliderInput(AdvancedInputBase):
    """滑块输入组件"""
    
    def __init__(self, element_id: str, input_range: InputRange, 
                 initial_value: Union[int, float] = None, **kwargs):
        super().__init__(element_id, **kwargs)
        
        self.input_range = input_range
        self._value = initial_value or input_range.min_value
        
        # 滑块特定属性
        self.show_value_label = True
        self.show_tick_marks = False
        self.tick_interval = input_range.step * 10
        
        self.logger.info(f"滑块输入组件初始化: {element_id}")
    
    def _validate_value(self, value: Any) -> bool:
        """验证滑块值"""
        try:
            if value is None:
                return False
            
            num_value = float(value)
            return (self.input_range.min_value <= num_value <= self.input_range.max_value)
            
        except (ValueError, TypeError):
            return False
    
    def set_range(self, min_value: Union[int, float], max_value: Union[int, float], 
                  step: Union[int, float] = 1):
        """设置滑块范围"""
        self.input_range = InputRange(min_value, max_value, step)
        
        # 调整当前值到新范围内
        if self._value is not None:
            self._value = max(min_value, min(max_value, self._value))
    
    def get_percentage(self) -> float:
        """获取当前值的百分比位置"""
        if self._value is None:
            return 0.0
        
        range_size = self.input_range.max_value - self.input_range.min_value
        if range_size == 0:
            return 0.0
        
        return (self._value - self.input_range.min_value) / range_size
    
    def set_percentage(self, percentage: float) -> bool:
        """通过百分比设置值"""
        if not (0.0 <= percentage <= 1.0):
            return False
        
        range_size = self.input_range.max_value - self.input_range.min_value
        value = self.input_range.min_value + (percentage * range_size)
        
        # 对齐到步长
        if self.input_range.step > 0:
            value = round(value / self.input_range.step) * self.input_range.step
        
        return self.set_value(value)


class DateTimeInput(AdvancedInputBase):
    """日期时间输入组件"""
    
    def __init__(self, element_id: str, include_time: bool = True, 
                 date_format: str = "%Y-%m-%d", time_format: str = "%H:%M:%S", **kwargs):
        super().__init__(element_id, **kwargs)
        
        self.include_time = include_time
        self.date_format = date_format
        self.time_format = time_format
        
        # 日期时间限制
        self.min_date: Optional[str] = None
        self.max_date: Optional[str] = None
        
        # 显示选项
        self.show_calendar = True
        self.show_time_picker = include_time
        self.first_day_of_week = 1  # 1=Monday, 0=Sunday
        
        self.logger.info(f"日期时间输入组件初始化: {element_id}")
    
    def _validate_value(self, value: Any) -> bool:
        """验证日期时间值"""
        if value is None:
            return True
        
        try:
            # 尝试解析日期时间字符串
            if isinstance(value, str):
                if self.include_time:
                    time.strptime(value, f"{self.date_format} {self.time_format}")
                else:
                    time.strptime(value, self.date_format)
            
            # 检查日期范围
            if self.min_date and value < self.min_date:
                return False
            if self.max_date and value > self.max_date:
                return False
            
            return True
            
        except (ValueError, TypeError):
            return False
    
    def set_date_range(self, min_date: str = None, max_date: str = None):
        """设置日期范围限制"""
        self.min_date = min_date
        self.max_date = max_date
    
    def get_formatted_value(self) -> str:
        """获取格式化的日期时间字符串"""
        if self._value is None:
            return ""
        
        if self.include_time:
            return f"{self._value}"  # 假设已经是正确格式
        else:
            return self._value.split()[0] if " " in str(self._value) else str(self._value)


class ColorPickerInput(AdvancedInputBase):
    """颜色选择器输入组件"""
    
    def __init__(self, element_id: str, color_format: str = "hex", 
                 show_alpha: bool = False, **kwargs):
        super().__init__(element_id, **kwargs)
        
        self.color_format = color_format  # hex, rgb, hsl
        self.show_alpha = show_alpha
        
        # 预设颜色
        self.preset_colors: List[str] = [
            "#FF0000", "#00FF00", "#0000FF", "#FFFF00", "#FF00FF", "#00FFFF",
            "#000000", "#FFFFFF", "#808080", "#800000", "#008000", "#000080"
        ]
        
        # 默认值
        self._value = "#000000"
        
        self.logger.info(f"颜色选择器输入组件初始化: {element_id}")
    
    def _validate_value(self, value: Any) -> bool:
        """验证颜色值"""
        if value is None:
            return True
        
        try:
            color_str = str(value).strip()
            
            if self.color_format == "hex":
                # 验证十六进制颜色
                if color_str.startswith("#"):
                    color_str = color_str[1:]
                
                if len(color_str) not in [3, 6, 8]:  # RGB, RRGGBB, RRGGBBAA
                    return False
                
                int(color_str, 16)  # 尝试解析为十六进制
                return True
                
            elif self.color_format == "rgb":
                # 验证RGB格式
                if color_str.startswith("rgb(") and color_str.endswith(")"):
                    rgb_values = color_str[4:-1].split(",")
                    if len(rgb_values) == 3:
                        for val in rgb_values:
                            num_val = int(val.strip())
                            if not (0 <= num_val <= 255):
                                return False
                        return True
                
            elif self.color_format == "hsl":
                # 验证HSL格式
                if color_str.startswith("hsl(") and color_str.endswith(")"):
                    hsl_values = color_str[4:-1].split(",")
                    if len(hsl_values) == 3:
                        # 简化验证
                        return True
            
            return False
            
        except (ValueError, TypeError):
            return False
    
    def set_preset_colors(self, colors: List[str]):
        """设置预设颜色"""
        self.preset_colors = colors
    
    def hex_to_rgb(self, hex_color: str) -> Tuple[int, int, int]:
        """将十六进制颜色转换为RGB"""
        hex_color = hex_color.lstrip("#")
        if len(hex_color) == 3:
            hex_color = "".join([c*2 for c in hex_color])
        
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
    
    def rgb_to_hex(self, r: int, g: int, b: int) -> str:
        """将RGB颜色转换为十六进制"""
        return f"#{r:02x}{g:02x}{b:02x}"


class FileUploadInput(AdvancedInputBase):
    """文件上传输入组件"""
    
    def __init__(self, element_id: str, accepted_types: List[str] = None,
                 max_file_size: int = 10*1024*1024, multiple: bool = False, **kwargs):
        super().__init__(element_id, **kwargs)
        
        self.accepted_types = accepted_types or []  # [".jpg", ".png", ".pdf"]
        self.max_file_size = max_file_size  # 字节
        self.multiple = multiple
        
        # 上传状态
        self.upload_progress = 0.0
        self.is_uploading = False
        self.uploaded_files: List[Dict[str, Any]] = []
        
        self.logger.info(f"文件上传输入组件初始化: {element_id}")
    
    def _validate_value(self, value: Any) -> bool:
        """验证文件"""
        if value is None:
            return True
        
        try:
            # 假设value是文件信息字典或文件路径
            if isinstance(value, dict):
                file_info = value
                file_size = file_info.get("size", 0)
                file_name = file_info.get("name", "")
                
                # 检查文件大小
                if file_size > self.max_file_size:
                    return False
                
                # 检查文件类型
                if self.accepted_types:
                    file_ext = "." + file_name.split(".")[-1].lower()
                    if file_ext not in [t.lower() for t in self.accepted_types]:
                        return False
                
                return True
            
            elif isinstance(value, str):
                # 文件路径验证
                import os
                if os.path.exists(value):
                    file_size = os.path.getsize(value)
                    if file_size > self.max_file_size:
                        return False
                    
                    if self.accepted_types:
                        file_ext = os.path.splitext(value)[1].lower()
                        if file_ext not in [t.lower() for t in self.accepted_types]:
                            return False
                    
                    return True
            
            return False
            
        except Exception:
            return False
    
    def set_upload_progress(self, progress: float):
        """设置上传进度"""
        self.upload_progress = max(0.0, min(100.0, progress))
    
    def start_upload(self):
        """开始上传"""
        self.is_uploading = True
        self.upload_progress = 0.0
    
    def complete_upload(self, file_info: Dict[str, Any]):
        """完成上传"""
        self.is_uploading = False
        self.upload_progress = 100.0
        self.uploaded_files.append(file_info)
    
    def get_file_size_text(self, size_bytes: int) -> str:
        """获取文件大小的可读文本"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} TB"


class MultiSelectInput(AdvancedInputBase):
    """多选输入组件"""
    
    def __init__(self, element_id: str, options: List[Dict[str, Any]], 
                 max_selections: int = None, **kwargs):
        super().__init__(element_id, **kwargs)
        
        self.options = options  # [{"value": "1", "label": "选项1", "disabled": False}]
        self.max_selections = max_selections
        self._value = []  # 选中的值列表
        
        # 显示选项
        self.show_search = True
        self.show_select_all = True
        self.group_options = False
        
        self.logger.info(f"多选输入组件初始化: {element_id}")
    
    def _validate_value(self, value: Any) -> bool:
        """验证多选值"""
        if value is None:
            return True
        
        try:
            if not isinstance(value, list):
                return False
            
            # 检查最大选择数量
            if self.max_selections and len(value) > self.max_selections:
                return False
            
            # 检查所有值是否在选项中
            option_values = [opt["value"] for opt in self.options]
            for val in value:
                if val not in option_values:
                    return False
            
            return True
            
        except (TypeError, KeyError):
            return False
    
    def add_selection(self, value: Any) -> bool:
        """添加选择"""
        if value in self._value:
            return False
        
        if self.max_selections and len(self._value) >= self.max_selections:
            return False
        
        new_value = self._value + [value]
        return self.set_value(new_value)
    
    def remove_selection(self, value: Any) -> bool:
        """移除选择"""
        if value not in self._value:
            return False
        
        new_value = [v for v in self._value if v != value]
        return self.set_value(new_value)
    
    def toggle_selection(self, value: Any) -> bool:
        """切换选择状态"""
        if value in self._value:
            return self.remove_selection(value)
        else:
            return self.add_selection(value)
    
    def select_all(self) -> bool:
        """全选"""
        all_values = [opt["value"] for opt in self.options if not opt.get("disabled", False)]
        
        if self.max_selections:
            all_values = all_values[:self.max_selections]
        
        return self.set_value(all_values)
    
    def clear_all(self) -> bool:
        """清空选择"""
        return self.set_value([])
    
    def get_selected_labels(self) -> List[str]:
        """获取选中项的标签"""
        selected_labels = []
        for option in self.options:
            if option["value"] in self._value:
                selected_labels.append(option["label"])
        return selected_labels


class TagInput(AdvancedInputBase):
    """标签输入组件"""
    
    def __init__(self, element_id: str, predefined_tags: List[str] = None,
                 max_tags: int = None, allow_custom: bool = True, **kwargs):
        super().__init__(element_id, **kwargs)
        
        self.predefined_tags = predefined_tags or []
        self.max_tags = max_tags
        self.allow_custom = allow_custom
        self._value = []  # 标签列表
        
        # 输入选项
        self.tag_separator = ","
        self.min_tag_length = 1
        self.max_tag_length = 50
        
        self.logger.info(f"标签输入组件初始化: {element_id}")
    
    def _validate_value(self, value: Any) -> bool:
        """验证标签值"""
        if value is None:
            return True
        
        try:
            if not isinstance(value, list):
                return False
            
            # 检查最大标签数量
            if self.max_tags and len(value) > self.max_tags:
                return False
            
            # 检查每个标签
            for tag in value:
                if not isinstance(tag, str):
                    return False
                
                tag = tag.strip()
                if len(tag) < self.min_tag_length or len(tag) > self.max_tag_length:
                    return False
                
                # 检查是否允许自定义标签
                if not self.allow_custom and tag not in self.predefined_tags:
                    return False
            
            return True
            
        except (TypeError, AttributeError):
            return False
    
    def add_tag(self, tag: str) -> bool:
        """添加标签"""
        tag = tag.strip()
        
        if not tag or tag in self._value:
            return False
        
        if self.max_tags and len(self._value) >= self.max_tags:
            return False
        
        if not self.allow_custom and tag not in self.predefined_tags:
            return False
        
        new_value = self._value + [tag]
        return self.set_value(new_value)
    
    def remove_tag(self, tag: str) -> bool:
        """移除标签"""
        if tag not in self._value:
            return False
        
        new_value = [t for t in self._value if t != tag]
        return self.set_value(new_value)
    
    def parse_tags_from_string(self, tag_string: str) -> List[str]:
        """从字符串解析标签"""
        if not tag_string:
            return []
        
        tags = [tag.strip() for tag in tag_string.split(self.tag_separator)]
        return [tag for tag in tags if tag]  # 过滤空标签


class RangeInput(AdvancedInputBase):
    """范围输入组件（双滑块）"""
    
    def __init__(self, element_id: str, input_range: InputRange, 
                 initial_range: Tuple[Union[int, float], Union[int, float]] = None, **kwargs):
        super().__init__(element_id, **kwargs)
        
        self.input_range = input_range
        self._value = initial_range or (input_range.min_value, input_range.max_value)
        
        # 范围限制
        self.min_gap = input_range.step  # 最小间隔
        
        self.logger.info(f"范围输入组件初始化: {element_id}")
    
    def _validate_value(self, value: Any) -> bool:
        """验证范围值"""
        try:
            if value is None:
                return True
            
            if not isinstance(value, (tuple, list)) or len(value) != 2:
                return False
            
            min_val, max_val = float(value[0]), float(value[1])
            
            # 检查范围边界
            if (min_val < self.input_range.min_value or 
                max_val > self.input_range.max_value):
                return False
            
            # 检查最小间隔
            if max_val - min_val < self.min_gap:
                return False
            
            return True
            
        except (ValueError, TypeError):
            return False
    
    def set_min_value(self, min_val: Union[int, float]) -> bool:
        """设置最小值"""
        if self._value is None:
            return False
        
        current_max = self._value[1]
        new_range = (min_val, current_max)
        return self.set_value(new_range)
    
    def set_max_value(self, max_val: Union[int, float]) -> bool:
        """设置最大值"""
        if self._value is None:
            return False
        
        current_min = self._value[0]
        new_range = (current_min, max_val)
        return self.set_value(new_range)
    
    def get_range_size(self) -> Union[int, float]:
        """获取范围大小"""
        if self._value is None:
            return 0
        
        return self._value[1] - self._value[0]


class RatingInput(AdvancedInputBase):
    """评分输入组件"""
    
    def __init__(self, element_id: str, max_rating: int = 5, 
                 allow_half: bool = False, **kwargs):
        super().__init__(element_id, **kwargs)
        
        self.max_rating = max_rating
        self.allow_half = allow_half
        self._value = 0
        
        # 显示选项
        self.show_labels = True
        self.rating_labels = ["很差", "差", "一般", "好", "很好"]
        self.icon_type = "star"  # star, heart, thumb
        
        self.logger.info(f"评分输入组件初始化: {element_id}")
    
    def _validate_value(self, value: Any) -> bool:
        """验证评分值"""
        try:
            if value is None:
                return True
            
            rating = float(value)
            
            if rating < 0 or rating > self.max_rating:
                return False
            
            # 检查半分支持
            if not self.allow_half and rating != int(rating):
                return False
            
            return True
            
        except (ValueError, TypeError):
            return False
    
    def set_rating(self, rating: Union[int, float]) -> bool:
        """设置评分"""
        if not self.allow_half:
            rating = int(rating)
        
        return self.set_value(rating)
    
    def get_rating_label(self) -> str:
        """获取评分标签"""
        if self._value is None or self._value == 0:
            return "未评分"
        
        if self.show_labels and self.rating_labels:
            index = min(int(self._value) - 1, len(self.rating_labels) - 1)
            return self.rating_labels[index]
        
        return f"{self._value}/{self.max_rating}"
