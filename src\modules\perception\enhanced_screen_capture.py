"""
增强屏幕捕获模块
支持后台窗口检测、捕获和操作功能
"""

import time
import numpy as np
import ctypes
import ctypes.wintypes
from typing import Optional, Dict, Any, Tuple, List
from enum import Enum
import mss
import cv2
import win32gui
import win32con
import win32api
import win32ui
from PIL import Image

from ...utils.logger import get_logger
from ...core.data_structures import GakumasuBotException
from .screen_capture import ScreenCapture, ScreenCaptureError, GameWindowNotFound


class BackgroundCaptureMethod(Enum):
    """后台捕获方式枚举"""
    AUTO = "auto"                           # 自动选择最佳方式
    MSS_BACKGROUND = "mss_background"       # MSS后台捕获
    WIN32_PRINTWINDOW = "win32_printwindow" # PrintWindow API
    WIN32_BITBLT = "win32_bitblt"          # BitBlt API
    WIN32_GETWINDOWDC = "win32_getwindowdc" # GetWindowDC API


class WindowState(Enum):
    """窗口状态枚举"""
    ACTIVE = "active"           # 活动窗口
    VISIBLE = "visible"         # 可见但非活动
    MINIMIZED = "minimized"     # 最小化
    HIDDEN = "hidden"           # 隐藏
    INVALID = "invalid"         # 无效


class EnhancedScreenCapture(ScreenCapture):
    """增强屏幕捕获类，支持后台操作"""
    
    def __init__(self,
                 game_window_title: str = "gakumas",
                 enable_background_mode: bool = True,
                 background_capture_method: BackgroundCaptureMethod = BackgroundCaptureMethod.AUTO,
                 crop_to_client_area: bool = True):
        """
        初始化增强屏幕捕获器

        Args:
            game_window_title: 游戏窗口标题
            enable_background_mode: 是否启用后台模式
            background_capture_method: 后台捕获方式
            crop_to_client_area: 是否裁剪到客户区（排除标题栏和边框）
        """
        super().__init__(game_window_title)

        self.enable_background_mode = enable_background_mode
        self.background_capture_method = background_capture_method
        self.crop_to_client_area = crop_to_client_area

        # 后台模式相关属性
        self._window_state_cache = {}
        self._last_state_check = 0
        self._state_check_interval = 1.0  # 1秒检查一次窗口状态

        # 性能统计
        self._capture_stats = {
            "total_captures": 0,
            "background_captures": 0,
            "foreground_captures": 0,
            "failed_captures": 0,
            "client_area_crops": 0,      # 客户区裁剪成功次数
            "crop_failures": 0,          # 裁剪失败次数
            "fallback_captures": 0       # 回退捕获次数
        }

        # Windows API 函数定义
        self._setup_win32_apis()

        self.logger.info(f"增强屏幕捕获器初始化完成，后台模式: {enable_background_mode}, 客户区裁剪: {crop_to_client_area}")
    
    def _setup_win32_apis(self):
        """设置Windows API函数"""
        try:
            # 定义必要的Windows API
            self.user32 = ctypes.windll.user32
            self.gdi32 = ctypes.windll.gdi32
            
            # 定义函数原型
            self.user32.PrintWindow.argtypes = [ctypes.wintypes.HWND, ctypes.wintypes.HDC, ctypes.wintypes.UINT]
            self.user32.PrintWindow.restype = ctypes.wintypes.BOOL
            
            self.user32.GetWindowDC.argtypes = [ctypes.wintypes.HWND]
            self.user32.GetWindowDC.restype = ctypes.wintypes.HDC
            
            self.user32.ReleaseDC.argtypes = [ctypes.wintypes.HWND, ctypes.wintypes.HDC]
            self.user32.ReleaseDC.restype = ctypes.c_int
            
        except Exception as e:
            self.logger.warning(f"Windows API设置失败: {e}")
    
    def get_window_state(self, force_refresh: bool = False) -> WindowState:
        """
        获取窗口状态
        
        Args:
            force_refresh: 是否强制刷新状态
            
        Returns:
            窗口状态
        """
        current_time = time.time()
        
        # 检查缓存
        if (not force_refresh and 
            current_time - self._last_state_check < self._state_check_interval):
            return self._window_state_cache.get("state", WindowState.INVALID)
        
        if not self._window_handle:
            self.find_game_window()
        
        if not self._window_handle:
            state = WindowState.INVALID
        else:
            try:
                # 检查窗口是否存在
                if not win32gui.IsWindow(self._window_handle):
                    state = WindowState.INVALID
                # 检查窗口是否可见
                elif not win32gui.IsWindowVisible(self._window_handle):
                    state = WindowState.HIDDEN
                # 检查窗口是否最小化
                elif win32gui.IsIconic(self._window_handle):
                    state = WindowState.MINIMIZED
                # 检查窗口是否为前台窗口
                elif win32gui.GetForegroundWindow() == self._window_handle:
                    state = WindowState.ACTIVE
                else:
                    state = WindowState.VISIBLE
                    
            except Exception as e:
                self.logger.error(f"获取窗口状态失败: {e}")
                state = WindowState.INVALID
        
        # 更新缓存
        self._window_state_cache = {
            "state": state,
            "timestamp": current_time
        }
        self._last_state_check = current_time
        
        return state
    
    def is_background_capture_needed(self) -> bool:
        """
        判断是否需要后台捕获
        
        Returns:
            是否需要后台捕获
        """
        if not self.enable_background_mode:
            return False
        
        state = self.get_window_state()
        return state in [WindowState.VISIBLE, WindowState.MINIMIZED]
    
    def capture_window_printwindow(self, hwnd: int) -> Optional[np.ndarray]:
        """
        使用PrintWindow API捕获窗口（支持客户区裁剪）

        Args:
            hwnd: 窗口句柄

        Returns:
            捕获的图像数组（根据配置决定是否裁剪到客户区）
        """
        try:
            if not self.crop_to_client_area:
                # 如果不需要裁剪，使用原有逻辑
                return self._capture_full_window_printwindow(hwnd)

            # 获取完整窗口尺寸
            window_rect = win32gui.GetWindowRect(hwnd)
            window_width = window_rect[2] - window_rect[0]
            window_height = window_rect[3] - window_rect[1]

            # 获取客户区尺寸
            client_rect = win32gui.GetClientRect(hwnd)
            client_width = client_rect[2]
            client_height = client_rect[3]

            # 验证客户区尺寸有效性
            if client_width <= 0 or client_height <= 0:
                self.logger.warning(f"客户区尺寸无效: {client_width}x{client_height}")
                self._capture_stats["crop_failures"] += 1
                return self._capture_client_area_direct(hwnd)

            # 计算客户区在窗口中的偏移量
            try:
                client_screen_pos = win32gui.ClientToScreen(hwnd, (0, 0))
                window_screen_pos = (window_rect[0], window_rect[1])
                offset_x = client_screen_pos[0] - window_screen_pos[0]
                offset_y = client_screen_pos[1] - window_screen_pos[1]
            except Exception as e:
                self.logger.warning(f"计算客户区偏移失败: {e}，使用回退方法")
                self._capture_stats["crop_failures"] += 1
                return self._capture_client_area_direct(hwnd)

            # 验证偏移量合理性
            if (offset_x < 0 or offset_y < 0 or
                offset_x + client_width > window_width or
                offset_y + client_height > window_height):
                self.logger.warning(f"客户区偏移量异常: offset=({offset_x},{offset_y}), "
                                  f"client=({client_width},{client_height}), "
                                  f"window=({window_width},{window_height})")
                self._capture_stats["crop_failures"] += 1
                return self._capture_client_area_direct(hwnd)

            # 创建设备上下文（使用完整窗口尺寸）
            hwndDC = win32gui.GetWindowDC(hwnd)
            mfcDC = win32ui.CreateDCFromHandle(hwndDC)
            saveDC = mfcDC.CreateCompatibleDC()

            # 创建位图（使用完整窗口尺寸）
            saveBitMap = win32ui.CreateBitmap()
            saveBitMap.CreateCompatibleBitmap(mfcDC, window_width, window_height)
            saveDC.SelectObject(saveBitMap)

            # 使用PrintWindow捕获完整窗口
            result = self.user32.PrintWindow(hwnd, saveDC.GetSafeHdc(), 0x00000002)  # PW_RENDERFULLCONTENT

            if result:
                # 获取位图数据
                bmpstr = saveBitMap.GetBitmapBits(True)

                # 转换为numpy数组
                img_array = np.frombuffer(bmpstr, dtype='uint8')
                img_array = img_array.reshape((window_height, window_width, 4))

                # 转换颜色格式：BGRA -> BGR
                img_array = cv2.cvtColor(img_array, cv2.COLOR_BGRA2BGR)

                # 裁剪出客户区部分
                cropped_image = img_array[offset_y:offset_y+client_height,
                                        offset_x:offset_x+client_width]

                # 清理资源
                win32gui.DeleteObject(saveBitMap.GetHandle())
                saveDC.DeleteDC()
                mfcDC.DeleteDC()
                win32gui.ReleaseDC(hwnd, hwndDC)

                self._capture_stats["client_area_crops"] += 1
                self.logger.debug(f"PrintWindow客户区捕获成功: 窗口({window_width}x{window_height}) -> "
                                f"客户区({client_width}x{client_height}), 偏移({offset_x},{offset_y})")

                return cropped_image
            else:
                self.logger.warning("PrintWindow捕获失败")
                self._capture_stats["crop_failures"] += 1
                return self._capture_client_area_direct(hwnd)

        except Exception as e:
            self.logger.error(f"PrintWindow捕获异常: {e}")
            self._capture_stats["crop_failures"] += 1
            return self._capture_client_area_direct(hwnd)

    def _capture_full_window_printwindow(self, hwnd: int) -> Optional[np.ndarray]:
        """
        使用PrintWindow API捕获完整窗口（原有逻辑）

        Args:
            hwnd: 窗口句柄

        Returns:
            捕获的完整窗口图像数组
        """
        try:
            # 获取窗口尺寸
            rect = win32gui.GetWindowRect(hwnd)
            width = rect[2] - rect[0]
            height = rect[3] - rect[1]

            # 创建设备上下文
            hwndDC = win32gui.GetWindowDC(hwnd)
            mfcDC = win32ui.CreateDCFromHandle(hwndDC)
            saveDC = mfcDC.CreateCompatibleDC()

            # 创建位图
            saveBitMap = win32ui.CreateBitmap()
            saveBitMap.CreateCompatibleBitmap(mfcDC, width, height)
            saveDC.SelectObject(saveBitMap)

            # 使用PrintWindow捕获
            result = self.user32.PrintWindow(hwnd, saveDC.GetSafeHdc(), 0x00000002)  # PW_RENDERFULLCONTENT

            if result:
                # 获取位图数据
                bmpstr = saveBitMap.GetBitmapBits(True)

                # 转换为numpy数组
                img_array = np.frombuffer(bmpstr, dtype='uint8')
                img_array = img_array.reshape((height, width, 4))

                # 转换颜色格式：BGRA -> BGR
                img_array = cv2.cvtColor(img_array, cv2.COLOR_BGRA2BGR)

                # 清理资源
                win32gui.DeleteObject(saveBitMap.GetHandle())
                saveDC.DeleteDC()
                mfcDC.DeleteDC()
                win32gui.ReleaseDC(hwnd, hwndDC)

                return img_array
            else:
                self.logger.warning("PrintWindow完整窗口捕获失败")
                return None

        except Exception as e:
            self.logger.error(f"PrintWindow完整窗口捕获异常: {e}")
            return None

    def _capture_client_area_direct(self, hwnd: int) -> Optional[np.ndarray]:
        """
        直接捕获客户区的回退方法

        Args:
            hwnd: 窗口句柄

        Returns:
            捕获的客户区图像数组
        """
        try:
            # 获取客户区尺寸
            client_rect = win32gui.GetClientRect(hwnd)
            width = client_rect[2]
            height = client_rect[3]

            if width <= 0 or height <= 0:
                self.logger.warning(f"回退方法：客户区尺寸无效: {width}x{height}")
                return None

            # 获取客户区设备上下文
            hwndDC = win32gui.GetDC(hwnd)  # 使用GetDC而不是GetWindowDC
            mfcDC = win32ui.CreateDCFromHandle(hwndDC)
            saveDC = mfcDC.CreateCompatibleDC()

            # 创建位图
            saveBitMap = win32ui.CreateBitmap()
            saveBitMap.CreateCompatibleBitmap(mfcDC, width, height)
            saveDC.SelectObject(saveBitMap)

            # 尝试使用PrintWindow捕获客户区
            result = self.user32.PrintWindow(hwnd, saveDC.GetSafeHdc(), 0x00000001)  # PW_CLIENTONLY

            if result:
                # 获取位图数据
                bmpstr = saveBitMap.GetBitmapBits(True)

                # 转换为numpy数组
                img_array = np.frombuffer(bmpstr, dtype='uint8')
                img_array = img_array.reshape((height, width, 4))

                # 转换颜色格式：BGRA -> BGR
                img_array = cv2.cvtColor(img_array, cv2.COLOR_BGRA2BGR)

                # 清理资源
                win32gui.DeleteObject(saveBitMap.GetHandle())
                saveDC.DeleteDC()
                mfcDC.DeleteDC()
                win32gui.ReleaseDC(hwnd, hwndDC)

                self._capture_stats["fallback_captures"] += 1
                self.logger.debug(f"回退方法捕获成功: {width}x{height}")
                return img_array
            else:
                self.logger.warning("回退方法：PrintWindow客户区捕获失败")
                # 清理资源
                win32gui.DeleteObject(saveBitMap.GetHandle())
                saveDC.DeleteDC()
                mfcDC.DeleteDC()
                win32gui.ReleaseDC(hwnd, hwndDC)
                return None

        except Exception as e:
            self.logger.error(f"回退方法捕获异常: {e}")
            return None

    def capture_window_bitblt(self, hwnd: int) -> Optional[np.ndarray]:
        """
        使用BitBlt API捕获窗口
        
        Args:
            hwnd: 窗口句柄
            
        Returns:
            捕获的图像数组
        """
        try:
            # 获取窗口客户区尺寸
            client_rect = win32gui.GetClientRect(hwnd)
            width = client_rect[2]
            height = client_rect[3]
            
            if width <= 0 or height <= 0:
                return None
            
            # 获取窗口设备上下文
            hwndDC = win32gui.GetWindowDC(hwnd)
            mfcDC = win32ui.CreateDCFromHandle(hwndDC)
            saveDC = mfcDC.CreateCompatibleDC()
            
            # 创建位图
            saveBitMap = win32ui.CreateBitmap()
            saveBitMap.CreateCompatibleBitmap(mfcDC, width, height)
            saveDC.SelectObject(saveBitMap)
            
            # 使用BitBlt复制窗口内容
            result = saveDC.BitBlt((0, 0), (width, height), mfcDC, (0, 0), win32con.SRCCOPY)
            
            if result:
                # 获取位图数据
                bmpstr = saveBitMap.GetBitmapBits(True)
                
                # 转换为numpy数组
                img_array = np.frombuffer(bmpstr, dtype='uint8')
                img_array = img_array.reshape((height, width, 4))
                
                # 转换颜色格式：BGRA -> BGR
                img_array = cv2.cvtColor(img_array, cv2.COLOR_BGRA2BGR)
                
                # 清理资源
                win32gui.DeleteObject(saveBitMap.GetHandle())
                saveDC.DeleteDC()
                mfcDC.DeleteDC()
                win32gui.ReleaseDC(hwnd, hwndDC)
                
                return img_array
            else:
                self.logger.warning("BitBlt捕获失败")
                return None
                
        except Exception as e:
            self.logger.error(f"BitBlt捕获异常: {e}")
            return None
    
    def capture_background_window(self) -> Optional[np.ndarray]:
        """
        后台窗口捕获
        
        Returns:
            捕获的图像数组
        """
        if not self._window_handle:
            self.find_game_window()
        
        if not self._window_handle:
            return None
        
        # 根据配置选择捕获方式
        if self.background_capture_method == BackgroundCaptureMethod.AUTO:
            # 自动选择：优先使用PrintWindow，失败则使用BitBlt
            result = self.capture_window_printwindow(self._window_handle)
            if result is None:
                result = self.capture_window_bitblt(self._window_handle)
        elif self.background_capture_method == BackgroundCaptureMethod.WIN32_PRINTWINDOW:
            result = self.capture_window_printwindow(self._window_handle)
        elif self.background_capture_method == BackgroundCaptureMethod.WIN32_BITBLT:
            result = self.capture_window_bitblt(self._window_handle)
        else:
            # 回退到MSS方式
            result = super().capture_screen()
        
        if result is not None:
            self._capture_stats["background_captures"] += 1
        
        return result
    
    def capture_screen(self, region: Optional[Dict[str, int]] = None) -> Optional[np.ndarray]:
        """
        智能屏幕捕获（支持前台和后台）
        
        Args:
            region: 指定捕获区域
            
        Returns:
            捕获的图像数组
        """
        self._capture_stats["total_captures"] += 1
        
        try:
            # 判断是否需要后台捕获
            if self.is_background_capture_needed():
                self.logger.debug("使用后台捕获模式")
                result = self.capture_background_window()
                if result is not None:
                    return result
                else:
                    self.logger.warning("后台捕获失败，回退到前台模式")
            
            # 使用前台捕获
            self.logger.debug("使用前台捕获模式")
            result = super().capture_screen(region)
            if result is not None:
                self._capture_stats["foreground_captures"] += 1
            
            return result
            
        except Exception as e:
            self._capture_stats["failed_captures"] += 1
            self.logger.error(f"智能屏幕捕获失败: {e}")
            raise ScreenCaptureError(f"智能屏幕捕获失败: {e}")
    
    def get_capture_stats(self) -> Dict[str, Any]:
        """获取捕获统计信息"""
        return {
            **self._capture_stats,
            "background_mode_enabled": self.enable_background_mode,
            "capture_method": self.background_capture_method.value,
            "window_state": self.get_window_state().value
        }
    
    def get_enhanced_window_info(self) -> Dict[str, Any]:
        """获取增强的窗口信息"""
        base_info = super().get_window_info()
        enhanced_info = {
            **base_info,
            "window_state": self.get_window_state().value,
            "background_mode_enabled": self.enable_background_mode,
            "capture_stats": self.get_capture_stats()
        }
        return enhanced_info
