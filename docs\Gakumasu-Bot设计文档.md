
# 软件设计文档：Gakumasu-Bot

**版本：3.2 (最终完整版)**

**日期：2025年6月19日**

## 一、 引言

### 编写目的

本软件设计文档（Software Design Document, SDD）旨在为名为 **Gakumasu-Bot** 的《学园偶像大师》自动化游玩程序提供一个全面的、统一的技术设计蓝图。本文档作为项目开发的核心指导文件，其目的在于：

* 为开发团队提供清晰、一致的实现指南，确保各模块开发协调统一。
* 详细记录系统的架构、模块设计、接口定义和关键算法，作为后续开发、测试和维护的技术依据。
* 明确项目范围、功能与非功能性需求，作为项目管理和验收的标准。
* 为所有项目参与者提供一个共同的、权威的参考源，促进高效沟通。

### 读者对象

本篇文档的目标读者包括：

* **软件开发工程师** ：将从本文档中获取详细的类设计、函数签名、模块接口定义和核心算法伪代码，作为其编码工作的直接依据。
* **测试工程师** ：能够根据模块职责、接口定义和预期的行为逻辑，设计出有针对性的单元测试、集成测试和端到端测试用例。
* **项目经理** ：可通过本文档了解项目的技术实现方案、模块依赖关系、开发工作的复杂度和预估的实施阶段，从而进行更精确的资源规划和进度跟踪。
* **AI算法研究员** ：可以深入理解MCTS等算法在复杂养成类游戏这一具体应用场景下的建模方式、状态空间定义和奖励函数设计。

### 项目概述

Gakumasu-Bot是一个旨在自动化《学园偶像大师》PC客户端中“育成”(Produce)流程的软件项目。它通过非侵入式的计算机视觉（CV）和人工智能（AI）技术，模拟人类玩家的观察与操作，以实现7x24小时无人值守的游戏托管。项目核心目标是减轻玩家在重复性育成过程中的负担，同时探索高级AI算法在复杂策略游戏中的应用潜力，最终实现一个能够根据用户预设策略高效完成育成任务的智能游戏助理。**本项目选定 Python 3.13.3 作为开发语言，以充分利用其在性能和并发处理方面的最新改进，并将以日语原版游戏环境作为第一支持优先级，同时保留对中文插件的兼容性。**

### 术语和缩写

| **术语/缩写** | **英文全称**            | **中文解释** |
| ------------------- | ----------------------------- | ------------------ |
| **AI**        | Artificial Intelligence       | 人工智能           |
| **Bot**       | Robot                         | 机器人，自动化程序 |
| **CV**        | Computer Vision               | 计算机视觉         |
| **GIL**       | Global Interpreter Lock       | 全局解释器锁       |
| **I18n**      | Internationalization          | 国际化             |
| **JIT**       | Just-In-Time                  | 即时编译           |
| **MCTS**      | Monte Carlo Tree Search       | 蒙特卡洛树搜索     |
| **OCR**       | Optical Character Recognition | 光学字符识别       |
| **ROI**       | Region of Interest            | 感兴趣区域         |
| **SDD**       | Software Design Document      | 软件设计文档       |

### 参考来源

* Python 3.13 官方文档 (dev版本)
* PEP 703 – Making the GIL Optional in CPython
* PEP 744 - JIT Compiler
* 《学园偶像大师》游戏维基与社区攻略
* Ultralytics YOLOv8, EasyOCR, OpenCV 官方文档

## 二、 概述

### 背景

《学园偶像大师》作为一款广受欢迎的偶像养成游戏，其核心玩法“育成”模式包含大量重复性操作和需要长时间投入的策略规划。玩家为了培养心仪的偶像、解锁剧情、提升等级，需要反复进行长达20-30分钟的育成流程。这种高强度的重复劳动（Grinding）占用了玩家大量时间，降低了游戏的娱乐性，并可能导致玩家流失。

因此，开发一款能够自动化执行育成流程的软件工具具有显著的应用价值。Gakumasu-Bot旨在解决这一痛点，它不仅能自动执行任务，还能通过内置的智能决策系统，追求更高的游戏表现，将玩家从重复操作中解放出来，同时为计算机视觉和AI算法提供了一个富有挑战性的真实应用场景。

### 目标

本项目的总体目标是开发一个高效、稳定、智能且可配置的自动化程序，具体实现：

1. **全自动化** ：实现从 **自动启动游戏客户端** 、资源管理、任务调度到完成完整育成周期的端到端自动化。
2. **智能化** ：在关键决策点（如课程选择、考试出牌）采用AI算法，以达成用户设定的育成目标（如高分、True End）。
3. **鲁棒性** ：能够适应游戏UI的微小变化，并具备完善的错误处理与恢复机制，支持长期稳定运行。
4. **高效率** ：通过时间任务调度系统，在资源（如体力）允许时才执行任务，最大化游戏效率和现实世界计算资源的利用率。

### 范围

#### 软件功能范围

* **游戏自动启动** ：能够自动启动DMM Player，并从中启动《学园偶像大师》游戏。
* **任务调度** ：基于时间的任务调度，自动执行育成、打工、日常任务。
* **场景识别与导航** ：准确识别游戏内各主要场景并实现自动导航。
* **全流程育成** ：自动化处理育成前配置、每周行动、所有考试及最终结算。
* **智能决策** ：集成启发式评分模型和MCTS算法进行策略选择。
* **事件处理** ：基于知识库自动处理游戏内随机事件，以日语为主要识别语言。
* **用户配置** ：允许用户通过配置文件定义高级策略，如育成目标和队伍配置。

#### 文档覆盖范围

本SDD覆盖了Gakumasu-Bot项目从需求分析到部署设计的全部软件工程阶段。内容包括但不限于功能与非功能需求、系统架构、模块化详细设计、数据结构、测试计划、安全与性能考量等。

## 三、 需求分析

### 功能需求

| **ID**    | **功能模块**             | **功能描述**                                         | **输入**             | **输出**     | **处理逻辑**                                                                     |
| --------------- | ------------------------------ | ---------------------------------------------------------- | -------------------------- | ------------------ | -------------------------------------------------------------------------------------- |
| **FR-01** | **任务调度系统**         | 实现一个基于时间的、可长期运行的任务调度核心。             | 当前时间、游戏状态         | 下一个待执行的任务 | 循环计算各任务（育成、打工、日常）的下次执行时间，休眠至最近任务时刻，然后触发执行。   |
| **FR-02** | **场景识别与导航**       | 能够准确识别当前所处的游戏场景。                           | 屏幕截图                   | 当前场景名称       | 通过匹配屏幕上关键UI元素的组合来唯一确定场景。                                         |
| **FR-03** | **全流程育成**           | 自动化执行一次完整的育成流程。                             | 用户策略配置               | 育成结果           | 依次执行育成准备、每周行动循环、中期/最终考试、结果结算等所有步骤。                    |
| **FR-04** | **智能决策**             | 在关键节点做出最优或次优的策略选择。                       | 游戏状态                   | 行动指令           | 使用启发式评分模型评估卡牌价值；使用MCTS算法规划出牌序列。                             |
| **FR-05** | **事件处理（日语优先）** | 自动处理游戏中的随机事件和对话。                           | 事件界面截图，语言配置     | 点击选择指令       | OCR主要识别日语文本，在 `events.json`知识库中查找匹配项并执行。                      |
| **FR-06** | **用户配置**             | 允许用户自定义机器人的高级行为策略。                       | `user_strategy.yaml`文件 | 更新的AI决策权重   | 程序启动时加载用户配置文件，并将其中的策略（如育成目标、队伍配置）应用到决策模块。     |
| **FR-07** | **游戏自动启动**         | 在机器人启动时，若游戏未运行，则自动启动DMM Player及游戏。 | DMM Player路径配置         | 游戏成功运行       | 通过系统命令启动DMM Player，然后使用模板匹配找到并点击游戏图标，直至感知到游戏主菜单。 |

### 非功能需求

| **ID**     | **需求类别** | **需求描述**                                                                                                                |
| ---------------- | ------------------ | --------------------------------------------------------------------------------------------------------------------------------- |
| **NFR-01** | **性能**     | 响应时间: 在育成战斗中，单次“感知-决策-行动”循环应在2秒内完成。资源占用: 在“等待”状态下，CPU占用率应低于5%。                  |
| **NFR-02** | **可靠性**   | 稳定性: 程序应能连续无故障运行至少24小时。恢复能力: 在游戏崩溃或意外重启后，应能自动恢复到上一个任务检查点。                      |
| **NFR-03** | **易用性**   | 控制: 提供简单的命令行界面（CLI）或Web UI，用于启动、停止和监控机器人状态。国际化: 机器人自身的UI（如日志、CLI输出）应支持UTF-8。 |
| **NFR-04** | **可维护性** | 模块化: 遵循高内聚、低耦合原则，各模块可独立测试和更新。代码规范: 遵循PEP 8编码规范，关键函数和类必须有文档字符串。               |
| **NFR-05** | **安全性**   | 非侵入性: 所有操作必须通过模拟键鼠输入完成，严禁读取游戏内存或修改文件。隐私: 程序不得存储或传输任何用户账号信息。                |
| **NFR-06** | **本地化**   | **语言支持** : 感知模块必须优先保证对日语文本的识别准确率。知识库和相关数据结构以日语为基准设计，并提供可选的中文兼容字段。 |

### 基本流程

软件的基本工作流程由一个高层的时间调度器驱动， **该调度器在进入主循环前会先执行一次游戏状态检查与启动流程** 。

```
flowchart TD
    subgraph 启动阶段
        Start[启动Gakumasu-Bot] --> CheckGame{游戏是否已运行?};
        CheckGame -- 是 --> MainLoop;
        CheckGame -- 否 --> Launch[执行自动启动流程];
        Launch --> VerifyLaunch{启动成功?};
        VerifyLaunch -- 是 --> MainLoop;
        VerifyLaunch -- 否 --> Terminate[报错并终止];
    end

    subgraph 高层调度循环 (宏观)
        MainLoop[进入主调度循环] --> B{计算所有任务下次执行时间};
        B --> C[放入任务优先级队列];
        C --> D{选择最早执行的任务};
        D --> E[智能休眠至任务时刻];
        E --> F(执行任务回调);
        F --> B;
    end

    subgraph 任务执行 (微观, e.g., 一回合出牌)
        G[开始执行任务] --> H[感知模块: 捕获屏幕, 识别场景和状态 (日语优先)];
        H --> I[决策模块: 根据状态和策略决定行动];
        I --> J[行动模块: 模拟键鼠执行指令];
        J --> K{任务步骤完成?};
        K -- 否 --> H;
        K -- 是 --> L[任务结束];
    end
  
    F --> G;

```

## 四、 架构设计

### 系统架构

本系统采用模块化的分层架构，以**时间任务调度器**为核心，负责宏观调度。底层由 **感知** 、 **决策** 、**行动**三大核心模块协同工作，执行具体任务。**行动模块**的职责被扩展至包含桌面级的自动化操作，以支持游戏启动。

```
C4Context
  System_Boundary(bot, "Gakumasu-Bot") {
    Component(scheduler, "任务调度器 (Scheduler)", "Python/APScheduler", "驱动所有任务的核心，管理时间表")
    Component(perception, "感知模块 (Perception)", "Python/OpenCV, EasyOCR", "从屏幕截图解析游戏状态，以日语识别为核心")
    Component(decision, "决策模块 (Decision)", "Python/MCTS, Heuristics", "根据当前状态和策略决定最佳行动")
    Component(action, "行动模块 (Action)", "Python/PyDirectInput, Subprocess", "模拟用户输入与游戏交互，包括启动外部程序")
    Component(state_manager, "状态管理器 (State Manager)", "Python/JSON", "维护和持久化机器人的内部状态")
    Component(config_loader, "配置加载器 (Config Loader)", "Python/PyYAML", "加载用户设置和策略")

    Rel(scheduler, perception, "请求状态更新 (检查体力等)")
    Rel(scheduler, state_manager, "读取/更新持久化状态")
    Rel(scheduler, decision, "触发任务执行", "回调函数")
    Rel(scheduler, action, "请求启动游戏")
  
    Rel(decision, perception, "获取详细游戏状态")
    Rel(decision, action, "发送行动指令")
  
    Rel(config_loader, decision, "提供用户策略")
    Rel(config_loader, perception, "提供识别参数 (含语言设置)")
    Rel(config_loader, action, "提供DMM路径")
  }

  System_Ext(game, "学园偶像大师 (PC客户端)", "日语原版")
  System_Ext(user, "用户", "操作和配置机器人")
  
  Rel(perception, game, "捕获屏幕截图")
  Rel(action, game, "模拟鼠标/键盘输入")
  Rel(user, config_loader, "编辑配置文件")

```

### 模块设计

#### 4.2.1 模块设计概述

* **模块化原则** :
* **模块化 (Modularity)** : 整个系统被划分为若干个高内聚、低耦合的功能模块。
* **封装 (Encapsulation)** : 每个模块隐藏其内部实现细节，仅通过明确的接口与外部交互。
* **抽象 (Abstraction)** : 接口的设计应关注“做什么”而非“怎么做”，简化模块间的交互。
* **层次化 (Layering)** : 模块按职责分层，如“调度层”、“执行层（感知-决策-行动）”和“数据层”，确保依赖关系单向流动。
* **高内聚、低耦合** : 通过将相关功能集中在同一模块（如所有CV操作在感知模块），并最小化模块间的直接依赖（通过接口和状态管理器通信），系统变得易于维护和扩展。例如，未来升级OCR引擎只需修改感知模块内部，而不影响决策模块。
* **模块划分依据** :

1. **功能职责** : 按“感知”、“决策”、“行动”等核心功能划分，符合自动化程序的基本逻辑。
2. **业务逻辑** : 按“任务调度”、“状态管理”、“配置”等业务支撑功能进行划分。

* **核心模块列表** :
* **调度模块 (Scheduler)** : 负责宏观的时间规划和任务触发。
* **感知模块 (Perception)** : 负责理解游戏世界。
* **决策模块 (Decision)** : 负责制定行动策略。
* **行动模块 (Action)** : 负责执行操作。
* **状态管理模块 (State Manager)** : 负责数据的存储和持久化。
* **配置模块 (Config Loader)** : 负责加载外部配置。
* **设计目标** :
* **提高开发效率** : 模块化分工使团队可以并行开发。
* **降低系统复杂度** : 将复杂问题分解为多个可管理的子问题。
* **增强可测试性** : 每个模块都可以进行独立的单元测试。
* **增强可扩展性** : 新功能（如新的AI算法）可以作为新模块或在现有模块内扩展，而不破坏整体架构。

#### 4.2.2 模块功能设计

| **模块名称** | **功能定义**                                                       | **功能实现方式**                             | **功能点列表 (输入 -> 处理 -> 输出)**                                                                              | **功能扩展性**                                                  |
| ------------------ | ------------------------------------------------------------------------ | -------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------ | --------------------------------------------------------------------- |
| **感知模块** | 核心功能：解析游戏屏幕，提取结构化状态。辅助功能：支持多语言。           | CV库(OpenCV, EasyOCR, Ultralytics)，图像处理算法。 | 1. 捕获屏幕：() -> numpy.ndarray2. 识别场景：image -> string3. 识别UI元素：image -> dict4. 读取文本：image_roi -> string | 可通过更新YOLO模型或模板库来适应游戏UI更新。可增加对新语言的支持。    |
| **决策模块** | 核心功能：根据游戏状态和策略决定下一步行动。辅助功能：支持多种决策算法。 | 启发式规则引擎，MCTS算法，DQN（未来）。            | 1. 决定行动：GameState -> Action2. 评分卡牌：Card, GameState -> float3. MCTS搜索：GameState -> Action                    | 可通过实现新的决策算法（如A*搜索）并注册到决策分派器中来扩展。        |
| **行动模块** | 核心功能：模拟用户输入。辅助功能：动作验证。                             | `PyDirectInput`库，操作系统API。                 | 1. 执行动作：Action -> bool (成功/失败)2. 验证结果：verifier_func, timeout -> bool                                       | 可增加新的动作类型（如拖拽、滚动），或适配新的输入模拟库。            |
| **调度模块** | 核心功能：管理和触发基于时间的任务。                                     | `PriorityQueue`，时间计算逻辑。                  | 1. 运行调度：() -> None2. 计算任务时间：Task -> datetime                                                                 | 可通过定义新的 `Task`子类来轻松添加新的长期自动化任务（如公会战）。 |

#### 4.2.3 模块接口设计

* **接口定义** :
* **`PerceptionModule.get_game_state() -> GameState`** :
  *  **输入** : 无
  *  **输出** : `GameState`对象，包含当前屏幕解析出的所有信息。
  *  **异常** : `GameWindowNotFound`, `ScreenCaptureError`。
* **`DecisionModule.decide_next_action(state: GameState) -> Action`** :
  *  **输入** : `GameState`对象。
  *  **输出** : `Action`对象，表示下一步要执行的操作。
  *  **异常** : `NoValidActionFound`。
* **`ActionController.execute_and_verify(action: Action, verifier: Callable) -> bool`** :
  *  **输入** : `Action`对象，一个用于验证结果的函数 `verifier`。
  *  **输出** : `bool`，表示动作是否执行并验证成功。
  *  **异常** : `InputSimulationError`。
* **接口特性** :
* **稳定性** : 接口签名在版本迭代中应保持向后兼容。
* **易用性** : 接口命名清晰，参数和返回值类型明确。
* **可扩展性** : 接口设计应考虑未来可能增加的参数（如使用 `**kwargs`）。
* **接口安全性** : 本项目为本地应用，模块间接口安全主要关注类型安全和数据契约。输入参数应进行有效性检查。
* **接口调用示例** :

```
  # 主循环中的一次完整迭代
  try:
      current_state = perception.get_game_state()
      next_action = decision.decide_next_action(current_state)

      # 定义验证函数
      def was_successful(new_image):
          # 检查新屏幕是否为预期的结果界面
          return perception.get_current_scene(new_image) == 'action_result_screen'

      action.execute_and_verify(next_action, was_successful)
  except Exception as e:
      logger.error(f"An error occurred in the main loop: {e}")

```

* **接口文档** : 每个公开的类和方法都必须有遵循reStructuredText或Google风格的文档字符串（Docstring），详细说明其功能、参数、返回值和可能抛出的异常。

#### 4.2.4 模块数据结构设计

* **数据结构定义** :
* 核心数据通过Python的 `dataclasses`来定义，如 `GameState`, `Card`, `Action`等，提供了类型提示和清晰的结构。
* 游戏知识库和配置使用JSON和YAML文件，其结构在物理数据设计中有详细Schema。
* **数据结构关系** :
* `GameState`是核心的聚合对象，包含了 `Card`对象列表（手牌）和各种状态属性。
* `DecisionModule`的输出是一个 `Action`对象，其 `target`属性可能是坐标，也可能是一个 `Card`对象的引用。
* **数据存储与访问** :
* **配置数据 (`.yaml`)** : 通过 `PyYAML`库在启动时一次性加载到内存中的配置对象中。
* **知识库 (`.json`)** : 通过 `json`库在启动时加载到内存中的字典或对象列表中，供决策模块快速查询。
* **持久化状态 (`state.json`)** : 通过 `State Manager`模块进行读写。读取在启动时，写入在关键任务完成后或程序退出时。
* **数据一致性** : 本地文件型数据库的一致性通过原子写操作保证（先写入临时文件，再重命名覆盖原文件），以防止在写入过程中程序中断导致文件损坏。

#### 4.2.5 模块依赖关系管理

* **依赖分析** :
* **主循环** 依赖于  **感知** 、 **决策** 、**行动**模块。
* **决策模块** 依赖于  **感知模块** （获取状态）。
* **行动模块** 依赖于  **感知模块** （用于验证）。
* **调度模块** 依赖于  **感知模块** （获取计算任务所需的状态）。
* **依赖关系图 (Mermaid)** :

```
  graph TD
      MainLoop --> Perception
      MainLoop --> Decision
      MainLoop --> Action

      Scheduler --> Perception
      Scheduler --> Decision

      Decision --> Perception

      Action --> Perception

      ConfigLoader --> Perception
      ConfigLoader --> Decision

      StateManager --> Scheduler

```

* **依赖管理策略** :
* **依赖注入 (Dependency Injection)** : 在程序启动时（如 `main.py`中），创建所有核心模块的实例，并将一个模块的实例作为参数传递给依赖于它的另一个模块的构造函数中。这避免了模块内部创建依赖实例，降低了耦合。
  ```
  # main.py 示例
  perception_module = PerceptionModule()
  action_module = ActionController(perception=perception_module) # 注入依赖
  decision_module = DecisionModule(perception=perception_module) # 注入依赖

    ```

* **外部依赖** : 所有第三方库及其版本都记录在 `requirements.txt`文件中，通过 `pip`进行统一管理。
* **模块独立性** : 模块间通过接口通信，不直接访问彼此的内部变量，确保了修改一个模块的内部实现不会影响其他模块。

#### 4.2.6 模块测试设计

* **测试策略** :
* **感知模块** : 单元测试验证图像处理函数的输出。集成测试使用固定的截图文件，验证 `get_game_state`能否解析出正确的结构化数据。
* **决策模块** : 单元测试验证启发式评分函数。对MCTS等算法，测试其基本结构和在简单模拟环境下的行为。
* **行动模块** : 由于依赖真实游戏窗口，主要进行手动系统测试。可以创建模拟的 `verifier`函数进行单元测试。
* **测试工具** : `pytest`作为主要的测试框架。`pytest-mock`用于模拟模块间的依赖。
* **测试结果评估** : 通过 `pytest-cov`插件生成测试覆盖率报告，目标为核心逻辑部分达到80%以上。

#### 4.2.7 模块部署与维护

* **部署方案** :
* 作为单体桌面应用，部署的核心是确保目标机器上正确配置了Python环境和所有依赖。
* 提供一个 `setup.py`或 `install.bat`脚本来自动化环境检查和 `pip install -r requirements.txt`过程。
* **监控与维护** :
* **日志** : 详尽的日志是主要的监控手段，记录在本地文件中。
* **维护计划** :
  *  **游戏更新后** : 优先更新 `assets/templates`和 `data/`中的知识库。
  *  **定期** : 审查核心决策逻辑，根据社区攻略更新启发式规则或AI训练策略。

#### 4.2.8 附录

本章节可包含特定模块的补充设计细节，例如：

* MCTS节点类的详细Python定义。
* 启发式评分模型中所有权重系数的详细列表及其解释。

### 数据库设计 (详细)

#### 4.5.1 逻辑数据模型 (ER图)

此图描述了核心数据实体及其关系，是文件结构设计的基础。

```
erDiagram
    PRODUCE_GOAL {
        string target PK
        string focus_stat
    }

    TEAM_COMPOSITION {
        string produce_idol PK
        string support_cards
    }

    USER_STRATEGY ||--|{ PRODUCE_GOAL : "定义"
    USER_STRATEGY ||--|{ TEAM_COMPOSITION : "定义"
  
    CARDS ||--o{ CARD_EFFECTS : "包含"
    EVENTS ||--o{ EVENT_CHOICES : "包含"
    EVENT_CHOICES ||--o{ CHOICE_EFFECTS : "导致"

    CARDS {
        string card_id PK
        string name_jp "PK"
        string name_cn
        string type
        int cost
        string rarity
    }
    CARD_EFFECTS {
        string card_id FK
        string effect_type
        string value
        string condition
    }
    EVENTS {
        string event_id PK
        string trigger_text_jp "PK"
        string trigger_text_cn
    }
    EVENT_CHOICES {
        string event_id FK
        string choice_text_jp "PK"
        string choice_text_cn
        bool is_recommended
    }
    CHOICE_EFFECTS {
        string event_id FK
        string choice_text_jp FK
        string effect_type
        string value
    }

```

#### 4.5.2 物理数据设计 (文件结构与Schema)

* **知识库 (`data/`)** :
* **`cards.json`** :
  *  **用途** : 存储所有卡牌的静态数据，作为AI决策的依据。
  *  **Schema** :
  ```
  [
    {
      "card_id": "string",
      "name_jp": "string", // 日文名称，作为主要匹配键
      "name_cn": "string", // 中文名称（可选）
      "type": "Vocal | Dance | Visual | Mental",
      "rarity": "N | R | SR | SSR",
      "cost": "integer",
      "effects": [
        {
          "type": "add_score | ...",
          "value": "integer | string",
          "duration": "integer | null",
          "condition": "string | null"
        }
      ]
    }
  ]

  ```
* **`events.json`** :
  *  **用途** : 存储已知随机事件的文本、选项和推荐策略。
  *  **Schema** :
  ```
  [
    {
      "event_id": "string",
      "trigger_keyword_jp": "string", // 日文关键词，主要匹配键
      "trigger_keyword_cn": "string", // 中文关键词（可选）
      "choices": [
        {
          "choice_text_jp": "string",
          "choice_text_cn": "string", // 中文选项文本（可选）
          "is_recommended": "boolean"
        }
      ]
    }
  ]

  ```
* **机器人状态 (`data/state.json`)** :
* **用途** : 持久化需要跨次运行保留的机器人状态。
* **Schema** :
  ```
  {
  "part_time_job_end_time_iso": "2025-06-18T18:00:00+09:00",
  "last_daily_reset_completed_date": "2025-06-17",
  "in_progress_produce": {
  "is_active": "boolean",
  "current_week": "integer",
  "idol_stats": { "vocal": 150, "dance": 120, "visual": 130 },
  "current_deck": ["card_id_1", "card_id_2", "..."]
  }
  }

    ```

* **配置 (`config/`)** :
* **`settings.yaml`** : 系统级配置，新增 `dmm_player_path`和 `game_language: 'ja' | 'cn'` 选项，默认'ja'。
* **`user_strategy.yaml`** : 用户策略配置。

#### 4.5.3 数据流

1. **启动** : `Config Loader` 加载 `settings.yaml` (确定语言) 和 `user_strategy.yaml`。`State Manager` 加载 `state.json`。知识库文件 (`cards.json`, `events.json`) 被 `DecisionModule` 加载到内存。
2. **调度** : `Scheduler` 读取 `state.json` 中的时间信息，并调用 `PerceptionModule` 获取实时体力，以计算任务时间。
3. **决策** : `DecisionModule` 接收 `GameState`，并结合从配置中获得的 `UserStrategy` 和从知识库中加载的卡牌/事件数据（优先使用日语字段），做出决策。
4. **状态更新** : 任务完成后，`State Manager` 将更新后的状态（如新的打工结束时间）写回 `state.json`。

## 五、 详细设计

### 用户界面设计

本系统主要为后台运行设计，但提供一个功能完备的命令行界面（CLI）和一个推荐实现的Web UI，以增强可用性。

#### 5.1.1 命令行界面 (CLI) 设计

* **启动界面** :

```
  ===================================================
  == Gakumasu-Bot v3.1 - 自动化偶像制作人 (Python 3.13) ==
  ===================================================
  [INFO] 正在加载配置文件: settings.yaml, user_strategy.yaml...
  [INFO] 语言设置为: 日语 (默认)
  [INFO] 正在加载知识库: cards.json, events.json...
  [INFO] 正在恢复状态: state.json...
  [INFO] 初始化完成。输入 'start' 开始任务调度。
  > 

```

* **核心命令** :
* `start`: 启动主任务调度循环。
* `stop`: 安全地停止机器人（等待当前任务步骤完成后）。
* `status`: 显示当前机器人的状态、正在执行的任务和任务队列。
* `force <task_name>`: 强制立即执行一个已定义的任务（如 `force NurturingTask`）。
* `reload config`: 重新加载 `user_strategy.yaml` 文件，无需重启程序。
* `help`: 显示所有可用命令。

#### 5.1.2 Web UI 设计 (推荐实现)

* **技术栈** : `Flask` 或 `FastAPI` 作为后端，`WebSocket` 用于实时日志推送，`Vue.js` 或 `React` 作为前端。
* **界面布局 (Mermaid Mockup)** :

```
  graph TD
      subgraph "Gakumasu-Bot Web UI"
          direction LR
          subgraph "侧边栏"
              A[状态监控]
              B[日志查看]
              C[策略配置]
          end
          subgraph "主内容区"
              D(内容显示区)
          end
      end
      A -- 点击 --> E(显示状态面板);
      B -- 点击 --> F(显示日志流);
      C -- 点击 --> G(显示可编辑的策略表单);

      E --> D;
      F --> D;
      G --> D;

      subgraph "状态面板"
          S1[当前任务: NurturingTask]
          S2[下一任务: PartTimeJobTask @ 08:00]
          S3[当前体力: 120/120]
          S4[控制按钮: Pause, Resume, Stop]
      end

```

* **功能** :
* **状态监控** : 实时显示当前执行的任务、下一个任务的时间、关键游戏资源（体力、元气）和机器人的宏观状态（运行中、等待中、错误）。
* **实时日志** : 通过WebSocket实时滚动显示程序日志，并可按级别（INFO, WARN, ERROR）过滤。
* **策略编辑器** : 提供一个表单，允许用户在网页上直接修改 `user_strategy.yaml` 的内容并保存。

#### 5.1.3 前端用户界面设计 (完整实现)

基于已完成的5个开发阶段（基础架构、感知模块、行动模块、决策模块、任务调度系统），本节详细设计一个功能完整、用户友好的前端界面系统。

##### 5.1.3.1 前端架构设计

**技术栈选择**

* **后端框架** : `FastAPI` (推荐) 或 `Flask`
  * FastAPI提供自动API文档生成、类型验证、异步支持
  * 更好的性能和现代化的开发体验
  * 内置WebSocket支持，便于实时通信

* **前端框架** : `Vue.js 3` (推荐) 或 `React`
  * Vue.js学习曲线平缓，开发效率高
  * 优秀的响应式系统，适合实时数据展示
  * 丰富的UI组件库支持

* **UI组件库** : `Element Plus` (Vue.js) 或 `Ant Design` (React)
  * 提供完整的企业级UI组件
  * 支持主题定制和国际化
  * 良好的文档和社区支持

* **实时通信** : `WebSocket` + `Socket.IO`
  * 双向实时通信，支持事件驱动
  * 自动重连和错误处理
  * 跨浏览器兼容性好

**系统架构图**

```
┌─────────────────────────────────────────────────────────────┐
│                    前端用户界面层                              │
├─────────────────────────────────────────────────────────────┤
│  Vue.js 3 + Element Plus + Socket.IO Client                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ 主控制面板   │ │ 配置管理界面 │ │ 日志查看器   │ │ 性能监控 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                    HTTP/WebSocket
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Web API 服务层                            │
├─────────────────────────────────────────────────────────────┤
│  FastAPI + Socket.IO Server                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ REST API    │ │ WebSocket   │ │ 认证授权     │ │ 静态资源 │ │
│  │ 控制器      │ │ 事件处理器   │ │ 中间件      │ │ 服务器   │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                        Python API
                              │
┌─────────────────────────────────────────────────────────────┐
│                  Gakumasu-Bot 核心系统                       │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ Scheduler   │ │ Perception  │ │ Decision    │ │ Action  │ │
│  │ 调度器      │ │ 感知模块     │ │ 决策模块     │ │ 行动模块 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐             │
│  │ StateManager│ │ ConfigManager│ │ TaskManager │             │
│  │ 状态管理器   │ │ 配置管理器   │ │ 任务管理器   │             │
│  └─────────────┘ └─────────────┘ └─────────────┘             │
└─────────────────────────────────────────────────────────────┘
```

**与现有调度系统的集成方案**

* **适配器模式** : 创建WebAPIAdapter类，封装对Scheduler的调用
* **事件驱动** : Scheduler发出事件，WebAPI监听并广播给前端
* **状态同步** : 定期同步系统状态，确保前后端数据一致性
* **线程安全** : 使用线程安全的队列和锁机制，避免并发冲突

```python
class WebAPIAdapter:
    """Web API适配器，连接前端和核心系统"""

    def __init__(self, scheduler: Scheduler):
        self.scheduler = scheduler
        self.event_listeners = []
        self.scheduler.add_event_listener(self._on_scheduler_event)

    def _on_scheduler_event(self, event_type: str, data: dict):
        """处理调度器事件并广播给前端"""
        for listener in self.event_listeners:
            listener(event_type, data)

    def add_event_listener(self, listener):
        """添加事件监听器"""
        self.event_listeners.append(listener)
```

##### 5.1.3.2 用户界面设计

**主控制面板设计**

主控制面板是用户的核心操作界面，提供系统状态监控和基本控制功能。

```
┌─────────────────────────────────────────────────────────────┐
│  Gakumasu-Bot 控制面板                    🟢 运行中  14:32:15 │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   系统状态       │  │   当前任务       │  │   资源监控       │ │
│  │                │  │                │  │                │ │
│  │ 🟢 调度器运行中   │  │ 📋 育成任务执行中 │  │ 💾 内存: 128MB   │ │
│  │ ⏰ 运行时间: 2h   │  │ ⏱️ 进度: 65%     │  │ 🖥️ CPU: 15%     │ │
│  │ 📊 任务队列: 3   │  │ 🎯 目标: 高分育成 │  │ 🌡️ 温度: 正常    │ │
│  │ ⚡ 状态: 正常    │  │ 📍 当前周: 8/12  │  │ 📈 性能: 良好    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                    快速控制                              │ │
│  │  [▶️ 启动]  [⏸️ 暂停]  [⏹️ 停止]  [🔄 重启]  [⚙️ 设置]    │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                    任务队列                              │ │
│  │  📋 育成任务 (进行中) - 预计完成: 16:45                    │ │
│  │  💼 打工任务 (等待中) - 计划开始: 18:00                    │ │
│  │  📅 日常任务 (等待中) - 计划开始: 明日 06:00               │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

**配置管理界面设计**

配置管理界面允许用户在线编辑和管理各种配置文件。

```
┌─────────────────────────────────────────────────────────────┐
│  配置管理                                      [💾 保存] [🔄 重置] │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────────────────────────────────┐ │
│  │ 配置分类     │  │              配置编辑器                  │ │
│  │             │  │                                        │ │
│  │ 📋 用户策略  │  │  # 育成目标配置                         │ │
│  │ ⚙️ 系统设置  │  │  produce_goal:                         │ │
│  │ 🎮 游戏配置  │  │    target: high_score                  │ │
│  │ 🔧 高级选项  │  │    focus_stat: vocal                   │ │
│  │ 📁 配置文件  │  │                                        │ │
│  │             │  │  # 队伍配置                            │ │
│  │             │  │  team_composition:                     │ │
│  │             │  │    produce_idol: '花海咲季'             │ │
│  │             │  │    support_cards:                      │ │
│  │             │  │      - '【SSR】まだ見ぬ景色'            │ │
│  │             │  │      - '【SR】あの日と同じように'        │ │
│  └─────────────┘  └─────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  ✅ 配置验证通过    📊 配置历史: 5个版本    🔄 最后更新: 2分钟前  │
└─────────────────────────────────────────────────────────────┘
```

**实时日志查看器设计**

日志查看器提供分级日志显示、搜索过滤和实时更新功能。

```
┌─────────────────────────────────────────────────────────────┐
│  实时日志                                    [🔍] [⚙️] [📥] [🗑️] │
├─────────────────────────────────────────────────────────────┤
│  过滤器: [🔴 ERROR] [🟡 WARN] [🔵 INFO] [⚪ DEBUG]  自动滚动: ✅ │
│  搜索: [________________] 模块: [全部 ▼] 时间: [最近1小时 ▼]    │
├─────────────────────────────────────────────────────────────┤
│  14:32:15 🔵 INFO  [Scheduler] 任务调度器启动成功              │
│  14:32:16 🔵 INFO  [Perception] 感知模块初始化完成             │
│  14:32:17 🔵 INFO  [Decision] 决策模块加载用户策略             │
│  14:32:18 🔵 INFO  [Action] 行动模块连接游戏窗口               │
│  14:32:20 🟡 WARN  [Perception] 游戏窗口未找到，重试中...      │
│  14:32:22 🔵 INFO  [Perception] 游戏窗口连接成功               │
│  14:32:25 🔵 INFO  [Scheduler] 开始执行育成任务                │
│  14:32:26 🔵 INFO  [Decision] 选择课程: Vocal训练              │
│  14:32:28 🔵 INFO  [Action] 点击Vocal课程按钮成功              │
│  14:32:30 🔵 INFO  [Perception] 场景切换到课程界面             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ 📊 统计信息                                              │ │
│  │ 总日志: 1,247 条 | ERROR: 2 | WARN: 15 | INFO: 1,230    │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

**性能监控面板设计**

性能监控面板展示系统资源使用情况和性能指标。

```
┌─────────────────────────────────────────────────────────────┐
│  性能监控                                          刷新间隔: 5s │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   CPU 使用率     │  │   内存使用      │  │   网络流量       │ │
│  │                │  │                │  │                │ │
│  │      15%       │  │   128/512 MB   │  │   ↑ 1.2 KB/s   │ │
│  │   ████░░░░░░    │  │   ██████░░░░    │  │   ↓ 0.8 KB/s   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                    性能指标趋势图                        │ │
│  │  CPU % │                                               │ │
│  │   100  │                                               │ │
│  │    80  │                                               │ │
│  │    60  │                                               │ │
│  │    40  │     ∩                                         │ │
│  │    20  │ ∩∩∩∩ ∪ ∩∩∩∩∩∩∩∩∩∩∩∩∩∩∩∩∩∩∩∩∩∩∩∩∩∩∩∩∩∩∩∩∩∩∩∩∩ │ │
│  │     0  └─────────────────────────────────────────────── │ │
│  │        14:00  14:15  14:30  14:45  15:00  15:15  15:30 │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  📊 模块性能统计                                             │ │
│  • 感知模块: 平均响应 45ms | 成功率 99.2% | 错误 2次          │ │
│  • 决策模块: 平均决策 120ms | MCTS使用 15% | 启发式 85%       │ │
│  • 行动模块: 平均执行 80ms | 成功率 98.5% | 重试 12次         │ │
│  • 调度系统: 任务完成 156个 | 平均延迟 8ms | 队列长度 3       │ │
└─────────────────────────────────────────────────────────────┘
```

##### 5.1.3.3 功能需求规格

**核心控制功能**

* **FR-UI-01 机器人生命周期控制**
  * 启动机器人：检查系统状态，初始化所有模块，开始任务调度
  * 暂停机器人：暂停当前任务执行，保持系统状态，允许恢复
  * 停止机器人：安全停止所有任务，保存状态，清理资源
  * 重启机器人：执行停止后重新启动的完整流程
  * 强制停止：紧急情况下的强制终止功能

* **FR-UI-02 实时状态监控**
  * 系统运行状态：运行中/暂停/停止/错误/维护
  * 当前任务信息：任务名称、进度、预计完成时间、执行状态
  * 任务队列状态：等待任务数量、优先级分布、预计执行时间
  * 游戏状态信息：体力、元气、当前场景、偶像属性
  * 资源使用情况：CPU、内存、磁盘、网络使用率

* **FR-UI-03 任务管理功能**
  * 任务队列查看：显示所有待执行和正在执行的任务
  * 任务优先级调整：允许用户修改任务的执行优先级
  * 任务手动触发：允许用户手动启动特定类型的任务
  * 任务取消功能：取消等待中的任务或中断正在执行的任务
  * 任务历史记录：查看已完成任务的执行历史和结果

**配置管理功能**

* **FR-UI-04 配置文件管理**
  * 在线编辑：提供语法高亮的YAML/JSON编辑器
  * 配置验证：实时验证配置文件的语法和逻辑正确性
  * 配置预览：显示配置变更对系统行为的影响预览
  * 配置备份：自动备份配置文件的历史版本
  * 配置恢复：支持从历史版本恢复配置文件

* **FR-UI-05 用户策略配置**
  * 育成目标设置：高分、True End、属性专精等目标选择
  * 队伍配置管理：偶像选择、支援卡配置、牌组策略设置
  * 行为偏好设置：风险偏好、体力管理、决策超时等参数
  * 策略模板管理：预设策略模板的保存、加载和分享
  * 策略效果分析：显示不同策略配置的历史效果统计

**监控和诊断功能**

* **FR-UI-06 实时日志系统**
  * 分级日志显示：ERROR、WARN、INFO、DEBUG四个级别
  * 日志搜索过滤：按时间、级别、模块、关键词进行过滤
  * 日志导出功能：支持导出指定时间段的日志文件
  * 日志统计分析：显示各级别日志的数量统计和趋势
  * 实时日志推送：通过WebSocket实时推送新的日志消息

* **FR-UI-07 性能监控面板**
  * 系统资源监控：CPU、内存、磁盘、网络的实时使用情况
  * 模块性能统计：各模块的响应时间、成功率、错误率统计
  * 性能趋势图表：历史性能数据的图表展示和趋势分析
  * 性能告警设置：设置性能阈值和告警通知机制
  * 性能报告生成：生成定期的性能分析报告

**数据管理功能**

* **FR-UI-08 历史记录管理**
  * 育成历史记录：显示历史育成的详细记录和结果统计
  * 决策历史分析：分析AI决策的准确性和效果
  * 错误历史追踪：记录和分析系统错误的发生模式
  * 数据导出功能：支持将历史数据导出为CSV、JSON等格式
  * 数据清理功能：定期清理过期的历史数据

* **FR-UI-09 故障诊断界面**
  * 系统健康检查：自动检测系统各组件的健康状态
  * 错误报告生成：自动生成详细的错误报告和诊断信息
  * 故障恢复建议：基于错误类型提供自动恢复建议
  * 远程诊断支持：支持远程技术支持的诊断功能
  * 系统维护模式：提供系统维护和修复的专用模式

##### 5.1.3.4 技术实现细节

**API接口设计 (RESTful API规范)**

```python
# API接口定义
from fastapi import FastAPI, WebSocket, HTTPException, Depends
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from datetime import datetime

app = FastAPI(title="Gakumasu-Bot API", version="1.0.0")

# 数据模型定义
class SystemStatus(BaseModel):
    """系统状态模型"""
    is_running: bool
    current_task: Optional[str]
    task_queue_size: int
    uptime: int
    last_update: datetime
    resource_usage: Dict[str, float]

class TaskInfo(BaseModel):
    """任务信息模型"""
    task_id: str
    name: str
    status: str
    priority: str
    progress: float
    estimated_completion: Optional[datetime]
    created_at: datetime

class ConfigUpdate(BaseModel):
    """配置更新模型"""
    config_type: str  # 'user_strategy', 'system_settings', etc.
    content: str
    validate_only: bool = False

# 核心API端点
@app.get("/api/v1/status", response_model=SystemStatus)
async def get_system_status():
    """获取系统状态"""
    return await api_adapter.get_system_status()

@app.post("/api/v1/control/{action}")
async def control_system(action: str):
    """系统控制 (start/stop/pause/resume)"""
    if action not in ['start', 'stop', 'pause', 'resume']:
        raise HTTPException(status_code=400, detail="Invalid action")

    result = await api_adapter.control_system(action)
    return {"success": True, "message": f"System {action} successful"}

@app.get("/api/v1/tasks", response_model=List[TaskInfo])
async def get_tasks():
    """获取任务列表"""
    return await api_adapter.get_task_list()

@app.post("/api/v1/tasks/{task_id}/cancel")
async def cancel_task(task_id: str):
    """取消任务"""
    result = await api_adapter.cancel_task(task_id)
    return {"success": result, "task_id": task_id}

@app.get("/api/v1/config/{config_type}")
async def get_config(config_type: str):
    """获取配置文件"""
    config = await api_adapter.get_config(config_type)
    return {"config_type": config_type, "content": config}

@app.post("/api/v1/config/{config_type}")
async def update_config(config_type: str, update: ConfigUpdate):
    """更新配置文件"""
    result = await api_adapter.update_config(config_type, update.content, update.validate_only)
    return {"success": result.success, "errors": result.errors}

@app.get("/api/v1/logs")
async def get_logs(
    level: Optional[str] = None,
    module: Optional[str] = None,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
    limit: int = 100
):
    """获取日志记录"""
    logs = await api_adapter.get_logs(level, module, start_time, end_time, limit)
    return {"logs": logs, "total": len(logs)}

@app.get("/api/v1/performance")
async def get_performance_metrics():
    """获取性能指标"""
    metrics = await api_adapter.get_performance_metrics()
    return metrics

# WebSocket端点
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket连接处理"""
    await websocket.accept()

    # 注册WebSocket连接
    connection_id = await api_adapter.register_websocket(websocket)

    try:
        while True:
            # 接收客户端消息
            data = await websocket.receive_json()

            # 处理不同类型的消息
            if data["type"] == "subscribe":
                await api_adapter.subscribe_events(connection_id, data["events"])
            elif data["type"] == "unsubscribe":
                await api_adapter.unsubscribe_events(connection_id, data["events"])
            elif data["type"] == "ping":
                await websocket.send_json({"type": "pong", "timestamp": datetime.now().isoformat()})

    except Exception as e:
        print(f"WebSocket error: {e}")
    finally:
        await api_adapter.unregister_websocket(connection_id)
```

**数据传输格式和安全考虑**

* **数据格式标准化**
  * 所有API响应使用统一的JSON格式
  * 时间戳使用ISO 8601格式
  * 错误响应包含错误码和详细描述
  * 支持数据压缩以减少传输量

* **安全机制**
  * JWT Token认证：用户登录后获取访问令牌
  * API访问限制：限制API调用频率，防止滥用
  * 输入验证：严格验证所有输入数据的格式和内容
  * HTTPS传输：生产环境强制使用HTTPS加密传输
  * CORS配置：合理配置跨域访问策略

```python
# 安全中间件
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
import jwt

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "https://yourdomain.com"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

# 受信任主机配置
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["localhost", "127.0.0.1", "yourdomain.com"]
)

# JWT认证依赖
security = HTTPBearer()

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """验证JWT令牌"""
    try:
        payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=["HS256"])
        username = payload.get("sub")
        if username is None:
            raise HTTPException(status_code=401, detail="Invalid token")
        return username
    except jwt.PyJWTError:
        raise HTTPException(status_code=401, detail="Invalid token")
```

**响应式设计和移动端适配**

* **响应式布局**
  * 使用CSS Grid和Flexbox实现自适应布局
  * 断点设计：桌面(>1200px)、平板(768-1200px)、手机(<768px)
  * 组件自适应：根据屏幕尺寸调整组件大小和布局
  * 触摸友好：移动端优化触摸操作和手势支持

* **移动端优化**
  * PWA支持：支持离线使用和桌面安装
  * 移动端导航：抽屉式导航菜单，适合小屏幕操作
  * 性能优化：懒加载、代码分割、资源压缩
  * 适配测试：在多种设备和浏览器上进行兼容性测试

```css
/* 响应式设计示例 */
.dashboard-container {
  display: grid;
  grid-template-columns: 250px 1fr;
  grid-template-rows: 60px 1fr;
  height: 100vh;
}

@media (max-width: 768px) {
  .dashboard-container {
    grid-template-columns: 1fr;
    grid-template-rows: 60px auto 1fr;
  }

  .sidebar {
    position: fixed;
    left: -250px;
    transition: left 0.3s ease;
  }

  .sidebar.open {
    left: 0;
  }
}

@media (max-width: 480px) {
  .control-buttons {
    flex-direction: column;
    gap: 8px;
  }

  .status-cards {
    grid-template-columns: 1fr;
  }
}
```

**用户权限和访问控制**

* **角色权限设计**
  * **管理员 (Admin)** : 完全访问权限，包括系统控制、配置管理、用户管理
  * **操作员 (Operator)** : 基本操作权限，包括启动/停止、查看状态、基础配置
  * **观察者 (Observer)** : 只读权限，仅能查看状态、日志和性能监控
  * **访客 (Guest)** : 受限访问，仅能查看基本状态信息

* **权限控制实现**
```python
from enum import Enum
from functools import wraps

class UserRole(Enum):
    ADMIN = "admin"
    OPERATOR = "operator"
    OBSERVER = "observer"
    GUEST = "guest"

class Permission(Enum):
    SYSTEM_CONTROL = "system_control"
    CONFIG_EDIT = "config_edit"
    CONFIG_VIEW = "config_view"
    LOG_VIEW = "log_view"
    STATUS_VIEW = "status_view"
    TASK_MANAGE = "task_manage"

# 角色权限映射
ROLE_PERMISSIONS = {
    UserRole.ADMIN: [Permission.SYSTEM_CONTROL, Permission.CONFIG_EDIT,
                     Permission.CONFIG_VIEW, Permission.LOG_VIEW,
                     Permission.STATUS_VIEW, Permission.TASK_MANAGE],
    UserRole.OPERATOR: [Permission.SYSTEM_CONTROL, Permission.CONFIG_VIEW,
                        Permission.LOG_VIEW, Permission.STATUS_VIEW,
                        Permission.TASK_MANAGE],
    UserRole.OBSERVER: [Permission.CONFIG_VIEW, Permission.LOG_VIEW,
                        Permission.STATUS_VIEW],
    UserRole.GUEST: [Permission.STATUS_VIEW]
}

def require_permission(permission: Permission):
    """权限检查装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            user = kwargs.get('current_user')
            if not user or not has_permission(user.role, permission):
                raise HTTPException(status_code=403, detail="Insufficient permissions")
            return await func(*args, **kwargs)
        return wrapper
    return decorator

def has_permission(user_role: UserRole, permission: Permission) -> bool:
    """检查用户是否具有指定权限"""
    return permission in ROLE_PERMISSIONS.get(user_role, [])
```

##### 5.1.3.5 集成方案

**与现有系统组件的集成**

* **Scheduler集成**
```python
class WebAPIAdapter:
    """Web API适配器"""

    def __init__(self, scheduler: Scheduler):
        self.scheduler = scheduler
        self.websocket_manager = WebSocketManager()
        self._setup_event_handlers()

    def _setup_event_handlers(self):
        """设置事件处理器"""
        self.scheduler.on('task_started', self._on_task_started)
        self.scheduler.on('task_completed', self._on_task_completed)
        self.scheduler.on('task_failed', self._on_task_failed)
        self.scheduler.on('status_changed', self._on_status_changed)
        self.scheduler.on('error_occurred', self._on_error_occurred)

    async def _on_task_started(self, task_info):
        """任务开始事件处理"""
        await self.websocket_manager.broadcast({
            "type": "task_started",
            "data": task_info
        })

    async def get_system_status(self) -> SystemStatus:
        """获取系统状态"""
        status = self.scheduler.get_status()
        return SystemStatus(
            is_running=status.is_running,
            current_task=status.current_task.name if status.current_task else None,
            task_queue_size=len(status.task_queue),
            uptime=status.uptime,
            last_update=datetime.now(),
            resource_usage=self._get_resource_usage()
        )
```

* **StateManager集成**
```python
class StateAPIHandler:
    """状态管理API处理器"""

    def __init__(self, state_manager: StateManager):
        self.state_manager = state_manager

    async def get_historical_data(self, start_date: datetime, end_date: datetime):
        """获取历史数据"""
        return await self.state_manager.get_state_history(start_date, end_date)

    async def export_state_data(self, format: str = 'json'):
        """导出状态数据"""
        current_state = await self.state_manager.get_current_state()
        if format == 'json':
            return current_state.to_json()
        elif format == 'csv':
            return current_state.to_csv()
        else:
            raise ValueError(f"Unsupported format: {format}")
```

* **ConfigManager集成**
```python
class ConfigAPIHandler:
    """配置管理API处理器"""

    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager

    async def validate_config(self, config_type: str, content: str) -> ValidationResult:
        """验证配置文件"""
        try:
            result = await self.config_manager.validate_config(config_type, content)
            return ValidationResult(success=True, errors=[])
        except ValidationError as e:
            return ValidationResult(success=False, errors=e.errors)

    async def update_config_with_backup(self, config_type: str, content: str):
        """更新配置并备份"""
        # 创建备份
        backup_id = await self.config_manager.create_backup(config_type)

        try:
            # 更新配置
            await self.config_manager.update_config(config_type, content)
            return {"success": True, "backup_id": backup_id}
        except Exception as e:
            # 恢复备份
            await self.config_manager.restore_backup(backup_id)
            raise e
```

**前后端数据同步机制**

* **实时数据同步**
```python
class WebSocketManager:
    """WebSocket连接管理器"""

    def __init__(self):
        self.connections: Dict[str, WebSocket] = {}
        self.subscriptions: Dict[str, List[str]] = {}

    async def register_connection(self, websocket: WebSocket) -> str:
        """注册WebSocket连接"""
        connection_id = str(uuid.uuid4())
        await websocket.accept()
        self.connections[connection_id] = websocket
        self.subscriptions[connection_id] = []
        return connection_id

    async def subscribe_events(self, connection_id: str, events: List[str]):
        """订阅事件"""
        if connection_id in self.subscriptions:
            self.subscriptions[connection_id].extend(events)

    async def broadcast_event(self, event_type: str, data: dict):
        """广播事件到订阅的连接"""
        message = {
            "type": event_type,
            "data": data,
            "timestamp": datetime.now().isoformat()
        }

        for connection_id, events in self.subscriptions.items():
            if event_type in events and connection_id in self.connections:
                try:
                    await self.connections[connection_id].send_json(message)
                except Exception as e:
                    # 连接已断开，清理
                    await self._cleanup_connection(connection_id)
```

* **数据缓存策略**
```python
class DataCacheManager:
    """数据缓存管理器"""

    def __init__(self):
        self.cache = {}
        self.cache_ttl = {}
        self.default_ttl = 30  # 30秒默认TTL

    async def get_cached_data(self, key: str, fetch_func, ttl: int = None):
        """获取缓存数据"""
        if key in self.cache and not self._is_expired(key):
            return self.cache[key]

        # 缓存过期或不存在，重新获取
        data = await fetch_func()
        await self.set_cache(key, data, ttl or self.default_ttl)
        return data

    async def set_cache(self, key: str, data: any, ttl: int):
        """设置缓存"""
        self.cache[key] = data
        self.cache_ttl[key] = datetime.now() + timedelta(seconds=ttl)

    def _is_expired(self, key: str) -> bool:
        """检查缓存是否过期"""
        return datetime.now() > self.cache_ttl.get(key, datetime.min)
```

**错误处理和异常恢复**

* **统一错误处理**
```python
from fastapi import Request
from fastapi.responses import JSONResponse

@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理器"""
    error_id = str(uuid.uuid4())

    # 记录错误日志
    logger.error(f"API Error [{error_id}]: {str(exc)}", exc_info=True)

    # 根据异常类型返回不同的错误响应
    if isinstance(exc, HTTPException):
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": exc.detail,
                "error_id": error_id,
                "timestamp": datetime.now().isoformat()
            }
        )
    else:
        return JSONResponse(
            status_code=500,
            content={
                "error": "Internal server error",
                "error_id": error_id,
                "timestamp": datetime.now().isoformat()
            }
        )

class APIErrorRecovery:
    """API错误恢复机制"""

    @staticmethod
    async def handle_scheduler_error(error: Exception):
        """处理调度器错误"""
        if isinstance(error, SchedulerNotRunningError):
            # 尝试重启调度器
            try:
                await scheduler.restart()
                return {"recovered": True, "action": "scheduler_restarted"}
            except Exception as e:
                return {"recovered": False, "error": str(e)}

        return {"recovered": False, "error": "Unknown scheduler error"}

    @staticmethod
    async def handle_config_error(error: Exception):
        """处理配置错误"""
        if isinstance(error, ConfigValidationError):
            # 恢复到上一个有效配置
            try:
                await config_manager.restore_last_valid_config()
                return {"recovered": True, "action": "config_restored"}
            except Exception as e:
                return {"recovered": False, "error": str(e)}

        return {"recovered": False, "error": "Unknown config error"}
```

**与阶段6系统集成测试的配合**

* **集成测试支持**
  * 提供测试模式API，支持模拟数据和测试场景
  * 集成测试数据收集，记录前端操作和系统响应
  * 自动化测试接口，支持端到端测试脚本
  * 性能测试支持，监控前端操作对系统性能的影响

* **开发调试功能**
  * 开发者模式：显示详细的调试信息和系统内部状态
  * API调试工具：内置API测试工具，方便开发调试
  * 实时日志流：开发时实时查看系统日志和错误信息
  * 性能分析器：分析前端操作的性能影响

```python
# 测试模式API
@app.post("/api/v1/test/simulate")
async def simulate_scenario(scenario: TestScenario):
    """模拟测试场景"""
    if not app.debug:
        raise HTTPException(status_code=403, detail="Test mode not enabled")

    return await test_simulator.run_scenario(scenario)

@app.get("/api/v1/debug/system-state")
async def get_debug_system_state():
    """获取调试用系统状态"""
    if not app.debug:
        raise HTTPException(status_code=403, detail="Debug mode not enabled")

    return {
        "scheduler_state": scheduler.get_debug_state(),
        "module_states": {
            "perception": perception_module.get_debug_state(),
            "decision": decision_module.get_debug_state(),
            "action": action_module.get_debug_state()
        },
        "memory_usage": get_memory_usage(),
        "thread_info": get_thread_info()
    }
```

### 功能设计 (详细)

#### 5.2.1 核心数据模型类设计 (Pythonic Pseudocode)

```
from dataclasses import dataclass, field
from datetime import datetime
from typing import List, Dict, Any, Optional

# 使用Python 3.10+的 "|" 联合类型语法，更简洁
@dataclass
class CardEffect:
    type: str
    value: int | str
    duration: Optional[int] = None
    condition: Optional[str] = None

@dataclass
class Card:
    card_id: str
    name_jp: str
    name_cn: Optional[str] = None
    cost: int
    effects: List[CardEffect] = field(default_factory=list)

@dataclass
class GameState:
    current_scene: str
    current_language: str # 'ja' or 'cn'
    stamina: int
    vigor: int
    score: int
    current_week: int
    hand: List[Card]
    deck_size: int
    discard_size: int
    active_buffs: Dict[str, int] = field(default_factory=dict) # e.g., {"score_up": 2}
    # ... 其他属性

@dataclass
class Action:
    type: str # 'click', 'keypress', 'scroll'
    target: Any # 坐标(x,y) 或 键名
    description: str

@dataclass
class UserStrategy:
    produce_goal: str
    focus_stat: str
    team_composition: Dict[str, Any]
    behavior: Dict[str, Any]

```

#### 5.2.2 关键功能逻辑设计

* **`launch_game_and_verify()` - 游戏自动启动流程**

  * **目标** : 确保《学园偶像大师》游戏正在运行并处于主菜单界面。
  * **实现方式** : 这是一个高层协调函数，将在主程序启动时被调用。
  * **流程图** :

  ```
  graph TD
      A[开始] --> B{感知模块: 游戏主菜单可见?};
      B -- 是 --> C[结束, 返回True];
      B -- 否 --> D[行动模块: 启动DMM Player<br>(通过配置路径)];
      D --> E{等待并验证DMM Player主界面};
      E -- 成功 --> F[感知模块: 在DMM库中查找游戏图标<br>(使用模板匹配)];
      E -- 超时失败 --> Z[结束, 返回False];
      F -- 找到 --> G[行动模块: 双击游戏图标];
      F -- 未找到 --> Z;
      G --> H{等待并验证游戏主菜单界面};
      H -- 成功 --> C;
      H -- 超时失败 --> Z;

  ```

  * **前置条件** :
  * 用户已在 `config/settings.yaml`中正确配置 `dmm_player_path`。
  * `assets/templates/`目录中已存放 `dmm_gakumasu_icon.png`模板图片。
* **`_handle_weekly_selection(game_state: GameState) -> Action`**

  * **目标** : 在育成主界面，根据当前状态和用户策略，从“课程”、“休息”、“外出”等选项中做出最佳选择。
  * **决策流程图** :

  ```
  graph TD
      A[开始决策] --> B{检查是否有强制任务<br>(e.g., 即将到期的约定)};
      B -- 是 --> C[选择约定对应的行动];
      B -- 否 --> D{体力 < 30% ?};
      D -- 是 --> E{“休息”选项可用?};
      E -- 是 --> F[选择“休息”];
      E -- 否 --> G[选择体力消耗最低的行动];
      D -- 否 --> H{检查是否有SP课程或<br>“！”事件};
      H -- 是 --> I[选择SP课程/事件];
      H -- 否 --> J{计算所有课程的评分};
      J --> K[选择评分最高的课程];
      C --> Z[生成行动指令];
      F --> Z;
      G --> Z;
      I --> Z;
      K --> Z;
      Z --> End[结束决策];

      subgraph "课程评分逻辑"
          direction LR
          L[对每个课程] --> M{获取属性加成};
          M --> N[乘以用户策略权重<br>(e.g., focus_stat)];
          N --> O{考虑潜在的卡牌获取};
          O --> P[输出课程综合分];
      end
      J-.->L;

  ```
* **`run_full_produce_cycle()` - 育成任务回调**

  * **目标** : 执行一个完整的、从开始到结束的育成流程。这是一个高层协调函数。
  * **步骤分解** :

  1. `_navigate_to_produce_setup()`: 导航到育成准备界面。
  2. `_select_team(strategy: UserStrategy)`: 根据用户策略选择偶像和支援卡。
  3. `_start_produce()`: 点击“育成开始”并处理初始动画/事件。
  4. 育成主循环 (Loop for each week):
     a.  state = perception.get_game_state(): 感知当前状态。
     b.  action = decision.decide_next_action(state): 做出每周行动决策。
     c.  action_controller.execute_and_verify(action): 执行并验证行动。
     d.  _handle_post_action_events(): 处理行动后可能出现的事件或卡牌获取。
  5. `_handle_final_exam(state: GameState)`: 执行最终考试的逻辑。
  6. `_process_results_and_rewards()`: 点击处理育成结束后的结算界面。
  7. `_navigate_to_main_menu()`: 安全返回主菜单。
  8. **返回 `True`** 表示成功完成。任何步骤失败则会抛出异常，由上层调度器捕获。

### 数据库设计 (Schema)

*此部分为对第四章数据库设计的补充和细化，作为详细设计的一部分。*

* **`cards.json` Schema** :

```
  [
    {
      "card_id": "string",
      "name_jp": "string",
      "name_cn": "string",
      "type": "Vocal | Dance | Visual | Mental",
      "rarity": "N | R | SR | SSR",
      "cost": "integer",
      "effects": [
        {
          "type": "add_score | add_vigor | reduce_stamina_cost | ...",
          "value": "integer | string"
        }
      ]
    }
  ]

```

* **`user_strategy.yaml` Schema** :

```
  # 用户策略配置文件
  produce_goal:
    # 育成目标: high_score, true_end, stat_focus
    target: high_score
    # 如果目标是 stat_focus, 指定属性
    focus_stat: vocal # vocal, dance, visual

  team_composition:
    # 要培养的偶像名称
    produce_idol: '花海咲季'
    # 要使用的支援卡名称列表
    support_cards:
      - '【SSR】まだ見ぬ景色'
      - '【SR】あの日と同じように'

  behavior:
    # AI决策时的风险偏好，0.0为完全保守，1.0为完全激进
    risk_aversion: 0.5
    # 体力管理风格: aggressive, balanced, conservative
    stamina_management_style: 'balanced'

```

## 六、 测试计划

### 测试目标

* **单元测试** : 核心算法和工具函数的代码覆盖率达到80%以上。
* **集成测试** : 模块间接口调用成功率达到100%。
* **系统测试** : 在日语环境下，成功完成一次完整育成流程的概率达到95%以上。
* **性能测试** : 确保各项性能指标（见NFR-01）达标。
* **错误检测率** : 关键错误（导致程序崩溃或流程中断）的捕获率达到99%。
* **兼容性测试** : 在中文插件环境下，核心流程（导航、育成、考试）能够正常运行。
* **新增测试目标** : 自动启动功能的成功率达到98%以上。

### 测试环境

* **硬件** : PC (CPU: Intel Core i5 或同等, RAM: 8GB+, GPU: NVIDIA GTX 1650 或以上用于加速CV/AI)。
* **软件** : Windows 10/11,  **Python 3.13.3+ (64-bit)** , 《学园偶像大师》PC客户端最新版（ **以日语原版作为主要测试环境** ）, DMM Game Player最新版。

### 测试方法

| **测试类型** | **测试工具** | **测试内容描述**                                                             | **示例**                                                                                       |
| ------------------ | ------------------ | ---------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------------------- |
| **单元测试** | `pytest`         | 对独立的函数和类方法进行测试，特别是决策逻辑和数据处理部分。                       | 测试 `score_card`函数在给定输入下是否返回预期的分数值。                                            |
| **集成测试** | `pytest`         | 测试模块之间的交互是否正确，如感知模块输出的 `GameState`能否被决策模块正确解析。 | 模拟一个日文界面的屏幕截图，验证OCR能否正确识别并传递给决策模块。                                    |
| **系统测试** | 手动+自动化脚本    | 在真实的PC客户端上运行完整的机器人程序，观察其端到端的行为。                       | **主要任务** : 在日语环境下启动机器人，让其自动完成一次完整的育成，记录成功/失败以及关键日志。 |
| **回归测试** | 自动化测试套件     | 在代码或游戏更新后，重新运行所有关键的单元和集成测试，确保现有功能未被破坏。       | 在修改了卡牌评分权重后，重新运行所有与决策相关的测试用例。                                           |

#### 测试用例示例

| **Case ID**   | **模块** | **测试描述**           | **预置条件**                      | **测试步骤**                               | **预期结果**                                       |
| ------------------- | -------------- | ---------------------------- | --------------------------------------- | ------------------------------------------------ | -------------------------------------------------------- |
| TC-PER-01           | 感知           | 【日语】识别主菜单的育成按钮 | 游戏在主菜单界面                        | 1. 捕获屏幕2. 调用find_template函数              | 函数返回按钮的正确坐标，置信度>0.8                       |
| TC-PER-02           | 感知           | 【日语】识别体力值           | 游戏在育成界面                          | 1. 捕获屏幕2. 调用OCR读取体力ROI                 | 函数返回正确的体力数值。                                 |
| TC-DEC-01           | 决策           | 体力低下时优先休息           | `GameState`中体力为20，且“休息”可用 | 1. 将该 `GameState`传入 `decide_next_action` | 函数返回的 `Action`是点击“休息”按钮                  |
| TC-SYS-01           | 系统           | 【日语】完整育成流程         | 体力充足，用户策略已配置                | 1. 启动机器人                                    | 机器人成功完成一次育成，并返回等待状态                   |
| **TC-SYS-02** | 系统           | 游戏未运行时自动启动         | 游戏和DMM均未运行                       | 1. 启动 `main.py`                              | 机器人成功启动DMM Player，再启动游戏，并进入等待状态。   |
| TC-COMP-01          | 系统           | 【中文】兼容性测试           | 切换到中文插件环境                      | 1. 启动机器人                                    | 机器人能够识别中文界面关键元素并完成核心导航和课程操作。 |

## 七、 安全设计

### 安全需求

* **SR-01 (隐私保护)** : 程序不得以任何形式记录、存储或传输用户的游戏账号、密码或任何个人身份信息。
* **SR-02 (系统完整性)** : 程序必须以非侵入式方式运行，不得读取游戏进程内存、修改游戏文件或网络封包。
* **SR-03 (反检测)** : 程序应模拟人类行为以降低被游戏反作弊系统检测到的风险。

### 安全措施

* **数据隔离** : 所有配置文件（包括可能包含用户偏好的 `user_strategy.yaml`）均存储在本地，程序不执行任何网络上传操作。
* **纯视觉交互** : 所有对游戏状态的感知仅通过屏幕截图和计算机视觉技术完成，不与游戏进程直接交互。
* **类人输入模拟** :
* 在连续的鼠标点击之间引入随机的微小延迟（例如50-150毫秒）。
* 鼠标移动（若需要）应模拟非线性的轨迹，而不是瞬移。
* 避免以远超人类极限的速度执行连续操作。
* **代码审计** : 定期审查代码，确保没有引入任何可能违反安全需求的第三方库或代码片段。

## 八、 性能设计

### 性能需求

* **PERF-01 (实时响应)** : 在育成考试等需要快速反应的场景，从捕获屏幕到执行操作的延迟必须小于2秒。
* **PERF-02 (低资源挂机)** : 在任务调度器的“等待”状态下，程序的平均CPU使用率应低于5%。
* **PERF-03 (高负载处理)** : 在执行MCTS等计算密集型任务时，应充分利用多核CPU，同时不影响操作系统的基本响应。

### 优化计划 (基于Python 3.13)

#### 8.3.1 JIT 编译器 (PEP 744)

* **影响** : Python 3.13 引入了一个新的JIT编译器，它会将热点Python代码动态编译成优化的机器码。这对于决策模块中复杂的、纯Python实现的循环（如MCTS的模拟阶段、启发式评分的计算循环）将带来显著的性能提升，无需修改代码。
* **计划** :  **无需特别计划** ，只需使用Python 3.13解释器运行程序即可自动受益。在性能分析阶段，重点关注JIT对决策模块计算时间的改善。

#### 8.3.2 无GIL模式的并发设计 (PEP 703)

* **影响** : Python 3.13 引入了 **实验性的无GIL模式** （通过编译时标志 `--disable-gil` 启用）。这彻底改变了Python的并发模型。在此模式下，`threading`模块可以实现真正的并行计算，对于CPU密集型任务不再有性能瓶颈。
* **计划** :

1. **架构重新设计** :
   *  **首选 `threading`** : 将原本推荐用于CPU密集型任务的 `multiprocessing`方案， **更改为首选 `threading`** 。例如，MCTS的搜索、YOLOv8的推理等计算任务可以放在单独的线程中运行，与主线程共享内存，这比进程间通信（IPC）更简单、高效。
   *  **简化并发模型** : I/O密集型（屏幕捕获、日志）和CPU密集型（AI计算）任务现在可以统一使用 `threading`模型进行管理，降低了架构的复杂性。
2. **实现策略** :
   * 在 `DecisionModule`中，将 `_run_mcts`或其它耗时的AI计算封装在一个函数中，并通过 `threading.Thread(target=..., args=...).start()`在新线程中启动。
   * 使用 `queue.Queue`进行线程间的数据同步和通信，例如主线程将 `GameState`放入请求队列，AI线程计算完毕后将 `Action`放入结果队列。
3. **备选方案** : 仍然保留 `multiprocessing`作为 **稳定后备方案** 。在部署时，如果无GIL模式遇到兼容性问题或不稳定，可以快速切换回基于进程的并发模型。

* **并发模型流程图 (No-GIL模式)** :

```
  graph TD
      A[主线程: 调度与I/O]
      B[AI计算线程]
      C[屏幕捕获线程]

      A -- 将GameState放入 --> Q1(请求队列);
      B -- 从队列获取 --> Q1;
      B -- 执行MCTS/DQN计算 --> B;
      B -- 将Action放入 --> Q2(结果队列);
      A -- 从队列获取 --> Q2;
      A -- 执行Action --> A;

      C -- 捕获屏幕 --> C;
      C -- 将图像放入 --> Q3(图像队列);
      A -- 从队列获取 --> Q3;

```

#### 8.3.3 其他优化

* **CV流程优化** :
* **区域处理** : 优先使用模板匹配或YOLOv8定位到ROI，然后仅对这些小区域执行计算成本较高的OCR。
* **模型轻量化** : 选用 `yolov8n.pt`等轻量级模型进行初步开发，在需要更高精度时再考虑升级。
* **缓存机制** : 对于不经常变化的游戏数据（如卡牌数据库 `cards.json`），在程序启动时加载到内存中，避免重复的文件I/O。

## 九、 错误处理和异常设计

### 错误处理机制

* **结构化异常处理** : 所有可能产生I/O错误、计算错误或逻辑错误的代码块，都必须包裹在 `try...except`语句中。
* **详细日志记录** : 捕获到任何异常时，必须记录详细的错误信息，包括错误类型、错误消息、发生错误的代码位置和当时的堆栈跟踪。日志级别应设为 `ERROR`或 `CRITICAL`。
* **任务重试机制** : `Task`对象中包含 `retries_left`计数器。当一个任务执行失败时，调度器会减少其重试次数并将其重新放入队列，在一定延迟后再次尝试。
* **用户通知** : 对于无法自动恢复的严重错误（如连续重试失败），程序应通过CLI或Web UI向用户发出明确的失败通知。

### 异常设计

| **异常类型**                  | **触发条件**                                           | **处理策略**                                                                                                          |
| ----------------------------------- | ------------------------------------------------------------ | --------------------------------------------------------------------------------------------------------------------------- |
| `GameUIElementNotFound`           | 在屏幕上找不到预期的UI元素（按钮、图标等）                   | 1. 记录WARNING日志。2. 等待1-2秒后重试感知2-3次。3. 若仍然失败，判定为未知场景，执行“返回主菜单”的安全操作序列。          |
| `GameCrashException`              | 检测到游戏窗口不存在或无响应                                 | 1. 记录CRITICAL日志。2. 尝试通过系统命令重启游戏客户端。3. 若重启成功，从上一个断点恢复任务；否则，程序安全退出并通知用户。 |
| `OCRUnreliableException`          | OCR连续多次返回空值或格式不符的乱码                          | 1. 记录ERROR日志。2. 尝试不同的图像预处理策略。3. 若仍然失败，跳过当前决策，执行一个默认的安全操作（如结束回合）。          |
| `InvalidConfiguration`            | 启动时加载的 `.yaml`或 `.json`文件格式错误或缺少关键字段 | 1. 程序启动失败，并向控制台打印具体的配置错误信息，指出哪个文件、哪一行有问题。2. 程序立即退出，要求用户修复配置。          |
| **`DMMPlayerLaunchFailed`** | 执行DMM Player程序失败（路径错误或程序损坏）                 | 1. 记录CRITICAL日志。2. 程序终止，并提示用户检查dmm_player_path配置或DMM客户端。                                            |
| **`GameIconNotFoundInDMM`** | DMM Player已启动，但在其界面上找不到游戏图标                 | 1. 记录ERROR日志。2. 重试查找3次。3. 仍然失败则终止程序，提示用户检查游戏是否已安装或模板图片是否正确。                     |

## 十、 部署设计

### 部署架构

本软件设计为单机桌面应用程序，部署在运行《学园偶像大师》PC客户端的同一台Windows计算机上。

* **硬件环境** :
* 操作系统: Windows 10 (64-bit) 或更高版本
* CPU: 4核或以上 @ 2.5 GHz+
* 内存: 推荐16 GB或以上
* GPU: NVIDIA GeForce GTX 1650 4GB或更高级别的显卡（用于AI计算加速）
* **软件环境** :
* **Python 3.13.3+ (64-bit)**
* 安装所有在 `requirements.txt`中列出的依赖库
* 《学园偶像大师》PC客户端
* **操作系统已安装目标语言字体（如MS Gothic/Meiryo）以保证UI正确渲染。**

### 部署流程

1. **环境准备** :

* 确保已安装NVIDIA显卡驱动和CUDA工具包（若需GPU加速）。
* **安装Python 3.13.3+** 。
* **(性能关键部署) 编译无GIL版本的Python** : 从源码编译Python时，使用 `./configure --disable-gil`选项。
* 安装Git。

1. **获取代码** :

* 通过 `git clone`命令从代码仓库克隆项目到本地。

1. **安装依赖** :

* 打开命令行，进入项目根目录。
* 创建并激活Python虚拟环境：`python -m venv venv` 和 `venv\Scripts\activate`。
* 安装所有依赖：`pip install -r requirements.txt`。

1. **配置程序** :

* 根据 `config/settings.yaml.example`创建 `settings.yaml`文件，并 **必须填入 `dmm_player_path`的正确路径** 。
* 根据 `config/user_strategy.yaml.example`创建 `user_strategy.yaml`文件，并定义用户的育成策略。
* **(首次运行)** 截取用户DMM Player库中的游戏图标，保存为 `assets/templates/dmm_gakumasu_icon.png`。

1. **运行程序** :

* 在激活的虚拟环境中，执行 `python main.py`启动机器人。

1. **(可选) 打包分发** :

* 未来可使用 `PyInstaller`等工具将整个Python项目打包成一个独立的 `.exe`可执行文件，方便无Python环境的用户直接使用。需注意打包工具对Python 3.13及无GIL模式的支持情况。

## 十一、 附录

### 补充材料

* **技术参考文献** :
* Python 3.13 Documentation: [https://docs.python.org/3.13/](https://docs.python.org/3.13/ "null")
* PEP 703 -- Making the GIL Optional in CPython: [https://peps.python.org/pep-0703/](https://peps.python.org/pep-0703/ "null")
* **游戏数据来源 (示例)** :
* 学园偶像大师攻略Wiki: [https://seesaawiki.jp/gakumasu/](https://seesaawiki.jp/gakumasu/ "null")

### 推荐库与版本 (Python 3.13)

以下列表基于当前（2025年6月）各库对Python 3.13的兼容性状况，具体版本可能需要根据发布情况进行微调。

| **库名称**          | **推荐版本** | **用途**                      | **备注**                                                                     |
| ------------------------- | ------------------ | ----------------------------------- | ---------------------------------------------------------------------------------- |
| `opencv-python`         | `4.9.0.80`+      | 图像处理                            | OpenCV社区通常会快速跟进新Python版本。                                             |
| `easyocr`               | `1.7.1`+         | 文本识别                            | 依赖PyTorch，需确保PyTorch支持Python 3.13。**必须包含日语 (`ja`) 支持** 。 |
| `torch`/`torchvision` | `2.3.0`+         | `easyocr`和 `ultralytics`的后端 | PyTorch通常会为新的Python版本提供预编译包。                                        |
| `ultralytics`           | `8.2.28`+        | YOLOv8对象检测                      | `ultralytics`库更新频繁。                                                        |
| `mss`                   | `9.0.1`+         | 屏幕捕获                            | 纯Python和 `ctypes`库，兼容性良好。                                              |
| `pydirectinput`         | `1.0.4`+         | 输入模拟                            | 核心依赖 `pywin32`，通常兼容性较好。                                             |
| `pyyaml`                | `6.0.1`+         | 配置加载                            | 成熟库，兼容性问题不大。                                                           |
| `pytest`                | `8.2.2`+         | 测试框架                            | 测试工具通常会优先支持新Python版本。                                               |

### 版本历史

| **版本号** | **修改日期** | **修改人** | **修改内容**                                                          |
| ---------------- | ------------------ | ---------------- | --------------------------------------------------------------------------- |
| **3.0**    | 2025-06-19         | Gemini           | 按照11章节标准格式重构整个文档，并深化第四、五章内容。                      |
| **3.1**    | 2025-06-19         | Gemini           | **增加自动启动DMM及游戏客户端的功能设计，更新相关需求、流程和配置。** |
