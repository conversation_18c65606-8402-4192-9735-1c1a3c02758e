# Gakumasu-Bot 用户策略配置文件
# 请复制此文件为 user_strategy.yaml 并根据个人喜好修改配置

# 育成目标配置
produce_goal:
  # 育成目标类型:
  # - high_score: 追求高分
  # - true_end: 追求True End
  # - stat_focus: 专注特定属性
  target: high_score
  
  # 专注属性（当target为stat_focus时有效）
  # 可选值: vocal, dance, visual, mental
  focus_stat: null
  
  # 目标分数（当target为high_score时的目标值）
  target_score: 15000
  
  # 属性优先级权重
  priority_weights:
    vocal: 1.0    # 声乐权重
    dance: 1.0    # 舞蹈权重  
    visual: 1.0   # 视觉权重
    mental: 0.8   # 精神权重

# 队伍配置
team_composition:
  # 要培养的偶像名称（日文名）
  produce_idol: "花海咲季"
  
  # 支援卡名称列表（日文名）
  support_cards:
    - "【SSR】まだ見ぬ景色"
    - "【SR】あの日と同じように"
    - "【SR】新しい私になって"
    - "【R】みんなでいっしょに"
    - "【R】今日という日を"
  
  # 牌组策略:
  # - balanced: 平衡型
  # - aggressive: 激进型
  # - defensive: 防守型
  deck_strategy: balanced

# 行为配置
behavior:
  # 风险规避程度 (0.0 = 完全激进, 1.0 = 完全保守)
  risk_aversion: 0.5
  
  # 体力管理风格:
  # - aggressive: 激进使用体力
  # - balanced: 平衡管理
  # - conservative: 保守使用体力
  stamina_management_style: balanced
  
  # 决策超时时间（秒）
  decision_timeout: 30.0
  
  # 是否启用MCTS算法
  enable_mcts: true
  
  # MCTS迭代次数
  mcts_iterations: 1000

# 高级策略配置
advanced:
  # 课程选择偏好
  lesson_preferences:
    # 优先选择SP课程
    prefer_sp_lessons: true
    
    # 优先选择有"！"标记的课程
    prefer_special_events: true
    
    # 体力不足时的行为
    low_stamina_behavior: rest  # rest, continue, auto
  
  # 考试策略
  exam_strategy:
    # 是否使用最优卡牌组合
    use_optimal_cards: true
    
    # 是否保留高价值卡牌到最后
    save_high_value_cards: true
    
    # 最小保证分数阈值
    minimum_score_threshold: 8000
  
  # 事件处理偏好
  event_handling:
    # 未知事件的默认选择（第几个选项，从1开始）
    unknown_event_default_choice: 1
    
    # 是否优先选择推荐选项
    prefer_recommended_choices: true
    
    # 是否跳过已知的负面事件
    skip_negative_events: false

# 自动化任务配置
automation:
  # 是否启用自动打工
  enable_part_time_job: true
  
  # 打工时间偏好（小时，24小时制）
  part_time_job_preferred_hours: [9, 14, 19]
  
  # 是否启用自动日常任务
  enable_daily_tasks: true
  
  # 日常任务执行时间（小时，24小时制）
  daily_tasks_time: 8
  
  # 是否启用自动育成
  enable_auto_produce: true
  
  # 连续育成次数限制（0为无限制）
  max_consecutive_produces: 5
  
  # 育成间隔时间（分钟）
  produce_interval_minutes: 5

# 通知设置
notifications:
  # 是否启用完成通知
  enable_completion_notifications: true
  
  # 是否启用错误通知
  enable_error_notifications: true
  
  # 通知方式: console, file, webhook
  notification_methods: [console, file]
  
  # Webhook URL（如果使用webhook通知）
  webhook_url: ""
