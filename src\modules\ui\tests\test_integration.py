"""
集成测试
测试UI架构各组件的集成功能
"""

import unittest
import time
from unittest.mock import Mock, patch

from .test_utils import UITestCase, create_test_scene_config
from .test_data import TEST_SEQUENCES, PERFORMANCE_TEST_DATA
from ....core.data_structures import GameScene
from ..elements.ui_element_factory import UIElementFactory, UIElementType
from ..elements.composite import FormElement, DialogElement
from ..managers.scene_factory import EnhancedSceneFactory
from ..managers.scene_manager import EnhancedSceneManager, NavigationResult
from ..managers.config_loader import UIConfigLoader
from ..managers.legacy_adapter import LegacyCompatibilityAdapter


class TestUIArchitectureIntegration(UITestCase):
    """UI架构集成测试"""
    
    def setUp(self):
        super().setUp()
        
        # 创建完整的UI架构组件
        self.config_loader = Mock(spec=UIConfigLoader)
        self.ui_factory = UIElementFactory(self.perception, self.action)
        self.scene_factory = EnhancedSceneFactory(self.perception, self.action, self.config_loader)
        self.scene_manager = EnhancedSceneManager(self.scene_factory)
        self.legacy_adapter = LegacyCompatibilityAdapter(self.scene_manager)
        
        # 设置模拟数据
        self.setup_integration_mocks()
    
    def setup_integration_mocks(self):
        """设置集成测试的模拟数据"""
        # 设置场景模板
        self.perception.set_mock_template("main_menu_logo", 960, 100, 0.9)
        self.perception.set_mock_template("produce_button", 500, 400, 0.85)
        self.perception.set_mock_template("start_produce_button", 960, 800, 0.85)
        self.perception.set_mock_template("vocal_lesson_button", 200, 300, 0.85)
        
        # 设置游戏状态
        self.perception.set_mock_game_state(GameScene.MAIN_MENU)
    
    def test_complete_workflow(self):
        """测试完整的工作流程"""
        # 1. 创建UI元素
        produce_button = self.ui_factory.create_button(
            "produce_button", 
            confidence_threshold=0.8,
            expected_scene_after_click="produce_setup"
        )
        
        self.assertIsNotNone(produce_button)
        
        # 2. 点击按钮
        result = produce_button.click()
        self.assertTrue(result)
        
        # 3. 验证点击历史
        self.assert_element_clicked("produce_button")
        
        # 4. 使用场景管理器导航
        navigation_result = self.scene_manager.navigate_to_scene(GameScene.PRODUCE_SETUP)
        
        # 由于是模拟环境，导航可能失败，但应该有尝试
        self.assertIn(navigation_result, [NavigationResult.SUCCESS, NavigationResult.FAILED])
    
    def test_legacy_compatibility(self):
        """测试遗留兼容性"""
        # 使用遗留方式点击元素
        result = self.legacy_adapter.click_ui_element_by_template("produce_button")
        
        # 由于没有注册场景类，可能会回退到原有方式
        # 这里主要测试适配器不会崩溃
        self.assertIsInstance(result, bool)
        
        # 测试场景检查
        is_main_menu = self.legacy_adapter.is_scene_current_legacy("main_menu")
        self.assertIsInstance(is_main_menu, bool)
        
        # 获取使用统计
        stats = self.legacy_adapter.get_usage_stats()
        self.assertGreater(stats['total_calls'], 0)
    
    def test_form_interaction_sequence(self):
        """测试表单交互序列"""
        # 创建表单元素
        form = self.ui_factory.create_form("test_form")
        self.assertIsNotNone(form)
        
        # 添加输入字段
        input_field = self.ui_factory.create_input_field("test_input")
        form.add_input_field("username", input_field, required=True)
        
        # 添加提交按钮
        submit_button = self.ui_factory.create_button("submit_button")
        form.add_submit_button("submit", submit_button)
        
        # 填写表单
        form_data = {"username": "测试用户"}
        result = form.fill_form(form_data)
        self.assertTrue(result)
        
        # 提交表单
        result = form.submit_form()
        self.assertTrue(result)
        
        # 验证交互历史
        self.assert_text_typed("测试用户")
        self.assert_element_clicked("submit_button")
    
    def test_dialog_handling(self):
        """测试对话框处理"""
        # 创建对话框元素
        dialog = self.ui_factory.create_dialog("test_dialog")
        self.assertIsNotNone(dialog)
        
        # 添加对话框组件
        ok_button = self.ui_factory.create_button("ok_button")
        cancel_button = self.ui_factory.create_button("cancel_button")
        
        dialog.add_child_element("ok", ok_button)
        dialog.add_child_element("cancel", cancel_button)
        dialog.set_ok_button("ok")
        dialog.set_cancel_button("cancel")
        
        # 处理对话框
        result = dialog.handle_dialog("ok")
        self.assertTrue(result)
        
        # 验证点击
        self.assert_element_clicked("ok_button")
    
    def test_scene_navigation_with_ui_elements(self):
        """测试场景导航与UI元素交互"""
        # 注册模拟场景类
        from .test_scenes import MockScene
        self.scene_factory.register_scene_class(GameScene.MAIN_MENU, MockScene)
        self.scene_factory.register_scene_class(GameScene.PRODUCE_SETUP, MockScene)
        
        # 获取当前场景
        current_scene = self.scene_manager.get_current_scene()
        
        # 如果有当前场景，添加UI元素
        if current_scene:
            produce_button = self.ui_factory.create_button("produce_button")
            current_scene.ui_elements["produce_button"] = produce_button
            
            # 获取并点击UI元素
            element = current_scene.get_ui_element("produce_button")
            if element:
                result = element.click()
                self.assertTrue(result)
        
        # 导航到新场景
        result = self.scene_manager.navigate_to_scene(GameScene.PRODUCE_SETUP)
        self.assertIn(result, [NavigationResult.SUCCESS, NavigationResult.FAILED])
    
    def test_error_handling_and_recovery(self):
        """测试错误处理和恢复"""
        # 设置点击失败
        self.action.set_click_result("produce_button", False)
        
        # 创建按钮并尝试点击
        button = self.ui_factory.create_button("produce_button", retry_count=3)
        result = button.click()
        
        # 应该失败
        self.assertFalse(result)
        
        # 检查重试次数
        history = self.action.get_action_history()
        click_attempts = [a for a in history if a['action'] == 'click' and a['template'] == 'produce_button']
        self.assertGreaterEqual(len(click_attempts), 1)  # 至少尝试一次
    
    def test_performance_monitoring(self):
        """测试性能监控"""
        # 创建多个元素并测试性能
        start_time = time.time()
        
        elements = []
        for i in range(10):
            element = self.ui_factory.create_button(f"button_{i}")
            elements.append(element)
        
        creation_time = time.time() - start_time
        
        # 验证创建时间合理
        self.assertLess(creation_time, 1.0)  # 10个元素创建应该在1秒内完成
        
        # 测试元素交互性能
        start_time = time.time()
        
        for element in elements:
            element.is_visible()
            element.get_position()
        
        interaction_time = time.time() - start_time
        
        # 验证交互时间合理
        self.assertLess(interaction_time, 0.5)  # 20次操作应该在0.5秒内完成
    
    def test_configuration_integration(self):
        """测试配置集成"""
        # 模拟配置加载
        mock_config = create_test_scene_config(GameScene.MAIN_MENU)
        self.config_loader.load_scene_config.return_value = mock_config
        
        # 创建场景
        scene = self.scene_factory.create_scene(GameScene.MAIN_MENU)
        
        # 验证配置被正确使用
        if scene:
            self.assertEqual(scene.config.scene_type, GameScene.MAIN_MENU)
            self.config_loader.load_scene_config.assert_called_with(GameScene.MAIN_MENU)
    
    def test_caching_integration(self):
        """测试缓存集成"""
        # 启用缓存
        self.scene_manager.set_cache_enabled(True)
        
        # 注册模拟场景类
        from .test_scenes import MockScene
        self.scene_factory.register_scene_class(GameScene.MAIN_MENU, MockScene)
        
        # 第一次获取场景
        scene1 = self.scene_manager.get_scene(GameScene.MAIN_MENU)
        
        # 第二次获取场景（应该从缓存）
        scene2 = self.scene_manager.get_scene(GameScene.MAIN_MENU)
        
        # 验证缓存工作
        self.assertIs(scene1, scene2)
        
        # 清理缓存
        self.scene_manager.clear_cache()
        
        # 再次获取（应该是新对象）
        scene3 = self.scene_manager.get_scene(GameScene.MAIN_MENU)
        self.assertIsNot(scene1, scene3)


class TestPerformanceBenchmarks(UITestCase):
    """性能基准测试"""
    
    def setUp(self):
        super().setUp()
        self.ui_factory = UIElementFactory(self.perception, self.action)
    
    def test_element_creation_performance(self):
        """测试元素创建性能"""
        test_data = PERFORMANCE_TEST_DATA["element_creation_benchmark"]
        
        for element_type in test_data["element_types"]:
            with self.subTest(element_type=element_type):
                start_time = time.time()
                
                for i in range(test_data["iterations"]):
                    if element_type == "button":
                        element = self.ui_factory.create_button(f"test_{i}")
                    elif element_type == "input_field":
                        element = self.ui_factory.create_input_field(f"test_{i}")
                    elif element_type == "label":
                        element = self.ui_factory.create_label(f"test_{i}")
                    elif element_type == "enhanced_button":
                        element = self.ui_factory.create_enhanced_button(f"test_{i}")
                    
                    self.assertIsNotNone(element)
                
                total_time = time.time() - start_time
                avg_time = total_time / test_data["iterations"]
                
                # 验证性能符合预期
                self.assertLess(avg_time, test_data["expected_max_time"])
                
                print(f"{element_type} 创建性能: {avg_time:.6f}秒/个 (总计: {total_time:.3f}秒)")
    
    def test_ui_interaction_performance(self):
        """测试UI交互性能"""
        test_data = PERFORMANCE_TEST_DATA["ui_element_interaction_benchmark"]
        
        # 创建测试元素
        button = self.ui_factory.create_button("perf_button")
        input_field = self.ui_factory.create_input_field("perf_input")
        label = self.ui_factory.create_label("perf_label")
        
        # 设置OCR结果
        region = {'x': 450, 'y': 585, 'width': 100, 'height': 30}
        self.perception.set_mock_ocr_result(region, "性能测试")
        
        for interaction in test_data["interactions"]:
            with self.subTest(interaction=interaction):
                start_time = time.time()
                
                for i in range(test_data["iterations"]):
                    if interaction == "click":
                        button.click()
                    elif interaction == "input_text":
                        input_field.input_text(f"测试文本{i}")
                    elif interaction == "read_text":
                        label.read_text()
                
                total_time = time.time() - start_time
                avg_time = total_time / test_data["iterations"]
                
                # 验证性能符合预期
                self.assertLess(avg_time, test_data["expected_max_time"])
                
                print(f"{interaction} 交互性能: {avg_time:.6f}秒/次 (总计: {total_time:.3f}秒)")
    
    def test_memory_usage(self):
        """测试内存使用"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss
        
        # 创建大量元素
        elements = []
        for i in range(1000):
            element = self.ui_factory.create_button(f"memory_test_{i}")
            elements.append(element)
        
        peak_memory = process.memory_info().rss
        memory_increase = peak_memory - initial_memory
        
        # 清理元素
        elements.clear()
        
        final_memory = process.memory_info().rss
        
        print(f"内存使用: 初始 {initial_memory/1024/1024:.2f}MB, "
              f"峰值 {peak_memory/1024/1024:.2f}MB, "
              f"增加 {memory_increase/1024/1024:.2f}MB, "
              f"最终 {final_memory/1024/1024:.2f}MB")
        
        # 验证内存使用合理（每个元素不超过1KB）
        avg_memory_per_element = memory_increase / 1000
        self.assertLess(avg_memory_per_element, 1024)  # 1KB per element


if __name__ == '__main__':
    # 运行测试时显示详细输出
    unittest.main(verbosity=2)
