"""
AI辅助决策系统
基于历史数据和当前状态提供智能决策建议
"""

import time
import json
from typing import List, Dict, Any, Optional, Tuple
from enum import Enum
from dataclasses import dataclass, field
from collections import defaultdict
import numpy as np

from ....utils.logger import get_logger
from ....core.data_structures import GameScene
from ..utils.performance_monitor import measure_block


class DecisionType(Enum):
    """决策类型枚举"""
    LESSON_SELECTION = "lesson_selection"      # 课程选择
    ACTION_SELECTION = "action_selection"      # 行动选择
    STRATEGY_SELECTION = "strategy_selection"  # 策略选择
    RESOURCE_ALLOCATION = "resource_allocation" # 资源分配
    TIMING_OPTIMIZATION = "timing_optimization" # 时机优化


class ConfidenceLevel(Enum):
    """置信度等级枚举"""
    VERY_HIGH = "very_high"    # 非常高 (>90%)
    HIGH = "high"              # 高 (70-90%)
    MEDIUM = "medium"          # 中等 (50-70%)
    LOW = "low"                # 低 (30-50%)
    VERY_LOW = "very_low"      # 非常低 (<30%)


class RiskLevel(Enum):
    """风险等级枚举"""
    VERY_LOW = "very_low"      # 非常低风险
    LOW = "low"                # 低风险
    MEDIUM = "medium"          # 中等风险
    HIGH = "high"              # 高风险
    VERY_HIGH = "very_high"    # 非常高风险


@dataclass
class DecisionContext:
    """决策上下文"""
    scene_type: GameScene
    current_stats: Dict[str, Any]
    available_actions: List[str]
    constraints: Dict[str, Any] = field(default_factory=dict)
    goals: Dict[str, Any] = field(default_factory=dict)
    history: List[Dict[str, Any]] = field(default_factory=list)
    timestamp: float = field(default_factory=time.time)


@dataclass
class DecisionRecommendation:
    """决策推荐"""
    action: str
    decision_type: DecisionType
    confidence: float
    confidence_level: ConfidenceLevel
    risk_level: RiskLevel
    expected_outcome: Dict[str, Any]
    reasoning: str
    alternatives: List[Dict[str, Any]] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


class DecisionAssistant:
    """AI辅助决策系统"""
    
    def __init__(self, learning_rate: float = 0.1, confidence_threshold: float = 0.7):
        """
        初始化决策辅助系统
        
        Args:
            learning_rate: 学习率
            confidence_threshold: 置信度阈值
        """
        self.learning_rate = learning_rate
        self.confidence_threshold = confidence_threshold
        self.logger = get_logger("DecisionAssistant")
        
        # 决策历史和学习数据
        self._decision_history: List[Dict[str, Any]] = []
        self._outcome_history: List[Dict[str, Any]] = []
        self._pattern_database: Dict[str, Any] = {}
        
        # 决策模型参数
        self._model_weights: Dict[str, float] = {
            "stat_balance": 0.3,
            "efficiency": 0.25,
            "risk_tolerance": 0.2,
            "goal_alignment": 0.15,
            "historical_success": 0.1
        }
        
        # 统计数据
        self._stats = {
            "total_decisions": 0,
            "successful_decisions": 0,
            "accuracy_rate": 0.0,
            "average_confidence": 0.0
        }
        
        self.logger.info("AI辅助决策系统初始化完成")
    
    def analyze_situation(self, context: DecisionContext) -> Dict[str, Any]:
        """
        分析当前情况
        
        Args:
            context: 决策上下文
            
        Returns:
            分析结果
        """
        try:
            with measure_block("situation_analysis"):
                analysis = {
                    "context_summary": self._summarize_context(context),
                    "stat_analysis": self._analyze_stats(context.current_stats),
                    "action_feasibility": self._analyze_action_feasibility(context),
                    "risk_assessment": self._assess_risks(context),
                    "opportunity_analysis": self._analyze_opportunities(context),
                    "historical_patterns": self._find_historical_patterns(context)
                }
                
                self.logger.debug(f"情况分析完成: {context.scene_type.value}")
                return analysis
                
        except Exception as e:
            self.logger.error(f"情况分析失败: {e}")
            return {}
    
    def recommend_action(self, context: DecisionContext) -> DecisionRecommendation:
        """
        推荐行动
        
        Args:
            context: 决策上下文
            
        Returns:
            决策推荐
        """
        try:
            with measure_block("action_recommendation"):
                # 分析当前情况
                analysis = self.analyze_situation(context)
                
                # 评估所有可用行动
                action_scores = self._evaluate_actions(context, analysis)
                
                # 选择最佳行动
                best_action = self._select_best_action(action_scores)
                
                # 生成推荐
                recommendation = self._generate_recommendation(
                    best_action, context, analysis, action_scores
                )
                
                # 记录决策
                self._record_decision(context, recommendation)
                
                self.logger.info(f"推荐行动: {recommendation.action} (置信度: {recommendation.confidence:.2f})")
                return recommendation
                
        except Exception as e:
            self.logger.error(f"推荐行动失败: {e}")
            return self._create_fallback_recommendation(context)
    
    def _summarize_context(self, context: DecisionContext) -> Dict[str, Any]:
        """总结上下文信息"""
        return {
            "scene": context.scene_type.value,
            "available_actions_count": len(context.available_actions),
            "has_constraints": len(context.constraints) > 0,
            "has_goals": len(context.goals) > 0,
            "history_length": len(context.history)
        }
    
    def _analyze_stats(self, stats: Dict[str, Any]) -> Dict[str, Any]:
        """分析属性状态"""
        try:
            # 基础属性分析
            core_stats = ["vocal", "dance", "visual", "mental"]
            stat_values = [stats.get(stat, 0) for stat in core_stats]
            
            if not stat_values or all(v == 0 for v in stat_values):
                return {"balance": 1.0, "total": 0, "average": 0, "weakness": None}
            
            total_stats = sum(stat_values)
            avg_stat = total_stats / len(stat_values)
            
            # 计算平衡度
            variance = sum((v - avg_stat) ** 2 for v in stat_values) / len(stat_values)
            std_dev = variance ** 0.5
            balance = max(0.0, 1.0 - (std_dev / max(avg_stat, 1)))
            
            # 找出最弱项
            min_stat_idx = stat_values.index(min(stat_values))
            weakness = core_stats[min_stat_idx]
            
            return {
                "balance": balance,
                "total": total_stats,
                "average": avg_stat,
                "weakness": weakness,
                "stamina": stats.get("stamina", 100),
                "motivation": stats.get("motivation", 3)
            }
            
        except Exception as e:
            self.logger.error(f"属性分析失败: {e}")
            return {"balance": 0.5, "total": 0, "average": 0, "weakness": "vocal"}
    
    def _analyze_action_feasibility(self, context: DecisionContext) -> Dict[str, float]:
        """分析行动可行性"""
        feasibility = {}
        
        for action in context.available_actions:
            score = 1.0  # 基础可行性
            
            # 检查约束条件
            if context.constraints:
                if action in context.constraints.get("forbidden_actions", []):
                    score = 0.0
                elif action in context.constraints.get("limited_actions", {}):
                    limit = context.constraints["limited_actions"][action]
                    score *= min(1.0, limit)
            
            # 检查资源需求
            stamina = context.current_stats.get("stamina", 100)
            if "lesson" in action and stamina < 20:
                score *= 0.3  # 体力不足时降低课程可行性
            elif action == "rest" and stamina > 80:
                score *= 0.5  # 体力充足时降低休息可行性
            
            feasibility[action] = score
        
        return feasibility
    
    def _assess_risks(self, context: DecisionContext) -> Dict[str, Any]:
        """评估风险"""
        risks = {
            "stamina_depletion": 0.0,
            "stat_imbalance": 0.0,
            "time_pressure": 0.0,
            "goal_deviation": 0.0
        }
        
        # 体力耗尽风险
        stamina = context.current_stats.get("stamina", 100)
        if stamina < 30:
            risks["stamina_depletion"] = 0.8
        elif stamina < 50:
            risks["stamina_depletion"] = 0.4
        
        # 属性不平衡风险
        stat_analysis = self._analyze_stats(context.current_stats)
        risks["stat_imbalance"] = 1.0 - stat_analysis["balance"]
        
        # 时间压力风险（基于历史长度估算）
        if len(context.history) > 50:
            risks["time_pressure"] = 0.6
        elif len(context.history) > 30:
            risks["time_pressure"] = 0.3
        
        return risks
    
    def _analyze_opportunities(self, context: DecisionContext) -> Dict[str, float]:
        """分析机会"""
        opportunities = {}
        
        stat_analysis = self._analyze_stats(context.current_stats)
        
        # 属性提升机会
        if stat_analysis["weakness"]:
            opportunities[f"improve_{stat_analysis['weakness']}"] = 0.8
        
        # 平衡优化机会
        if stat_analysis["balance"] < 0.7:
            opportunities["balance_stats"] = 0.6
        
        # 效率提升机会
        if context.current_stats.get("motivation", 3) >= 4:
            opportunities["high_efficiency_training"] = 0.7
        
        return opportunities
    
    def _find_historical_patterns(self, context: DecisionContext) -> Dict[str, Any]:
        """查找历史模式"""
        patterns = {
            "successful_sequences": [],
            "failure_patterns": [],
            "optimal_timing": {}
        }
        
        # 简化实现：基于决策历史查找模式
        if len(self._decision_history) > 10:
            # 查找成功的决策序列
            successful_decisions = [
                d for d in self._decision_history 
                if d.get("outcome_success", False)
            ]
            
            if successful_decisions:
                # 统计成功决策的行动类型
                action_success_rate = defaultdict(list)
                for decision in successful_decisions:
                    action = decision.get("action", "")
                    confidence = decision.get("confidence", 0.0)
                    action_success_rate[action].append(confidence)
                
                # 计算平均成功率
                for action, confidences in action_success_rate.items():
                    if len(confidences) >= 3:  # 至少3次记录
                        avg_confidence = sum(confidences) / len(confidences)
                        patterns["successful_sequences"].append({
                            "action": action,
                            "success_rate": avg_confidence,
                            "frequency": len(confidences)
                        })
        
        return patterns
    
    def _evaluate_actions(self, context: DecisionContext, analysis: Dict[str, Any]) -> Dict[str, Dict[str, float]]:
        """评估所有可用行动"""
        action_scores = {}
        
        for action in context.available_actions:
            scores = {
                "feasibility": analysis["action_feasibility"].get(action, 0.0),
                "stat_benefit": self._calculate_stat_benefit(action, analysis["stat_analysis"]),
                "risk_score": self._calculate_risk_score(action, analysis["risk_assessment"]),
                "opportunity_score": self._calculate_opportunity_score(action, analysis["opportunity_analysis"]),
                "historical_score": self._calculate_historical_score(action, analysis["historical_patterns"])
            }
            
            # 计算加权总分
            total_score = sum(
                scores[key] * self._model_weights.get(key.replace("_score", "").replace("_benefit", ""), 0.2)
                for key in scores.keys()
            )
            
            scores["total"] = total_score
            action_scores[action] = scores
        
        return action_scores
    
    def _calculate_stat_benefit(self, action: str, stat_analysis: Dict[str, Any]) -> float:
        """计算属性收益"""
        if "lesson" in action:
            # 课程对应的属性提升
            lesson_stat_map = {
                "vocal_lesson": "vocal",
                "dance_lesson": "dance", 
                "visual_lesson": "visual",
                "mental_lesson": "mental"
            }
            
            target_stat = lesson_stat_map.get(action)
            if target_stat and target_stat == stat_analysis.get("weakness"):
                return 0.9  # 针对弱项训练高收益
            elif target_stat:
                return 0.6  # 一般属性训练中等收益
        
        elif action == "rest":
            # 休息对体力的收益
            stamina = stat_analysis.get("stamina", 100)
            if stamina < 50:
                return 0.8
            elif stamina < 80:
                return 0.4
            else:
                return 0.1
        
        elif action == "outing":
            # 外出对干劲的收益
            motivation = stat_analysis.get("motivation", 3)
            if motivation < 3:
                return 0.7
            else:
                return 0.3
        
        return 0.5  # 默认收益
    
    def _calculate_risk_score(self, action: str, risk_assessment: Dict[str, Any]) -> float:
        """计算风险评分（风险越低分数越高）"""
        base_score = 0.8
        
        # 体力耗尽风险
        if "lesson" in action and risk_assessment.get("stamina_depletion", 0) > 0.5:
            base_score -= 0.3
        
        # 属性不平衡风险
        if risk_assessment.get("stat_imbalance", 0) > 0.7:
            if "lesson" in action:
                base_score += 0.2  # 训练有助于平衡
            else:
                base_score -= 0.1
        
        return max(0.0, min(1.0, base_score))
    
    def _calculate_opportunity_score(self, action: str, opportunity_analysis: Dict[str, float]) -> float:
        """计算机会评分"""
        score = 0.5
        
        for opportunity, value in opportunity_analysis.items():
            if "improve" in opportunity and "lesson" in action:
                target_stat = opportunity.replace("improve_", "")
                if target_stat in action:
                    score += value * 0.5
            elif opportunity == "balance_stats" and "lesson" in action:
                score += value * 0.3
            elif opportunity == "high_efficiency_training" and "lesson" in action:
                score += value * 0.4
        
        return min(1.0, score)
    
    def _calculate_historical_score(self, action: str, historical_patterns: Dict[str, Any]) -> float:
        """计算历史评分"""
        score = 0.5
        
        successful_sequences = historical_patterns.get("successful_sequences", [])
        for sequence in successful_sequences:
            if sequence["action"] == action:
                # 基于历史成功率调整分数
                success_rate = sequence["success_rate"]
                frequency = sequence["frequency"]
                
                # 频率权重：更多的历史数据更可信
                weight = min(1.0, frequency / 10.0)
                score = score * (1 - weight) + success_rate * weight
                break
        
        return score
    
    def _select_best_action(self, action_scores: Dict[str, Dict[str, float]]) -> str:
        """选择最佳行动"""
        if not action_scores:
            return "rest"  # 默认行动
        
        # 按总分排序
        sorted_actions = sorted(
            action_scores.items(),
            key=lambda x: x[1]["total"],
            reverse=True
        )
        
        return sorted_actions[0][0]
    
    def _generate_recommendation(self, action: str, context: DecisionContext, 
                               analysis: Dict[str, Any], action_scores: Dict[str, Dict[str, float]]) -> DecisionRecommendation:
        """生成推荐"""
        action_score = action_scores.get(action, {})
        confidence = action_score.get("total", 0.5)
        
        # 确定置信度等级
        if confidence >= 0.9:
            confidence_level = ConfidenceLevel.VERY_HIGH
        elif confidence >= 0.7:
            confidence_level = ConfidenceLevel.HIGH
        elif confidence >= 0.5:
            confidence_level = ConfidenceLevel.MEDIUM
        elif confidence >= 0.3:
            confidence_level = ConfidenceLevel.LOW
        else:
            confidence_level = ConfidenceLevel.VERY_LOW
        
        # 确定风险等级
        risk_score = action_score.get("risk_score", 0.5)
        if risk_score >= 0.8:
            risk_level = RiskLevel.VERY_LOW
        elif risk_score >= 0.6:
            risk_level = RiskLevel.LOW
        elif risk_score >= 0.4:
            risk_level = RiskLevel.MEDIUM
        elif risk_score >= 0.2:
            risk_level = RiskLevel.HIGH
        else:
            risk_level = RiskLevel.VERY_HIGH
        
        # 生成推理说明
        reasoning = self._generate_reasoning(action, analysis, action_score)
        
        # 预期结果
        expected_outcome = self._predict_outcome(action, context, analysis)
        
        # 备选方案
        alternatives = self._generate_alternatives(action_scores, action, 3)
        
        return DecisionRecommendation(
            action=action,
            decision_type=self._determine_decision_type(action),
            confidence=confidence,
            confidence_level=confidence_level,
            risk_level=risk_level,
            expected_outcome=expected_outcome,
            reasoning=reasoning,
            alternatives=alternatives,
            metadata={
                "analysis": analysis,
                "scores": action_score,
                "timestamp": time.time()
            }
        )
    
    def _determine_decision_type(self, action: str) -> DecisionType:
        """确定决策类型"""
        if "lesson" in action:
            return DecisionType.LESSON_SELECTION
        else:
            return DecisionType.ACTION_SELECTION
    
    def _generate_reasoning(self, action: str, analysis: Dict[str, Any], action_score: Dict[str, float]) -> str:
        """生成推理说明"""
        reasons = []
        
        # 基于分数生成推理
        if action_score.get("stat_benefit", 0) > 0.7:
            if "lesson" in action:
                stat_analysis = analysis.get("stat_analysis", {})
                weakness = stat_analysis.get("weakness", "")
                if weakness in action:
                    reasons.append(f"针对最弱属性{weakness}进行训练")
                else:
                    reasons.append("有助于属性提升")
            elif action == "rest":
                reasons.append("体力不足，需要休息恢复")
            elif action == "outing":
                reasons.append("干劲不足，需要外出提升")
        
        if action_score.get("opportunity_score", 0) > 0.6:
            reasons.append("当前时机适合此行动")
        
        if action_score.get("risk_score", 0) > 0.7:
            reasons.append("风险较低，安全可行")
        
        if action_score.get("historical_score", 0) > 0.6:
            reasons.append("历史数据显示此行动效果良好")
        
        if not reasons:
            reasons.append("综合考虑当前情况，这是最佳选择")
        
        return "；".join(reasons)
    
    def _predict_outcome(self, action: str, context: DecisionContext, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """预测结果"""
        outcome = {
            "success_probability": 0.7,
            "expected_changes": {},
            "potential_risks": [],
            "estimated_duration": 1
        }
        
        stat_analysis = analysis.get("stat_analysis", {})
        
        if "lesson" in action:
            # 预测属性变化
            lesson_stat_map = {
                "vocal_lesson": "vocal",
                "dance_lesson": "dance",
                "visual_lesson": "visual", 
                "mental_lesson": "mental"
            }
            
            target_stat = lesson_stat_map.get(action)
            if target_stat:
                current_value = context.current_stats.get(target_stat, 0)
                expected_gain = 10 + (5 if target_stat == stat_analysis.get("weakness") else 0)
                outcome["expected_changes"][target_stat] = current_value + expected_gain
                outcome["expected_changes"]["stamina"] = max(0, context.current_stats.get("stamina", 100) - 15)
        
        elif action == "rest":
            outcome["expected_changes"]["stamina"] = min(100, context.current_stats.get("stamina", 100) + 30)
            outcome["success_probability"] = 0.95
        
        elif action == "outing":
            outcome["expected_changes"]["motivation"] = min(5, context.current_stats.get("motivation", 3) + 1)
            outcome["expected_changes"]["stamina"] = max(0, context.current_stats.get("stamina", 100) - 5)
            outcome["success_probability"] = 0.8
        
        return outcome
    
    def _generate_alternatives(self, action_scores: Dict[str, Dict[str, float]], 
                             selected_action: str, count: int) -> List[Dict[str, Any]]:
        """生成备选方案"""
        alternatives = []
        
        # 按分数排序，排除已选择的行动
        sorted_actions = sorted(
            [(action, scores) for action, scores in action_scores.items() if action != selected_action],
            key=lambda x: x[1]["total"],
            reverse=True
        )
        
        for i, (action, scores) in enumerate(sorted_actions[:count]):
            alternatives.append({
                "action": action,
                "score": scores["total"],
                "rank": i + 2,  # 排名（第一名是选中的行动）
                "reason": f"备选方案，综合评分: {scores['total']:.2f}"
            })
        
        return alternatives
    
    def _create_fallback_recommendation(self, context: DecisionContext) -> DecisionRecommendation:
        """创建后备推荐"""
        return DecisionRecommendation(
            action="rest",
            decision_type=DecisionType.ACTION_SELECTION,
            confidence=0.3,
            confidence_level=ConfidenceLevel.LOW,
            risk_level=RiskLevel.LOW,
            expected_outcome={"success_probability": 0.8},
            reasoning="系统异常，选择安全的休息行动",
            alternatives=[],
            metadata={"fallback": True}
        )
    
    def _record_decision(self, context: DecisionContext, recommendation: DecisionRecommendation):
        """记录决策"""
        decision_record = {
            "timestamp": time.time(),
            "scene": context.scene_type.value,
            "action": recommendation.action,
            "confidence": recommendation.confidence,
            "risk_level": recommendation.risk_level.value,
            "context_summary": {
                "stats": context.current_stats.copy(),
                "available_actions": context.available_actions.copy(),
                "constraints": context.constraints.copy()
            }
        }
        
        self._decision_history.append(decision_record)
        self._stats["total_decisions"] += 1
        
        # 保持历史记录在合理范围内
        if len(self._decision_history) > 1000:
            self._decision_history = self._decision_history[-500:]
    
    def record_outcome(self, decision_id: str, outcome: Dict[str, Any]):
        """记录决策结果"""
        try:
            outcome_record = {
                "decision_id": decision_id,
                "timestamp": time.time(),
                "success": outcome.get("success", False),
                "actual_changes": outcome.get("changes", {}),
                "satisfaction": outcome.get("satisfaction", 0.5)
            }
            
            self._outcome_history.append(outcome_record)
            
            if outcome.get("success", False):
                self._stats["successful_decisions"] += 1
            
            # 更新准确率
            if self._stats["total_decisions"] > 0:
                self._stats["accuracy_rate"] = self._stats["successful_decisions"] / self._stats["total_decisions"]
            
            # 学习和调整模型权重
            self._update_model_weights(outcome_record)
            
        except Exception as e:
            self.logger.error(f"记录决策结果失败: {e}")
    
    def _update_model_weights(self, outcome: Dict[str, Any]):
        """更新模型权重"""
        try:
            # 简化的权重更新逻辑
            if outcome.get("success", False):
                # 成功时略微增加所有权重的置信度
                adjustment = self.learning_rate * 0.1
                for key in self._model_weights:
                    self._model_weights[key] = min(1.0, self._model_weights[key] + adjustment)
            else:
                # 失败时略微降低权重
                adjustment = self.learning_rate * 0.05
                for key in self._model_weights:
                    self._model_weights[key] = max(0.1, self._model_weights[key] - adjustment)
            
            # 重新归一化权重
            total_weight = sum(self._model_weights.values())
            if total_weight > 0:
                for key in self._model_weights:
                    self._model_weights[key] /= total_weight
            
        except Exception as e:
            self.logger.error(f"更新模型权重失败: {e}")
    
    def get_decision_stats(self) -> Dict[str, Any]:
        """获取决策统计"""
        return {
            "total_decisions": self._stats["total_decisions"],
            "successful_decisions": self._stats["successful_decisions"],
            "accuracy_rate": self._stats["accuracy_rate"],
            "average_confidence": self._stats["average_confidence"],
            "model_weights": self._model_weights.copy(),
            "history_length": len(self._decision_history),
            "outcome_history_length": len(self._outcome_history)
        }
    
    def reset_learning_data(self):
        """重置学习数据"""
        self._decision_history.clear()
        self._outcome_history.clear()
        self._pattern_database.clear()
        self._stats = {
            "total_decisions": 0,
            "successful_decisions": 0,
            "accuracy_rate": 0.0,
            "average_confidence": 0.0
        }
        
        self.logger.info("决策学习数据已重置")
