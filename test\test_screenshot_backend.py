"""
截图后端功能测试脚本
用于验证截图收集器和API接口的基本功能
"""

import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.modules.screenshot_collector import (
    ScreenshotCollector, ScreenshotConfig, ScreenshotMode, ScreenshotFormat
)
from src.web.main import api_adapter


async def test_screenshot_collector():
    """测试截图收集器基本功能"""
    print("🧪 测试截图收集器...")
    
    try:
        # 创建截图收集器
        collector = ScreenshotCollector()
        print("✅ 截图收集器创建成功")
        
        # 测试配置创建
        config = ScreenshotConfig(
            mode=ScreenshotMode.WINDOW,
            format=ScreenshotFormat.PNG,
            quality=90,
            save_to_disk=True,
            filename_prefix="test"
        )
        print("✅ 截图配置创建成功")
        
        # 测试单次截图
        print("📸 执行测试截图...")
        result = await collector.capture_single_shot(config)
        
        if result.success:
            print(f"✅ 截图成功: {result.filename}")
            print(f"   文件大小: {result.file_size} 字节")
            print(f"   图像尺寸: {result.image_size}")
        else:
            print(f"❌ 截图失败: {result.error_message}")
        
        # 测试历史记录
        history = collector.get_capture_history(10)
        print(f"📋 历史记录数量: {len(history)}")
        
        # 测试统计信息
        stats = collector.get_stats()
        print(f"📊 统计信息: {stats}")
        
        return result.success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


async def test_api_adapter():
    """测试API适配器功能"""
    print("\n🧪 测试API适配器...")
    
    try:
        # 初始化API适配器
        api_adapter.initialize_core_modules()
        print("✅ API适配器初始化成功")
        
        # 测试截图配置转换
        config_dict = {
            "mode": "window",
            "format": "png",
            "quality": 90,
            "save_to_disk": True,
            "filename_prefix": "api_test"
        }
        
        # 测试截图执行
        print("📸 通过API执行截图...")
        result = await api_adapter.capture_screenshot(config_dict)
        
        if result.success:
            print(f"✅ API截图成功: {result.filename}")
        else:
            print(f"❌ API截图失败: {result.error_message}")
        
        # 测试历史记录获取
        history = await api_adapter.get_screenshot_history(5)
        print(f"📋 API历史记录数量: {len(history)}")
        
        # 测试统计信息获取
        stats = await api_adapter.get_screenshot_stats()
        print(f"📊 API统计信息: {stats}")
        
        return result.success
        
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False


async def test_preview_functionality():
    """测试预览功能"""
    print("\n🧪 测试预览功能...")
    
    try:
        # 测试单帧预览
        print("🖼️ 获取预览图像...")
        preview_data = await api_adapter.get_screenshot_preview()
        
        if preview_data:
            print(f"✅ 预览获取成功，数据大小: {len(preview_data)} 字节")
            return True
        else:
            print("❌ 预览获取失败")
            return False
            
    except Exception as e:
        print(f"❌ 预览测试失败: {e}")
        return False


async def cleanup_test_files():
    """清理测试文件"""
    print("\n🧹 清理测试文件...")
    
    try:
        screenshots_dir = Path("screenshots")
        if screenshots_dir.exists():
            test_files = list(screenshots_dir.glob("test_*.png")) + list(screenshots_dir.glob("api_test_*.png"))
            
            for file_path in test_files:
                try:
                    file_path.unlink()
                    print(f"🗑️ 已删除: {file_path.name}")
                except Exception as e:
                    print(f"⚠️ 删除失败 {file_path.name}: {e}")
            
            # 清理缩略图
            thumbnails_dir = screenshots_dir / "thumbnails"
            if thumbnails_dir.exists():
                thumb_files = list(thumbnails_dir.glob("test_*_thumb.png")) + list(thumbnails_dir.glob("api_test_*_thumb.png"))
                for file_path in thumb_files:
                    try:
                        file_path.unlink()
                        print(f"🗑️ 已删除缩略图: {file_path.name}")
                    except Exception as e:
                        print(f"⚠️ 删除缩略图失败 {file_path.name}: {e}")
        
        print("✅ 测试文件清理完成")
        
    except Exception as e:
        print(f"❌ 清理失败: {e}")


async def main():
    """主测试函数"""
    print("🚀 开始截图后端功能测试")
    print("=" * 50)
    
    # 测试结果
    results = []
    
    # 测试截图收集器
    collector_result = await test_screenshot_collector()
    results.append(("截图收集器", collector_result))
    
    # 测试API适配器
    api_result = await test_api_adapter()
    results.append(("API适配器", api_result))
    
    # 测试预览功能
    preview_result = await test_preview_functionality()
    results.append(("预览功能", preview_result))
    
    # 清理测试文件
    await cleanup_test_files()
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有测试通过！后端功能正常")
    else:
        print("⚠️ 部分测试失败，请检查错误信息")
    
    return all_passed


if __name__ == "__main__":
    # 运行测试
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
