# 数据收集截图工具前端UI实现方案

## 1. 前端架构设计

### 1.1 技术栈
- **前端框架**: Vue.js 3 (与现有项目保持一致)
- **UI组件库**: Element Plus (与现有项目保持一致)
- **构建工具**: 直接集成到现有FastAPI静态文件服务
- **实时通信**: WebSocket + 原生JavaScript
- **图像处理**: Canvas API + HTML5
- **状态管理**: Vue 3 Composition API

### 1.2 界面布局设计
```
┌─────────────────────────────────────────────────────────────┐
│                    截图工具 - 顶部导航栏                      │
├─────────────────┬─────────────────────┬─────────────────────┤
│                 │                     │                     │
│   任务配置面板   │    游戏窗口预览区    │    状态监控面板     │
│                 │                     │                     │
│  - 任务类型选择  │  - 实时预览画面     │  - 活动任务列表     │
│  - 参数配置     │  - 区域选择工具     │  - 任务状态显示     │
│  - 快速操作     │  - 截图控制按钮     │  - 性能监控       │
│                 │                     │                     │
├─────────────────┴─────────────────────┴─────────────────────┤
│                    截图历史记录面板                          │
│  - 历史记录列表  - 文件预览  - 批量操作  - 导出功能         │
└─────────────────────────────────────────────────────────────┘
```

### 1.3 组件架构
```
ScreenshotToolApp (主应用)
├── ToolHeader (顶部导航)
├── MainLayout (主布局)
│   ├── ControlPanel (左侧控制面板)
│   │   ├── TaskConfigForm (任务配置表单)
│   │   ├── QuickActions (快速操作)
│   │   └── RegionSelector (区域选择器)
│   ├── PreviewArea (中间预览区域)
│   │   ├── GameWindowPreview (游戏窗口预览)
│   │   ├── RegionOverlay (区域选择覆盖层)
│   │   └── PreviewControls (预览控制)
│   └── StatusPanel (右侧状态面板)
│       ├── ActiveTasksList (活动任务列表)
│       ├── TaskStatusDisplay (任务状态显示)
│       └── PerformanceMonitor (性能监控)
└── HistoryPanel (底部历史面板)
    ├── HistoryList (历史记录列表)
    ├── FilePreview (文件预览)
    └── BatchOperations (批量操作)
```

## 2. 核心组件详细设计

### 2.1 主应用组件 (ScreenshotToolApp)

#### 2.1.1 组件结构
```javascript
const ScreenshotToolApp = {
  template: `
    <div class="screenshot-tool-app">
      <tool-header 
        :connection-status="connectionStatus"
        :active-tasks-count="activeTasks.length"
        @refresh="refreshAll"
      />
      
      <main-layout
        :tasks="activeTasks"
        :preview-image="previewImage"
        :selected-region="selectedRegion"
        @task-created="handleTaskCreated"
        @task-action="handleTaskAction"
        @region-selected="handleRegionSelected"
      />
      
      <history-panel
        :history="screenshotHistory"
        @file-selected="handleFileSelected"
        @batch-operation="handleBatchOperation"
      />
    </div>
  `,
  
  setup() {
    // 响应式数据
    const connectionStatus = ref('disconnected')
    const activeTasks = ref([])
    const previewImage = ref(null)
    const selectedRegion = ref(null)
    const screenshotHistory = ref([])
    
    // WebSocket连接
    const websocket = ref(null)
    
    // 生命周期
    onMounted(() => {
      initializeApp()
    })
    
    onUnmounted(() => {
      if (websocket.value) {
        websocket.value.close()
      }
    })
    
    return {
      connectionStatus,
      activeTasks,
      previewImage,
      selectedRegion,
      screenshotHistory,
      // 方法
      refreshAll,
      handleTaskCreated,
      handleTaskAction,
      handleRegionSelected,
      handleFileSelected,
      handleBatchOperation
    }
  }
}
```

#### 2.1.2 WebSocket集成
```javascript
// WebSocket管理
const useWebSocket = () => {
  const ws = ref(null)
  const connectionStatus = ref('disconnected')
  
  const connect = () => {
    ws.value = new WebSocket('ws://localhost:8000/ws/screenshot')
    
    ws.value.onopen = () => {
      connectionStatus.value = 'connected'
      console.log('WebSocket连接已建立')
    }
    
    ws.value.onmessage = (event) => {
      const data = JSON.parse(event.data)
      handleWebSocketMessage(data)
    }
    
    ws.value.onclose = () => {
      connectionStatus.value = 'disconnected'
      console.log('WebSocket连接已断开')
      // 自动重连
      setTimeout(connect, 3000)
    }
    
    ws.value.onerror = (error) => {
      console.error('WebSocket错误:', error)
    }
  }
  
  const sendMessage = (type, payload) => {
    if (ws.value && ws.value.readyState === WebSocket.OPEN) {
      ws.value.send(JSON.stringify({ type, payload }))
    }
  }
  
  return { connect, sendMessage, connectionStatus }
}
```

### 2.2 任务配置组件 (TaskConfigForm)

#### 2.2.1 组件模板
```javascript
const TaskConfigForm = {
  template: `
    <el-card class="task-config-card">
      <template #header>
        <div class="card-header">
          <span>截图任务配置</span>
          <el-button size="small" @click="resetForm">重置</el-button>
        </div>
      </template>
      
      <el-form :model="taskConfig" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="任务类型" prop="type">
          <el-select v-model="taskConfig.type" @change="onTaskTypeChange">
            <el-option label="单次截图" value="single" />
            <el-option label="定时截图" value="interval" />
            <el-option label="批量截图" value="batch" />
            <el-option label="连续截图" value="continuous" />
          </el-select>
        </el-form-item>
        
        <el-form-item 
          label="截图间隔" 
          prop="interval"
          v-if="['interval', 'continuous'].includes(taskConfig.type)"
        >
          <el-input-number 
            v-model="taskConfig.interval" 
            :min="0.1" 
            :max="60" 
            :step="0.1"
            :precision="1"
          />
          <span class="unit">秒</span>
        </el-form-item>
        
        <el-form-item 
          label="持续时间" 
          prop="duration"
          v-if="taskConfig.type !== 'single'"
        >
          <el-input-number 
            v-model="taskConfig.duration" 
            :min="1" 
            :max="3600"
          />
          <span class="unit">秒</span>
        </el-form-item>
        
        <el-form-item 
          label="截图数量" 
          prop="count"
          v-if="taskConfig.type === 'batch'"
        >
          <el-input-number 
            v-model="taskConfig.count" 
            :min="1" 
            :max="1000"
          />
          <span class="unit">张</span>
        </el-form-item>
        
        <el-form-item label="截图区域" prop="region">
          <el-radio-group v-model="taskConfig.regionType">
            <el-radio label="fullscreen">全屏</el-radio>
            <el-radio label="window">游戏窗口</el-radio>
            <el-radio label="custom">自定义区域</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item 
          label="区域坐标" 
          v-if="taskConfig.regionType === 'custom'"
        >
          <el-row :gutter="10">
            <el-col :span="6">
              <el-input-number v-model="taskConfig.region.x" placeholder="X" />
            </el-col>
            <el-col :span="6">
              <el-input-number v-model="taskConfig.region.y" placeholder="Y" />
            </el-col>
            <el-col :span="6">
              <el-input-number v-model="taskConfig.region.width" placeholder="宽度" />
            </el-col>
            <el-col :span="6">
              <el-input-number v-model="taskConfig.region.height" placeholder="高度" />
            </el-col>
          </el-row>
        </el-form-item>
        
        <el-form-item label="保存设置">
          <el-input v-model="taskConfig.savePath" placeholder="保存路径">
            <template #append>
              <el-button @click="selectSavePath">选择</el-button>
            </template>
          </el-input>
        </el-form-item>
        
        <el-form-item label="文件格式">
          <el-select v-model="taskConfig.format">
            <el-option label="PNG" value="png" />
            <el-option label="JPEG" value="jpg" />
            <el-option label="BMP" value="bmp" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="图片质量" v-if="taskConfig.format === 'jpg'">
          <el-slider v-model="taskConfig.quality" :min="10" :max="100" />
        </el-form-item>
        
        <el-form-item>
          <el-button 
            type="primary" 
            @click="createTask"
            :loading="creating"
            :disabled="!isFormValid"
          >
            创建任务
          </el-button>
          <el-button @click="previewCapture">预览截图</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  `,
  
  setup(props, { emit }) {
    const formRef = ref(null)
    const creating = ref(false)
    
    // 表单数据
    const taskConfig = reactive({
      type: 'single',
      interval: 1.0,
      duration: 60,
      count: 10,
      regionType: 'window',
      region: {
        x: 0,
        y: 0,
        width: 800,
        height: 600
      },
      savePath: 'data/screenshots',
      format: 'png',
      quality: 95
    })
    
    // 表单验证规则
    const rules = {
      type: [{ required: true, message: '请选择任务类型' }],
      interval: [{ required: true, message: '请设置截图间隔' }],
      duration: [{ required: true, message: '请设置持续时间' }]
    }
    
    // 计算属性
    const isFormValid = computed(() => {
      // 表单验证逻辑
      return true
    })
    
    // 方法
    const createTask = async () => {
      try {
        creating.value = true
        await formRef.value.validate()
        
        const taskData = {
          ...taskConfig,
          region: taskConfig.regionType === 'custom' ? taskConfig.region : null
        }
        
        emit('task-created', taskData)
        ElMessage.success('任务创建成功')
      } catch (error) {
        ElMessage.error('任务创建失败: ' + error.message)
      } finally {
        creating.value = false
      }
    }
    
    const previewCapture = async () => {
      emit('preview-capture', taskConfig)
    }
    
    const resetForm = () => {
      formRef.value.resetFields()
    }
    
    const onTaskTypeChange = (type) => {
      // 根据任务类型调整默认参数
      if (type === 'single') {
        taskConfig.interval = 1.0
        taskConfig.duration = 0
      } else if (type === 'interval') {
        taskConfig.interval = 2.0
        taskConfig.duration = 60
      }
    }
    
    return {
      formRef,
      creating,
      taskConfig,
      rules,
      isFormValid,
      createTask,
      previewCapture,
      resetForm,
      onTaskTypeChange
    }
  }
}
```

### 2.3 游戏窗口预览组件 (GameWindowPreview)

#### 2.3.1 组件模板
```javascript
const GameWindowPreview = {
  template: `
    <el-card class="preview-card">
      <template #header>
        <div class="card-header">
          <span>游戏窗口预览</span>
          <div class="preview-controls">
            <el-button size="small" @click="refreshPreview">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button size="small" @click="toggleAutoRefresh">
              {{ autoRefresh ? '停止自动刷新' : '自动刷新' }}
            </el-button>
            <el-button size="small" @click="captureNow">
              <el-icon><Camera /></el-icon>
              立即截图
            </el-button>
          </div>
        </div>
      </template>
      
      <div class="preview-container" ref="containerRef">
        <canvas 
          ref="canvasRef"
          class="preview-canvas"
          @mousedown="startRegionSelect"
          @mousemove="updateRegionSelect"
          @mouseup="endRegionSelect"
          @mouseleave="cancelRegionSelect"
        />
        
        <!-- 区域选择覆盖层 -->
        <div 
          v-if="isSelecting || selectedRegion" 
          class="region-overlay"
          :style="regionOverlayStyle"
        >
          <div class="region-info">
            <span v-if="currentRegion">
              区域: ({{ currentRegion.x }}, {{ currentRegion.y }}) 
              尺寸: {{ currentRegion.width }} × {{ currentRegion.height }}
            </span>
          </div>
          
          <!-- 调整手柄 -->
          <div 
            v-if="selectedRegion && !isSelecting"
            class="resize-handles"
          >
            <div class="handle handle-nw" @mousedown="startResize('nw')"></div>
            <div class="handle handle-ne" @mousedown="startResize('ne')"></div>
            <div class="handle handle-sw" @mousedown="startResize('sw')"></div>
            <div class="handle handle-se" @mousedown="startResize('se')"></div>
          </div>
        </div>
        
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-overlay">
          <el-icon class="is-loading"><Loading /></el-icon>
          <span>加载预览图像...</span>
        </div>
        
        <!-- 错误状态 -->
        <div v-if="error" class="error-overlay">
          <el-icon><Warning /></el-icon>
          <span>{{ error }}</span>
          <el-button size="small" @click="refreshPreview">重试</el-button>
        </div>
      </div>
      
      <!-- 预览信息 -->
      <div class="preview-info">
        <el-row :gutter="10">
          <el-col :span="8">
            <el-statistic title="窗口尺寸" :value="windowSize" />
          </el-col>
          <el-col :span="8">
            <el-statistic title="更新频率" :value="updateRate" suffix="fps" />
          </el-col>
          <el-col :span="8">
            <el-statistic title="最后更新" :value="lastUpdate" />
          </el-col>
        </el-row>
      </div>
    </el-card>
  `,
  
  setup(props, { emit }) {
    const canvasRef = ref(null)
    const containerRef = ref(null)
    
    // 状态数据
    const loading = ref(false)
    const error = ref(null)
    const autoRefresh = ref(false)
    const previewImage = ref(null)
    
    // 区域选择相关
    const isSelecting = ref(false)
    const selectedRegion = ref(null)
    const currentRegion = ref(null)
    const selectionStart = ref(null)
    
    // 统计信息
    const windowSize = ref('0 × 0')
    const updateRate = ref(0)
    const lastUpdate = ref('从未')
    
    // 自动刷新定时器
    let refreshTimer = null
    let fpsCounter = 0
    let fpsTimer = null
    
    // 计算属性
    const regionOverlayStyle = computed(() => {
      if (!currentRegion.value) return {}
      
      const region = currentRegion.value
      return {
        left: region.x + 'px',
        top: region.y + 'px',
        width: region.width + 'px',
        height: region.height + 'px'
      }
    })
    
    // 方法
    const refreshPreview = async () => {
      try {
        loading.value = true
        error.value = null
        
        const response = await fetch('/api/v1/screenshot/preview')
        if (!response.ok) {
          throw new Error('获取预览失败')
        }
        
        const blob = await response.blob()
        const imageUrl = URL.createObjectURL(blob)
        
        await loadImageToCanvas(imageUrl)
        
        fpsCounter++
        lastUpdate.value = new Date().toLocaleTimeString()
        
      } catch (err) {
        error.value = err.message
      } finally {
        loading.value = false
      }
    }
    
    const loadImageToCanvas = (imageUrl) => {
      return new Promise((resolve, reject) => {
        const img = new Image()
        img.onload = () => {
          const canvas = canvasRef.value
          const ctx = canvas.getContext('2d')
          
          // 调整canvas尺寸
          canvas.width = img.width
          canvas.height = img.height
          
          // 绘制图像
          ctx.drawImage(img, 0, 0)
          
          windowSize.value = `${img.width} × ${img.height}`
          URL.revokeObjectURL(imageUrl)
          resolve()
        }
        img.onerror = reject
        img.src = imageUrl
      })
    }
    
    const toggleAutoRefresh = () => {
      autoRefresh.value = !autoRefresh.value
      
      if (autoRefresh.value) {
        refreshTimer = setInterval(refreshPreview, 2000)
        fpsTimer = setInterval(() => {
          updateRate.value = fpsCounter / 2
          fpsCounter = 0
        }, 2000)
      } else {
        if (refreshTimer) {
          clearInterval(refreshTimer)
          refreshTimer = null
        }
        if (fpsTimer) {
          clearInterval(fpsTimer)
          fpsTimer = null
        }
      }
    }
    
    const captureNow = async () => {
      try {
        const config = {
          region: selectedRegion.value,
          save: true,
          filename: `manual_${Date.now()}.png`
        }
        
        const response = await fetch('/api/v1/screenshot/capture', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(config)
        })
        
        if (response.ok) {
          ElMessage.success('截图已保存')
          emit('screenshot-captured')
        } else {
          throw new Error('截图失败')
        }
      } catch (error) {
        ElMessage.error('截图失败: ' + error.message)
      }
    }
    
    // 区域选择方法
    const startRegionSelect = (event) => {
      const rect = canvasRef.value.getBoundingClientRect()
      const x = event.clientX - rect.left
      const y = event.clientY - rect.top
      
      isSelecting.value = true
      selectionStart.value = { x, y }
      currentRegion.value = { x, y, width: 0, height: 0 }
    }
    
    const updateRegionSelect = (event) => {
      if (!isSelecting.value) return
      
      const rect = canvasRef.value.getBoundingClientRect()
      const x = event.clientX - rect.left
      const y = event.clientY - rect.top
      
      const startX = Math.min(selectionStart.value.x, x)
      const startY = Math.min(selectionStart.value.y, y)
      const width = Math.abs(x - selectionStart.value.x)
      const height = Math.abs(y - selectionStart.value.y)
      
      currentRegion.value = { x: startX, y: startY, width, height }
    }
    
    const endRegionSelect = () => {
      if (!isSelecting.value) return
      
      isSelecting.value = false
      
      if (currentRegion.value.width > 10 && currentRegion.value.height > 10) {
        selectedRegion.value = { ...currentRegion.value }
        emit('region-selected', selectedRegion.value)
      } else {
        currentRegion.value = null
      }
    }
    
    const cancelRegionSelect = () => {
      isSelecting.value = false
      currentRegion.value = selectedRegion.value
    }
    
    // 生命周期
    onMounted(() => {
      refreshPreview()
    })
    
    onUnmounted(() => {
      if (refreshTimer) clearInterval(refreshTimer)
      if (fpsTimer) clearInterval(fpsTimer)
    })
    
    return {
      canvasRef,
      containerRef,
      loading,
      error,
      autoRefresh,
      isSelecting,
      selectedRegion,
      currentRegion,
      windowSize,
      updateRate,
      lastUpdate,
      regionOverlayStyle,
      refreshPreview,
      toggleAutoRefresh,
      captureNow,
      startRegionSelect,
      updateRegionSelect,
      endRegionSelect,
      cancelRegionSelect
    }
  }
}
```

## 3. 样式设计

### 3.1 主题样式
```css
/* screenshot-tool.css */
.screenshot-tool-app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.tool-header {
  background: #fff;
  border-bottom: 1px solid #e6e6e6;
  padding: 0 20px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.main-layout {
  flex: 1;
  display: flex;
  gap: 20px;
  padding: 20px;
  overflow: hidden;
}

.control-panel {
  width: 300px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.preview-area {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.status-panel {
  width: 300px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.history-panel {
  height: 200px;
  background: #fff;
  border-top: 1px solid #e6e6e6;
  padding: 20px;
}
```

### 3.2 预览区域样式
```css
.preview-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.preview-container {
  position: relative;
  flex: 1;
  overflow: hidden;
  background: #000;
  border-radius: 4px;
}

.preview-canvas {
  width: 100%;
  height: 100%;
  object-fit: contain;
  cursor: crosshair;
}

.region-overlay {
  position: absolute;
  border: 2px dashed #409eff;
  background: rgba(64, 158, 255, 0.1);
  pointer-events: none;
}

.region-info {
  position: absolute;
  top: -30px;
  left: 0;
  background: #409eff;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
}

.resize-handles .handle {
  position: absolute;
  width: 8px;
  height: 8px;
  background: #409eff;
  border: 1px solid #fff;
  border-radius: 50%;
  pointer-events: all;
  cursor: pointer;
}

.handle-nw { top: -4px; left: -4px; cursor: nw-resize; }
.handle-ne { top: -4px; right: -4px; cursor: ne-resize; }
.handle-sw { bottom: -4px; left: -4px; cursor: sw-resize; }
.handle-se { bottom: -4px; right: -4px; cursor: se-resize; }
```

## 4. 集成方案

### 4.1 与现有系统集成
1. **路由集成**: 添加到现有FastAPI路由
2. **样式统一**: 使用相同的Element Plus主题
3. **状态共享**: 通过WebSocket与后端状态同步
4. **权限控制**: 集成现有的用户认证系统

### 4.2 部署配置
```python
# 在 src/web/main.py 中添加路由
@app.get("/screenshot-tool")
async def screenshot_tool():
    return FileResponse("src/web/static/screenshot_tool/index.html")

# 静态文件服务
app.mount("/static/screenshot_tool", 
          StaticFiles(directory="src/web/static/screenshot_tool"), 
          name="screenshot_tool_static")
```

---

**实施准备**: 前端UI设计方案已完成，可以开始具体的开发实施工作。
