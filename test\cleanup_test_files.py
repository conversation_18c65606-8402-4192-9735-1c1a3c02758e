"""
测试文件清理脚本
清理开发过程中产生的所有测试文件
"""

import os
import shutil
from pathlib import Path


def cleanup_test_files():
    """清理测试文件"""
    print("🧹 开始清理测试文件...")
    
    project_root = Path(__file__).parent.parent
    deleted_count = 0
    
    # 清理截图测试文件
    screenshots_dir = project_root / "screenshots"
    if screenshots_dir.exists():
        print(f"📁 检查截图目录: {screenshots_dir}")
        
        # 测试文件模式
        test_patterns = [
            "test_*.png", "test_*.jpg", "test_*.jpeg", "test_*.bmp",
            "integration_test_*.png", "integration_test_*.jpg", "integration_test_*.jpeg",
            "perf_test_*.png", "perf_test_*.jpg", "perf_test_*.jpeg",
            "api_test_*.png", "api_test_*.jpg", "api_test_*.jpeg",
            "js_test_*.png", "js_test_*.jpg", "js_test_*.jpeg"
        ]
        
        # 清理主目录中的测试文件
        for pattern in test_patterns:
            for file_path in screenshots_dir.glob(pattern):
                try:
                    file_path.unlink()
                    print(f"🗑️ 已删除: {file_path.name}")
                    deleted_count += 1
                except Exception as e:
                    print(f"⚠️ 删除失败 {file_path.name}: {e}")
        
        # 清理缩略图目录
        thumbnails_dir = screenshots_dir / "thumbnails"
        if thumbnails_dir.exists():
            print(f"📁 检查缩略图目录: {thumbnails_dir}")
            
            for pattern in test_patterns:
                # 转换为缩略图模式
                thumb_pattern = pattern.replace(".", "_thumb.")
                for file_path in thumbnails_dir.glob(thumb_pattern):
                    try:
                        file_path.unlink()
                        print(f"🗑️ 已删除缩略图: {file_path.name}")
                        deleted_count += 1
                    except Exception as e:
                        print(f"⚠️ 删除缩略图失败 {file_path.name}: {e}")
    
    # 清理临时文件
    temp_patterns = [
        "*.tmp", "*.temp", "*.log", "*.cache"
    ]
    
    for pattern in temp_patterns:
        for file_path in project_root.glob(pattern):
            try:
                file_path.unlink()
                print(f"🗑️ 已删除临时文件: {file_path.name}")
                deleted_count += 1
            except Exception as e:
                print(f"⚠️ 删除临时文件失败 {file_path.name}: {e}")
    
    # 清理 Python 缓存
    print("🐍 清理 Python 缓存...")
    for pycache_dir in project_root.rglob("__pycache__"):
        try:
            shutil.rmtree(pycache_dir)
            print(f"🗑️ 已删除缓存目录: {pycache_dir}")
            deleted_count += 1
        except Exception as e:
            print(f"⚠️ 删除缓存目录失败 {pycache_dir}: {e}")
    
    # 清理 .pyc 文件
    for pyc_file in project_root.rglob("*.pyc"):
        try:
            pyc_file.unlink()
            print(f"🗑️ 已删除: {pyc_file}")
            deleted_count += 1
        except Exception as e:
            print(f"⚠️ 删除失败 {pyc_file}: {e}")
    
    # 清理前端构建文件（如果存在）
    frontend_dir = project_root / "frontend"
    if frontend_dir.exists():
        build_dirs = ["dist", "build", "node_modules/.cache"]
        for build_dir in build_dirs:
            build_path = frontend_dir / build_dir
            if build_path.exists():
                try:
                    shutil.rmtree(build_path)
                    print(f"🗑️ 已删除构建目录: {build_path}")
                    deleted_count += 1
                except Exception as e:
                    print(f"⚠️ 删除构建目录失败 {build_path}: {e}")
    
    print(f"\n✅ 清理完成，共删除 {deleted_count} 个文件/目录")
    return deleted_count


def cleanup_empty_directories():
    """清理空目录"""
    print("\n📂 清理空目录...")
    
    project_root = Path(__file__).parent.parent
    removed_count = 0
    
    # 检查可能的空目录
    check_dirs = [
        project_root / "screenshots" / "thumbnails",
        project_root / "logs",
        project_root / "temp",
        project_root / "cache"
    ]
    
    for dir_path in check_dirs:
        if dir_path.exists() and dir_path.is_dir():
            try:
                # 检查目录是否为空
                if not any(dir_path.iterdir()):
                    dir_path.rmdir()
                    print(f"🗑️ 已删除空目录: {dir_path}")
                    removed_count += 1
            except Exception as e:
                print(f"⚠️ 删除空目录失败 {dir_path}: {e}")
    
    print(f"✅ 空目录清理完成，共删除 {removed_count} 个目录")
    return removed_count


def verify_important_files():
    """验证重要文件是否存在"""
    print("\n🔍 验证重要文件...")
    
    project_root = Path(__file__).parent.parent
    
    important_files = [
        "src/modules/screenshot_collector.py",
        "src/web/main.py",
        "src/web/models.py",
        "frontend/src/views/ScreenshotTool.vue",
        "frontend/src/components/screenshot/ControlPanel.vue",
        "frontend/src/components/screenshot/PreviewArea.vue",
        "frontend/src/components/screenshot/HistoryPanel.vue",
        "frontend/src/components/screenshot/SettingsPanel.vue",
        "frontend/src/composables/useScreenshotApi.js",
        "frontend/src/composables/useWebSocket.js",
        "docs/截图工具技术文档.md",
        "docs/截图工具用户使用指南.md",
        "docs/截图工具API参考.md"
    ]
    
    missing_files = []
    for file_path in important_files:
        full_path = project_root / file_path
        if not full_path.exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 发现缺失的重要文件:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    else:
        print("✅ 所有重要文件都存在")
        return True


def generate_cleanup_report():
    """生成清理报告"""
    print("\n📊 生成清理报告...")
    
    project_root = Path(__file__).parent.parent
    report_path = project_root / "test" / "cleanup_report.txt"
    
    try:
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# 测试文件清理报告\n\n")
            f.write(f"清理时间: {Path(__file__).stat().st_mtime}\n")
            f.write("清理范围:\n")
            f.write("- 截图测试文件 (test_*, integration_test_*, perf_test_*, api_test_*)\n")
            f.write("- 缩略图测试文件\n")
            f.write("- Python 缓存文件 (__pycache__, *.pyc)\n")
            f.write("- 临时文件 (*.tmp, *.temp, *.log, *.cache)\n")
            f.write("- 前端构建缓存\n")
            f.write("- 空目录\n\n")
            f.write("保留文件:\n")
            f.write("- 所有源代码文件\n")
            f.write("- 配置文件\n")
            f.write("- 文档文件\n")
            f.write("- 非测试截图文件\n")
        
        print(f"✅ 清理报告已生成: {report_path}")
        return True
        
    except Exception as e:
        print(f"❌ 生成清理报告失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 开始测试文件清理")
    print("=" * 50)
    
    try:
        # 执行清理
        file_count = cleanup_test_files()
        dir_count = cleanup_empty_directories()
        
        # 验证重要文件
        files_ok = verify_important_files()
        
        # 生成报告
        report_ok = generate_cleanup_report()
        
        # 总结
        print("\n" + "=" * 50)
        print("📊 清理总结:")
        print(f"   删除文件: {file_count} 个")
        print(f"   删除目录: {dir_count} 个")
        print(f"   重要文件: {'✅ 完整' if files_ok else '❌ 缺失'}")
        print(f"   清理报告: {'✅ 已生成' if report_ok else '❌ 失败'}")
        
        if files_ok and report_ok:
            print("\n🎉 测试文件清理完成！项目已准备交付")
            return True
        else:
            print("\n⚠️ 清理过程中发现问题，请检查")
            return False
            
    except Exception as e:
        print(f"\n❌ 清理过程发生错误: {e}")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
