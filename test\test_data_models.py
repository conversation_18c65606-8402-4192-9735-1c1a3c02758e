"""
数据模型验证测试脚本
验证所有Pydantic模型的正确性和API兼容性
"""

import sys
from pathlib import Path
from datetime import datetime
from typing import Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.web.models import *


def test_system_models():
    """测试系统相关模型"""
    print("🧪 测试系统相关模型...")
    
    try:
        # 测试 SystemStatus
        status = SystemStatus(
            is_running=True,
            current_task="test_task",
            task_queue_size=5,
            uptime=3600.0,
            last_update=datetime.now(),
            resource_usage={"cpu": 50.0, "memory": 1024},
            game_status={"scene": "main_menu"}
        )
        print(f"✅ SystemStatus: {status.is_running}")
        
        # 测试 ControlRequest
        control = ControlRequest(
            action="start",
            parameters={"mode": "auto"}
        )
        print(f"✅ ControlRequest: {control.action}")
        
        # 测试 TaskRequest
        task = TaskRequest(
            task_type="screenshot",
            parameters={"mode": "window"}
        )
        print(f"✅ TaskRequest: {task.task_type}")
        
        # 测试 ConfigUpdateRequest
        config_update = ConfigUpdateRequest(
            section="screenshot",
            key="quality",
            value=90
        )
        print(f"✅ ConfigUpdateRequest: {config_update.section}")
        
        return True
        
    except Exception as e:
        print(f"❌ 系统模型测试失败: {e}")
        return False


def test_screenshot_models():
    """测试截图相关模型"""
    print("\n🧪 测试截图相关模型...")
    
    try:
        # 测试枚举
        mode = ScreenshotModeEnum.WINDOW
        format_enum = ScreenshotFormatEnum.PNG
        print(f"✅ 枚举类型: {mode.value}, {format_enum.value}")
        
        # 测试 RegionConfig
        region = RegionConfig(x=100, y=200, width=800, height=600)
        print(f"✅ RegionConfig: {region.width}x{region.height}")
        
        # 测试 ScreenshotConfig
        config = ScreenshotConfig(
            mode=ScreenshotModeEnum.REGION,
            format=ScreenshotFormatEnum.PNG,
            quality=90,
            region=region,
            save_to_disk=True,
            filename_prefix="test"
        )
        print(f"✅ ScreenshotConfig: {config.mode.value}")
        
        # 测试 ScreenshotRequest
        request = ScreenshotRequest(config=config)
        print(f"✅ ScreenshotRequest: {request.config.mode.value}")
        
        # 测试 ImageSize
        image_size = ImageSize(width=1920, height=1080)
        print(f"✅ ImageSize: {image_size.width}x{image_size.height}")
        
        # 测试 ScreenshotResult
        result = ScreenshotResult(
            id="test-123",
            filename="test.png",
            filepath="/path/to/test.png",
            config=config,
            timestamp=datetime.now(),
            file_size=1024000,
            image_size=image_size,
            success=True
        )
        print(f"✅ ScreenshotResult: {result.success}")
        
        # 测试 ScreenshotRecord
        record = ScreenshotRecord(
            id="record-123",
            filename="record.png",
            filepath="/path/to/record.png",
            thumbnail_path="/path/to/thumb.png",
            timestamp=datetime.now(),
            file_size=512000,
            image_size=image_size,
            config=config.dict()
        )
        print(f"✅ ScreenshotRecord: {record.filename}")
        
        return True
        
    except Exception as e:
        print(f"❌ 截图模型测试失败: {e}")
        return False


def test_websocket_models():
    """测试WebSocket相关模型"""
    print("\n🧪 测试WebSocket相关模型...")
    
    try:
        # 测试 WebSocketMessage
        message = WebSocketMessage(
            type=WebSocketMessageType.PING,
            data={"test": "data"},
            timestamp=datetime.now()
        )
        print(f"✅ WebSocketMessage: {message.type.value}")
        
        # 测试 WebSocketResponse
        response = WebSocketResponse(
            type=WebSocketMessageType.PONG,
            data={"response": "data"},
            timestamp=datetime.now(),
            success=True
        )
        print(f"✅ WebSocketResponse: {response.success}")
        
        return True
        
    except Exception as e:
        print(f"❌ WebSocket模型测试失败: {e}")
        return False


def test_response_models():
    """测试响应模型"""
    print("\n🧪 测试响应模型...")
    
    try:
        # 测试 ApiResponse
        api_response = ApiResponse(
            success=True,
            message="操作成功",
            data={"result": "test"},
            timestamp=datetime.now()
        )
        print(f"✅ ApiResponse: {api_response.success}")
        
        # 测试 ErrorResponse
        error_response = ErrorResponse(
            success=False,
            error="测试错误",
            error_code="TEST_ERROR",
            timestamp=datetime.now()
        )
        print(f"✅ ErrorResponse: {error_response.error}")
        
        # 测试 HealthCheckResponse
        health = HealthCheckResponse(
            status="healthy",
            timestamp=datetime.now(),
            version="1.0.0",
            uptime=3600.0,
            components={"database": "ok", "cache": "ok"}
        )
        print(f"✅ HealthCheckResponse: {health.status}")
        
        return True
        
    except Exception as e:
        print(f"❌ 响应模型测试失败: {e}")
        return False


def test_config_models():
    """测试配置模型"""
    print("\n🧪 测试配置模型...")
    
    try:
        # 测试 GameWindowConfig
        window_config = GameWindowConfig(
            window_title="测试窗口",
            enable_background_mode=True,
            capture_method="auto"
        )
        print(f"✅ GameWindowConfig: {window_config.window_title}")
        
        # 测试 StorageConfig
        storage_config = StorageConfig(
            base_directory="test_screenshots",
            max_history_size=500,
            auto_cleanup=True,
            cleanup_days=15
        )
        print(f"✅ StorageConfig: {storage_config.base_directory}")
        
        # 测试 ScreenshotToolConfig
        tool_config = ScreenshotToolConfig(
            game_window=window_config,
            storage=storage_config,
            preview_fps=5,
            default_quality=85,
            default_format=ScreenshotFormatEnum.JPEG
        )
        print(f"✅ ScreenshotToolConfig: {tool_config.preview_fps}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置模型测试失败: {e}")
        return False


def test_model_serialization():
    """测试模型序列化"""
    print("\n🧪 测试模型序列化...")
    
    try:
        # 创建复杂模型
        config = ScreenshotConfig(
            mode=ScreenshotModeEnum.WINDOW,
            format=ScreenshotFormatEnum.PNG,
            quality=90
        )
        
        result = ScreenshotResult(
            id="serialize-test",
            filename="serialize.png",
            filepath="/test/serialize.png",
            config=config,
            timestamp=datetime.now(),
            file_size=1024,
            image_size=ImageSize(width=800, height=600),
            success=True
        )
        
        # 测试序列化为字典
        result_dict = result.dict()
        print(f"✅ 序列化为字典: {len(result_dict)} 个字段")
        
        # 测试序列化为JSON
        result_json = result.json()
        print(f"✅ 序列化为JSON: {len(result_json)} 字符")
        
        # 测试从字典反序列化
        new_result = ScreenshotResult(**result_dict)
        print(f"✅ 从字典反序列化: {new_result.id}")
        
        return True
        
    except Exception as e:
        print(f"❌ 序列化测试失败: {e}")
        return False


def test_model_validation():
    """测试模型验证"""
    print("\n🧪 测试模型验证...")
    
    try:
        # 测试有效数据
        valid_region = RegionConfig(x=0, y=0, width=100, height=100)
        print("✅ 有效区域配置通过验证")
        
        # 测试无效数据
        try:
            invalid_region = RegionConfig(x=-1, y=0, width=0, height=100)
            print("❌ 无效数据应该被拒绝")
            return False
        except Exception:
            print("✅ 无效数据被正确拒绝")
        
        # 测试质量范围验证
        try:
            invalid_config = ScreenshotConfig(quality=150)  # 超出范围
            print("❌ 超出范围的质量值应该被拒绝")
            return False
        except Exception:
            print("✅ 超出范围的质量值被正确拒绝")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始数据模型验证测试")
    print("=" * 50)

    # 测试结果
    results = []

    try:
        # 执行各项测试
        results.append(("系统模型", test_system_models()))
        results.append(("截图模型", test_screenshot_models()))
        results.append(("WebSocket模型", test_websocket_models()))
        results.append(("响应模型", test_response_models()))
        results.append(("配置模型", test_config_models()))
        results.append(("模型序列化", test_model_serialization()))
        results.append(("模型验证", test_model_validation()))
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        return False

    # 输出测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")

    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if not result:
            all_passed = False

    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有模型测试通过！数据模型定义正确")
    else:
        print("⚠️ 部分模型测试失败，请检查错误信息")

    return all_passed


if __name__ == "__main__":
    # 运行测试
    success = main()
    sys.exit(0 if success else 1)
