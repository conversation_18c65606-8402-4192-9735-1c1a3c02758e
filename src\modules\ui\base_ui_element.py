"""
UI元素基类实现
提供面向对象的UI元素抽象
"""

from abc import ABC, abstractmethod
from typing import Optional, Tuple, Any, Callable
import time

from ...utils.logger import get_logger
from ...core.data_structures import GameScene


class BaseUIElement(ABC):
    """UI元素抽象基类"""
    
    def __init__(self, 
                 template_name: str,
                 perception_module,
                 action_controller,
                 position: Optional[Tuple[int, int]] = None,
                 confidence_threshold: float = 0.8,
                 timeout: float = 5.0):
        """
        初始化UI元素
        
        Args:
            template_name: 模板名称
            perception_module: 感知模块实例
            action_controller: 行动控制器实例
            position: 预设位置坐标
            confidence_threshold: 识别置信度阈值
            timeout: 操作超时时间
        """
        self.template_name = template_name
        self.perception = perception_module
        self.action = action_controller
        self.position = position
        self.confidence_threshold = confidence_threshold
        self.timeout = timeout
        self.logger = get_logger(f"UIElement.{self.__class__.__name__}")
    
    def is_visible(self) -> bool:
        """
        检查UI元素是否可见
        
        Returns:
            是否可见
        """
        try:
            match_result = self.perception.find_ui_element(self.template_name)
            return match_result is not None and match_result.confidence >= self.confidence_threshold
        except Exception as e:
            self.logger.error(f"检查UI元素可见性失败: {e}")
            return False
    
    def get_position(self) -> Optional[Tuple[int, int]]:
        """
        获取UI元素当前位置
        
        Returns:
            元素中心坐标，如果未找到返回None
        """
        try:
            match_result = self.perception.find_ui_element(self.template_name)
            if match_result and match_result.confidence >= self.confidence_threshold:
                return (match_result.center_x, match_result.center_y)
            return self.position  # 返回预设位置作为备选
        except Exception as e:
            self.logger.error(f"获取UI元素位置失败: {e}")
            return self.position
    
    @abstractmethod
    def click(self) -> bool:
        """
        点击UI元素（抽象方法）
        
        Returns:
            是否点击成功
        """
        pass
    
    def wait_for_visible(self, timeout: Optional[float] = None) -> bool:
        """
        等待UI元素变为可见
        
        Args:
            timeout: 等待超时时间
            
        Returns:
            是否在超时前变为可见
        """
        if timeout is None:
            timeout = self.timeout
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            if self.is_visible():
                return True
            time.sleep(0.5)
        
        return False
    
    def wait_for_invisible(self, timeout: Optional[float] = None) -> bool:
        """
        等待UI元素变为不可见
        
        Args:
            timeout: 等待超时时间
            
        Returns:
            是否在超时前变为不可见
        """
        if timeout is None:
            timeout = self.timeout
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            if not self.is_visible():
                return True
            time.sleep(0.5)
        
        return False
    
    def get_confidence(self) -> float:
        """
        获取当前匹配置信度
        
        Returns:
            匹配置信度，如果未找到返回0.0
        """
        try:
            match_result = self.perception.find_ui_element(self.template_name)
            return match_result.confidence if match_result else 0.0
        except Exception as e:
            self.logger.error(f"获取置信度失败: {e}")
            return 0.0
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"{self.__class__.__name__}(template='{self.template_name}')"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return (f"{self.__class__.__name__}("
                f"template='{self.template_name}', "
                f"position={self.position}, "
                f"confidence_threshold={self.confidence_threshold})")


class Button(BaseUIElement):
    """通用按钮类"""
    
    def __init__(self, 
                 template_name: str,
                 perception_module,
                 action_controller,
                 click_behavior: Optional[Callable] = None,
                 **kwargs):
        """
        初始化按钮
        
        Args:
            template_name: 模板名称
            perception_module: 感知模块
            action_controller: 行动控制器
            click_behavior: 自定义点击行为
            **kwargs: 其他参数
        """
        super().__init__(template_name, perception_module, action_controller, **kwargs)
        self.click_behavior = click_behavior
        self.enabled = True
    
    def click(self) -> bool:
        """
        点击按钮
        
        Returns:
            是否点击成功
        """
        if not self.enabled:
            self.logger.warning(f"按钮 {self.template_name} 已禁用")
            return False
        
        try:
            self.logger.info(f"点击按钮: {self.template_name}")
            
            # 使用现有的行动控制器执行点击
            success = self.action.click_ui_element(
                self.template_name,
                confidence_threshold=self.confidence_threshold,
                timeout=self.timeout
            )
            
            if success and self.click_behavior:
                # 执行自定义点击行为
                return self.click_behavior()
            
            return success
            
        except Exception as e:
            self.logger.error(f"点击按钮失败: {self.template_name}, 错误: {e}")
            return False
    
    def set_enabled(self, enabled: bool):
        """设置按钮启用状态"""
        self.enabled = enabled
        self.logger.debug(f"按钮 {self.template_name} 状态设置为: {'启用' if enabled else '禁用'}")
    
    def is_enabled(self) -> bool:
        """检查按钮是否启用"""
        return self.enabled


class InputField(BaseUIElement):
    """输入框类"""
    
    def __init__(self, template_name: str, perception_module, action_controller, **kwargs):
        super().__init__(template_name, perception_module, action_controller, **kwargs)
    
    def click(self) -> bool:
        """点击输入框以获得焦点"""
        try:
            self.logger.info(f"点击输入框: {self.template_name}")
            return self.action.click_ui_element(
                self.template_name,
                confidence_threshold=self.confidence_threshold,
                timeout=self.timeout
            )
        except Exception as e:
            self.logger.error(f"点击输入框失败: {e}")
            return False
    
    def input_text(self, text: str) -> bool:
        """
        在输入框中输入文本
        
        Args:
            text: 要输入的文本
            
        Returns:
            是否输入成功
        """
        try:
            # 首先点击输入框获得焦点
            if not self.click():
                return False
            
            # 清空现有内容（Ctrl+A + Delete）
            if hasattr(self.action.input_simulator, 'key_press'):
                self.action.input_simulator.key_press(['ctrl', 'a'])
                time.sleep(0.1)
                self.action.input_simulator.key_press('delete')
                time.sleep(0.1)
            
            # 输入新文本
            if hasattr(self.action.input_simulator, 'type_text'):
                return self.action.input_simulator.type_text(text)
            
            return False
            
        except Exception as e:
            self.logger.error(f"输入文本失败: {e}")
            return False


class Label(BaseUIElement):
    """标签类（只读文本元素）"""
    
    def __init__(self, template_name: str, perception_module, action_controller, **kwargs):
        super().__init__(template_name, perception_module, action_controller, **kwargs)
    
    def click(self) -> bool:
        """标签通常不支持点击操作"""
        self.logger.warning(f"标签 {self.template_name} 不支持点击操作")
        return False
    
    def get_text(self) -> Optional[str]:
        """
        获取标签文本内容（需要OCR支持）
        
        Returns:
            标签文本内容，如果获取失败返回None
        """
        try:
            # 这里需要集成OCR功能来读取文本
            # 暂时返回None，实际实现需要根据具体的OCR模块来完成
            self.logger.warning("获取标签文本功能尚未实现")
            return None
        except Exception as e:
            self.logger.error(f"获取标签文本失败: {e}")
            return None
