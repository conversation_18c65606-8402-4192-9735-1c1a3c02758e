"""
标签类实现
使用传统Class处理复杂文本识别逻辑
"""

import time
import re
from typing import Optional, List, Dict, Any

from ....utils.logger import get_logger
from ..base.base_ui_element import BaseUIElement
from ..config.ui_element_config import LabelConfig


class Label(BaseUIElement):
    """标签类 - 使用传统Class处理复杂文本识别逻辑"""

    def __init__(self, config: LabelConfig, perception_module, action_controller):
        """
        初始化标签
        
        Args:
            config: 标签配置
            perception_module: 感知模块
            action_controller: 行动控制器
        """
        super().__init__(config, perception_module, action_controller)
        self.label_config = config  # 类型提示用的强类型引用
        
        # 标签特定状态
        self._last_recognized_text = ""
        self._text_history: List[Dict[str, Any]] = []
        self._ocr_cache: Dict[str, tuple] = {}

    def click(self) -> bool:
        """
        点击标签（某些标签可能是可点击的）
        
        Returns:
            是否点击成功
        """
        try:
            self.logger.info(f"点击标签: {self.config.template_name}")
            
            if not self._pre_action_validation():
                return False

            success = self.action.click_ui_element(
                self.config.template_name,
                confidence_threshold=self.config.confidence_threshold,
                timeout=self.config.timeout
            )
            
            if success:
                self._post_action_processing(True)
            
            return success

        except Exception as e:
            self.logger.error(f"点击标签失败: {e}")
            return False

    def read_text(self) -> Optional[str]:
        """
        读取标签文本
        
        Returns:
            识别到的文本，失败返回None
        """
        start_time = time.time()
        
        try:
            if not self.label_config.text_recognition_enabled:
                self.logger.warning("文本识别功能已禁用")
                return None

            # 使用缓存检查
            if self.config.cache_enabled:
                cached_text = self._get_cached_text()
                if cached_text is not None:
                    return cached_text

            # 执行OCR识别
            text = self._perform_ocr()
            
            # 文本后处理
            if text:
                processed_text = self._process_recognized_text(text)
                
                # 更新缓存和历史
                self._update_text_cache(processed_text)
                self._record_text_history(processed_text, start_time)
                
                return processed_text

            return None

        except Exception as e:
            self.logger.error(f"读取文本失败: {e}")
            self._record_performance('read_text', start_time, False, str(e))
            return None

    def _get_cached_text(self) -> Optional[str]:
        """获取缓存的文本"""
        current_time = time.time()
        cache_key = f"text_{self.config.template_name}"
        
        if cache_key in self._ocr_cache:
            cached_time, cached_text = self._ocr_cache[cache_key]
            if current_time - cached_time < self.config.cache_ttl:
                return cached_text
        
        return None

    def _perform_ocr(self) -> Optional[str]:
        """执行OCR识别"""
        try:
            # 获取标签位置和区域
            position = self.get_position()
            if not position:
                self.logger.error("无法获取标签位置")
                return None

            # 定义OCR区域
            if self.config.size:
                ocr_region = {
                    'x': position.x - self.config.size.width // 2,
                    'y': position.y - self.config.size.height // 2,
                    'width': self.config.size.width,
                    'height': self.config.size.height
                }
            else:
                # 使用默认区域大小
                ocr_region = {
                    'x': position.x - 50,
                    'y': position.y - 15,
                    'width': 100,
                    'height': 30
                }

            # 执行OCR
            if hasattr(self.perception, 'read_text_from_region'):
                text = self.perception.read_text_from_region(
                    ocr_region,
                    language=self.label_config.ocr_language,
                    confidence_threshold=self.label_config.ocr_confidence_threshold
                )
                return text
            else:
                self.logger.error("感知模块不支持OCR功能")
                return None

        except Exception as e:
            self.logger.error(f"OCR识别失败: {e}")
            return None

    def _process_recognized_text(self, raw_text: str) -> str:
        """处理识别到的文本"""
        if not self.label_config.text_cleanup_enabled:
            return raw_text

        try:
            # 基础清理
            processed_text = raw_text.strip()
            
            # 移除多余的空白字符
            processed_text = re.sub(r'\s+', ' ', processed_text)
            
            # 移除特殊字符（根据需要调整）
            processed_text = re.sub(r'[^\w\s\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF]', '', processed_text)
            
            self.logger.debug(f"文本处理: '{raw_text}' -> '{processed_text}'")
            return processed_text

        except Exception as e:
            self.logger.error(f"文本处理失败: {e}")
            return raw_text

    def _update_text_cache(self, text: str):
        """更新文本缓存"""
        current_time = time.time()
        cache_key = f"text_{self.config.template_name}"
        self._ocr_cache[cache_key] = (current_time, text)
        self._last_recognized_text = text

    def _record_text_history(self, text: str, start_time: float):
        """记录文本历史"""
        history_entry = {
            'text': text,
            'timestamp': time.time(),
            'recognition_time': time.time() - start_time
        }
        
        self._text_history.append(history_entry)
        
        # 保持历史记录在合理范围内
        if len(self._text_history) > 100:
            self._text_history = self._text_history[-100:]
        
        # 记录性能
        self._record_performance('read_text', start_time, True)

    def verify_text(self, expected_text: str, exact_match: bool = False) -> bool:
        """
        验证标签文本
        
        Args:
            expected_text: 期望的文本
            exact_match: 是否精确匹配
            
        Returns:
            是否匹配
        """
        try:
            current_text = self.read_text()
            if not current_text:
                return False

            if exact_match:
                result = current_text.strip() == expected_text.strip()
            else:
                result = expected_text.lower() in current_text.lower()

            self.logger.debug(f"文本验证: 期望 '{expected_text}', 实际 '{current_text}', 结果: {result}")
            return result

        except Exception as e:
            self.logger.error(f"文本验证失败: {e}")
            return False

    def verify_text_pattern(self, pattern: str) -> bool:
        """
        使用正则表达式验证文本
        
        Args:
            pattern: 正则表达式模式
            
        Returns:
            是否匹配模式
        """
        try:
            current_text = self.read_text()
            if not current_text:
                return False

            result = bool(re.search(pattern, current_text))
            self.logger.debug(f"模式验证: 模式 '{pattern}', 文本 '{current_text}', 结果: {result}")
            return result

        except Exception as e:
            self.logger.error(f"模式验证失败: {e}")
            return False

    def wait_for_text(self, expected_text: str, timeout: float = 10.0, 
                     exact_match: bool = False) -> bool:
        """
        等待特定文本出现
        
        Args:
            expected_text: 期望的文本
            timeout: 等待超时时间
            exact_match: 是否精确匹配
            
        Returns:
            是否在超时前出现了期望文本
        """
        start_time = time.time()
        self.logger.info(f"等待文本 '{expected_text}' 出现，超时时间: {timeout}秒")
        
        while time.time() - start_time < timeout:
            if self.verify_text(expected_text, exact_match):
                elapsed_time = time.time() - start_time
                self.logger.info(f"文本 '{expected_text}' 出现，等待时间: {elapsed_time:.2f}秒")
                return True
            
            time.sleep(0.2)  # 短暂等待后重试
        
        self.logger.warning(f"等待文本 '{expected_text}' 超时")
        return False

    def wait_for_text_change(self, timeout: float = 10.0) -> bool:
        """
        等待文本发生变化
        
        Args:
            timeout: 等待超时时间
            
        Returns:
            是否在超时前文本发生了变化
        """
        start_time = time.time()
        initial_text = self.read_text()
        
        self.logger.info(f"等待文本变化，初始文本: '{initial_text}'")
        
        while time.time() - start_time < timeout:
            current_text = self.read_text()
            if current_text != initial_text:
                elapsed_time = time.time() - start_time
                self.logger.info(f"文本已变化: '{initial_text}' -> '{current_text}', 等待时间: {elapsed_time:.2f}秒")
                return True
            
            time.sleep(0.2)
        
        self.logger.warning("等待文本变化超时")
        return False

    def get_last_text(self) -> str:
        """获取最后识别的文本"""
        return self._last_recognized_text

    def get_text_history(self) -> List[Dict[str, Any]]:
        """获取文本历史"""
        return self._text_history.copy()

    def clear_text_cache(self):
        """清理文本缓存"""
        self._ocr_cache.clear()

    def get_text_stats(self) -> Dict[str, Any]:
        """获取文本识别统计"""
        if not self._text_history:
            return {}

        recognition_times = [entry['recognition_time'] for entry in self._text_history]
        
        return {
            'total_recognitions': len(self._text_history),
            'average_recognition_time': sum(recognition_times) / len(recognition_times),
            'min_recognition_time': min(recognition_times),
            'max_recognition_time': max(recognition_times),
            'last_recognized_text': self._last_recognized_text,
            'cache_enabled': self.config.cache_enabled
        }


class EnhancedLabel(Label):
    """增强标签类 - 提供更多高级功能"""

    def __init__(self, config: LabelConfig, perception_module, action_controller):
        super().__init__(config, perception_module, action_controller)
        
        # 增强功能
        self._text_change_callbacks = []
        self._monitoring_enabled = False

    def add_text_change_callback(self, callback):
        """添加文本变化回调"""
        self._text_change_callbacks.append(callback)

    def start_text_monitoring(self, interval: float = 1.0):
        """开始文本监控"""
        import threading
        
        def monitor():
            last_text = self.read_text()
            while self._monitoring_enabled:
                current_text = self.read_text()
                if current_text != last_text:
                    for callback in self._text_change_callbacks:
                        try:
                            callback(last_text, current_text)
                        except Exception as e:
                            self.logger.error(f"文本变化回调失败: {e}")
                    last_text = current_text
                time.sleep(interval)
        
        self._monitoring_enabled = True
        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()

    def stop_text_monitoring(self):
        """停止文本监控"""
        self._monitoring_enabled = False

    def extract_numbers(self) -> List[float]:
        """从文本中提取数字"""
        text = self.read_text()
        if not text:
            return []
        
        # 提取数字（包括小数）
        numbers = re.findall(r'-?\d+\.?\d*', text)
        return [float(num) for num in numbers if num]

    def extract_text_by_pattern(self, pattern: str) -> List[str]:
        """使用正则表达式提取文本"""
        text = self.read_text()
        if not text:
            return []
        
        matches = re.findall(pattern, text)
        return matches
