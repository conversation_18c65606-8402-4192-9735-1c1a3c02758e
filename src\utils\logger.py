"""
Gakumasu-Bot 日志系统
提供统一的日志记录功能，支持彩色输出和文件记录
"""

import logging
import os
from datetime import datetime
from pathlib import Path
from typing import Optional

try:
    import colorlog
    HAS_COLORLOG = True
except ImportError:
    HAS_COLORLOG = False


class GakumasuBotLogger:
    """Gakumasu-Bot专用日志器"""
    
    def __init__(self, name: str = "GakumasuBot"):
        self.name = name
        self.logger = None
        self._setup_complete = False
    
    def setup(self, 
              log_level: str = "INFO",
              log_dir: str = "logs",
              enable_file_logging: bool = True,
              enable_console_logging: bool = True) -> logging.Logger:
        """
        设置日志系统
        
        Args:
            log_level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
            log_dir: 日志文件目录
            enable_file_logging: 是否启用文件日志
            enable_console_logging: 是否启用控制台日志
            
        Returns:
            配置好的Logger对象
        """
        if self._setup_complete:
            return self.logger
            
        # 创建logger
        self.logger = logging.getLogger(self.name)
        self.logger.setLevel(getattr(logging, log_level.upper()))
        
        # 清除现有的handlers
        self.logger.handlers.clear()
        
        # 创建日志目录
        if enable_file_logging:
            log_path = Path(log_dir)
            log_path.mkdir(exist_ok=True)
        
        # 设置日志格式
        detailed_formatter = logging.Formatter(
            fmt='%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        simple_formatter = logging.Formatter(
            fmt='%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%H:%M:%S'
        )
        
        # 控制台处理器
        if enable_console_logging:
            console_handler = logging.StreamHandler()
            
            if HAS_COLORLOG:
                # 使用彩色日志
                color_formatter = colorlog.ColoredFormatter(
                    fmt='%(log_color)s%(asctime)s - %(levelname)s - %(message)s%(reset)s',
                    datefmt='%H:%M:%S',
                    log_colors={
                        'DEBUG': 'cyan',
                        'INFO': 'green',
                        'WARNING': 'yellow',
                        'ERROR': 'red',
                        'CRITICAL': 'red,bg_white',
                    }
                )
                console_handler.setFormatter(color_formatter)
            else:
                console_handler.setFormatter(simple_formatter)
            
            self.logger.addHandler(console_handler)
        
        # 文件处理器
        if enable_file_logging:
            # 主日志文件
            main_log_file = log_path / f"gakumasu_bot_{datetime.now().strftime('%Y%m%d')}.log"
            file_handler = logging.FileHandler(main_log_file, encoding='utf-8')
            file_handler.setFormatter(detailed_formatter)
            self.logger.addHandler(file_handler)
            
            # 错误日志文件
            error_log_file = log_path / f"gakumasu_bot_error_{datetime.now().strftime('%Y%m%d')}.log"
            error_handler = logging.FileHandler(error_log_file, encoding='utf-8')
            error_handler.setLevel(logging.ERROR)
            error_handler.setFormatter(detailed_formatter)
            self.logger.addHandler(error_handler)
        
        self._setup_complete = True
        self.logger.info(f"日志系统初始化完成 - 级别: {log_level}")
        
        return self.logger
    
    def get_logger(self) -> Optional[logging.Logger]:
        """获取logger实例"""
        return self.logger


# 全局日志器实例
_global_logger = GakumasuBotLogger()


def setup_logger(log_level: str = "INFO",
                log_dir: str = "logs",
                enable_file_logging: bool = True,
                enable_console_logging: bool = True) -> logging.Logger:
    """
    设置全局日志系统
    
    Args:
        log_level: 日志级别
        log_dir: 日志目录
        enable_file_logging: 是否启用文件日志
        enable_console_logging: 是否启用控制台日志
        
    Returns:
        配置好的Logger对象
    """
    return _global_logger.setup(
        log_level=log_level,
        log_dir=log_dir,
        enable_file_logging=enable_file_logging,
        enable_console_logging=enable_console_logging
    )


def get_logger(name: Optional[str] = None) -> logging.Logger:
    """
    获取日志器实例
    
    Args:
        name: 日志器名称，如果为None则返回全局日志器
        
    Returns:
        Logger对象
    """
    if name is None:
        logger = _global_logger.get_logger()
        if logger is None:
            # 如果全局日志器未初始化，使用默认设置初始化
            logger = setup_logger()
        return logger
    else:
        # 返回指定名称的子日志器
        return logging.getLogger(f"GakumasuBot.{name}")


# 便捷的日志记录函数
def log_info(message: str, logger_name: Optional[str] = None):
    """记录INFO级别日志"""
    get_logger(logger_name).info(message)


def log_warning(message: str, logger_name: Optional[str] = None):
    """记录WARNING级别日志"""
    get_logger(logger_name).warning(message)


def log_error(message: str, logger_name: Optional[str] = None, exc_info: bool = False):
    """记录ERROR级别日志"""
    get_logger(logger_name).error(message, exc_info=exc_info)


def log_debug(message: str, logger_name: Optional[str] = None):
    """记录DEBUG级别日志"""
    get_logger(logger_name).debug(message)


def log_critical(message: str, logger_name: Optional[str] = None):
    """记录CRITICAL级别日志"""
    get_logger(logger_name).critical(message)
