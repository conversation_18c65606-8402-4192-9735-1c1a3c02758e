"""
决策模块集成测试
测试决策模块与感知、行动模块的协作功能
"""

import pytest
import time
from unittest.mock import Mock, patch, MagicMock
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.modules.decision import DecisionModule, EventType
from src.modules.perception import PerceptionModule, MatchResult
from src.modules.action import ActionController
from src.core.data_structures import (
    GameState, Card, Action, ActionType, GameScene, CardType, CardRarity, UserStrategy
)


class TestDecisionPerceptionIntegration:
    """测试决策-感知模块集成"""
    
    def setup_method(self):
        """测试前准备"""
        self.user_strategy = UserStrategy()
        self.decision_module = DecisionModule(self.user_strategy)
        
        # 创建模拟的感知模块
        self.mock_perception = Mock(spec=PerceptionModule)
        
        # 模拟屏幕捕获器
        mock_screen_capture = Mock()
        mock_screen_capture.get_window_rect.return_value = {"left": 0, "top": 0, "width": 1920, "height": 1080}
        mock_screen_capture.is_game_window_active.return_value = True
        self.mock_perception.screen_capture = mock_screen_capture
    
    def test_decision_with_perception_input(self):
        """测试基于感知输入的决策"""
        # 模拟感知模块提供的游戏状态
        game_state = GameState(
            current_scene=GameScene.PRODUCE_MAIN,
            stamina=80,
            vigor=70,
            current_week=10
        )
        
        # 模拟感知到的UI元素
        game_state.ui_elements = {
            "vocal_lesson": MatchResult(200, 300, 100, 50, 0.9, "vocal_lesson"),
            "dance_lesson": MatchResult(300, 300, 100, 50, 0.85, "dance_lesson"),
            "rest_button": MatchResult(500, 300, 80, 40, 0.8, "rest_button")
        }
        
        # 执行决策
        chosen_action = self.decision_module.make_decision(game_state)
        
        # 验证决策结果
        assert chosen_action is not None
        assert chosen_action.action_type == ActionType.CLICK
        assert isinstance(chosen_action.target, tuple)
        assert len(chosen_action.target) == 2
    
    def test_decision_with_scene_recognition(self):
        """测试基于场景识别的决策"""
        # 测试不同场景的决策
        scenes_and_expected_actions = [
            (GameScene.PRODUCE_MAIN, "课程"),
            (GameScene.PRODUCE_BATTLE, "卡牌"),
            (GameScene.MAIN_MENU, "开始")
        ]
        
        for scene, expected_keyword in scenes_and_expected_actions:
            game_state = GameState(current_scene=scene, stamina=80, vigor=70)
            
            chosen_action = self.decision_module.make_decision(game_state)
            
            assert chosen_action is not None
            # 验证行动描述包含预期关键词（部分匹配）或者是合理的默认行动
            valid_keywords = [expected_keyword, "等待", "休息", "声乐", "舞蹈", "视觉", "开始", "育成", "打工", "任务", "跳过"]
            assert any(keyword in chosen_action.description for keyword in valid_keywords)
    
    def test_decision_with_card_evaluation(self):
        """测试基于卡牌评估的决策"""
        # 创建包含卡牌的游戏状态
        cards = [
            Card("card_001", "高级声乐卡", CardType.VOCAL, CardRarity.SR, cost=3),
            Card("card_002", "普通舞蹈卡", CardType.DANCE, CardRarity.R, cost=2),
            Card("card_003", "特殊视觉卡", CardType.VISUAL, CardRarity.SSR, cost=4)
        ]
        
        game_state = GameState(
            current_scene=GameScene.PRODUCE_BATTLE,
            hand=cards,
            stamina=90,
            vigor=80
        )
        
        # 执行决策
        chosen_action = self.decision_module.make_decision(game_state)
        
        # 验证决策选择了合适的卡牌
        assert chosen_action is not None
        assert "卡牌" in chosen_action.description or "跳过" in chosen_action.description
    
    def test_decision_with_low_resources(self):
        """测试低资源状态下的决策"""
        game_state = GameState(
            current_scene=GameScene.PRODUCE_MAIN,
            stamina=15,  # 低体力
            vigor=20,    # 低元气
            current_week=30
        )
        
        chosen_action = self.decision_module.make_decision(game_state)
        
        # 低资源状态应该倾向于选择休息
        assert chosen_action is not None
        # 可能选择休息或其他保守行动
        assert chosen_action.action_type in [ActionType.CLICK, ActionType.KEYPRESS]


class TestDecisionActionIntegration:
    """测试决策-行动模块集成"""
    
    def setup_method(self):
        """测试前准备"""
        self.user_strategy = UserStrategy()
        self.decision_module = DecisionModule(self.user_strategy)
        
        # 创建模拟的行动控制器
        self.mock_action_controller = Mock(spec=ActionController)
        self.mock_action_controller.execute_action.return_value = True
        self.mock_action_controller.click_ui_element.return_value = True
    
    def test_decision_to_action_execution(self):
        """测试决策到行动执行的流程"""
        game_state = GameState(
            current_scene=GameScene.PRODUCE_MAIN,
            stamina=80,
            vigor=70
        )
        
        # 获取决策
        chosen_action = self.decision_module.make_decision(game_state)
        
        # 模拟执行行动
        execution_result = self.mock_action_controller.execute_action(chosen_action)
        
        # 验证执行结果
        assert execution_result is True
        self.mock_action_controller.execute_action.assert_called_once_with(chosen_action)
    
    def test_decision_action_sequence(self):
        """测试决策生成的行动序列"""
        game_state = GameState(
            current_scene=GameScene.PRODUCE_MAIN,
            stamina=80,
            vigor=70
        )
        
        # 生成多个决策
        actions = []
        for _ in range(3):
            action = self.decision_module.make_decision(game_state)
            actions.append(action)
            
            # 模拟状态变化
            game_state.stamina = max(0, game_state.stamina - 10)
        
        # 验证行动序列
        assert len(actions) == 3
        assert all(isinstance(action, Action) for action in actions)
        
        # 验证行动序列的评估
        sequence_eval = self.decision_module.evaluate_action_sequence(actions, game_state)
        assert sequence_eval.total_score >= 0.0
        assert sequence_eval.confidence > 0.0
    
    def test_decision_with_action_verification(self):
        """测试带验证的决策-行动流程"""
        game_state = GameState(
            current_scene=GameScene.PRODUCE_BATTLE,
            stamina=80,
            vigor=70
        )
        
        # 获取决策
        chosen_action = self.decision_module.make_decision(game_state)
        
        # 添加验证函数
        def verify_action_success():
            return True
        
        chosen_action.verify_func = verify_action_success
        
        # 模拟执行带验证的行动
        self.mock_action_controller.execute_action.return_value = True
        execution_result = self.mock_action_controller.execute_action(chosen_action)
        
        assert execution_result is True


class TestDecisionFullIntegration:
    """测试决策模块完整集成"""
    
    def setup_method(self):
        """测试前准备"""
        self.user_strategy = UserStrategy()
        self.decision_module = DecisionModule(self.user_strategy)
        
        # 创建模拟的感知和行动模块
        self.mock_perception = Mock(spec=PerceptionModule)
        self.mock_action_controller = Mock(spec=ActionController)
        
        # 配置模拟对象
        mock_screen_capture = Mock()
        mock_screen_capture.get_window_rect.return_value = {"left": 0, "top": 0, "width": 1920, "height": 1080}
        mock_screen_capture.is_game_window_active.return_value = True
        self.mock_perception.screen_capture = mock_screen_capture
        
        self.mock_action_controller.execute_action.return_value = True
    
    def test_complete_decision_workflow(self):
        """测试完整的决策工作流程"""
        # 1. 感知阶段：获取游戏状态
        game_state = GameState(
            current_scene=GameScene.PRODUCE_MAIN,
            stamina=80,
            vigor=70,
            current_week=15
        )
        
        # 模拟感知模块识别UI元素
        self.mock_perception.find_ui_element.return_value = MatchResult(
            200, 300, 100, 50, 0.9, "vocal_lesson"
        )
        
        # 2. 决策阶段：基于感知结果做决策
        chosen_action = self.decision_module.make_decision(game_state)
        
        # 3. 行动阶段：执行决策
        execution_result = self.mock_action_controller.execute_action(chosen_action)
        
        # 4. 验证完整流程
        assert chosen_action is not None
        assert execution_result is True
        
        # 验证决策历史记录
        recent_decisions = self.decision_module.get_recent_decisions(1)
        assert len(recent_decisions) == 1
        assert recent_decisions[0]["action_description"] == chosen_action.description
    
    def test_adaptive_decision_making(self):
        """测试自适应决策制定"""
        # 模拟游戏状态变化和相应的决策调整
        scenarios = [
            # 场景1：正常状态
            {
                "state": GameState(GameScene.PRODUCE_MAIN, stamina=80, vigor=70),
                "expected_behavior": "active"
            },
            # 场景2：低体力状态
            {
                "state": GameState(GameScene.PRODUCE_MAIN, stamina=20, vigor=70),
                "expected_behavior": "conservative"
            },
            # 场景3：战斗场景
            {
                "state": GameState(GameScene.PRODUCE_BATTLE, stamina=80, vigor=70),
                "expected_behavior": "strategic"
            }
        ]
        
        decisions = []
        for scenario in scenarios:
            decision = self.decision_module.make_decision(scenario["state"])
            decisions.append({
                "action": decision,
                "scenario": scenario["expected_behavior"]
            })
        
        # 验证决策的多样性和适应性
        assert len(decisions) == 3
        assert all(d["action"] is not None for d in decisions)
        
        # 验证不同场景产生不同类型的决策
        action_types = [d["action"].action_type for d in decisions]
        assert len(set(action_types)) >= 1  # 至少有一种行动类型
    
    def test_decision_performance_metrics(self):
        """测试决策性能指标"""
        game_state = GameState(
            current_scene=GameScene.PRODUCE_MAIN,
            stamina=80,
            vigor=70
        )
        
        # 执行多次决策以测试性能
        start_time = time.time()
        decisions_count = 10
        
        for _ in range(decisions_count):
            self.decision_module.make_decision(game_state)
        
        total_time = time.time() - start_time
        
        # 验证性能指标
        stats = self.decision_module.get_decision_statistics()
        assert stats["total_decisions"] == decisions_count
        assert stats["average_decision_time"] > 0.0
        assert stats["average_decision_time"] < 1.0  # 决策应该在1秒内完成
        
        # 验证总时间合理
        assert total_time < decisions_count * 1.0  # 平均每个决策不超过1秒
    
    def test_decision_error_handling(self):
        """测试决策错误处理"""
        # 测试无效游戏状态
        invalid_state = GameState(
            current_scene=GameScene.UNKNOWN,
            stamina=-10  # 无效值
        )
        
        # 决策模块应该能处理无效状态并返回安全的默认行动
        chosen_action = self.decision_module.make_decision(invalid_state)
        
        assert chosen_action is not None
        assert chosen_action.action_type in [ActionType.CLICK, ActionType.KEYPRESS]
        
        # 验证错误被记录在决策历史中
        recent_decisions = self.decision_module.get_recent_decisions(1)
        assert len(recent_decisions) == 1
        assert recent_decisions[0]["decision_method"] == "fallback"
    
    def test_decision_configuration_impact(self):
        """测试决策配置的影响"""
        game_state = GameState(
            current_scene=GameScene.PRODUCE_MAIN,
            stamina=80,
            vigor=70
        )
        
        # 测试不同配置下的决策
        configurations = [
            {"decision_mode": "heuristic_only", "enable_mcts": False},
            {"decision_mode": "hybrid", "enable_mcts": True, "mcts_time_limit": 1.0}
        ]
        
        results = []
        for config in configurations:
            # 重置统计信息
            self.decision_module.reset_statistics()
            
            # 应用配置
            self.decision_module.configure(**config)
            
            # 执行决策
            decision = self.decision_module.make_decision(game_state)
            stats = self.decision_module.get_decision_statistics()
            
            results.append({
                "config": config,
                "decision": decision,
                "stats": stats
            })
        
        # 验证不同配置产生的结果
        assert len(results) == 2
        assert all(r["decision"] is not None for r in results)
        
        # 验证配置确实影响了决策过程
        # 注意：决策模块保持最后一次配置的状态
        final_config = configurations[-1]
        assert self.decision_module.decision_mode == final_config["decision_mode"]
        assert self.decision_module.enable_mcts == final_config["enable_mcts"]


class TestDecisionModulePerformance:
    """测试决策模块性能"""
    
    def setup_method(self):
        """测试前准备"""
        self.decision_module = DecisionModule()
    
    def test_decision_speed_benchmark(self):
        """测试决策速度基准"""
        game_state = GameState(
            current_scene=GameScene.PRODUCE_MAIN,
            stamina=80,
            vigor=70
        )
        
        # 基准测试：100次决策
        iterations = 100
        start_time = time.time()
        
        for _ in range(iterations):
            self.decision_module.make_decision(game_state)
        
        total_time = time.time() - start_time
        avg_time = total_time / iterations
        
        # 性能要求：平均决策时间应小于500ms（考虑到MCTS算法的复杂性）
        assert avg_time < 0.5, f"决策速度过慢: {avg_time:.3f}s per decision"
        
        # 验证统计信息
        stats = self.decision_module.get_decision_statistics()
        assert stats["total_decisions"] == iterations
        assert abs(stats["average_decision_time"] - avg_time) < 0.01
    
    def test_memory_usage_stability(self):
        """测试内存使用稳定性"""
        game_state = GameState(
            current_scene=GameScene.PRODUCE_MAIN,
            stamina=80,
            vigor=70
        )
        
        # 执行大量决策以测试内存稳定性
        for i in range(200):
            self.decision_module.make_decision(game_state)
            
            # 每50次检查一次历史记录大小
            if i % 50 == 0:
                history_size = len(self.decision_module.decision_history)
                # 历史记录应该被限制在合理范围内
                assert history_size <= self.decision_module.max_history_size
        
        # 最终验证
        final_history_size = len(self.decision_module.decision_history)
        assert final_history_size <= self.decision_module.max_history_size


if __name__ == "__main__":
    pytest.main([__file__])
