"""
测试截图枚举类型修复
验证枚举类型转换是否正确工作
"""

import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.web.main import api_adapter
from src.web.models import ScreenshotConfig as WebScreenshotConfig, ScreenshotModeEnum, ScreenshotFormatEnum


async def test_enum_conversion():
    """测试枚举类型转换"""
    print("🧪 测试枚举类型转换...")
    
    try:
        # 初始化API适配器
        api_adapter.initialize_core_modules()
        print("✅ API适配器初始化成功")
        
        # 测试不同的配置格式
        test_configs = [
            # 1. 使用字符串值（前端通常发送的格式）
            {
                "mode": "window",
                "format": "png",
                "quality": 90,
                "save_to_disk": True,
                "filename_prefix": "enum_test_str"
            },
            
            # 2. 使用枚举对象（可能的情况）
            {
                "mode": ScreenshotModeEnum.WINDOW,
                "format": ScreenshotFormatEnum.PNG,
                "quality": 90,
                "save_to_disk": True,
                "filename_prefix": "enum_test_enum"
            },
            
            # 3. 混合格式
            {
                "mode": ScreenshotModeEnum.FULLSCREEN,
                "format": "jpeg",
                "quality": 80,
                "save_to_disk": True,
                "filename_prefix": "enum_test_mixed"
            }
        ]
        
        results = []
        for i, config in enumerate(test_configs, 1):
            print(f"\n📸 测试配置 {i}: {config}")
            
            try:
                # 测试配置转换
                result = await api_adapter.capture_screenshot(config)
                
                if result.success:
                    print(f"✅ 配置 {i} 截图成功: {result.filename}")
                    results.append(True)
                else:
                    print(f"❌ 配置 {i} 截图失败: {result.error_message}")
                    results.append(False)
                    
            except Exception as e:
                print(f"❌ 配置 {i} 异常: {e}")
                results.append(False)
        
        # 统计结果
        success_count = sum(results)
        total_count = len(results)
        
        print(f"\n📊 测试结果: {success_count}/{total_count} 成功")
        
        if success_count == total_count:
            print("🎉 所有枚举类型转换测试通过！")
            return True
        else:
            print("⚠️ 部分测试失败，需要进一步检查")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


async def test_pydantic_model_conversion():
    """测试Pydantic模型转换"""
    print("\n🧪 测试Pydantic模型转换...")
    
    try:
        # 创建Pydantic模型
        web_config = WebScreenshotConfig(
            mode=ScreenshotModeEnum.WINDOW,
            format=ScreenshotFormatEnum.PNG,
            quality=90,
            save_to_disk=True,
            filename_prefix="pydantic_test"
        )
        
        print(f"📋 Pydantic模型: {web_config}")
        
        # 转换为字典
        config_dict = web_config.dict()
        print(f"📋 转换后字典: {config_dict}")
        
        # 检查枚举类型
        mode_type = type(config_dict.get('mode'))
        format_type = type(config_dict.get('format'))
        
        print(f"📋 模式类型: {mode_type}")
        print(f"📋 格式类型: {format_type}")
        
        # 测试截图
        result = await api_adapter.capture_screenshot(config_dict)
        
        if result.success:
            print(f"✅ Pydantic模型转换测试成功: {result.filename}")
            return True
        else:
            print(f"❌ Pydantic模型转换测试失败: {result.error_message}")
            return False
            
    except Exception as e:
        print(f"❌ Pydantic模型转换测试异常: {e}")
        return False


async def test_api_endpoint_simulation():
    """模拟API端点调用"""
    print("\n🧪 模拟API端点调用...")
    
    try:
        from src.web.models import ScreenshotRequest
        
        # 创建请求模型
        web_config = WebScreenshotConfig(
            mode=ScreenshotModeEnum.WINDOW,
            format=ScreenshotFormatEnum.PNG,
            quality=90
        )
        
        request = ScreenshotRequest(config=web_config)
        
        # 模拟API端点的处理逻辑
        config_dict = request.config.dict(by_alias=True, exclude_unset=False)
        
        # 确保枚举值被正确转换为字符串
        if 'mode' in config_dict and hasattr(config_dict['mode'], 'value'):
            config_dict['mode'] = config_dict['mode'].value
        if 'format' in config_dict and hasattr(config_dict['format'], 'value'):
            config_dict['format'] = config_dict['format'].value
        
        print(f"📋 API处理后的配置: {config_dict}")
        
        # 执行截图
        result = await api_adapter.capture_screenshot(config_dict)
        
        if result.success:
            print(f"✅ API端点模拟测试成功: {result.filename}")
            return True
        else:
            print(f"❌ API端点模拟测试失败: {result.error_message}")
            return False
            
    except Exception as e:
        print(f"❌ API端点模拟测试异常: {e}")
        return False


async def cleanup_test_files():
    """清理测试文件"""
    print("\n🧹 清理测试文件...")
    
    try:
        screenshots_dir = Path("screenshots")
        if screenshots_dir.exists():
            test_patterns = [
                "enum_test_*.png", "enum_test_*.jpg", "enum_test_*.jpeg",
                "pydantic_test_*.png", "pydantic_test_*.jpg"
            ]
            
            deleted_count = 0
            for pattern in test_patterns:
                for file_path in screenshots_dir.glob(pattern):
                    try:
                        file_path.unlink()
                        print(f"🗑️ 已删除: {file_path.name}")
                        deleted_count += 1
                    except Exception as e:
                        print(f"⚠️ 删除失败 {file_path.name}: {e}")
            
            # 清理缩略图
            thumbnails_dir = screenshots_dir / "thumbnails"
            if thumbnails_dir.exists():
                for pattern in test_patterns:
                    thumb_pattern = pattern.replace(".", "_thumb.")
                    for file_path in thumbnails_dir.glob(thumb_pattern):
                        try:
                            file_path.unlink()
                            print(f"🗑️ 已删除缩略图: {file_path.name}")
                            deleted_count += 1
                        except Exception as e:
                            print(f"⚠️ 删除缩略图失败 {file_path.name}: {e}")
            
            print(f"✅ 清理完成，共删除 {deleted_count} 个文件")
        
        return True
        
    except Exception as e:
        print(f"❌ 清理失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始截图枚举类型修复测试")
    print("=" * 60)
    
    # 执行测试
    tests = [
        ("枚举类型转换", test_enum_conversion),
        ("Pydantic模型转换", test_pydantic_model_conversion),
        ("API端点模拟", test_api_endpoint_simulation)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 清理测试文件
    await cleanup_test_files()
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 枚举类型修复测试结果:")
    
    passed_count = 0
    total_count = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed_count += 1
    
    print(f"\n总体结果: {passed_count}/{total_count} 通过 ({passed_count/total_count*100:.1f}%)")
    
    if passed_count == total_count:
        print("🎉 枚举类型修复成功！截图功能已恢复正常")
        return True
    else:
        print("⚠️ 部分测试失败，可能需要进一步调试")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
