"""
工作流管理器
提供复杂流程编排、状态机和流程控制功能
"""

import time
import json
from typing import List, Dict, Any, Optional, Callable
from enum import Enum
from dataclasses import dataclass, field
from collections import defaultdict

from ....utils.logger import get_logger
from ..utils.performance_monitor import measure_block
from .automation_engine import AutomationEngine, WorkflowStep, ExecutionResult, StepType


class WorkflowStatus(Enum):
    """工作流状态枚举"""
    DRAFT = "draft"               # 草稿
    ACTIVE = "active"             # 激活
    RUNNING = "running"           # 运行中
    PAUSED = "paused"             # 暂停
    COMPLETED = "completed"       # 完成
    FAILED = "failed"             # 失败
    CANCELLED = "cancelled"       # 取消


class WorkflowPriority(Enum):
    """工作流优先级枚举"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4
    CRITICAL = 5


class FlowControlType(Enum):
    """流程控制类型枚举"""
    SEQUENTIAL = "sequential"     # 顺序执行
    PARALLEL = "parallel"         # 并行执行
    CONDITIONAL = "conditional"   # 条件执行
    LOOP = "loop"                # 循环执行
    SWITCH = "switch"            # 分支执行


@dataclass
class Workflow:
    """工作流定义"""
    id: str
    name: str
    description: str
    steps: List[WorkflowStep]
    status: WorkflowStatus = WorkflowStatus.DRAFT
    priority: WorkflowPriority = WorkflowPriority.NORMAL
    flow_control: FlowControlType = FlowControlType.SEQUENTIAL
    created_time: float = field(default_factory=time.time)
    updated_time: float = field(default_factory=time.time)
    version: int = 1
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # 执行相关
    execution_context: Dict[str, Any] = field(default_factory=dict)
    execution_history: List[ExecutionResult] = field(default_factory=list)
    current_step_index: int = 0
    retry_count: int = 0
    max_retries: int = 3
    
    # 调度相关
    schedule_config: Optional[Dict[str, Any]] = None
    dependencies: List[str] = field(default_factory=list)
    timeout: Optional[float] = None


class WorkflowManager:
    """工作流管理器"""
    
    def __init__(self, automation_engine: AutomationEngine):
        """
        初始化工作流管理器
        
        Args:
            automation_engine: 自动化引擎实例
        """
        self.automation_engine = automation_engine
        self.logger = get_logger("WorkflowManager")
        
        # 工作流存储
        self._workflows: Dict[str, Workflow] = {}
        self._workflow_templates: Dict[str, Dict[str, Any]] = {}
        
        # 执行状态
        self._running_workflows: Dict[str, Dict[str, Any]] = {}
        self._workflow_queue: List[str] = []
        self._paused_workflows: Dict[str, Dict[str, Any]] = {}
        
        # 依赖关系
        self._dependency_graph: Dict[str, List[str]] = defaultdict(list)
        self._reverse_dependencies: Dict[str, List[str]] = defaultdict(list)
        
        # 统计数据
        self._stats = {
            "total_workflows": 0,
            "active_workflows": 0,
            "completed_workflows": 0,
            "failed_workflows": 0,
            "average_execution_time": 0.0
        }
        
        self.logger.info("工作流管理器初始化完成")
    
    def create_workflow(self, workflow_id: str, name: str, description: str,
                       steps: List[WorkflowStep], **kwargs) -> Workflow:
        """
        创建工作流
        
        Args:
            workflow_id: 工作流ID
            name: 工作流名称
            description: 工作流描述
            steps: 工作流步骤
            **kwargs: 其他参数
            
        Returns:
            创建的工作流
        """
        try:
            if workflow_id in self._workflows:
                raise ValueError(f"工作流ID已存在: {workflow_id}")
            
            workflow = Workflow(
                id=workflow_id,
                name=name,
                description=description,
                steps=steps,
                **kwargs
            )
            
            self._workflows[workflow_id] = workflow
            self._stats["total_workflows"] += 1
            
            # 构建依赖关系
            if workflow.dependencies:
                self._build_dependencies(workflow_id, workflow.dependencies)
            
            self.logger.info(f"创建工作流: {workflow_id}")
            return workflow
            
        except Exception as e:
            self.logger.error(f"创建工作流失败: {e}")
            raise
    
    def start_workflow(self, workflow_id: str, context: Dict[str, Any] = None) -> bool:
        """
        启动工作流
        
        Args:
            workflow_id: 工作流ID
            context: 执行上下文
            
        Returns:
            是否启动成功
        """
        try:
            if workflow_id not in self._workflows:
                self.logger.error(f"工作流不存在: {workflow_id}")
                return False
            
            workflow = self._workflows[workflow_id]
            
            # 检查依赖关系
            if not self._check_dependencies(workflow_id):
                self.logger.warning(f"工作流{workflow_id}依赖未满足，添加到队列")
                if workflow_id not in self._workflow_queue:
                    self._workflow_queue.append(workflow_id)
                return True
            
            # 检查状态
            if workflow.status not in [WorkflowStatus.DRAFT, WorkflowStatus.ACTIVE]:
                self.logger.error(f"工作流{workflow_id}状态不允许启动: {workflow.status}")
                return False
            
            # 设置执行上下文
            if context:
                workflow.execution_context.update(context)
            
            # 更新状态
            workflow.status = WorkflowStatus.RUNNING
            workflow.current_step_index = 0
            workflow.execution_history.clear()
            
            # 启动执行
            execution_id = self.automation_engine.execute_workflow(
                workflow.steps,
                workflow_id=f"wf_{workflow_id}",
                priority=workflow.priority.value,
                context=workflow.execution_context
            )
            
            # 记录运行状态
            self._running_workflows[workflow_id] = {
                "execution_id": execution_id,
                "start_time": time.time(),
                "workflow": workflow
            }
            
            self._stats["active_workflows"] += 1
            
            self.logger.info(f"启动工作流: {workflow_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"启动工作流失败: {e}")
            return False
    
    def pause_workflow(self, workflow_id: str) -> bool:
        """暂停工作流"""
        try:
            if workflow_id not in self._running_workflows:
                return False
            
            workflow_info = self._running_workflows.pop(workflow_id)
            workflow = workflow_info["workflow"]
            
            # 取消自动化引擎中的执行
            self.automation_engine.cancel_workflow(workflow_info["execution_id"])
            
            # 更新状态
            workflow.status = WorkflowStatus.PAUSED
            
            # 保存暂停状态
            self._paused_workflows[workflow_id] = workflow_info
            
            self._stats["active_workflows"] -= 1
            
            self.logger.info(f"暂停工作流: {workflow_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"暂停工作流失败: {e}")
            return False
    
    def resume_workflow(self, workflow_id: str) -> bool:
        """恢复工作流"""
        try:
            if workflow_id not in self._paused_workflows:
                return False
            
            workflow_info = self._paused_workflows.pop(workflow_id)
            workflow = workflow_info["workflow"]
            
            # 从当前步骤继续执行
            remaining_steps = workflow.steps[workflow.current_step_index:]
            
            if remaining_steps:
                execution_id = self.automation_engine.execute_workflow(
                    remaining_steps,
                    workflow_id=f"wf_{workflow_id}_resume",
                    priority=workflow.priority.value,
                    context=workflow.execution_context
                )
                
                workflow_info["execution_id"] = execution_id
                workflow_info["resume_time"] = time.time()
                
                self._running_workflows[workflow_id] = workflow_info
                workflow.status = WorkflowStatus.RUNNING
                
                self._stats["active_workflows"] += 1
                
                self.logger.info(f"恢复工作流: {workflow_id}")
                return True
            else:
                # 已经完成
                workflow.status = WorkflowStatus.COMPLETED
                self._complete_workflow(workflow_id, workflow)
                return True
                
        except Exception as e:
            self.logger.error(f"恢复工作流失败: {e}")
            return False
    
    def cancel_workflow(self, workflow_id: str) -> bool:
        """取消工作流"""
        try:
            workflow = self._workflows.get(workflow_id)
            if not workflow:
                return False
            
            # 从运行中移除
            if workflow_id in self._running_workflows:
                workflow_info = self._running_workflows.pop(workflow_id)
                self.automation_engine.cancel_workflow(workflow_info["execution_id"])
                self._stats["active_workflows"] -= 1
            
            # 从暂停中移除
            if workflow_id in self._paused_workflows:
                self._paused_workflows.pop(workflow_id)
            
            # 从队列中移除
            if workflow_id in self._workflow_queue:
                self._workflow_queue.remove(workflow_id)
            
            # 更新状态
            workflow.status = WorkflowStatus.CANCELLED
            
            self.logger.info(f"取消工作流: {workflow_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"取消工作流失败: {e}")
            return False
    
    def get_workflow(self, workflow_id: str) -> Optional[Workflow]:
        """获取工作流"""
        return self._workflows.get(workflow_id)
    
    def list_workflows(self, status: Optional[WorkflowStatus] = None,
                      tags: Optional[List[str]] = None) -> List[Workflow]:
        """
        列出工作流
        
        Args:
            status: 过滤状态
            tags: 过滤标签
            
        Returns:
            工作流列表
        """
        workflows = list(self._workflows.values())
        
        if status:
            workflows = [w for w in workflows if w.status == status]
        
        if tags:
            workflows = [w for w in workflows if any(tag in w.tags for tag in tags)]
        
        return workflows
    
    def delete_workflow(self, workflow_id: str) -> bool:
        """删除工作流"""
        try:
            if workflow_id not in self._workflows:
                return False
            
            # 先取消工作流
            self.cancel_workflow(workflow_id)
            
            # 删除工作流
            del self._workflows[workflow_id]
            
            # 清理依赖关系
            self._cleanup_dependencies(workflow_id)
            
            self._stats["total_workflows"] -= 1
            
            self.logger.info(f"删除工作流: {workflow_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"删除工作流失败: {e}")
            return False
    
    def create_workflow_template(self, template_id: str, template_data: Dict[str, Any]):
        """创建工作流模板"""
        self._workflow_templates[template_id] = template_data
        self.logger.info(f"创建工作流模板: {template_id}")
    
    def create_workflow_from_template(self, template_id: str, workflow_id: str,
                                    parameters: Dict[str, Any] = None) -> Optional[Workflow]:
        """从模板创建工作流"""
        try:
            if template_id not in self._workflow_templates:
                self.logger.error(f"工作流模板不存在: {template_id}")
                return None
            
            template = self._workflow_templates[template_id]
            
            # 应用参数
            if parameters:
                template = self._apply_template_parameters(template, parameters)
            
            # 创建步骤
            steps = []
            for step_data in template.get("steps", []):
                step = WorkflowStep(
                    id=step_data["id"],
                    name=step_data["name"],
                    step_type=StepType(step_data["step_type"]),
                    action=step_data["action"],
                    parameters=step_data.get("parameters", {}),
                    conditions=step_data.get("conditions", []),
                    description=step_data.get("description", "")
                )
                steps.append(step)
            
            # 创建工作流
            return self.create_workflow(
                workflow_id=workflow_id,
                name=template.get("name", "从模板创建"),
                description=template.get("description", ""),
                steps=steps,
                tags=template.get("tags", [])
            )
            
        except Exception as e:
            self.logger.error(f"从模板创建工作流失败: {e}")
            return None
    
    def _build_dependencies(self, workflow_id: str, dependencies: List[str]):
        """构建依赖关系"""
        for dep in dependencies:
            self._dependency_graph[dep].append(workflow_id)
            self._reverse_dependencies[workflow_id].append(dep)
    
    def _cleanup_dependencies(self, workflow_id: str):
        """清理依赖关系"""
        # 清理正向依赖
        for dep_list in self._dependency_graph.values():
            if workflow_id in dep_list:
                dep_list.remove(workflow_id)
        
        # 清理反向依赖
        if workflow_id in self._reverse_dependencies:
            del self._reverse_dependencies[workflow_id]
        
        if workflow_id in self._dependency_graph:
            del self._dependency_graph[workflow_id]
    
    def _check_dependencies(self, workflow_id: str) -> bool:
        """检查依赖关系是否满足"""
        dependencies = self._reverse_dependencies.get(workflow_id, [])
        
        for dep_id in dependencies:
            dep_workflow = self._workflows.get(dep_id)
            if not dep_workflow or dep_workflow.status != WorkflowStatus.COMPLETED:
                return False
        
        return True
    
    def _complete_workflow(self, workflow_id: str, workflow: Workflow):
        """完成工作流"""
        workflow.status = WorkflowStatus.COMPLETED
        self._stats["completed_workflows"] += 1
        
        # 检查依赖此工作流的其他工作流
        dependent_workflows = self._dependency_graph.get(workflow_id, [])
        for dep_workflow_id in dependent_workflows:
            if dep_workflow_id in self._workflow_queue:
                # 尝试启动依赖的工作流
                if self._check_dependencies(dep_workflow_id):
                    self._workflow_queue.remove(dep_workflow_id)
                    self.start_workflow(dep_workflow_id)
    
    def _apply_template_parameters(self, template: Dict[str, Any], 
                                 parameters: Dict[str, Any]) -> Dict[str, Any]:
        """应用模板参数"""
        # 简化实现：直接替换字符串中的占位符
        template_str = json.dumps(template)
        
        for key, value in parameters.items():
            placeholder = f"${{{key}}}"
            template_str = template_str.replace(placeholder, str(value))
        
        return json.loads(template_str)
    
    def get_workflow_status(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """获取工作流状态"""
        workflow = self._workflows.get(workflow_id)
        if not workflow:
            return None
        
        status_info = {
            "id": workflow.id,
            "name": workflow.name,
            "status": workflow.status.value,
            "priority": workflow.priority.value,
            "current_step": workflow.current_step_index,
            "total_steps": len(workflow.steps),
            "retry_count": workflow.retry_count,
            "created_time": workflow.created_time,
            "updated_time": workflow.updated_time
        }
        
        # 添加运行时信息
        if workflow_id in self._running_workflows:
            run_info = self._running_workflows[workflow_id]
            status_info["start_time"] = run_info["start_time"]
            status_info["duration"] = time.time() - run_info["start_time"]
            
            # 获取自动化引擎的状态
            engine_status = self.automation_engine.get_workflow_status(run_info["execution_id"])
            if engine_status:
                status_info.update(engine_status)
        
        return status_info
    
    def get_manager_stats(self) -> Dict[str, Any]:
        """获取管理器统计"""
        return {
            "total_workflows": self._stats["total_workflows"],
            "active_workflows": self._stats["active_workflows"],
            "completed_workflows": self._stats["completed_workflows"],
            "failed_workflows": self._stats["failed_workflows"],
            "queued_workflows": len(self._workflow_queue),
            "paused_workflows": len(self._paused_workflows),
            "workflow_templates": len(self._workflow_templates),
            "dependency_relationships": len(self._dependency_graph)
        }
