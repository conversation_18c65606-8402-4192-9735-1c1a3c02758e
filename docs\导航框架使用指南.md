# 游戏导航框架使用指南

## 概述

游戏导航框架是 Gakumasu-Bot 中负责跨模块协调的高层次业务流程组件。它实现了"从主菜单导航到开始育成"的完整流程，遵循设计文档中的分层架构原则。

## 架构设计

### 分层架构
```
调度层 (GameNavigationTasks)
    ↓ 协调调用
执行层 (Perception + Decision + Action)
    ↓ 使用
数据层 (GameState, UserStrategy等)
```

### 模块职责
- **感知模块**: 识别当前场景和UI状态
- **决策模块**: 根据用户策略决定导航路径
- **行动模块**: 执行具体的点击和导航操作
- **导航框架**: 协调三个模块，实现完整的业务流程

## 核心功能

### 1. 导航流程步骤

导航框架将复杂的导航过程分解为四个标准步骤：

1. **场景识别** (`SCENE_RECOGNITION`)
   - 使用感知模块识别当前所在的游戏场景
   - 验证场景识别结果的有效性

2. **路径规划** (`PATH_PLANNING`)
   - 根据当前场景和目标场景确定导航路径
   - 可选择使用决策模块优化路径

3. **操作执行** (`ACTION_EXECUTION`)
   - 使用行动模块执行规划好的导航操作序列
   - 支持多步骤操作和场景切换等待

4. **结果验证** (`RESULT_VERIFICATION`)
   - 确认是否成功到达目标界面
   - 验证关键UI元素的存在

### 2. 配置管理

导航框架通过配置文件管理所有UI元素和参数：

```yaml
# config/settings.yaml
navigation:
  ui_elements:
    main_menu:
      produce_button:
        x: 500
        y: 400
        template: "assets/templates/produce_button.png"
    produce_setup:
      start_produce_button:
        x: 960
        y: 800
        template: "assets/templates/start_produce.png"
  
  timeouts:
    scene_recognition: 10.0
    action_execution: 5.0
    scene_transition: 15.0
  
  retry_settings:
    max_retries: 3
    retry_delay: 2.0
```

## 使用方法

### 1. 基本使用

```python
from src.modules.scheduler.game_tasks import GameNavigationTasks

# 创建导航任务实例（依赖注入）
navigation_tasks = GameNavigationTasks(
    perception_module=perception_module,
    decision_module=decision_module,
    action_controller=action_controller
)

# 执行导航
user_strategy = {"team_composition": {"produce_idol": "花海咲季"}}
success = navigation_tasks.navigate_to_produce_start(user_strategy)

if success:
    print("导航成功完成")
else:
    print("导航失败")
```

### 2. 在育成任务中使用

```python
class ProduceTask(Task):
    def _navigate_to_produce_setup(self):
        """导航到育成准备界面"""
        navigation_tasks = GameNavigationTasks(
            perception_module=self.perception,
            decision_module=self.decision,
            action_controller=self.action
        )
        
        success = navigation_tasks.navigate_to_produce_start(self.user_strategy)
        if not success:
            raise GakumasuBotException("导航到育成准备界面失败")
```

### 3. 自定义配置

```python
from src.utils.config_loader import ConfigLoader

# 使用自定义配置加载器
config_loader = ConfigLoader(config_dir="custom_config")
navigation_tasks = GameNavigationTasks(
    perception_module=perception_module,
    decision_module=decision_module,
    action_controller=action_controller,
    config_loader=config_loader
)
```

## 错误处理

### 1. 异常类型

- **场景识别失败**: 当前场景无法识别或为未知场景
- **路径规划失败**: 无法确定从当前场景到目标场景的路径
- **操作执行失败**: UI元素点击失败或场景切换超时
- **结果验证失败**: 未能到达预期的目标场景

### 2. 恢复机制

导航框架提供自动恢复功能：

```python
def _attempt_recovery(self):
    """尝试从导航失败中恢复"""
    # 按ESC键尝试返回
    # 等待界面稳定
    # 记录恢复操作结果
```

### 3. 重试策略

支持配置化的重试机制：

```yaml
retry_settings:
  max_retries: 3      # 最大重试次数
  retry_delay: 2.0    # 重试间隔（秒）
```

## 扩展指南

### 1. 添加新的导航路径

在 `_determine_navigation_path` 方法中添加新的场景组合：

```python
elif current_scene == GameScene.NEW_SCENE and target_scene == GameScene.PRODUCE_SETUP:
    navigation_path = [
        {
            'action_type': 'click',
            'target_element': 'new_button',
            'description': '点击新按钮',
            'expected_scene': GameScene.PRODUCE_SETUP,
            'timeout': 15.0
        }
    ]
```

### 2. 添加新的UI元素

在配置文件中添加新元素：

```yaml
ui_elements:
  new_scene:
    new_button:
      x: 100
      y: 200
      template: "assets/templates/new_button.png"
```

### 3. 自定义验证逻辑

重写验证方法：

```python
def _verify_custom_result(self) -> bool:
    """自定义结果验证"""
    # 实现特定的验证逻辑
    return True
```

## 性能优化

### 1. 配置缓存

导航配置会被缓存，避免重复加载：

```python
self._navigation_config = None  # 配置缓存
```

### 2. 模板匹配优化

支持模板匹配和固定坐标的混合使用：

```python
# 优先使用模板匹配
if template_path and self.perception:
    match_result = self.perception.find_template(template_path)
    if match_result and match_result.get('confidence', 0) > 0.8:
        return match_result.get('center')

# 回退到固定坐标
return (x, y)
```

### 3. 统计信息

框架提供详细的性能统计：

```python
def _log_navigation_statistics(self):
    """记录导航统计信息"""
    total_duration = sum(self.step_durations.values())
    self.logger.info(f"总耗时: {total_duration:.1f}秒")
    
    for step, duration in self.step_durations.items():
        self.logger.info(f"{step.value}: {duration:.1f}秒")
```

## 测试

### 1. 单元测试

```bash
python -m pytest test/test_navigation_framework.py -v
```

### 2. 集成测试

```python
# 测试完整导航流程
def test_full_navigation_integration():
    # 设置真实的模块实例
    # 执行导航流程
    # 验证结果
```

## 最佳实践

1. **依赖注入**: 始终通过构造函数注入模块依赖
2. **配置驱动**: 将所有UI坐标和参数放在配置文件中
3. **错误处理**: 为每个步骤提供适当的错误处理和恢复机制
4. **日志记录**: 记录详细的执行过程和统计信息
5. **模块化设计**: 保持各步骤的独立性和可测试性

## 故障排除

### 常见问题

1. **场景识别失败**
   - 检查感知模块是否正常工作
   - 验证游戏窗口是否可见
   - 确认模板图片是否正确

2. **UI元素找不到**
   - 检查配置文件中的坐标是否正确
   - 验证模板图片路径是否存在
   - 确认游戏界面是否发生变化

3. **导航超时**
   - 增加超时时间配置
   - 检查网络连接和游戏响应速度
   - 验证操作序列是否正确

### 调试技巧

1. 启用详细日志：
```yaml
debug:
  enable_verbose_logging: true
```

2. 保存调试截图：
```yaml
debug:
  save_debug_screenshots: true
```

3. 查看导航历史：
```python
print(navigation_tasks.navigation_history)
```
