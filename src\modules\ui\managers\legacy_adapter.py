"""
遗留兼容性适配器
提供新旧架构之间的兼容性支持
"""

from typing import Dict, Optional, Any, Union
import time

from ....utils.logger import get_logger
from ....core.data_structures import GameScene
from .scene_manager import SceneManager


class LegacyCompatibilityAdapter:
    """遗留兼容性适配器 - 确保新架构与旧代码的兼容性"""
    
    def __init__(self, scene_manager: SceneManager):
        """
        初始化兼容性适配器
        
        Args:
            scene_manager: 场景管理器实例
        """
        self.scene_manager = scene_manager
        self.logger = get_logger("LegacyCompatibilityAdapter")
        
        # 模板名称映射表
        self.template_mapping = {
            # 主菜单相关
            "produce_button": ("main_menu", "produce_button"),
            "part_time_job_button": ("main_menu", "part_time_job_button"),
            "daily_tasks_button": ("main_menu", "daily_tasks_button"),
            
            # 育成准备相关
            "idol_selection_button": ("produce_setup", "idol_selection_button"),
            "support_card_button": ("produce_setup", "support_card_button"),
            "start_produce_button": ("produce_setup", "start_produce_button"),
            
            # 育成主界面相关
            "vocal_lesson_button": ("produce_main", "vocal_lesson_button"),
            "dance_lesson_button": ("produce_main", "dance_lesson_button"),
            "visual_lesson_button": ("produce_main", "visual_lesson_button"),
            "mental_lesson_button": ("produce_main", "mental_lesson_button"),
            "rest_button": ("produce_main", "rest_button"),
            "outing_button": ("produce_main", "outing_button"),
            
            # 通用按钮
            "confirm_button": ("current", "confirm_button"),
            "cancel_button": ("current", "cancel_button"),
            "back_button": ("current", "back_button"),
            "menu_button": ("current", "menu_button"),
        }
        
        # 场景名称映射
        self.scene_name_mapping = {
            "main_menu": GameScene.MAIN_MENU,
            "produce_setup": GameScene.PRODUCE_SETUP,
            "produce_main": GameScene.PRODUCE_MAIN,
            "produce_battle": GameScene.PRODUCE_BATTLE,
            "produce_exam": GameScene.PRODUCE_EXAM,
            "produce_result": GameScene.PRODUCE_RESULT,
            "part_time_job": GameScene.PART_TIME_JOB,
            "daily_tasks": GameScene.DAILY_TASKS,
        }
        
        # 使用统计
        self._usage_stats = {
            'total_calls': 0,
            'successful_calls': 0,
            'template_usage': {},
            'scene_usage': {}
        }
    
    def click_ui_element_by_template(self, template_name: str, 
                                   confidence_threshold: float = 0.8,
                                   timeout: float = 5.0) -> bool:
        """
        兼容旧的模板名称调用方式
        
        Args:
            template_name: 模板名称
            confidence_threshold: 置信度阈值
            timeout: 超时时间
            
        Returns:
            是否点击成功
        """
        start_time = time.time()
        self._usage_stats['total_calls'] += 1
        
        try:
            self.logger.debug(f"兼容性适配器处理点击: {template_name}")
            
            # 查找模板映射
            mapping = self.template_mapping.get(template_name)
            if not mapping:
                self.logger.warning(f"未找到模板映射: {template_name}")
                return self._fallback_click(template_name, confidence_threshold, timeout)
            
            scene_name, element_name = mapping
            
            # 处理当前场景的情况
            if scene_name == "current":
                return self._click_in_current_scene(element_name, confidence_threshold, timeout)
            
            # 获取指定场景
            scene_type = self.scene_name_mapping.get(scene_name)
            if not scene_type:
                self.logger.error(f"未找到场景映射: {scene_name}")
                return False
            
            scene = self.scene_manager.get_scene(scene_type)
            if not scene:
                self.logger.error(f"无法获取场景: {scene_type.value}")
                return False
            
            # 获取UI元素并点击
            element = scene.get_ui_element(element_name)
            if not element:
                self.logger.error(f"未找到UI元素: {element_name} in {scene_type.value}")
                return False
            
            success = element.click()
            
            # 记录统计
            if success:
                self._usage_stats['successful_calls'] += 1
            
            self._record_template_usage(template_name, success, time.time() - start_time)
            
            return success
            
        except Exception as e:
            self.logger.error(f"兼容性适配器点击失败: {template_name}, 错误: {e}")
            return False
    
    def _click_in_current_scene(self, element_name: str, 
                               confidence_threshold: float, timeout: float) -> bool:
        """在当前场景中点击元素"""
        try:
            current_scene = self.scene_manager.get_current_scene()
            if not current_scene:
                self.logger.error("无法获取当前场景")
                return False
            
            element = current_scene.get_ui_element(element_name)
            if not element:
                self.logger.error(f"当前场景中未找到UI元素: {element_name}")
                return False
            
            return element.click()
            
        except Exception as e:
            self.logger.error(f"在当前场景中点击失败: {element_name}, 错误: {e}")
            return False
    
    def _fallback_click(self, template_name: str, 
                       confidence_threshold: float, timeout: float) -> bool:
        """回退到原有的点击方式"""
        try:
            self.logger.info(f"使用回退方式点击: {template_name}")
            
            # 这里需要调用原有的行动控制器
            # 假设场景管理器可以访问行动控制器
            if hasattr(self.scene_manager.scene_factory, 'action'):
                action_controller = self.scene_manager.scene_factory.action
                return action_controller.click_ui_element(
                    template_name, 
                    confidence_threshold=confidence_threshold,
                    timeout=timeout
                )
            else:
                self.logger.error("无法访问行动控制器进行回退操作")
                return False
                
        except Exception as e:
            self.logger.error(f"回退点击失败: {template_name}, 错误: {e}")
            return False
    
    def wait_for_scene_and_click(self, target_scene_name: str, template_name: str,
                                scene_timeout: float = 15.0, click_timeout: float = 5.0) -> bool:
        """
        兼容旧的等待场景并点击方法
        
        Args:
            target_scene_name: 目标场景名称
            template_name: 模板名称
            scene_timeout: 场景等待超时时间
            click_timeout: 点击超时时间
            
        Returns:
            是否成功
        """
        try:
            self.logger.info(f"等待场景 {target_scene_name} 并点击 {template_name}")
            
            # 转换场景名称
            scene_type = self.scene_name_mapping.get(target_scene_name)
            if not scene_type:
                self.logger.error(f"未找到场景映射: {target_scene_name}")
                return False
            
            # 等待场景出现
            if not self.scene_manager.wait_for_scene(scene_type, scene_timeout):
                self.logger.error(f"等待场景超时: {target_scene_name}")
                return False
            
            # 点击UI元素
            return self.click_ui_element_by_template(template_name, timeout=click_timeout)
            
        except Exception as e:
            self.logger.error(f"等待场景并点击失败: {e}")
            return False
    
    def navigate_to_scene_legacy(self, scene_name: str, timeout: float = 30.0) -> bool:
        """
        兼容旧的场景导航方法
        
        Args:
            scene_name: 场景名称
            timeout: 导航超时时间
            
        Returns:
            是否导航成功
        """
        try:
            # 转换场景名称
            scene_type = self.scene_name_mapping.get(scene_name)
            if not scene_type:
                self.logger.error(f"未找到场景映射: {scene_name}")
                return False
            
            # 执行导航
            from .scene_manager import NavigationResult
            result = self.scene_manager.navigate_to_scene(scene_type, timeout)
            
            success = result == NavigationResult.SUCCESS
            self._record_scene_usage(scene_name, success)
            
            return success
            
        except Exception as e:
            self.logger.error(f"兼容性场景导航失败: {scene_name}, 错误: {e}")
            return False
    
    def is_scene_current_legacy(self, scene_name: str) -> bool:
        """
        兼容旧的场景检查方法
        
        Args:
            scene_name: 场景名称
            
        Returns:
            是否为当前场景
        """
        try:
            scene_type = self.scene_name_mapping.get(scene_name)
            if not scene_type:
                return False
            
            current_scene = self.scene_manager.get_current_scene()
            return (current_scene is not None and 
                   current_scene.config.scene_type == scene_type)
            
        except Exception as e:
            self.logger.error(f"场景检查失败: {scene_name}, 错误: {e}")
            return False
    
    def add_template_mapping(self, template_name: str, scene_name: str, element_name: str):
        """
        添加模板映射
        
        Args:
            template_name: 模板名称
            scene_name: 场景名称
            element_name: 元素名称
        """
        self.template_mapping[template_name] = (scene_name, element_name)
        self.logger.info(f"添加模板映射: {template_name} -> ({scene_name}, {element_name})")
    
    def add_scene_mapping(self, scene_name: str, scene_type: GameScene):
        """
        添加场景映射
        
        Args:
            scene_name: 场景名称
            scene_type: 场景类型
        """
        self.scene_name_mapping[scene_name] = scene_type
        self.logger.info(f"添加场景映射: {scene_name} -> {scene_type.value}")
    
    def _record_template_usage(self, template_name: str, success: bool, duration: float):
        """记录模板使用统计"""
        if template_name not in self._usage_stats['template_usage']:
            self._usage_stats['template_usage'][template_name] = {
                'total_calls': 0,
                'successful_calls': 0,
                'total_duration': 0.0
            }
        
        stats = self._usage_stats['template_usage'][template_name]
        stats['total_calls'] += 1
        stats['total_duration'] += duration
        
        if success:
            stats['successful_calls'] += 1
    
    def _record_scene_usage(self, scene_name: str, success: bool):
        """记录场景使用统计"""
        if scene_name not in self._usage_stats['scene_usage']:
            self._usage_stats['scene_usage'][scene_name] = {
                'total_navigations': 0,
                'successful_navigations': 0
            }
        
        stats = self._usage_stats['scene_usage'][scene_name]
        stats['total_navigations'] += 1
        
        if success:
            stats['successful_navigations'] += 1
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """获取使用统计"""
        return {
            'total_calls': self._usage_stats['total_calls'],
            'successful_calls': self._usage_stats['successful_calls'],
            'success_rate': (self._usage_stats['successful_calls'] / 
                           max(1, self._usage_stats['total_calls'])),
            'template_mappings': len(self.template_mapping),
            'scene_mappings': len(self.scene_name_mapping),
            'template_usage': self._usage_stats['template_usage'],
            'scene_usage': self._usage_stats['scene_usage']
        }
    
    def reset_stats(self):
        """重置使用统计"""
        self._usage_stats = {
            'total_calls': 0,
            'successful_calls': 0,
            'template_usage': {},
            'scene_usage': {}
        }
        self.logger.info("兼容性适配器统计已重置")
    
    def get_mapping_info(self) -> Dict[str, Any]:
        """获取映射信息"""
        return {
            'template_mappings': dict(self.template_mapping),
            'scene_mappings': {k: v.value for k, v in self.scene_name_mapping.items()}
        }
