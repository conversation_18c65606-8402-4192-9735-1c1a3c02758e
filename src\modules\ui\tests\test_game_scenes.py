"""
游戏场景测试
测试具体的游戏场景实现类
"""

import unittest
import time
from unittest.mock import Mock, patch

from .test_utils import SceneTestCase, create_test_scene_config
from .test_data import TEST_SCENE_CONFIGS, MOCK_GAME_STATES, MOCK_OCR_RESULTS
from ....core.data_structures import GameScene
from ..scenes.base_game_scene import BaseGameScene
from ..scenes.main_menu import MainMenuScene
from ..scenes.produce_setup import ProduceSetupScene
from ..scenes.produce_main import ProduceMainScene, LessonType, ActionType
from ..managers.ui_system_initializer import UISystemInitializer


class TestBaseGameScene(SceneTestCase):
    """基础游戏场景测试类"""
    
    def setUp(self):
        super().setUp()
        self.config = create_test_scene_config(GameScene.MAIN_MENU)
        
        # 创建模拟的基础游戏场景
        class MockGameScene(BaseGameScene):
            def _create_scene_ui_elements(self):
                self.ui_elements["test_button"] = self.ui_factory.create_button("test_button")
                self.ui_elements["test_label"] = self.ui_factory.create_label("test_label")
            
            def _get_critical_elements(self):
                return ["test_button", "test_label"]
        
        self.scene = MockGameScene(self.config, self.perception, self.action)
    
    def test_scene_initialization(self):
        """测试场景初始化"""
        self.assertIsNotNone(self.scene)
        self.assertEqual(self.scene.config.scene_type, GameScene.MAIN_MENU)
        self.assertIsNotNone(self.scene.ui_factory)
        self.assertIsNotNone(self.scene.scene_logger)
    
    def test_ui_elements_creation(self):
        """测试UI元素创建"""
        # 触发UI元素初始化
        self.scene._init_ui_elements()
        
        # 验证UI元素已创建
        self.assertIn("test_button", self.scene.ui_elements)
        self.assertIn("test_label", self.scene.ui_elements)
        self.assertTrue(self.scene.is_scene_ready())
    
    def test_element_interaction(self):
        """测试元素交互"""
        self.scene._init_ui_elements()
        
        # 测试点击交互
        result = self.scene.interact_with_element("test_button", "click")
        self.assertTrue(result)
        
        # 验证交互记录
        self.assertGreater(self.scene._interaction_count, 0)
        self.assertGreater(self.scene._last_interaction_time, 0)
    
    def test_scene_verification(self):
        """测试场景验证"""
        # 设置场景为当前场景
        self.set_current_scene(GameScene.MAIN_MENU)
        self.scene._init_ui_elements()
        
        # 验证场景状态
        result = self.scene.verify_scene_state()
        self.assertTrue(result)
    
    def test_scene_statistics(self):
        """测试场景统计"""
        self.scene._init_ui_elements()
        self.scene.interact_with_element("test_button", "click")
        
        stats = self.scene.get_scene_statistics()
        
        self.assertIn("scene_ready", stats)
        self.assertIn("interaction_count", stats)
        self.assertIn("critical_elements", stats)
        self.assertEqual(stats["interaction_count"], 1)


class TestMainMenuScene(SceneTestCase):
    """主菜单场景测试类"""
    
    def setUp(self):
        super().setUp()
        self.config = TEST_SCENE_CONFIGS[GameScene.MAIN_MENU]
        self.scene = MainMenuScene(self.config, self.perception, self.action)
        
        # 设置模拟数据
        self.setup_main_menu_mocks()
    
    def setup_main_menu_mocks(self):
        """设置主菜单模拟数据"""
        # 设置主菜单相关模板
        self.perception.set_mock_template("main_menu_logo", 960, 100, 0.9)
        self.perception.set_mock_template("produce_button", 500, 400, 0.85)
        self.perception.set_mock_template("part_time_job_button", 300, 600, 0.85)
        self.perception.set_mock_template("daily_tasks_button", 700, 600, 0.85)
        
        # 设置用户信息OCR结果
        self.perception.set_mock_ocr_result({'x': 100, 'y': 50, 'width': 150, 'height': 30}, "TestUser")
        self.perception.set_mock_ocr_result({'x': 100, 'y': 80, 'width': 50, 'height': 20}, "Lv.25")
        self.perception.set_mock_ocr_result({'x': 100, 'y': 110, 'width': 100, 'height': 20}, "12,345")
    
    def test_main_menu_initialization(self):
        """测试主菜单初始化"""
        self.assertIsNotNone(self.scene)
        self.assertEqual(self.scene.config.scene_type, GameScene.MAIN_MENU)
        
        # 初始化UI元素
        self.scene._init_ui_elements()
        
        # 验证关键元素存在
        critical_elements = self.scene._get_critical_elements()
        for element_name in critical_elements:
            self.assertIn(element_name, self.scene.ui_elements)
    
    def test_start_produce(self):
        """测试开始育成"""
        self.scene._init_ui_elements()
        
        result = self.scene.start_produce()
        self.assertTrue(result)
        
        # 验证育成按钮被点击
        self.assert_element_clicked("produce_button")
    
    def test_start_part_time_job(self):
        """测试开始打工"""
        self.scene._init_ui_elements()
        
        result = self.scene.start_part_time_job()
        self.assertTrue(result)
        
        # 验证打工按钮被点击
        self.assert_element_clicked("part_time_job_button")
    
    def test_open_daily_tasks(self):
        """测试打开日常任务"""
        self.scene._init_ui_elements()
        
        result = self.scene.open_daily_tasks()
        self.assertTrue(result)
        
        # 验证日常任务按钮被点击
        self.assert_element_clicked("daily_tasks_button")
    
    def test_get_user_info(self):
        """测试获取用户信息"""
        self.scene._init_ui_elements()
        
        user_info = self.scene.get_user_info()
        
        # 验证用户信息
        self.assertIn("username", user_info)
        self.assertIn("level", user_info)
        self.assertIn("currency", user_info)
        
        self.assertEqual(user_info["username"], "TestUser")
        self.assertEqual(user_info["level"], 25)
        self.assertEqual(user_info["currency"], 12345)
    
    def test_scene_verification(self):
        """测试主菜单场景验证"""
        self.scene._init_ui_elements()
        self.set_current_scene(GameScene.MAIN_MENU)
        
        result = self.scene.verify_scene_state()
        self.assertTrue(result)
    
    def test_navigation_to_produce_setup(self):
        """测试导航到育成准备"""
        self.scene._init_ui_elements()
        
        result = self.scene._navigate_to_produce_setup(30.0)
        self.assertTrue(result)
        
        # 验证导航操作
        self.assert_element_clicked("produce_button")


class TestProduceSetupScene(SceneTestCase):
    """育成准备场景测试类"""
    
    def setUp(self):
        super().setUp()
        self.config = TEST_SCENE_CONFIGS[GameScene.PRODUCE_SETUP]
        self.scene = ProduceSetupScene(self.config, self.perception, self.action)
        
        # 设置模拟数据
        self.setup_produce_setup_mocks()
    
    def setup_produce_setup_mocks(self):
        """设置育成准备模拟数据"""
        # 设置场景标识
        self.perception.set_mock_template("produce_setup_title", 960, 100, 0.9)
        self.perception.set_mock_template("idol_selection_area", 960, 400, 0.8)
        self.perception.set_mock_template("support_card_area", 960, 600, 0.8)
        
        # 设置功能按钮
        self.perception.set_mock_template("idol_selection_button", 300, 500, 0.8)
        self.perception.set_mock_template("support_card_button", 600, 500, 0.8)
        self.perception.set_mock_template("start_produce_button", 960, 800, 0.9)
        
        # 设置信息显示
        self.perception.set_mock_ocr_result({'x': 400, 'y': 750, 'width': 200, 'height': 30}, "春香")
        self.perception.set_mock_ocr_result({'x': 800, 'y': 750, 'width': 50, 'height': 20}, "5/6")
    
    def test_produce_setup_initialization(self):
        """测试育成准备初始化"""
        self.assertIsNotNone(self.scene)
        self.assertEqual(self.scene.config.scene_type, GameScene.PRODUCE_SETUP)
        
        # 初始化UI元素
        self.scene._init_ui_elements()
        
        # 验证关键元素存在
        critical_elements = self.scene._get_critical_elements()
        for element_name in critical_elements:
            self.assertIn(element_name, self.scene.ui_elements)
    
    def test_select_idol(self):
        """测试选择偶像"""
        self.scene._init_ui_elements()
        
        result = self.scene.select_idol("春香")
        self.assertTrue(result)
        
        # 验证状态更新
        self.assertTrue(self.scene._idol_selected)
        
        # 验证按钮点击
        self.assert_element_clicked("idol_selection_button")
    
    def test_configure_support_cards_auto(self):
        """测试自动配置支援卡"""
        self.scene._init_ui_elements()
        
        result = self.scene.configure_support_cards(auto_config=True)
        self.assertTrue(result)
        
        # 验证状态更新
        self.assertTrue(self.scene._support_cards_configured)
        
        # 验证按钮点击
        self.assert_element_clicked("support_card_button")
    
    def test_start_produce(self):
        """测试开始育成"""
        self.scene._init_ui_elements()
        
        # 先完成准备工作
        self.scene.select_idol()
        self.scene.configure_support_cards()
        
        result = self.scene.start_produce()
        self.assertTrue(result)
        
        # 验证按钮点击
        self.assert_element_clicked("start_produce_button")
    
    def test_setup_status(self):
        """测试获取准备状态"""
        self.scene._init_ui_elements()
        
        # 初始状态
        status = self.scene.get_setup_status()
        self.assertFalse(status["idol_selected"])
        self.assertFalse(status["support_cards_configured"])
        self.assertFalse(status["setup_complete"])
        
        # 完成准备后
        self.scene.select_idol()
        self.scene.configure_support_cards()
        
        status = self.scene.get_setup_status()
        self.assertTrue(status["idol_selected"])
        self.assertTrue(status["support_cards_configured"])
    
    def test_reset_setup(self):
        """测试重置准备"""
        self.scene._init_ui_elements()
        
        # 先完成一些准备工作
        self.scene.select_idol()
        self.scene.configure_support_cards()
        
        # 重置
        result = self.scene.reset_setup()
        self.assertTrue(result)
        
        # 验证状态重置
        status = self.scene.get_setup_status()
        self.assertFalse(status["idol_selected"])
        self.assertFalse(status["support_cards_configured"])
        self.assertFalse(status["setup_complete"])


class TestProduceMainScene(SceneTestCase):
    """育成主界面场景测试类"""
    
    def setUp(self):
        super().setUp()
        self.config = TEST_SCENE_CONFIGS[GameScene.PRODUCE_MAIN]
        self.scene = ProduceMainScene(self.config, self.perception, self.action)
        
        # 设置模拟数据
        self.setup_produce_main_mocks()
    
    def setup_produce_main_mocks(self):
        """设置育成主界面模拟数据"""
        # 设置场景标识
        self.perception.set_mock_template("produce_main_ui", 960, 100, 0.9)
        self.perception.set_mock_template("lesson_area", 960, 400, 0.8)
        
        # 设置课程按钮
        self.perception.set_mock_template("vocal_lesson_button", 200, 300, 0.85)
        self.perception.set_mock_template("dance_lesson_button", 400, 300, 0.85)
        self.perception.set_mock_template("visual_lesson_button", 600, 300, 0.85)
        self.perception.set_mock_template("mental_lesson_button", 800, 300, 0.85)
        
        # 设置行动按钮
        self.perception.set_mock_template("rest_button", 1000, 300, 0.85)
        self.perception.set_mock_template("outing_button", 1200, 300, 0.85)
        
        # 设置状态显示OCR结果
        self.perception.set_mock_ocr_result({'x': 100, 'y': 50, 'width': 50, 'height': 20}, "15/78")
        self.perception.set_mock_ocr_result({'x': 200, 'y': 100, 'width': 50, 'height': 20}, "1250")
        self.perception.set_mock_ocr_result({'x': 300, 'y': 100, 'width': 50, 'height': 20}, "980")
        self.perception.set_mock_ocr_result({'x': 400, 'y': 100, 'width': 50, 'height': 20}, "750")
        self.perception.set_mock_ocr_result({'x': 500, 'y': 100, 'width': 50, 'height': 20}, "650")
        self.perception.set_mock_ocr_result({'x': 600, 'y': 100, 'width': 50, 'height': 20}, "85/100")
    
    def test_produce_main_initialization(self):
        """测试育成主界面初始化"""
        self.assertIsNotNone(self.scene)
        self.assertEqual(self.scene.config.scene_type, GameScene.PRODUCE_MAIN)
        
        # 初始化UI元素
        self.scene._init_ui_elements()
        
        # 验证关键元素存在
        critical_elements = self.scene._get_critical_elements()
        for element_name in critical_elements:
            self.assertIn(element_name, self.scene.ui_elements)
    
    def test_take_vocal_lesson(self):
        """测试上声乐课"""
        self.scene._init_ui_elements()
        
        result = self.scene.take_lesson(LessonType.VOCAL)
        self.assertTrue(result)
        
        # 验证按钮点击
        self.assert_element_clicked("vocal_lesson_button")
        
        # 验证行动记录
        history = self.scene.get_action_history()
        self.assertGreater(len(history), 0)
        self.assertEqual(history[-1]["action_type"], ActionType.LESSON.value)
        self.assertEqual(history[-1]["detail"], LessonType.VOCAL.value)
    
    def test_take_rest(self):
        """测试休息"""
        self.scene._init_ui_elements()
        
        result = self.scene.take_rest()
        self.assertTrue(result)
        
        # 验证按钮点击
        self.assert_element_clicked("rest_button")
        
        # 验证行动记录
        history = self.scene.get_action_history()
        self.assertGreater(len(history), 0)
        self.assertEqual(history[-1]["action_type"], ActionType.REST.value)
    
    def test_go_outing(self):
        """测试外出"""
        self.scene._init_ui_elements()
        
        result = self.scene.go_outing()
        self.assertTrue(result)
        
        # 验证按钮点击
        self.assert_element_clicked("outing_button")
        
        # 验证行动记录
        history = self.scene.get_action_history()
        self.assertGreater(len(history), 0)
        self.assertEqual(history[-1]["action_type"], ActionType.OUTING.value)
    
    def test_get_current_stats(self):
        """测试获取当前属性"""
        self.scene._init_ui_elements()
        
        # 更新属性信息
        self.scene._update_stats()
        
        stats = self.scene.get_current_stats()
        
        # 验证属性值
        self.assertIn("vocal", stats)
        self.assertIn("dance", stats)
        self.assertIn("visual", stats)
        self.assertIn("mental", stats)
        self.assertIn("stamina", stats)
    
    def test_get_turn_info(self):
        """测试获取回合信息"""
        self.scene._init_ui_elements()
        
        # 更新回合信息
        self.scene._update_turn_info()
        
        current_turn, max_turns = self.scene.get_turn_info()
        
        # 验证回合信息
        self.assertGreaterEqual(current_turn, 0)
        self.assertGreater(max_turns, 0)
    
    def test_produce_progress(self):
        """测试育成进度"""
        self.scene._init_ui_elements()
        self.scene._current_turn = 39
        self.scene._max_turns = 78
        
        progress = self.scene.get_produce_progress()
        self.assertAlmostEqual(progress, 0.5, places=2)
        
        # 测试完成状态
        self.scene._current_turn = 78
        self.assertTrue(self.scene.is_produce_complete())


if __name__ == '__main__':
    unittest.main(verbosity=2)
