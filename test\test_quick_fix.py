"""
快速测试修复结果
"""

import requests
import json


def test_screenshot_fix():
    """测试截图修复"""
    print("🧪 测试截图修复...")
    
    # 测试配置
    test_data = {
        "config": {
            "mode": "window",
            "format": "png",
            "quality": 90,
            "save_to_disk": True,
            "filename_prefix": "fix_test"
        }
    }
    
    try:
        print(f"📡 发送请求...")
        print(f"📋 请求数据: {json.dumps(test_data, indent=2)}")
        
        response = requests.post(
            "http://localhost:8000/api/v1/screenshot/capture",
            json=test_data,
            timeout=30
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 截图API修复成功!")
            print(f"📋 响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get("success"):
                print(f"🎉 截图执行成功: {result.get('filename')}")
                return True
            else:
                print(f"⚠️ 截图执行失败: {result.get('error_message')}")
                return False
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"📋 错误响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False


if __name__ == "__main__":
    success = test_screenshot_fix()
    if success:
        print("\n🎉 修复验证成功！")
    else:
        print("\n❌ 修复验证失败！")
    
    exit(0 if success else 1)
