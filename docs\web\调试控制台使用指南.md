# 调试控制台使用指南

## 概述

调试控制台是Gakumasu-Bot项目的重要组成部分，为开发者和用户提供了完整的系统调试和功能测试工具。通过调试控制台，您可以实时监控系统状态、测试各项功能、查看日志信息以及管理系统配置。

## 功能模块

### 1. 系统状态监控

**功能描述**：实时显示系统运行状态和性能指标

**主要特性**：
- 系统运行状态（运行中/已停止/未知）
- 调度器状态和任务统计
- 连接状态监控（API/WebSocket）
- 游戏状态详细信息
- 系统控制操作（启动/暂停/停止/重启）

**使用方法**：
1. 点击左侧菜单的"系统状态"
2. 查看各项状态指标
3. 使用"刷新状态"按钮手动更新
4. 开启"自动刷新"实现实时监控
5. 使用系统控制按钮管理系统运行状态

### 2. 游戏状态监控

**功能描述**：监控游戏画面和状态信息

**主要特性**：
- 实时游戏画面预览
- 游戏状态详细信息（场景、体力、分数等）
- 资源状态可视化（体力值、元气值进度条）
- 偶像属性统计
- 手牌信息显示
- 全屏预览功能

**使用方法**：
1. 点击左侧菜单的"游戏监控"
2. 点击"获取预览"获取当前游戏画面
3. 点击"截图测试"执行截图功能测试
4. 点击截图可全屏查看
5. 查看右侧的游戏状态详细信息

### 3. 功能测试面板

**功能描述**：测试系统各模块功能

**主要特性**：
- **感知模块测试**：屏幕捕获、场景识别
- **决策模块测试**：MCTS算法、启发式评分
- **行动模块测试**：输入模拟、操作验证
- **调度模块测试**：任务调度、配置管理
- **综合测试**：完整系统功能测试
- 测试结果详细显示和历史记录

**使用方法**：
1. 点击左侧菜单的"功能测试"
2. 选择要测试的模块功能
3. 点击相应的测试按钮
4. 查看右侧的测试结果
5. 展开测试结果查看详细信息
6. 使用"清空结果"清理测试历史

### 4. 日志查看器

**功能描述**：实时查看和管理系统日志

**主要特性**：
- 实时日志流显示
- 日志级别过滤（DEBUG/INFO/WARNING/ERROR/CRITICAL）
- 日志内容搜索
- 自动滚动/手动滚动切换
- 日志统计信息
- 日志复制功能

**使用方法**：
1. 点击左侧菜单的"日志查看"
2. 使用级别下拉框过滤日志
3. 在搜索框中输入关键词搜索
4. 开启/关闭自动滚动
5. 点击日志条目的"复制"按钮复制内容
6. 查看底部的日志统计信息

### 5. 配置管理

**功能描述**：动态管理系统配置参数

**主要特性**：
- 分类配置管理（系统/游戏/AI/UI/高级）
- 实时配置修改和验证
- 配置预设快速切换
- JSON高级编辑模式
- 配置重置和备份

**配置分类**：
- **系统配置**：日志级别、自动保存间隔、调试模式
- **游戏配置**：窗口标题、操作延迟、截图间隔、自动重试
- **AI配置**：MCTS参数、启发式权重、探索系数
- **UI配置**：主题、语言、动画、刷新间隔
- **高级配置**：JSON格式直接编辑

**使用方法**：
1. 点击左侧菜单的"配置管理"
2. 选择配置分类
3. 修改相应的配置参数
4. 点击"保存"或"保存配置"应用更改
5. 使用"重置"恢复默认设置
6. 在高级配置中直接编辑JSON

## 快速开始

### 启动调试控制台

1. 确保前后端服务都已启动
   ```bash
   # 启动后端服务
   python -m uvicorn src.web.main:app --reload --host 127.0.0.1 --port 8000
   
   # 启动前端服务
   cd frontend && npm run dev
   ```

2. 在浏览器中访问：`http://localhost:3000/debug-console`

3. 在主导航栏中点击"调试控制台"菜单项

### 基本操作流程

1. **系统检查**：
   - 进入"系统状态"查看系统是否正常运行
   - 检查各模块连接状态
   - 确认游戏状态信息

2. **功能测试**：
   - 进入"功能测试"面板
   - 依次测试各模块功能
   - 查看测试结果确认功能正常

3. **问题诊断**：
   - 进入"日志查看"检查错误信息
   - 使用日志过滤和搜索定位问题
   - 根据日志信息进行问题排查

4. **配置调优**：
   - 进入"配置管理"调整系统参数
   - 根据实际需求优化配置
   - 保存配置并验证效果

## 注意事项

### 安全提醒

- 系统控制操作（启动/停止/重启）会影响整个系统运行，请谨慎使用
- 配置修改可能影响系统稳定性，建议在测试环境中验证
- 高级配置的JSON编辑需要确保格式正确

### 性能建议

- 长时间使用时建议定期清理日志和测试结果
- 自动刷新功能会增加系统负载，按需开启
- 大量测试操作可能影响系统性能

### 故障排除

**常见问题**：

1. **无法连接后端**
   - 检查后端服务是否启动
   - 确认端口配置是否正确
   - 查看网络连接状态

2. **功能测试失败**
   - 检查相关模块是否正常初始化
   - 查看日志获取详细错误信息
   - 确认系统配置是否正确

3. **配置保存失败**
   - 检查配置参数是否有效
   - 确认有足够的权限修改配置
   - 查看后端日志获取错误详情

## 技术支持

如果在使用过程中遇到问题，请：

1. 查看日志获取详细错误信息
2. 检查系统状态确认各模块运行正常
3. 参考项目文档获取更多技术细节
4. 在项目仓库中提交Issue报告问题

---

**版本信息**：V1.0  
**更新日期**：2025-08-05  
**适用版本**：Gakumasu-Bot v1.0+
