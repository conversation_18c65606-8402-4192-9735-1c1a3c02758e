<template>
  <div class="home">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>Gakumasu-Bot 控制面板</span>
        </div>
      </template>
      <div class="welcome">
        <el-icon size="48" color="#409eff"><Monitor /></el-icon>
        <h2>欢迎使用 Gakumasu-Bot</h2>
        <p>学园偶像大师自动化程序控制面板</p>
        <el-button type="primary" size="large">
          <el-icon><VideoPlay /></el-icon>
          开始使用
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { Monitor, VideoPlay } from '@element-plus/icons-vue'
</script>

<style scoped>
.home {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}

.card-header {
  display: flex;
  justify-content: center;
  font-weight: 600;
  font-size: 18px;
}

.welcome {
  text-align: center;
  padding: 40px 20px;
}

.welcome h2 {
  margin: 20px 0 10px 0;
  color: #333;
}

.welcome p {
  color: #666;
  margin-bottom: 30px;
}
</style>
