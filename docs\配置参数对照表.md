# Gakumasu-Bot 配置参数对照表

## 系统配置 (system)

| 参数名 | 类型 | 默认值 | 取值范围 | 说明 |
|--------|------|--------|----------|------|
| `log_level` | string | INFO | DEBUG/INFO/WARNING/ERROR/CRITICAL | 日志级别 |
| `language` | string | ja | ja/cn | 游戏语言设置 |
| `screen_resolution` | array | [1920, 1080] | [宽度, 高度] | 屏幕分辨率 |
| `game_window_title` | string | gakumas | 任意字符串 | 游戏窗口标题 |

## DMM Player 配置 (dmm)

| 参数名 | 类型 | 默认值 | 取值范围 | 说明 |
|--------|------|--------|----------|------|
| `dmm_player_path` | string | 无 | 有效文件路径 | DMM Player可执行文件路径 |
| `launch_timeout` | number | 60 | 30-300 | 启动超时时间(秒) |
| `auto_launch` | boolean | true | true/false | 是否自动启动游戏 |

## 性能配置 (performance)

| 参数名 | 类型 | 默认值 | 取值范围 | 说明 | 影响 |
|--------|------|--------|----------|------|------|
| `screenshot_interval` | number | 0.5 | 0.1-2.0 | 截图间隔(秒) | 响应速度 vs CPU占用 |
| `action_delay_min` | number | 0.05 | 0.01-0.5 | 最小操作延迟(秒) | 反检测能力 |
| `action_delay_max` | number | 0.15 | 0.1-1.0 | 最大操作延迟(秒) | 反检测能力 |
| `decision_timeout` | number | 30.0 | 5.0-120.0 | 决策超时时间(秒) | 决策质量 vs 响应速度 |
| `enable_gpu_acceleration` | boolean | false | true/false | 是否启用GPU加速 | 性能提升 |
| `max_worker_threads` | number | 4 | 1-16 | 最大工作线程数 | 并发处理能力 |

## 模板匹配配置 (template_matching)

| 参数名 | 类型 | 默认值 | 取值范围 | 说明 | 调优建议 |
|--------|------|--------|----------|------|----------|
| `confidence_threshold` | number | 0.8 | 0.5-0.95 | 匹配置信度阈值 | 高分辨率: 0.85-0.9<br>普通: 0.75-0.8<br>调试: 0.6-0.7 |
| `enable_multi_scale` | boolean | true | true/false | 是否启用多尺度匹配 | 提高适应性，略降性能 |
| `scale_range` | array | [0.8, 1.2] | [最小, 最大] | 尺度匹配范围 | 固定分辨率: [0.9, 1.1]<br>多分辨率: [0.7, 1.3] |
| `scale_step` | number | 0.1 | 0.05-0.2 | 尺度步长 | 精度 vs 性能平衡 |

## OCR配置 (ocr)

| 参数名 | 类型 | 默认值 | 取值范围 | 说明 | 场景优化 |
|--------|------|--------|----------|------|----------|
| `enable_ocr` | boolean | true | true/false | 是否启用OCR | 动态文本识别必需 |
| `languages` | array | ['ja', 'en'] | 语言代码列表 | 支持的语言 | 日语: ['ja', 'en']<br>中文: ['ch_sim', 'en'] |
| `confidence_threshold` | number | 0.7 | 0.3-0.9 | OCR置信度阈值 | 清晰文字: 0.8-0.9<br>模糊文字: 0.5-0.7<br>艺术字: 0.3-0.5 |
| `use_gpu` | boolean | false | true/false | 是否使用GPU加速 | 提升OCR速度 |

## AI决策配置 (ai)

| 参数名 | 类型 | 默认值 | 取值范围 | 说明 | 性能影响 |
|--------|------|--------|----------|------|----------|
| `enable_mcts` | boolean | true | true/false | 是否启用MCTS算法 | 决策质量 vs 计算开销 |
| `mcts_iterations` | number | 1000 | 100-10000 | MCTS迭代次数 | 高端: 1500-2000<br>中端: 800-1200<br>低端: 300-600 |
| `mcts_timeout` | number | 10.0 | 1.0-60.0 | MCTS超时时间(秒) | 决策质量上限 |
| `mcts_exploration_constant` | number | 1.414 | 0.5-2.0 | MCTS探索参数 | 探索 vs 利用平衡 |

### 启发式权重配置 (ai.heuristic_weights)

| 参数名 | 类型 | 默认值 | 取值范围 | 说明 |
|--------|------|--------|----------|------|
| `score` | number | 1.0 | 0.0-2.0 | 分数权重 |
| `stamina` | number | 0.8 | 0.0-2.0 | 体力权重 |
| `vigor` | number | 0.6 | 0.0-2.0 | 元气权重 |
| `card_synergy` | number | 0.4 | 0.0-2.0 | 卡牌协同权重 |
| `risk_factor` | number | 0.3 | 0.0-2.0 | 风险因子权重 |

## 错误处理配置 (error_handling)

| 参数名 | 类型 | 默认值 | 取值范围 | 说明 |
|--------|------|--------|----------|------|
| `max_retries` | number | 3 | 1-10 | 最大重试次数 |
| `retry_interval` | number | 2.0 | 0.5-10.0 | 重试间隔(秒) |
| `enable_auto_recovery` | boolean | true | true/false | 是否启用自动恢复 |
| `recovery_strategy` | string | restart | restart/skip/abort | 恢复策略 |
| `save_error_screenshots` | boolean | true | true/false | 是否保存错误截图 |

## 调试配置 (debug)

| 参数名 | 类型 | 默认值 | 取值范围 | 说明 |
|--------|------|--------|----------|------|
| `enable_debug_mode` | boolean | false | true/false | 是否启用调试模式 |
| `save_debug_screenshots` | boolean | false | true/false | 是否保存调试截图 |
| `enable_verbose_logging` | boolean | false | true/false | 是否启用详细日志 |
| `show_match_visualization` | boolean | false | true/false | 是否显示匹配可视化 |

## 配置组合建议

### 高性能配置
```yaml
performance:
  screenshot_interval: 0.2
  action_delay_min: 0.02
  action_delay_max: 0.05
  enable_gpu_acceleration: true

template_matching:
  confidence_threshold: 0.9
  enable_multi_scale: true

ai:
  mcts_iterations: 2000
  mcts_timeout: 15.0
```

### 稳定性配置
```yaml
performance:
  screenshot_interval: 0.8
  action_delay_min: 0.1
  action_delay_max: 0.25

template_matching:
  confidence_threshold: 0.75
  scale_range: [0.9, 1.1]

error_handling:
  max_retries: 5
  retry_interval: 3.0
```

### 调试配置
```yaml
system:
  log_level: DEBUG

debug:
  enable_debug_mode: true
  save_debug_screenshots: true
  enable_verbose_logging: true

template_matching:
  confidence_threshold: 0.6

ai:
  enable_mcts: false
```

## 参数依赖关系

### 性能相关依赖
- `screenshot_interval` ↔ `decision_timeout`: 截图间隔影响决策频率
- `mcts_iterations` ↔ `mcts_timeout`: 迭代次数受超时时间限制
- `enable_gpu_acceleration` → `use_gpu` (OCR): GPU加速需要一致启用

### 精度相关依赖
- `confidence_threshold` ↔ `max_retries`: 低置信度需要更多重试
- `enable_multi_scale` → `scale_range`: 多尺度匹配需要合理的尺度范围
- `language` → `languages` (OCR): 系统语言应与OCR语言匹配

### 调试相关依赖
- `enable_debug_mode` → 所有debug选项: 调试模式启用其他调试功能
- `log_level: DEBUG` → `enable_verbose_logging`: 调试日志级别需要详细日志
- `save_debug_screenshots` → `debug_screenshot_directory`: 保存截图需要指定目录

## 故障排除参数调整

### 识别失败问题
1. 降低 `confidence_threshold` (0.8 → 0.7)
2. 启用 `enable_multi_scale`
3. 扩大 `scale_range` ([0.8,1.2] → [0.7,1.3])
4. 启用调试模式查看匹配结果

### 性能问题
1. 增加 `screenshot_interval` (0.5 → 0.8)
2. 减少 `mcts_iterations` (1000 → 500)
3. 禁用 `enable_multi_scale`
4. 启用 `enable_gpu_acceleration`

### 稳定性问题
1. 增加 `max_retries` (3 → 5)
2. 增加 `retry_interval` (2.0 → 3.0)
3. 启用 `enable_auto_recovery`
4. 降低 `confidence_threshold`

### OCR识别问题
1. 调整 `confidence_threshold` (0.7 → 0.5)
2. 启用所有 `preprocessing` 选项
3. 检查 `languages` 设置是否正确
4. 尝试启用 `use_gpu`
