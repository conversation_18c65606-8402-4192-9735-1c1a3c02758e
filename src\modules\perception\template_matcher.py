"""
模板匹配器
负责在屏幕截图中查找UI元素
"""

import cv2
import numpy as np
from typing import List, Tuple, Optional, Dict, Any
from pathlib import Path

from ...utils.logger import get_logger
from ...core.data_structures import GameUIElementNotFound


class MatchResult:
    """匹配结果类"""
    
    def __init__(self, x: int, y: int, width: int, height: int, confidence: float, template_name: str):
        self.x = x
        self.y = y
        self.width = width
        self.height = height
        self.confidence = confidence
        self.template_name = template_name
        
        # 计算中心点
        self.center_x = x + width // 2
        self.center_y = y + height // 2
    
    def __repr__(self):
        return f"MatchResult({self.template_name}, pos=({self.x},{self.y}), confidence={self.confidence:.3f})"


class TemplateMatcher:
    """模板匹配器类"""
    
    def __init__(self, templates_dir: str = "assets/templates"):
        """
        初始化模板匹配器
        
        Args:
            templates_dir: 模板图片目录
        """
        self.templates_dir = Path(templates_dir)
        self.logger = get_logger("TemplateMatcher")
        
        # 模板缓存
        self._template_cache: Dict[str, np.ndarray] = {}
        
        # 匹配参数
        self.default_confidence_threshold = 0.8
        self.multi_scale_enabled = True
        self.scale_range = (0.8, 1.2)
        self.scale_step = 0.1
        
        self.logger.info(f"模板匹配器初始化完成，模板目录: {templates_dir}")
    
    def load_template(self, template_name: str, force_reload: bool = False) -> Optional[np.ndarray]:
        """
        加载模板图片
        
        Args:
            template_name: 模板名称（不含扩展名）
            force_reload: 是否强制重新加载
            
        Returns:
            模板图像数组，如果加载失败返回None
        """
        # 检查缓存
        if not force_reload and template_name in self._template_cache:
            return self._template_cache[template_name]
        
        # 支持的图片格式
        extensions = ['.png', '.jpg', '.jpeg', '.bmp']
        
        template_path = None
        for ext in extensions:
            potential_path = self.templates_dir / f"{template_name}{ext}"
            if potential_path.exists():
                template_path = potential_path
                break
        
        if not template_path:
            self.logger.warning(f"模板文件不存在: {template_name}")
            return None
        
        try:
            # 加载图片
            template = cv2.imread(str(template_path), cv2.IMREAD_COLOR)
            if template is None:
                self.logger.error(f"无法加载模板图片: {template_path}")
                return None
            
            # 缓存模板
            self._template_cache[template_name] = template
            self.logger.debug(f"模板加载成功: {template_name}, 尺寸: {template.shape}")
            
            return template
            
        except Exception as e:
            self.logger.error(f"加载模板失败: {template_name}, 错误: {e}")
            return None
    
    def match_template(self, 
                      image: np.ndarray, 
                      template_name: str, 
                      confidence_threshold: Optional[float] = None,
                      method: int = cv2.TM_CCOEFF_NORMED) -> Optional[MatchResult]:
        """
        在图像中匹配单个模板
        
        Args:
            image: 源图像
            template_name: 模板名称
            confidence_threshold: 置信度阈值
            method: 匹配方法
            
        Returns:
            匹配结果，如果未找到返回None
        """
        template = self.load_template(template_name)
        if template is None:
            return None
        
        if confidence_threshold is None:
            confidence_threshold = self.default_confidence_threshold
        
        try:
            # 单尺度匹配
            result = cv2.matchTemplate(image, template, method)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            # 根据匹配方法选择最佳位置
            if method in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
                best_match_loc = min_loc
                confidence = 1 - min_val  # 转换为置信度
            else:
                best_match_loc = max_loc
                confidence = max_val
            
            if confidence >= confidence_threshold:
                h, w = template.shape[:2]
                match_result = MatchResult(
                    x=best_match_loc[0],
                    y=best_match_loc[1],
                    width=w,
                    height=h,
                    confidence=confidence,
                    template_name=template_name
                )
                
                self.logger.debug(f"模板匹配成功: {match_result}")
                return match_result
            
            # 如果启用多尺度匹配且单尺度匹配失败
            if self.multi_scale_enabled and confidence < confidence_threshold:
                return self._multi_scale_match(image, template, template_name, confidence_threshold, method)
            
            self.logger.debug(f"模板匹配失败: {template_name}, 置信度: {confidence:.3f} < {confidence_threshold}")
            return None
            
        except Exception as e:
            self.logger.error(f"模板匹配异常: {template_name}, 错误: {e}")
            return None
    
    def _multi_scale_match(self, 
                          image: np.ndarray, 
                          template: np.ndarray, 
                          template_name: str,
                          confidence_threshold: float,
                          method: int) -> Optional[MatchResult]:
        """
        多尺度模板匹配
        
        Args:
            image: 源图像
            template: 模板图像
            template_name: 模板名称
            confidence_threshold: 置信度阈值
            method: 匹配方法
            
        Returns:
            最佳匹配结果
        """
        best_match = None
        best_confidence = 0
        
        # 生成缩放比例
        scales = np.arange(self.scale_range[0], self.scale_range[1] + self.scale_step, self.scale_step)
        
        for scale in scales:
            # 缩放模板
            scaled_template = cv2.resize(template, None, fx=scale, fy=scale)
            
            # 检查缩放后的模板是否超出图像边界
            if scaled_template.shape[0] > image.shape[0] or scaled_template.shape[1] > image.shape[1]:
                continue
            
            try:
                # 执行匹配
                result = cv2.matchTemplate(image, scaled_template, method)
                min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
                
                # 根据匹配方法选择最佳位置和置信度
                if method in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
                    best_match_loc = min_loc
                    confidence = 1 - min_val
                else:
                    best_match_loc = max_loc
                    confidence = max_val
                
                # 更新最佳匹配
                if confidence > best_confidence:
                    best_confidence = confidence
                    h, w = scaled_template.shape[:2]
                    best_match = MatchResult(
                        x=best_match_loc[0],
                        y=best_match_loc[1],
                        width=w,
                        height=h,
                        confidence=confidence,
                        template_name=f"{template_name}@{scale:.1f}x"
                    )
                
            except Exception as e:
                self.logger.debug(f"多尺度匹配异常 (scale={scale:.1f}): {e}")
                continue
        
        if best_match and best_confidence >= confidence_threshold:
            self.logger.debug(f"多尺度匹配成功: {best_match}")
            return best_match
        
        return None
    
    def match_multiple_templates(self, 
                               image: np.ndarray, 
                               template_names: List[str],
                               confidence_threshold: Optional[float] = None) -> List[MatchResult]:
        """
        在图像中匹配多个模板
        
        Args:
            image: 源图像
            template_names: 模板名称列表
            confidence_threshold: 置信度阈值
            
        Returns:
            匹配结果列表
        """
        results = []
        
        for template_name in template_names:
            match_result = self.match_template(image, template_name, confidence_threshold)
            if match_result:
                results.append(match_result)
        
        # 按置信度排序
        results.sort(key=lambda x: x.confidence, reverse=True)
        
        return results
    
    def find_all_matches(self, 
                        image: np.ndarray, 
                        template_name: str,
                        confidence_threshold: Optional[float] = None,
                        overlap_threshold: float = 0.5) -> List[MatchResult]:
        """
        查找图像中所有匹配的位置
        
        Args:
            image: 源图像
            template_name: 模板名称
            confidence_threshold: 置信度阈值
            overlap_threshold: 重叠阈值，用于去除重复匹配
            
        Returns:
            所有匹配结果列表
        """
        template = self.load_template(template_name)
        if template is None:
            return []
        
        if confidence_threshold is None:
            confidence_threshold = self.default_confidence_threshold
        
        try:
            result = cv2.matchTemplate(image, template, cv2.TM_CCOEFF_NORMED)
            locations = np.where(result >= confidence_threshold)
            
            matches = []
            h, w = template.shape[:2]
            
            for pt in zip(*locations[::-1]):  # 转换坐标顺序
                confidence = result[pt[1], pt[0]]
                match = MatchResult(
                    x=pt[0],
                    y=pt[1],
                    width=w,
                    height=h,
                    confidence=confidence,
                    template_name=template_name
                )
                matches.append(match)
            
            # 去除重叠的匹配
            filtered_matches = self._remove_overlapping_matches(matches, overlap_threshold)
            
            # 按置信度排序
            filtered_matches.sort(key=lambda x: x.confidence, reverse=True)
            
            self.logger.debug(f"找到 {len(filtered_matches)} 个匹配: {template_name}")
            return filtered_matches
            
        except Exception as e:
            self.logger.error(f"查找所有匹配异常: {template_name}, 错误: {e}")
            return []
    
    def _remove_overlapping_matches(self, matches: List[MatchResult], overlap_threshold: float) -> List[MatchResult]:
        """
        去除重叠的匹配结果
        
        Args:
            matches: 匹配结果列表
            overlap_threshold: 重叠阈值
            
        Returns:
            去除重叠后的匹配结果列表
        """
        if not matches:
            return []
        
        # 按置信度排序
        matches.sort(key=lambda x: x.confidence, reverse=True)
        
        filtered = []
        for match in matches:
            is_overlapping = False
            
            for existing in filtered:
                # 计算重叠面积
                overlap_area = self._calculate_overlap_area(match, existing)
                match_area = match.width * match.height
                
                if overlap_area / match_area > overlap_threshold:
                    is_overlapping = True
                    break
            
            if not is_overlapping:
                filtered.append(match)
        
        return filtered
    
    def _calculate_overlap_area(self, match1: MatchResult, match2: MatchResult) -> float:
        """
        计算两个匹配结果的重叠面积
        
        Args:
            match1: 匹配结果1
            match2: 匹配结果2
            
        Returns:
            重叠面积
        """
        x1 = max(match1.x, match2.x)
        y1 = max(match1.y, match2.y)
        x2 = min(match1.x + match1.width, match2.x + match2.width)
        y2 = min(match1.y + match1.height, match2.y + match2.height)
        
        if x2 <= x1 or y2 <= y1:
            return 0
        
        return (x2 - x1) * (y2 - y1)
    
    def get_template_info(self) -> Dict[str, Any]:
        """
        获取模板匹配器信息
        
        Returns:
            信息字典
        """
        return {
            "templates_dir": str(self.templates_dir),
            "cached_templates": list(self._template_cache.keys()),
            "default_confidence_threshold": self.default_confidence_threshold,
            "multi_scale_enabled": self.multi_scale_enabled,
            "scale_range": self.scale_range
        }
