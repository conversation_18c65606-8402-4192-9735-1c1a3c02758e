#!/usr/bin/env python3
"""
快速检查模板文件状态
"""

import os

def main():
    print("🔍 Gakumasu-Bot 模板文件状态检查")
    print("=" * 50)
    
    template_dir = "assets/templates"
    
    # 高优先级模板文件
    high_priority_templates = [
        ("main_menu_full.png", "主菜单完整截图"),
        ("produce_setup_title.png", "育成准备界面标题"),
        ("start_produce_button.png", "开始育成按钮"),
        ("vocal_lesson_button.png", "声乐课程按钮"),
        ("dance_lesson_button.png", "舞蹈课程按钮"),
        ("visual_lesson_button.png", "视觉课程按钮"),
        ("mental_lesson_button.png", "精神课程按钮"),
        ("rest_button.png", "休息按钮"),
        ("confirm_button.png", "确认按钮"),
        ("cancel_button.png", "取消按钮")
    ]
    
    # 中优先级模板文件
    medium_priority_templates = [
        ("main_menu_logo.png", "主菜单标识"),
        ("produce_button.png", "育成按钮"),
        ("week_indicator.png", "周数指示器"),
        ("stamina_bar.png", "体力条"),
        ("idol_selection.png", "偶像选择区域"),
        ("battle_ui.png", "战斗界面UI"),
        ("card_hand.png", "手牌区域"),
        ("back_button.png", "返回按钮")
    ]
    
    # 检查目录是否存在
    if not os.path.exists(template_dir):
        print(f"❌ 模板目录不存在: {template_dir}")
        print("请先创建模板目录:")
        print(f"  mkdir -p {template_dir}")
        return
    
    print(f"📁 模板目录: {template_dir}")
    
    # 检查高优先级模板
    print(f"\n🔴 高优先级模板文件:")
    high_existing = 0
    for filename, description in high_priority_templates:
        filepath = os.path.join(template_dir, filename)
        if os.path.exists(filepath):
            print(f"  ✅ {filename:<30} - {description}")
            high_existing += 1
        else:
            print(f"  📋 {filename:<30} - {description} (需要创建)")
    
    # 检查中优先级模板
    print(f"\n🟡 中优先级模板文件:")
    medium_existing = 0
    for filename, description in medium_priority_templates:
        filepath = os.path.join(template_dir, filename)
        if os.path.exists(filepath):
            print(f"  ✅ {filename:<30} - {description}")
            medium_existing += 1
        else:
            print(f"  📋 {filename:<30} - {description} (需要创建)")
    
    # 统计信息
    total_high = len(high_priority_templates)
    total_medium = len(medium_priority_templates)
    total_templates = total_high + total_medium
    total_existing = high_existing + medium_existing
    
    print(f"\n📊 统计信息:")
    print(f"  高优先级: {high_existing}/{total_high} ({high_existing/total_high*100:.1f}%)")
    print(f"  中优先级: {medium_existing}/{total_medium} ({medium_existing/total_medium*100:.1f}%)")
    print(f"  总计: {total_existing}/{total_templates} ({total_existing/total_templates*100:.1f}%)")
    
    # 给出建议
    if high_existing == total_high:
        print(f"\n🎉 所有高优先级模板已完成！系统可以基本运行。")
    elif high_existing >= total_high * 0.7:
        print(f"\n💡 大部分高优先级模板已完成，建议补充剩余模板以提高识别准确率。")
    else:
        print(f"\n⚠️ 高优先级模板不足，建议优先创建以下模板:")
        for filename, description in high_priority_templates:
            filepath = os.path.join(template_dir, filename)
            if not os.path.exists(filepath):
                print(f"    • {filename} - {description}")
    
    print(f"\n📖 详细说明请参考: docs/模板文件路径对照表.md")
    print(f"🛠️ 模板制作指南请参考: README.md 中的模板文件配置章节")

if __name__ == "__main__":
    main()
