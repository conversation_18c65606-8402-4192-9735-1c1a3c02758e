# 游戏截图数据收集工具用户使用指南

**版本：** 1.0  
**日期：** 2025年7月27日  
**适用于：** Gakumasu-Bot 用户

## 一、快速开始

### 1.1 启动工具
1. 运行 Gakumasu-Bot 主程序：`python gui.py`
2. 在浏览器中打开控制面板（通常是 `http://localhost:8000`）
3. 点击导航栏中的"截图工具"进入截图工具界面

### 1.2 界面概览
截图工具界面分为三个主要区域：
- **左侧控制面板**：截图模式选择、参数配置、快速操作
- **中间预览区域**：游戏窗口实时预览、区域选择
- **右侧历史面板**：截图历史记录、搜索筛选、批量操作

## 二、基础功能使用

### 2.1 执行截图

#### 2.1.1 全屏截图
1. 在控制面板中选择"全屏截图"模式
2. 调整图片格式和质量（可选）
3. 点击"立即截图"按钮
4. 截图完成后会在历史面板中显示

#### 2.1.2 窗口截图
1. 确保游戏窗口已打开
2. 选择"窗口截图"模式（默认模式）
3. 点击"立即截图"按钮
4. 工具会自动识别游戏窗口并截图

#### 2.1.3 区域截图
1. 选择"区域截图"模式
2. 在预览区域中拖拽鼠标选择截图区域
3. 可以通过拖拽角落的手柄调整区域大小
4. 点击"立即截图"按钮执行区域截图

### 2.2 实时预览

#### 2.2.1 启动预览
1. 点击控制面板中的"开始预览"按钮
2. 预览区域将显示游戏窗口的实时画面
3. 预览状态会显示为"实时预览"

#### 2.2.2 停止预览
1. 点击"停止预览"按钮
2. 预览流将停止，状态显示为"静态预览"
3. 可以点击"刷新"按钮获取单帧预览

#### 2.2.3 预览设置
- **帧率调整**：在设置中可以调整预览帧率（1-10 FPS）
- **质量优化**：预览会自动优化图像质量以减少网络传输

### 2.3 历史记录管理

#### 2.3.1 查看历史
- 右侧历史面板显示所有截图记录
- 每条记录显示缩略图、文件名、大小、时间等信息
- 点击缩略图可以预览完整图片

#### 2.3.2 搜索和筛选
- **搜索**：在搜索框中输入文件名关键词
- **格式筛选**：选择特定的图片格式（PNG/JPEG/BMP）
- **排序**：历史记录按时间倒序排列（最新的在前）

#### 2.3.3 文件操作
- **预览**：点击眼睛图标预览完整图片
- **下载**：点击下载图标保存到本地
- **删除**：点击删除图标删除单个截图

#### 2.3.4 批量操作
1. 点击"批量"按钮进入批量模式
2. 勾选需要操作的截图
3. 点击"删除选中"进行批量删除
4. 点击"取消"退出批量模式

## 三、高级功能

### 3.1 参数配置

#### 3.1.1 图片格式
- **PNG**：无损压缩，质量最高，文件较大
- **JPEG**：有损压缩，文件较小，可调节质量
- **BMP**：无压缩，文件最大，兼容性最好

#### 3.1.2 图片质量
- 范围：10-100
- 仅对 JPEG 格式有效
- 数值越高质量越好，文件越大

#### 3.1.3 文件前缀
- 自定义截图文件名前缀
- 默认为"screenshot"
- 实际文件名格式：`前缀_时间戳_唯一ID.扩展名`

### 3.2 工具设置

#### 3.2.1 打开设置
点击界面右上角的"设置"按钮打开设置对话框

#### 3.2.2 游戏窗口设置
- **窗口标题**：用于识别游戏窗口的标题
- **后台模式**：启用后可在游戏窗口被遮挡时截图
- **捕获方法**：选择适合的屏幕捕获方法

#### 3.2.3 存储设置
- **存储目录**：截图文件保存位置
- **历史记录数**：最多保留的历史记录数量
- **自动清理**：是否自动删除过期文件
- **清理天数**：文件保留天数

#### 3.2.4 预览设置
- **预览帧率**：实时预览的刷新频率（1-10 FPS）
- 帧率越高越流畅，但消耗更多系统资源

#### 3.2.5 默认设置
- **默认质量**：新截图任务的默认质量
- **默认格式**：新截图任务的默认格式

### 3.3 配置管理

#### 3.3.1 导出配置
1. 在设置对话框中点击"导出配置"
2. 选择保存位置
3. 配置将保存为 JSON 文件

#### 3.3.2 导入配置
1. 点击"导入配置"按钮
2. 选择之前导出的配置文件
3. 配置将自动应用到工具中

#### 3.3.3 恢复默认
点击"恢复默认"按钮可以将所有设置恢复到初始状态

## 四、故障排除

### 4.1 常见问题

#### 4.1.1 截图失败
**问题**：点击截图按钮后提示截图失败
**解决方案**：
1. 确保游戏窗口已打开且可见
2. 检查游戏窗口标题设置是否正确
3. 尝试切换不同的捕获方法
4. 确保有足够的磁盘空间

#### 4.1.2 预览无法显示
**问题**：预览区域显示空白或错误
**解决方案**：
1. 检查网络连接是否正常
2. 刷新浏览器页面
3. 重启 Gakumasu-Bot 程序
4. 检查防火墙设置

#### 4.1.3 区域选择不准确
**问题**：选择的区域与实际截图不符
**解决方案**：
1. 确保游戏窗口没有移动
2. 重新刷新预览
3. 检查显示器缩放设置
4. 使用窗口截图模式替代

#### 4.1.4 文件无法下载
**问题**：点击下载按钮没有反应
**解决方案**：
1. 检查浏览器下载设置
2. 确保文件仍然存在
3. 检查文件权限
4. 尝试刷新页面

### 4.2 性能优化

#### 4.2.1 预览卡顿
- 降低预览帧率
- 关闭其他占用资源的程序
- 检查网络带宽

#### 4.2.2 截图速度慢
- 选择较低的图片质量
- 使用 JPEG 格式替代 PNG
- 清理历史记录

#### 4.2.3 磁盘空间不足
- 定期清理历史记录
- 启用自动清理功能
- 减少历史记录保留数量

### 4.3 错误代码

| 错误代码 | 说明 | 解决方案 |
|---------|------|----------|
| WINDOW_NOT_FOUND | 游戏窗口未找到 | 检查窗口标题设置 |
| CAPTURE_FAILED | 屏幕捕获失败 | 尝试不同捕获方法 |
| SAVE_FAILED | 文件保存失败 | 检查磁盘空间和权限 |
| INVALID_REGION | 无效的截图区域 | 重新选择截图区域 |
| NETWORK_ERROR | 网络连接错误 | 检查网络连接 |

## 五、最佳实践

### 5.1 截图建议
1. **选择合适的格式**：
   - 游戏界面推荐使用 PNG 格式
   - 需要节省空间时使用 JPEG 格式
   - 质量设置在 80-95 之间较为合适

2. **区域截图技巧**：
   - 先启动预览确认游戏窗口位置
   - 精确选择需要的区域
   - 避免包含不必要的界面元素

3. **批量操作**：
   - 定期清理不需要的截图
   - 使用搜索功能快速定位文件
   - 批量删除时注意确认选择

### 5.2 性能建议
1. **预览设置**：
   - 日常使用建议帧率设置为 2-3 FPS
   - 需要精确操作时可临时提高帧率
   - 不使用时及时停止预览

2. **存储管理**：
   - 设置合理的历史记录数量限制
   - 启用自动清理功能
   - 定期备份重要截图

3. **系统资源**：
   - 关闭不必要的后台程序
   - 确保足够的内存和磁盘空间
   - 定期重启程序释放资源

### 5.3 数据管理
1. **文件组织**：
   - 使用有意义的文件前缀
   - 定期导出重要截图
   - 建立备份策略

2. **配置备份**：
   - 定期导出工具配置
   - 记录重要的设置参数
   - 在系统迁移时导入配置

---

**技术支持**：如遇到其他问题，请参考技术文档或联系开发团队。
