"""
场景管理器和工厂类实现
提供场景的创建、管理和导航功能
"""

from typing import Dict, Optional, Any, Callable
import time

from ...utils.logger import get_logger
from ...core.data_structures import GameScene
from .game_scenes import BaseScene, MainMenuScene, ProduceSetupScene, ProduceMainScene


class SceneFactory:
    """场景工厂类"""
    
    def __init__(self, perception_module, action_controller, config_loader):
        self.perception = perception_module
        self.action = action_controller
        self.config_loader = config_loader
        self.logger = get_logger("SceneFactory")
        
        # 场景类映射
        self._scene_classes = {
            GameScene.MAIN_MENU: MainMenuScene,
            GameScene.PRODUCE_SETUP: ProduceSetupScene,
            GameScene.PRODUCE_MAIN: ProduceMainScene,
            # 可以继续添加其他场景类
        }
    
    def create_scene(self, scene_type: GameScene) -> Optional[BaseScene]:
        """
        创建场景实例
        
        Args:
            scene_type: 场景类型
            
        Returns:
            场景实例，如果不支持该场景类型返回None
        """
        scene_class = self._scene_classes.get(scene_type)
        if not scene_class:
            self.logger.error(f"不支持的场景类型: {scene_type.value}")
            return None
        
        try:
            # 从配置文件加载场景配置
            scene_config = self._load_scene_config(scene_type)
            
            # 创建场景实例
            scene = scene_class(
                self.perception,
                self.action,
                scene_config
            )
            
            self.logger.info(f"成功创建场景: {scene_type.value}")
            return scene
            
        except Exception as e:
            self.logger.error(f"创建场景失败: {scene_type.value}, 错误: {e}")
            return None
    
    def _load_scene_config(self, scene_type: GameScene) -> Dict[str, Any]:
        """
        加载场景配置
        
        Args:
            scene_type: 场景类型
            
        Returns:
            场景配置字典
        """
        try:
            # 从配置文件加载UI元素配置
            navigation_config = self.config_loader.get_config('navigation', {})
            ui_elements_config = navigation_config.get('ui_elements', {})
            
            # 根据场景类型获取对应配置
            scene_key = scene_type.value
            scene_config = ui_elements_config.get(scene_key, {})
            
            return scene_config
            
        except Exception as e:
            self.logger.error(f"加载场景配置失败: {scene_type.value}, 错误: {e}")
            return {}
    
    def get_current_scene(self) -> Optional[BaseScene]:
        """
        获取当前场景实例
        
        Returns:
            当前场景实例，如果无法识别返回None
        """
        try:
            # 获取当前游戏状态
            game_state = self.perception.get_game_state()
            current_scene_type = game_state.current_scene
            
            # 创建对应的场景实例
            return self.create_scene(current_scene_type)
            
        except Exception as e:
            self.logger.error(f"获取当前场景失败: {e}")
            return None
    
    def register_scene_class(self, scene_type: GameScene, scene_class: type):
        """
        注册新的场景类
        
        Args:
            scene_type: 场景类型
            scene_class: 场景类
        """
        self._scene_classes[scene_type] = scene_class
        self.logger.info(f"注册场景类: {scene_type.value} -> {scene_class.__name__}")


class SceneManager:
    """场景管理器"""
    
    def __init__(self, perception_module, action_controller, config_loader):
        self.perception = perception_module
        self.action = action_controller
        self.config_loader = config_loader
        self.scene_factory = SceneFactory(perception_module, action_controller, config_loader)
        self.logger = get_logger("SceneManager")
        
        # 场景缓存
        self._scene_cache: Dict[GameScene, BaseScene] = {}
        
        # 导航路径映射
        self._navigation_map: Dict[tuple, Callable] = {}
        self._init_navigation_map()
    
    def _init_navigation_map(self):
        """初始化导航路径映射"""
        self._navigation_map = {
            (GameScene.MAIN_MENU, GameScene.PRODUCE_SETUP): self._navigate_main_to_produce_setup,
            (GameScene.PRODUCE_SETUP, GameScene.PRODUCE_MAIN): self._navigate_setup_to_main,
            # 可以继续添加其他导航路径
        }
    
    def get_scene(self, scene_type: GameScene, use_cache: bool = True) -> Optional[BaseScene]:
        """
        获取场景实例
        
        Args:
            scene_type: 场景类型
            use_cache: 是否使用缓存
            
        Returns:
            场景实例
        """
        if use_cache and scene_type in self._scene_cache:
            return self._scene_cache[scene_type]
        
        scene = self.scene_factory.create_scene(scene_type)
        if scene and use_cache:
            self._scene_cache[scene_type] = scene
        
        return scene
    
    def get_current_scene(self) -> Optional[BaseScene]:
        """获取当前场景实例"""
        return self.scene_factory.get_current_scene()
    
    def navigate_to_scene(self, target_scene: GameScene, timeout: float = 30.0) -> bool:
        """
        导航到指定场景
        
        Args:
            target_scene: 目标场景
            timeout: 导航超时时间
            
        Returns:
            是否导航成功
        """
        self.logger.info(f"开始导航到场景: {target_scene.value}")
        
        start_time = time.time()
        
        try:
            while time.time() - start_time < timeout:
                # 获取当前场景
                current_scene = self.get_current_scene()
                if not current_scene:
                    self.logger.error("无法获取当前场景")
                    time.sleep(1.0)
                    continue
                
                # 如果已经在目标场景
                if current_scene.scene_type == target_scene:
                    self.logger.info("已经在目标场景")
                    return True
                
                # 执行导航逻辑
                if self._execute_navigation(current_scene.scene_type, target_scene):
                    # 等待场景切换
                    time.sleep(2.0)
                    
                    # 验证是否到达目标场景
                    if self._verify_scene_transition(target_scene):
                        self.logger.info(f"成功导航到场景: {target_scene.value}")
                        return True
                else:
                    self.logger.error("导航步骤执行失败")
                    return False
            
            self.logger.error(f"导航超时: {target_scene.value}")
            return False
            
        except Exception as e:
            self.logger.error(f"导航失败: {e}")
            return False
    
    def _execute_navigation(self, from_scene: GameScene, to_scene: GameScene) -> bool:
        """
        执行具体的导航逻辑
        
        Args:
            from_scene: 起始场景
            to_scene: 目标场景
            
        Returns:
            是否导航成功
        """
        navigation_func = self._navigation_map.get((from_scene, to_scene))
        if navigation_func:
            return navigation_func()
        else:
            self.logger.error(f"不支持的导航路径: {from_scene.value} -> {to_scene.value}")
            return False
    
    def _navigate_main_to_produce_setup(self) -> bool:
        """从主菜单导航到育成准备界面"""
        main_menu = self.get_scene(GameScene.MAIN_MENU)
        if not main_menu:
            return False
        
        return main_menu.navigate_to_produce()
    
    def _navigate_setup_to_main(self) -> bool:
        """从育成准备界面导航到育成主界面"""
        produce_setup = self.get_scene(GameScene.PRODUCE_SETUP)
        if not produce_setup:
            return False
        
        return produce_setup.start_produce()
    
    def _verify_scene_transition(self, expected_scene: GameScene, timeout: float = 10.0) -> bool:
        """
        验证场景切换是否成功
        
        Args:
            expected_scene: 期望的场景
            timeout: 验证超时时间
            
        Returns:
            是否成功切换到期望场景
        """
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                game_state = self.perception.get_game_state()
                if game_state.current_scene == expected_scene:
                    return True
                time.sleep(0.5)
            except Exception as e:
                self.logger.debug(f"验证场景切换时出错: {e}")
                time.sleep(0.5)
        
        return False
    
    def register_navigation_path(self, from_scene: GameScene, to_scene: GameScene, navigation_func: Callable):
        """
        注册新的导航路径
        
        Args:
            from_scene: 起始场景
            to_scene: 目标场景
            navigation_func: 导航函数
        """
        self._navigation_map[(from_scene, to_scene)] = navigation_func
        self.logger.info(f"注册导航路径: {from_scene.value} -> {to_scene.value}")
    
    def clear_cache(self):
        """清空场景缓存"""
        self._scene_cache.clear()
        self.logger.info("场景缓存已清空")
    
    def get_scene_info(self, scene_type: GameScene) -> Dict[str, Any]:
        """
        获取场景信息
        
        Args:
            scene_type: 场景类型
            
        Returns:
            场景信息字典
        """
        scene = self.get_scene(scene_type)
        if not scene:
            return {}
        
        info = {
            'scene_type': scene_type.value,
            'is_current': scene.is_current_scene(),
            'ui_elements': list(scene.ui_elements.keys()),
            'visible_elements': list(scene.get_all_visible_elements().keys())
        }
        
        return info
    
    def get_all_scenes_info(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有场景的信息
        
        Returns:
            所有场景信息字典
        """
        all_info = {}
        
        for scene_type in self.scene_factory._scene_classes.keys():
            all_info[scene_type.value] = self.get_scene_info(scene_type)
        
        return all_info


class LegacyCompatibilityAdapter:
    """遗留代码兼容性适配器"""
    
    def __init__(self, scene_manager: SceneManager):
        self.scene_manager = scene_manager
        self.logger = get_logger("LegacyAdapter")
    
    def click_ui_element_by_scene(self, scene_type: GameScene, element_name: str) -> bool:
        """
        通过场景和元素名称点击UI元素（兼容旧接口）
        
        Args:
            scene_type: 场景类型
            element_name: UI元素名称
            
        Returns:
            是否点击成功
        """
        try:
            scene = self.scene_manager.get_scene(scene_type)
            if not scene:
                return False
            
            element = scene.get_ui_element(element_name)
            if not element:
                self.logger.error(f"场景 {scene_type.value} 中未找到元素 {element_name}")
                return False
            
            return element.click()
            
        except Exception as e:
            self.logger.error(f"点击UI元素失败: {e}")
            return False
    
    def navigate_to_scene_legacy(self, target_scene_name: str) -> bool:
        """
        通过场景名称导航（兼容旧接口）
        
        Args:
            target_scene_name: 目标场景名称
            
        Returns:
            是否导航成功
        """
        try:
            # 将字符串转换为GameScene枚举
            target_scene = GameScene(target_scene_name)
            return self.scene_manager.navigate_to_scene(target_scene)
        except ValueError:
            self.logger.error(f"无效的场景名称: {target_scene_name}")
            return False
        except Exception as e:
            self.logger.error(f"导航失败: {e}")
            return False
