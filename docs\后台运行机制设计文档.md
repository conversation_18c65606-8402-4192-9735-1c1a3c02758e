# Gakumasu-Bot 后台运行机制设计文档

## 1. 项目概述

### 1.1 设计目标
为Gakumasu-Bot实现完整的后台运行机制，使bot能够在游戏窗口不在前台的情况下正常工作，不干扰用户的其他操作。

### 1.2 核心需求
- **后台窗口检测与操作**：游戏窗口不在前台时仍能检测和操作
- **后台屏幕捕获**：在游戏窗口被遮挡时正常截取画面
- **后台输入模拟**：向后台窗口发送输入而不影响前台应用
- **配置管理**：提供后台运行模式的配置选项
- **稳定性保证**：确保后台运行的稳定性和可靠性

### 1.3 技术约束
- 基于现有的Windows API（win32gui, win32con等）
- 兼容当前的模块架构（ScreenCapture, InputSimulator等）
- 保持向后兼容性
- 支持gakumas游戏窗口（句柄: 856094, 尺寸: 1081x1921）

## 2. 技术架构设计

### 2.1 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    后台运行控制层                              │
├─────────────────────────────────────────────────────────────┤
│  BackgroundModeManager  │  WindowStateMonitor  │  ConfigManager │
├─────────────────────────────────────────────────────────────┤
│                    核心功能增强层                              │
├─────────────────────────────────────────────────────────────┤
│ EnhancedScreenCapture │ BackgroundInputSimulator │ WindowManager │
├─────────────────────────────────────────────────────────────┤
│                    Windows API 层                            │
├─────────────────────────────────────────────────────────────┤
│  win32gui  │  win32con  │  win32api  │  ctypes  │  mss       │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 核心组件设计

#### 2.2.1 BackgroundModeManager（后台模式管理器）
**职责**：统一管理后台运行模式的启用/禁用和协调各模块
**核心功能**：
- 后台模式状态管理
- 模块间协调
- 性能监控和优化
- 异常处理和恢复

#### 2.2.2 EnhancedScreenCapture（增强屏幕捕获器）
**职责**：扩展现有ScreenCapture，支持后台窗口截图
**核心功能**：
- 后台窗口内容捕获
- 窗口状态智能检测
- 多种捕获方式支持
- 缓存和性能优化

#### 2.2.3 BackgroundInputSimulator（后台输入模拟器）
**职责**：实现向后台窗口发送输入的功能
**核心功能**：
- 后台鼠标操作
- 后台键盘输入
- 输入队列管理
- 防检测机制

#### 2.2.4 WindowStateMonitor（窗口状态监控器）
**职责**：实时监控游戏窗口状态变化
**核心功能**：
- 窗口可见性监控
- 窗口位置变化检测
- 窗口最小化/最大化状态跟踪
- 状态变化事件通知

## 3. 详细实现方案

### 3.1 后台窗口检测与操作

#### 3.1.1 窗口句柄持久化
```python
class EnhancedWindowManager:
    def __init__(self):
        self.window_handle_cache = {}
        self.window_state_cache = {}
        
    def get_persistent_window_handle(self, window_title: str) -> Optional[int]:
        """获取持久化的窗口句柄，支持后台检测"""
        # 1. 检查缓存
        # 2. 验证句柄有效性
        # 3. 重新搜索（如果需要）
        # 4. 更新缓存
```

#### 3.1.2 后台窗口状态检测
```python
def is_window_valid_background(self, hwnd: int) -> bool:
    """检测后台窗口是否有效"""
    # 1. 检查窗口是否存在
    # 2. 检查窗口是否可见（包括被遮挡的情况）
    # 3. 检查窗口进程状态
    # 4. 返回综合判断结果
```

### 3.2 后台屏幕捕获实现

#### 3.2.1 多种捕获方式支持
```python
class BackgroundCaptureMethod(Enum):
    MSS_BACKGROUND = "mss_background"           # MSS后台捕获
    WIN32_PRINTWINDOW = "win32_printwindow"     # PrintWindow API
    WIN32_BITBLT = "win32_bitblt"               # BitBlt API
    DWMAPI_THUMBNAIL = "dwmapi_thumbnail"       # DWM缩略图API
```

#### 3.2.2 智能捕获策略
```python
def capture_background_window(self, hwnd: int) -> Optional[np.ndarray]:
    """智能后台窗口捕获"""
    # 1. 检测窗口状态
    # 2. 选择最佳捕获方式
    # 3. 执行捕获操作
    # 4. 验证捕获结果
    # 5. 返回处理后的图像
```

### 3.3 后台输入模拟实现

#### 3.3.1 后台鼠标操作
```python
def send_background_mouse_click(self, hwnd: int, x: int, y: int, button: str = "left"):
    """向后台窗口发送鼠标点击"""
    # 1. 计算窗口相对坐标
    # 2. 构造消息参数
    # 3. 发送WM_LBUTTONDOWN/WM_LBUTTONUP消息
    # 4. 验证操作结果
```

#### 3.3.2 后台键盘输入
```python
def send_background_key_input(self, hwnd: int, key_code: int, key_char: str = ""):
    """向后台窗口发送键盘输入"""
    # 1. 构造键盘消息
    # 2. 发送WM_KEYDOWN/WM_KEYUP消息
    # 3. 处理特殊键和组合键
    # 4. 验证输入结果
```

### 3.4 配置选项设计

#### 3.4.1 配置文件结构
```yaml
background_mode:
  enabled: true                    # 是否启用后台模式
  capture_method: "auto"           # 捕获方式：auto/mss/printwindow/bitblt
  input_method: "message"          # 输入方式：message/sendkeys
  performance:
    capture_interval: 0.5          # 后台捕获间隔（秒）
    cache_window_handle: true      # 是否缓存窗口句柄
    monitor_window_state: true     # 是否监控窗口状态
  compatibility:
    fallback_to_foreground: true   # 后台失败时是否回退到前台模式
    auto_bring_to_front: false     # 是否自动将窗口置于前台
```

## 4. 实施计划

### 4.1 第一阶段：核心功能实现（预计2-3小时）
1. **增强ScreenCapture模块**
   - 添加后台窗口检测功能
   - 实现PrintWindow API捕获
   - 添加窗口状态缓存机制

2. **创建BackgroundInputSimulator**
   - 实现后台鼠标操作
   - 实现后台键盘输入
   - 添加消息队列管理

### 4.2 第二阶段：配置和集成（预计1-2小时）
1. **配置管理增强**
   - 添加后台模式配置选项
   - 实现配置热重载
   - 添加配置验证

2. **模块集成**
   - 集成到现有架构
   - 添加向后兼容性
   - 实现智能模式切换

### 4.3 第三阶段：测试和优化（预计1-2小时）
1. **功能测试**
   - 后台捕获测试
   - 后台输入测试
   - 稳定性测试

2. **性能优化**
   - 内存使用优化
   - CPU占用优化
   - 响应速度优化

## 5. 风险评估与应对

### 5.1 技术风险
- **风险**：某些游戏可能阻止后台操作
- **应对**：提供多种捕获和输入方式，实现智能回退机制

- **风险**：后台操作可能被安全软件拦截
- **应对**：使用合法的Windows API，添加白名单说明

### 5.2 性能风险
- **风险**：后台运行可能增加系统负担
- **应对**：实现智能调度，添加性能监控和限制

### 5.3 兼容性风险
- **风险**：不同Windows版本API行为差异
- **应对**：添加版本检测，提供兼容性适配

## 6. 成功标准

### 6.1 功能标准
- ✅ 游戏窗口被遮挡时能正常截图
- ✅ 游戏窗口在后台时能正常发送输入
- ✅ 后台操作不影响用户前台应用
- ✅ 配置选项完整且易用

### 6.2 性能标准
- ✅ 后台捕获延迟 < 1秒
- ✅ 内存占用增加 < 50MB
- ✅ CPU占用增加 < 5%

### 6.3 稳定性标准
- ✅ 连续运行24小时无崩溃
- ✅ 窗口状态变化时自动适应
- ✅ 异常情况下能自动恢复

---

**文档版本**: v1.0  
**创建时间**: 2025-07-26  
**最后更新**: 2025-07-26
