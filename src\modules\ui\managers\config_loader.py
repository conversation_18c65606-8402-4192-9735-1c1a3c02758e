"""
UI配置加载器
负责加载和管理UI相关配置
"""

import os
import yaml
import json
from typing import Dict, Optional, Any, List
from pathlib import Path

from ....utils.logger import get_logger
from ....core.data_structures import GameScene
from ..config.scene_config import SceneConfig, NavigationConfig, NavigationStep
from ..config.ui_element_config import UIElementConfig, ButtonConfig, InputFieldConfig, LabelConfig


class UIConfigLoader:
    """UI配置加载器"""
    
    def __init__(self, config_dir: str = "config"):
        """
        初始化配置加载器
        
        Args:
            config_dir: 配置文件目录
        """
        self.config_dir = Path(config_dir)
        self.logger = get_logger("UIConfigLoader")
        
        # 配置缓存
        self._config_cache: Dict[str, Any] = {}
        self._cache_enabled = True
        
        # 配置文件路径
        self.ui_config_file = self.config_dir / "ui_config.yaml"
        self.scene_config_file = self.config_dir / "scene_config.yaml"
        self.navigation_config_file = self.config_dir / "navigation_config.yaml"
        
        # 确保配置目录存在
        self.config_dir.mkdir(exist_ok=True)
    
    def load_scene_config(self, scene_type: GameScene) -> Optional[SceneConfig]:
        """
        加载场景配置
        
        Args:
            scene_type: 场景类型
            
        Returns:
            场景配置，失败返回None
        """
        try:
            # 检查缓存
            cache_key = f"scene_{scene_type.value}"
            if self._cache_enabled and cache_key in self._config_cache:
                return self._config_cache[cache_key]
            
            # 加载配置文件
            scene_configs = self._load_yaml_file(self.scene_config_file)
            if not scene_configs:
                return None
            
            # 获取指定场景的配置
            scene_config_data = scene_configs.get('scenes', {}).get(scene_type.value)
            if not scene_config_data:
                self.logger.warning(f"未找到场景配置: {scene_type.value}")
                return None
            
            # 创建配置对象
            config = self._create_scene_config(scene_type, scene_config_data)
            
            # 添加到缓存
            if config and self._cache_enabled:
                self._config_cache[cache_key] = config
            
            return config
            
        except Exception as e:
            self.logger.error(f"加载场景配置失败: {scene_type.value}, 错误: {e}")
            return None
    
    def _create_scene_config(self, scene_type: GameScene, config_data: Dict[str, Any]) -> SceneConfig:
        """创建场景配置对象"""
        try:
            # 基础配置
            scene_name = config_data.get('scene_name', scene_type.value)
            scene_indicators = config_data.get('scene_indicators', [])
            recognition_confidence = config_data.get('recognition_confidence', 0.8)
            recognition_timeout = config_data.get('recognition_timeout', 10.0)
            
            # UI元素配置
            ui_elements = config_data.get('ui_elements', {})
            
            # 导航配置
            navigation_data = config_data.get('navigation')
            navigation_config = None
            if navigation_data:
                navigation_config = self._create_navigation_config(navigation_data)
            
            # 创建场景配置
            config = SceneConfig(
                scene_type=scene_type,
                scene_name=scene_name,
                ui_elements=ui_elements,
                scene_indicators=scene_indicators,
                recognition_confidence=recognition_confidence,
                recognition_timeout=recognition_timeout,
                navigation=navigation_config
            )
            
            return config
            
        except Exception as e:
            self.logger.error(f"创建场景配置对象失败: {e}")
            raise
    
    def _create_navigation_config(self, navigation_data: Dict[str, Any]) -> NavigationConfig:
        """创建导航配置对象"""
        try:
            from ..config.scene_config import NavigationStrategy
            
            strategy_str = navigation_data.get('strategy', 'smart')
            strategy = NavigationStrategy(strategy_str)
            
            max_navigation_time = navigation_data.get('max_navigation_time', 30.0)
            
            # 导航步骤
            steps_data = navigation_data.get('steps', [])
            steps = []
            for step_data in steps_data:
                step = NavigationStep(
                    action_type=step_data.get('action_type', 'click'),
                    target=step_data.get('target', ''),
                    timeout=step_data.get('timeout', 5.0),
                    retry_count=step_data.get('retry_count', 3),
                    optional=step_data.get('optional', False),
                    description=step_data.get('description', '')
                )
                steps.append(step)
            
            # 回退步骤
            fallback_steps_data = navigation_data.get('fallback_steps', [])
            fallback_steps = []
            for step_data in fallback_steps_data:
                step = NavigationStep(
                    action_type=step_data.get('action_type', 'click'),
                    target=step_data.get('target', ''),
                    timeout=step_data.get('timeout', 5.0),
                    retry_count=step_data.get('retry_count', 3),
                    optional=step_data.get('optional', False),
                    description=step_data.get('description', '')
                )
                fallback_steps.append(step)
            
            config = NavigationConfig(
                strategy=strategy,
                max_navigation_time=max_navigation_time,
                steps=steps,
                fallback_enabled=navigation_data.get('fallback_enabled', True),
                fallback_steps=fallback_steps
            )
            
            return config
            
        except Exception as e:
            self.logger.error(f"创建导航配置对象失败: {e}")
            raise
    
    def load_ui_element_config(self, element_name: str, element_type: str = "button") -> Optional[UIElementConfig]:
        """
        加载UI元素配置
        
        Args:
            element_name: 元素名称
            element_type: 元素类型
            
        Returns:
            UI元素配置，失败返回None
        """
        try:
            # 检查缓存
            cache_key = f"element_{element_name}_{element_type}"
            if self._cache_enabled and cache_key in self._config_cache:
                return self._config_cache[cache_key]
            
            # 加载配置文件
            ui_configs = self._load_yaml_file(self.ui_config_file)
            if not ui_configs:
                return None
            
            # 获取元素配置
            element_configs = ui_configs.get('ui_elements', {})
            element_config_data = element_configs.get(element_name)
            
            if not element_config_data:
                self.logger.warning(f"未找到UI元素配置: {element_name}")
                return None
            
            # 创建配置对象
            config = self._create_ui_element_config(element_name, element_type, element_config_data)
            
            # 添加到缓存
            if config and self._cache_enabled:
                self._config_cache[cache_key] = config
            
            return config
            
        except Exception as e:
            self.logger.error(f"加载UI元素配置失败: {element_name}, 错误: {e}")
            return None
    
    def _create_ui_element_config(self, element_name: str, element_type: str, 
                                 config_data: Dict[str, Any]) -> UIElementConfig:
        """创建UI元素配置对象"""
        try:
            # 基础配置参数
            template_name = config_data.get('template_name', element_name)
            confidence_threshold = config_data.get('confidence_threshold', 0.8)
            timeout = config_data.get('timeout', 5.0)
            retry_count = config_data.get('retry_count', 3)
            enabled = config_data.get('enabled', True)
            
            # 位置配置
            position_data = config_data.get('position')
            position = None
            if position_data:
                from ..config.ui_element_config import Position
                position = Position(position_data.get('x', 0), position_data.get('y', 0))
            
            # 根据元素类型创建相应的配置
            if element_type == "button":
                config = ButtonConfig(
                    template_name=template_name,
                    confidence_threshold=confidence_threshold,
                    timeout=timeout,
                    retry_count=retry_count,
                    enabled=enabled,
                    position=position,
                    click_behavior=config_data.get('click_behavior'),
                    double_click_enabled=config_data.get('double_click_enabled', False),
                    long_press_duration=config_data.get('long_press_duration', 1.0),
                    expected_scene_after_click=config_data.get('expected_scene_after_click')
                )
            elif element_type == "input_field":
                config = InputFieldConfig(
                    template_name=template_name,
                    confidence_threshold=confidence_threshold,
                    timeout=timeout,
                    retry_count=retry_count,
                    enabled=enabled,
                    position=position,
                    clear_before_input=config_data.get('clear_before_input', True),
                    input_delay=config_data.get('input_delay', 0.1),
                    max_length=config_data.get('max_length'),
                    input_method=config_data.get('input_method', 'direct')
                )
            elif element_type == "label":
                config = LabelConfig(
                    template_name=template_name,
                    confidence_threshold=confidence_threshold,
                    timeout=timeout,
                    retry_count=retry_count,
                    enabled=enabled,
                    position=position,
                    text_recognition_enabled=config_data.get('text_recognition_enabled', True),
                    ocr_language=config_data.get('ocr_language', 'ja'),
                    ocr_confidence_threshold=config_data.get('ocr_confidence_threshold', 0.7)
                )
            else:
                # 默认使用基础配置
                config = UIElementConfig(
                    template_name=template_name,
                    confidence_threshold=confidence_threshold,
                    timeout=timeout,
                    retry_count=retry_count,
                    enabled=enabled,
                    position=position
                )
            
            return config
            
        except Exception as e:
            self.logger.error(f"创建UI元素配置对象失败: {e}")
            raise
    
    def _load_yaml_file(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """加载YAML配置文件"""
        try:
            if not file_path.exists():
                self.logger.warning(f"配置文件不存在: {file_path}")
                return None
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
            
            self.logger.debug(f"成功加载配置文件: {file_path}")
            return data
            
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {file_path}, 错误: {e}")
            return None
    
    def save_scene_config(self, scene_type: GameScene, config: SceneConfig) -> bool:
        """保存场景配置"""
        try:
            # 加载现有配置
            scene_configs = self._load_yaml_file(self.scene_config_file) or {'scenes': {}}
            
            # 转换配置对象为字典
            config_dict = self._scene_config_to_dict(config)
            
            # 更新配置
            scene_configs['scenes'][scene_type.value] = config_dict
            
            # 保存文件
            with open(self.scene_config_file, 'w', encoding='utf-8') as f:
                yaml.dump(scene_configs, f, default_flow_style=False, allow_unicode=True)
            
            # 更新缓存
            cache_key = f"scene_{scene_type.value}"
            if self._cache_enabled:
                self._config_cache[cache_key] = config
            
            self.logger.info(f"保存场景配置成功: {scene_type.value}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存场景配置失败: {scene_type.value}, 错误: {e}")
            return False
    
    def _scene_config_to_dict(self, config: SceneConfig) -> Dict[str, Any]:
        """将场景配置对象转换为字典"""
        config_dict = {
            'scene_name': config.scene_name,
            'scene_indicators': config.scene_indicators,
            'recognition_confidence': config.recognition_confidence,
            'recognition_timeout': config.recognition_timeout,
            'ui_elements': config.ui_elements,
            'cache_enabled': config.cache_enabled,
            'preload_ui_elements': config.preload_ui_elements,
            'error_recovery_enabled': config.error_recovery_enabled,
            'max_error_retry': config.max_error_retry,
            'error_recovery_delay': config.error_recovery_delay
        }
        
        # 导航配置
        if config.navigation:
            config_dict['navigation'] = {
                'strategy': config.navigation.strategy.value,
                'max_navigation_time': config.navigation.max_navigation_time,
                'fallback_enabled': config.navigation.fallback_enabled,
                'verify_each_step': config.navigation.verify_each_step,
                'final_verification': config.navigation.final_verification,
                'verification_timeout': config.navigation.verification_timeout,
                'steps': [
                    {
                        'action_type': step.action_type,
                        'target': step.target,
                        'timeout': step.timeout,
                        'retry_count': step.retry_count,
                        'optional': step.optional,
                        'description': step.description
                    }
                    for step in config.navigation.steps
                ],
                'fallback_steps': [
                    {
                        'action_type': step.action_type,
                        'target': step.target,
                        'timeout': step.timeout,
                        'retry_count': step.retry_count,
                        'optional': step.optional,
                        'description': step.description
                    }
                    for step in config.navigation.fallback_steps
                ]
            }
        
        return config_dict
    
    def clear_cache(self):
        """清理配置缓存"""
        self._config_cache.clear()
        self.logger.info("配置缓存已清理")
    
    def set_cache_enabled(self, enabled: bool):
        """设置缓存启用状态"""
        self._cache_enabled = enabled
        if not enabled:
            self.clear_cache()
        self.logger.info(f"配置缓存{'启用' if enabled else '禁用'}")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        return {
            'cache_enabled': self._cache_enabled,
            'cached_items': len(self._config_cache),
            'cache_keys': list(self._config_cache.keys())
        }


class ConfigurationMigrator:
    """配置迁移器 - 将旧配置格式转换为新格式"""
    
    def __init__(self):
        self.logger = get_logger("ConfigurationMigrator")
    
    def migrate_legacy_config(self, legacy_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        将旧配置格式转换为新格式
        
        Args:
            legacy_config: 旧配置格式
            
        Returns:
            新配置格式
        """
        try:
            new_config = {"ui_architecture": {"scenes": {}}}
            
            # 处理导航配置
            navigation_config = legacy_config.get("navigation", {})
            ui_elements = navigation_config.get("ui_elements", {})
            
            for scene_name, scene_config in ui_elements.items():
                new_scene_config = {
                    "scene_name": scene_name.replace('_', ' ').title(),
                    "scene_indicators": scene_config.get("scene_indicators", []),
                    "ui_elements": {}
                }
                
                for element_name, element_config in scene_config.items():
                    if element_name == "scene_indicators":
                        continue
                    
                    if isinstance(element_config, dict) and "template" in element_config:
                        new_element_config = {
                            "template_name": element_config["template"].replace("assets/templates/", "").replace(".png", ""),
                            "confidence_threshold": 0.8,
                            "timeout": 5.0,
                            "retry_count": 3,
                            "enabled": True
                        }
                        
                        # 位置信息
                        if "x" in element_config and "y" in element_config:
                            new_element_config["position"] = {
                                "x": element_config["x"],
                                "y": element_config["y"]
                            }
                        
                        new_scene_config["ui_elements"][element_name] = new_element_config
                
                new_config["ui_architecture"]["scenes"][scene_name] = new_scene_config
            
            self.logger.info("配置迁移完成")
            return new_config
            
        except Exception as e:
            self.logger.error(f"配置迁移失败: {e}")
            return {}
    
    def save_migrated_config(self, migrated_config: Dict[str, Any], output_file: str) -> bool:
        """保存迁移后的配置"""
        try:
            output_path = Path(output_file)
            output_path.parent.mkdir(parents=True, exist_ok=True)

            with open(output_path, 'w', encoding='utf-8') as f:
                yaml.dump(migrated_config, f, default_flow_style=False, allow_unicode=True)

            self.logger.info(f"迁移配置保存成功: {output_file}")
            return True

        except Exception as e:
            self.logger.error(f"保存迁移配置失败: {e}")
            return False
