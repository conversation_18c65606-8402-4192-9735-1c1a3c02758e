<template>
  <div class="control-panel">
    <!-- 截图模式选择 -->
    <el-card header="截图模式" class="panel-card screenshot-mode-card">
      <el-radio-group 
        :model-value="config.mode" 
        @update:model-value="updateConfig('mode', $event)"
        class="mode-selector"
      >
        <el-radio value="fullscreen" class="mode-option">
          <div class="mode-content">
            <el-icon><FullScreen /></el-icon>
            <span>全屏截图</span>
          </div>
        </el-radio>
        <el-radio value="window" class="mode-option">
          <div class="mode-content">
            <el-icon><Monitor /></el-icon>
            <span>窗口截图</span>
          </div>
        </el-radio>
        <el-radio value="region" class="mode-option">
          <div class="mode-content">
            <el-icon><Crop /></el-icon>
            <span>区域截图</span>
          </div>
        </el-radio>
      </el-radio-group>
    </el-card>

    <!-- 截图设置 -->
    <el-card header="截图设置" class="panel-card screenshot-settings-card">
      <el-form label-width="80px" size="small">
        <el-form-item label="图片格式">
          <el-select 
            :model-value="config.format" 
            @update:model-value="updateConfig('format', $event)"
            style="width: 100%"
          >
            <el-option label="PNG" value="png" />
            <el-option label="JPEG" value="jpeg" />
            <el-option label="BMP" value="bmp" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="图片质量">
          <el-slider 
            :model-value="config.quality" 
            @update:model-value="updateConfig('quality', $event)"
            :min="10" 
            :max="100" 
            show-input
            :disabled="config.format === 'png'"
          />
          <div class="quality-hint" v-if="config.format === 'png'">
            PNG格式无损压缩，质量设置无效
          </div>
        </el-form-item>
        
        <el-form-item label="文件前缀">
          <el-input 
            :model-value="config.filename_prefix" 
            @update:model-value="updateConfig('filename_prefix', $event)"
            placeholder="screenshot"
          />
        </el-form-item>
        
        <el-form-item>
          <el-checkbox 
            :model-value="config.save_to_disk" 
            @update:model-value="updateConfig('save_to_disk', $event)"
          >
            保存到磁盘
          </el-checkbox>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 快速操作 -->
    <el-card header="快速操作" class="panel-card quick-actions-card">
      <div class="quick-actions">
        <el-button 
          type="primary" 
          :loading="loading"
          @click="$emit('capture')"
          size="large"
          class="action-button"
        >
          <el-icon><Camera /></el-icon>
          立即截图
        </el-button>
        
        <el-button 
          type="success" 
          @click="$emit('start-preview')"
          size="large"
          class="action-button"
        >
          <el-icon><VideoPlay /></el-icon>
          开始预览
        </el-button>
        
        <el-button 
          type="warning" 
          @click="$emit('stop-preview')"
          size="large"
          class="action-button"
        >
          <el-icon><VideoPause /></el-icon>
          停止预览
        </el-button>
      </div>
    </el-card>

    <!-- 统计信息 -->
    <el-card header="统计信息" class="panel-card stats-info-card">
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-label">总截图数</div>
          <div class="stat-value">{{ stats.total_screenshots || 0 }}</div>
        </div>
        <div class="stat-item">
          <div class="stat-label">存储大小</div>
          <div class="stat-value">{{ formatFileSize(stats.total_size_bytes || 0) }}</div>
        </div>
        <div class="stat-item">
          <div class="stat-label">预览状态</div>
          <div class="stat-value">
            <el-tag :type="stats.preview_active ? 'success' : 'info'" size="small">
              {{ stats.preview_active ? '活动' : '停止' }}
            </el-tag>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-label">存储目录</div>
          <div class="stat-value stat-path">{{ stats.storage_directory || '-' }}</div>
        </div>
      </div>
    </el-card>

    <!-- 区域选择提示 -->
    <el-card v-if="config.mode === 'region'" header="区域选择" class="panel-card">
      <el-alert
        title="区域截图模式"
        type="info"
        :closable="false"
        show-icon
      >
        <template #default>
          在预览区域中拖拽鼠标选择截图区域，选择完成后点击"立即截图"按钮。
        </template>
      </el-alert>
    </el-card>
  </div>
</template>

<script setup>
import { 
  FullScreen, 
  Monitor, 
  Crop, 
  Camera, 
  VideoPlay, 
  VideoPause 
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  config: {
    type: Object,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  },
  stats: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits([
  'config-change',
  'capture',
  'start-preview',
  'stop-preview'
])

// 方法
function updateConfig(key, value) {
  const newConfig = { ...props.config, [key]: value }
  emit('config-change', newConfig)
}

function formatFileSize(bytes) {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
</script>

<style scoped>
.control-panel {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 0.25rem;
  box-sizing: border-box;
}

/* 自定义滚动条样式 */
.control-panel::-webkit-scrollbar {
  width: 0.375rem;
}

.control-panel::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 0.1875rem;
}

.control-panel::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 0.1875rem;
  transition: background 0.3s ease;
}

.control-panel::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Firefox滚动条样式 */
.control-panel {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

.panel-card {
  border-radius: 0.5rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  min-height: 13rem;
}

/* 特定卡片的最小高度设置 */
.screenshot-mode-card {
  min-height: 17rem;
}

.screenshot-settings-card {
  min-height: 17rem;
}

.quick-actions-card {
  min-height: 13rem;
}

.stats-info-card {
  min-height: 13rem;
}

.panel-card :deep(.el-card__header) {
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  font-weight: 600;
  color: #495057;
}

/* 强制Element Plus组件适应容器宽度 */
.panel-card :deep(.el-select) {
  min-width: 0 !important;
  max-width: 100% !important;
}

.panel-card :deep(.el-select .el-input) {
  min-width: 0 !important;
}

.panel-card :deep(.el-slider) {
  min-width: 0 !important;
  max-width: 100% !important;
}

.panel-card :deep(.el-slider__input) {
  min-width: 60px !important; /* 确保输入框最小可用宽度 */
}

.mode-selector {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  width: 100%;
  box-sizing: border-box;
}

/* 重置Element Plus radio组件的默认样式 */
.mode-selector :deep(.el-radio-group) {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

/* 穿透Element Plus样式，确保radio按钮完全填充宽度 */
.mode-selector :deep(.el-radio) {
  width: 100% !important;
  margin-right: 0 !important;
  margin-bottom: 0 !important;
  display: flex !important;
  align-items: center;
  box-sizing: border-box;
  position: relative;
  z-index: 2 !important;
  pointer-events: auto;
}

.mode-selector :deep(.el-radio__input) {
  margin-right: 0.5rem;
  flex-shrink: 0;
  position: relative;
  z-index: 3 !important;
}

.mode-selector :deep(.el-radio__label) {
  flex: 1;
  padding-left: 0 !important;
  padding-right: 0 !important;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.mode-option {
  width: 100% !important;
  margin: 0 !important;
  padding: 0.75rem 1rem;
  border: 2px solid #e9ecef;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  display: flex !important;
  align-items: center;
  min-height: 3rem;
  box-sizing: border-box;
  position: relative;
}

.mode-option:hover {
  border-color: #409eff;
  background: #f0f8ff;
}

.mode-option.is-checked {
  border-color: #409eff;
  background: #e6f3ff;
}

.mode-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  font-size: 0.875rem;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.mode-content .el-icon {
  font-size: 1.125rem;
  color: #409eff;
  flex-shrink: 0;
}

.mode-content span {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 0;
}

.quality-hint {
  font-size: 0.75rem;
  color: #909399;
  margin-top: 0.25rem;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  align-items: stretch;
}

/* 穿透Element Plus样式，重置按钮默认样式 */
.quick-actions :deep(.el-button) {
  margin: 0 !important;
  width: 100%;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 1rem;
}

.action-button {
  width: 100% !important;
  height: 2rem !important;
  font-size: 1rem;
  font-weight: 500;
  margin: 0 !important;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.stat-item {
  text-align: center;
}

.stat-label {
  font-size: 0.75rem;
  color: #909399;
  margin-bottom: 0.25rem;
}

.stat-value {
  font-size: 1rem;
  font-weight: 600;
  color: #303133;
}

.stat-path {
  font-size: 0.75rem;
  word-break: break-all;
  grid-column: 1 / -1;
}

.el-form-item {
  margin-bottom: 1rem;
}

.el-alert {
  border-radius: 0.375rem;
}

/* 移动设备响应式优化 */
@media (max-width: 768px) {
  .panel-card {
    margin-bottom: 0.75rem;
  }

  .mode-selector {
    gap: 0.5rem;
  }

  .mode-selector :deep(.el-radio-group) {
    gap: 0.5rem;
  }

  .mode-option {
    padding: 0.625rem 0.75rem !important;
    min-height: 2.75rem;
  }

  .mode-content {
    font-size: 0.8125rem;
    gap: 0.375rem;
  }

  .mode-content .el-icon {
    font-size: 1rem;
  }

  .mode-content span {
    font-size: 0.8125rem;
  }

  .quick-actions {
    gap: 0.625rem;
  }

  .quick-actions :deep(.el-button) {
    height: 2.75rem;
    font-size: 0.875rem;
    gap: 0.375rem;
  }

  .action-button {
    height: 2.75rem !important;
    font-size: 0.875rem;
    font-weight: 500;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: left;
  }

  .stat-label {
    font-size: 0.6875rem;
    margin-bottom: 0;
  }

  .stat-value {
    font-size: 0.875rem;
  }

  .stat-path {
    grid-column: 1;
    font-size: 0.6875rem;
    text-align: center;
  }
}

/* 中等分辨率下统计信息优化 */
@media (min-width: 1150px) and (max-width: 1250px) {
  .stats-grid {
    grid-template-columns: 1fr; /* 改为单列布局 */
    gap: 0.5rem;
  }

  .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: left;
    padding: 0.375rem 0;
  }

  .stat-label {
    font-size: 0.75rem;
    margin-bottom: 0;
    flex-shrink: 0;
  }

  .stat-value {
    font-size: 0.875rem;
    text-align: right;
  }

  .stat-path {
    font-size: 0.6875rem;
    text-align: right;
    word-break: break-all;
  }
}

/* 平板设备优化 */
@media (min-width: 769px) and (max-width: 1024px) {
  .mode-option {
    padding: 0.6875rem 0.875rem;
    min-height: 2.875rem;
  }

  .mode-content {
    font-size: 0.8125rem;
  }

  .mode-content .el-icon {
    font-size: 1.0625rem;
  }

  .quick-actions {
    gap: 0.6875rem;
  }

  .quick-actions :deep(.el-button) {
    height: 2.875rem;
    font-size: 0.9375rem;
  }

  .action-button {
    height: 2.875rem !important;
    font-size: 0.9375rem;
  }

  .stats-grid {
    gap: 0.875rem;
  }

  .stat-label {
    font-size: 0.6875rem;
  }

  .stat-value {
    font-size: 0.9375rem;
  }

  .stat-path {
    font-size: 0.6875rem;
  }
}
</style>
