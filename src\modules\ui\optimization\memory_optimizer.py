"""
内存优化器
提供内存使用监控、缓存管理和内存泄漏检测功能
"""

import gc
import time
import threading
from typing import Dict, Any, Optional, List, Callable, Union
from enum import Enum
from dataclasses import dataclass, field
from collections import defaultdict
import weakref

from ....utils.logger import get_logger
from ..utils.performance_monitor import measure_block


class CachePolicy(Enum):
    """缓存策略枚举"""
    LRU = "lru"                   # 最近最少使用
    LFU = "lfu"                   # 最少使用频率
    FIFO = "fifo"                 # 先进先出
    TTL = "ttl"                   # 生存时间
    ADAPTIVE = "adaptive"         # 自适应


class MemoryLevel(Enum):
    """内存使用级别枚举"""
    LOW = "low"                   # 低使用
    NORMAL = "normal"             # 正常使用
    HIGH = "high"                 # 高使用
    CRITICAL = "critical"         # 临界使用


@dataclass
class MemoryStats:
    """内存统计信息"""
    total_memory: int = 0         # 总内存（字节）
    used_memory: int = 0          # 已使用内存
    free_memory: int = 0          # 空闲内存
    cache_memory: int = 0         # 缓存内存
    gc_collections: int = 0       # GC回收次数
    memory_level: MemoryLevel = MemoryLevel.NORMAL
    timestamp: float = field(default_factory=time.time)


@dataclass
class CacheEntry:
    """缓存条目"""
    key: str
    value: Any
    size: int
    access_count: int = 0
    last_access: float = field(default_factory=time.time)
    created_time: float = field(default_factory=time.time)
    ttl: Optional[float] = None   # 生存时间（秒）


class CacheManager:
    """缓存管理器"""
    
    def __init__(self, max_size: int = 100 * 1024 * 1024, policy: CachePolicy = CachePolicy.LRU):
        """
        初始化缓存管理器
        
        Args:
            max_size: 最大缓存大小（字节）
            policy: 缓存策略
        """
        self.max_size = max_size
        self.policy = policy
        self.logger = get_logger("CacheManager")
        
        # 缓存存储
        self._cache: Dict[str, CacheEntry] = {}
        self._current_size = 0
        
        # 统计信息
        self._hits = 0
        self._misses = 0
        self._evictions = 0
        
        # 线程安全
        self._lock = threading.RLock()
        
        self.logger.info(f"缓存管理器初始化，最大大小: {max_size} bytes，策略: {policy.value}")
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self._lock:
            if key not in self._cache:
                self._misses += 1
                return None
            
            entry = self._cache[key]
            
            # 检查TTL
            if entry.ttl and (time.time() - entry.created_time) > entry.ttl:
                self._remove_entry(key)
                self._misses += 1
                return None
            
            # 更新访问信息
            entry.access_count += 1
            entry.last_access = time.time()
            
            self._hits += 1
            return entry.value
    
    def put(self, key: str, value: Any, ttl: Optional[float] = None) -> bool:
        """存储缓存值"""
        try:
            with self._lock:
                # 计算值大小
                size = self._calculate_size(value)
                
                # 检查是否超过最大大小
                if size > self.max_size:
                    self.logger.warning(f"缓存项过大，无法存储: {key} ({size} bytes)")
                    return False
                
                # 如果键已存在，先移除
                if key in self._cache:
                    self._remove_entry(key)
                
                # 确保有足够空间
                while self._current_size + size > self.max_size:
                    if not self._evict_entry():
                        break
                
                # 创建缓存条目
                entry = CacheEntry(
                    key=key,
                    value=value,
                    size=size,
                    ttl=ttl
                )
                
                self._cache[key] = entry
                self._current_size += size
                
                return True
                
        except Exception as e:
            self.logger.error(f"存储缓存失败: {e}")
            return False
    
    def remove(self, key: str) -> bool:
        """移除缓存项"""
        with self._lock:
            if key in self._cache:
                self._remove_entry(key)
                return True
            return False
    
    def clear(self):
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self._current_size = 0
            self.logger.info("缓存已清空")
    
    def _remove_entry(self, key: str):
        """移除缓存条目"""
        if key in self._cache:
            entry = self._cache.pop(key)
            self._current_size -= entry.size
    
    def _evict_entry(self) -> bool:
        """根据策略驱逐缓存条目"""
        if not self._cache:
            return False
        
        if self.policy == CachePolicy.LRU:
            # 最近最少使用
            oldest_key = min(self._cache.keys(), key=lambda k: self._cache[k].last_access)
        elif self.policy == CachePolicy.LFU:
            # 最少使用频率
            oldest_key = min(self._cache.keys(), key=lambda k: self._cache[k].access_count)
        elif self.policy == CachePolicy.FIFO:
            # 先进先出
            oldest_key = min(self._cache.keys(), key=lambda k: self._cache[k].created_time)
        elif self.policy == CachePolicy.TTL:
            # 优先移除即将过期的
            current_time = time.time()
            ttl_entries = [(k, e) for k, e in self._cache.items() if e.ttl]
            if ttl_entries:
                oldest_key = min(ttl_entries, key=lambda x: x[1].created_time + x[1].ttl)[0]
            else:
                oldest_key = min(self._cache.keys(), key=lambda k: self._cache[k].created_time)
        else:
            # 默认LRU
            oldest_key = min(self._cache.keys(), key=lambda k: self._cache[k].last_access)
        
        self._remove_entry(oldest_key)
        self._evictions += 1
        return True
    
    def _calculate_size(self, value: Any) -> int:
        """计算值的大小"""
        try:
            import sys
            return sys.getsizeof(value)
        except Exception:
            # 简化估算
            if isinstance(value, str):
                return len(value.encode('utf-8'))
            elif isinstance(value, (list, tuple)):
                return sum(self._calculate_size(item) for item in value)
            elif isinstance(value, dict):
                return sum(self._calculate_size(k) + self._calculate_size(v) for k, v in value.items())
            else:
                return 64  # 默认大小
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        with self._lock:
            total_requests = self._hits + self._misses
            hit_rate = (self._hits / total_requests) if total_requests > 0 else 0.0
            
            return {
                "entries": len(self._cache),
                "current_size": self._current_size,
                "max_size": self.max_size,
                "usage_ratio": self._current_size / self.max_size,
                "hits": self._hits,
                "misses": self._misses,
                "hit_rate": hit_rate,
                "evictions": self._evictions,
                "policy": self.policy.value
            }


class MemoryOptimizer:
    """内存优化器"""
    
    def __init__(self, monitoring_interval: float = 30.0):
        """
        初始化内存优化器
        
        Args:
            monitoring_interval: 监控间隔（秒）
        """
        self.monitoring_interval = monitoring_interval
        self.logger = get_logger("MemoryOptimizer")
        
        # 缓存管理器
        self.cache_managers: Dict[str, CacheManager] = {}
        
        # 内存监控
        self._monitoring = False
        self._monitor_thread: Optional[threading.Thread] = None
        self._memory_stats: List[MemoryStats] = []
        self._max_stats_history = 1000
        
        # 内存阈值
        self.memory_thresholds = {
            MemoryLevel.LOW: 0.3,      # 30%
            MemoryLevel.NORMAL: 0.6,   # 60%
            MemoryLevel.HIGH: 0.8,     # 80%
            MemoryLevel.CRITICAL: 0.95 # 95%
        }
        
        # 弱引用跟踪
        self._tracked_objects: Dict[str, weakref.WeakSet] = defaultdict(weakref.WeakSet)
        
        # 事件回调
        self.on_memory_warning: Optional[Callable] = None
        self.on_memory_critical: Optional[Callable] = None
        self.on_gc_triggered: Optional[Callable] = None
        
        self.logger.info("内存优化器初始化完成")
    
    def start_monitoring(self):
        """开始内存监控"""
        if self._monitoring:
            return
        
        self._monitoring = True
        self._monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self._monitor_thread.start()
        
        self.logger.info("内存监控已启动")
    
    def stop_monitoring(self):
        """停止内存监控"""
        self._monitoring = False
        if self._monitor_thread and self._monitor_thread.is_alive():
            self._monitor_thread.join(timeout=5.0)
        
        self.logger.info("内存监控已停止")
    
    def _monitoring_loop(self):
        """监控循环"""
        while self._monitoring:
            try:
                stats = self.get_memory_stats()
                self._memory_stats.append(stats)
                
                # 保持历史记录在限制内
                if len(self._memory_stats) > self._max_stats_history:
                    self._memory_stats = self._memory_stats[-self._max_stats_history//2:]
                
                # 检查内存级别
                self._check_memory_level(stats)
                
                time.sleep(self.monitoring_interval)
                
            except Exception as e:
                self.logger.error(f"内存监控异常: {e}")
                time.sleep(5.0)
    
    def _check_memory_level(self, stats: MemoryStats):
        """检查内存使用级别"""
        if stats.memory_level == MemoryLevel.CRITICAL:
            if self.on_memory_critical:
                self.on_memory_critical(stats)
            self._trigger_emergency_cleanup()
        elif stats.memory_level == MemoryLevel.HIGH:
            if self.on_memory_warning:
                self.on_memory_warning(stats)
            self._trigger_cleanup()
    
    def get_memory_stats(self) -> MemoryStats:
        """获取内存统计"""
        try:
            import psutil
            import os
            
            # 获取进程内存信息
            process = psutil.Process(os.getpid())
            memory_info = process.memory_info()
            
            # 获取系统内存信息
            system_memory = psutil.virtual_memory()
            
            used_memory = memory_info.rss
            total_memory = system_memory.total
            usage_ratio = used_memory / total_memory
            
            # 确定内存级别
            memory_level = MemoryLevel.LOW
            for level, threshold in sorted(self.memory_thresholds.items(), 
                                         key=lambda x: x[1], reverse=True):
                if usage_ratio >= threshold:
                    memory_level = level
                    break
            
            # 计算缓存内存
            cache_memory = sum(cm._current_size for cm in self.cache_managers.values())
            
            return MemoryStats(
                total_memory=total_memory,
                used_memory=used_memory,
                free_memory=total_memory - used_memory,
                cache_memory=cache_memory,
                gc_collections=len(gc.get_stats()) if hasattr(gc, 'get_stats') else 0,
                memory_level=memory_level
            )
            
        except ImportError:
            # 简化版本，不依赖psutil
            return MemoryStats(
                total_memory=1024*1024*1024,  # 假设1GB
                used_memory=512*1024*1024,    # 假设512MB
                free_memory=512*1024*1024,
                memory_level=MemoryLevel.NORMAL
            )
        except Exception as e:
            self.logger.error(f"获取内存统计失败: {e}")
            return MemoryStats()
    
    def create_cache_manager(self, name: str, max_size: int = 50*1024*1024, 
                           policy: CachePolicy = CachePolicy.LRU) -> CacheManager:
        """创建缓存管理器"""
        cache_manager = CacheManager(max_size, policy)
        self.cache_managers[name] = cache_manager
        
        self.logger.info(f"创建缓存管理器: {name}")
        return cache_manager
    
    def get_cache_manager(self, name: str) -> Optional[CacheManager]:
        """获取缓存管理器"""
        return self.cache_managers.get(name)
    
    def remove_cache_manager(self, name: str):
        """移除缓存管理器"""
        if name in self.cache_managers:
            self.cache_managers[name].clear()
            del self.cache_managers[name]
            self.logger.info(f"移除缓存管理器: {name}")
    
    def track_object(self, obj: Any, category: str = "default"):
        """跟踪对象"""
        try:
            self._tracked_objects[category].add(obj)
        except TypeError:
            # 对象不支持弱引用
            pass
    
    def get_tracked_objects_count(self, category: str = None) -> Dict[str, int]:
        """获取跟踪对象数量"""
        if category:
            return {category: len(self._tracked_objects[category])}
        else:
            return {cat: len(objects) for cat, objects in self._tracked_objects.items()}
    
    def force_garbage_collection(self) -> int:
        """强制垃圾回收"""
        try:
            with measure_block("garbage_collection"):
                collected = gc.collect()
                
                if self.on_gc_triggered:
                    self.on_gc_triggered(collected)
                
                self.logger.info(f"垃圾回收完成，回收对象数: {collected}")
                return collected
                
        except Exception as e:
            self.logger.error(f"垃圾回收失败: {e}")
            return 0
    
    def _trigger_cleanup(self):
        """触发清理"""
        self.logger.info("触发内存清理")
        
        # 清理缓存
        for cache_manager in self.cache_managers.values():
            # 清理过期项
            self._cleanup_expired_cache(cache_manager)
        
        # 强制垃圾回收
        self.force_garbage_collection()
    
    def _trigger_emergency_cleanup(self):
        """触发紧急清理"""
        self.logger.warning("触发紧急内存清理")
        
        # 清理所有缓存
        for cache_manager in self.cache_managers.values():
            cache_manager.clear()
        
        # 多次垃圾回收
        for _ in range(3):
            self.force_garbage_collection()
            time.sleep(0.1)
    
    def _cleanup_expired_cache(self, cache_manager: CacheManager):
        """清理过期缓存"""
        current_time = time.time()
        expired_keys = []
        
        with cache_manager._lock:
            for key, entry in cache_manager._cache.items():
                if entry.ttl and (current_time - entry.created_time) > entry.ttl:
                    expired_keys.append(key)
        
        for key in expired_keys:
            cache_manager.remove(key)
        
        if expired_keys:
            self.logger.debug(f"清理过期缓存项: {len(expired_keys)}")
    
    def optimize_memory_usage(self):
        """优化内存使用"""
        try:
            with measure_block("memory_optimization"):
                # 获取当前内存状态
                stats = self.get_memory_stats()
                
                if stats.memory_level in [MemoryLevel.HIGH, MemoryLevel.CRITICAL]:
                    # 清理缓存
                    total_freed = 0
                    for name, cache_manager in self.cache_managers.items():
                        old_size = cache_manager._current_size
                        
                        # 根据内存级别决定清理程度
                        if stats.memory_level == MemoryLevel.CRITICAL:
                            cache_manager.clear()
                        else:
                            # 清理一半最少使用的项
                            entries_to_remove = len(cache_manager._cache) // 2
                            for _ in range(entries_to_remove):
                                if not cache_manager._evict_entry():
                                    break
                        
                        freed = old_size - cache_manager._current_size
                        total_freed += freed
                        
                        self.logger.info(f"缓存{name}释放内存: {freed} bytes")
                    
                    # 垃圾回收
                    collected = self.force_garbage_collection()
                    
                    self.logger.info(f"内存优化完成，释放缓存: {total_freed} bytes，回收对象: {collected}")
                
        except Exception as e:
            self.logger.error(f"内存优化失败: {e}")
    
    def get_optimization_report(self) -> Dict[str, Any]:
        """获取优化报告"""
        current_stats = self.get_memory_stats()
        
        # 缓存统计
        cache_stats = {}
        total_cache_size = 0
        for name, cache_manager in self.cache_managers.items():
            stats = cache_manager.get_stats()
            cache_stats[name] = stats
            total_cache_size += stats["current_size"]
        
        # 跟踪对象统计
        tracked_stats = self.get_tracked_objects_count()
        
        return {
            "memory_stats": {
                "total_memory": current_stats.total_memory,
                "used_memory": current_stats.used_memory,
                "free_memory": current_stats.free_memory,
                "memory_level": current_stats.memory_level.value,
                "usage_ratio": current_stats.used_memory / current_stats.total_memory
            },
            "cache_stats": cache_stats,
            "total_cache_size": total_cache_size,
            "tracked_objects": tracked_stats,
            "monitoring_active": self._monitoring,
            "stats_history_length": len(self._memory_stats)
        }
    
    def set_memory_thresholds(self, **thresholds):
        """设置内存阈值"""
        for level_name, threshold in thresholds.items():
            try:
                level = MemoryLevel(level_name)
                self.memory_thresholds[level] = threshold
            except ValueError:
                self.logger.warning(f"无效的内存级别: {level_name}")
    
    def __del__(self):
        """析构函数"""
        self.stop_monitoring()
