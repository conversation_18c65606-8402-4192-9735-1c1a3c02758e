"""
按钮类实现
使用传统Class处理复杂点击逻辑
"""

import time
from typing import Optional, Callable, Any

from ....utils.logger import get_logger
from ....core.data_structures import GameScene
from ..base.base_ui_element import BaseUIElement
from ..config.ui_element_config import ButtonConfig, UIElementConfig


class Button(BaseUIElement):
    """通用按钮类 - 使用传统Class处理复杂逻辑"""

    def __init__(self, config: ButtonConfig, perception_module, action_controller):
        """
        初始化按钮
        
        Args:
            config: 按钮配置
            perception_module: 感知模块
            action_controller: 行动控制器
        """
        super().__init__(config, perception_module, action_controller)
        self.button_config = config  # 类型提示用的强类型引用
        
        # 按钮特定状态
        self._click_count = 0
        self._last_click_time = 0.0
        self._double_click_window = 0.5  # 双击时间窗口

    def click(self) -> bool:
        """
        复杂的点击逻辑
        
        Returns:
            是否点击成功
        """
        start_time = time.time()
        
        try:
            # 执行预点击检查
            if not self._pre_click_validation():
                return False

            # 检查双击逻辑
            if self.button_config.double_click_enabled:
                if self._should_perform_double_click():
                    return self._execute_double_click()

            # 执行单击操作
            success = self._execute_click_with_retry()

            # 执行后置处理
            if success:
                self._post_click_processing(start_time)

            return success

        except Exception as e:
            self.logger.error(f"按钮点击失败: {e}")
            self._record_performance('click', start_time, False, str(e))
            return False

    def _pre_click_validation(self) -> bool:
        """预点击验证"""
        if not self._pre_action_validation():
            return False

        # 检查点击间隔
        current_time = time.time()
        if (self._last_click_time > 0 and 
            current_time - self._last_click_time < self.button_config.click_delay):
            self.logger.debug("点击间隔太短，跳过此次点击")
            return False

        return True

    def _should_perform_double_click(self) -> bool:
        """判断是否应该执行双击"""
        current_time = time.time()
        if (self._last_click_time > 0 and 
            current_time - self._last_click_time < self._double_click_window):
            return True
        return False

    def _execute_double_click(self) -> bool:
        """执行双击操作"""
        self.logger.info(f"执行双击: {self.config.template_name}")
        
        # 执行两次快速点击
        success1 = self._execute_single_click()
        if not success1:
            return False
        
        time.sleep(0.1)  # 短暂间隔
        success2 = self._execute_single_click()
        
        return success1 and success2

    def _execute_click_with_retry(self) -> bool:
        """带重试的点击执行"""
        for attempt in range(self.config.retry_count):
            if self._execute_single_click():
                return True
            
            if attempt < self.config.retry_count - 1:
                retry_delay = 0.1 * (attempt + 1)  # 递增延迟
                self.logger.debug(f"点击失败，{retry_delay}秒后重试 (尝试 {attempt + 1}/{self.config.retry_count})")
                time.sleep(retry_delay)

        return False

    def _execute_single_click(self) -> bool:
        """执行单次点击"""
        try:
            # 使用现有的行动控制器执行点击
            success = self.action.click_ui_element(
                self.config.template_name,
                confidence_threshold=self.config.confidence_threshold,
                timeout=self.config.timeout
            )
            
            if success:
                self.logger.debug(f"单击成功: {self.config.template_name}")
            else:
                self.logger.debug(f"单击失败: {self.config.template_name}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"执行单击时出错: {e}")
            return False

    def _post_click_processing(self, start_time: float):
        """点击后处理"""
        current_time = time.time()
        self._last_click_time = current_time
        self._click_count += 1
        
        # 记录性能
        self._record_performance('click', start_time, True)
        
        # 执行后置处理
        self._post_action_processing(True)
        
        # 等待点击延迟
        if self.button_config.click_delay > 0:
            time.sleep(self.button_config.click_delay)
        
        # 验证点击结果
        if self.button_config.verify_click_result:
            self._verify_click_result()

    def _verify_click_result(self) -> bool:
        """验证点击结果"""
        if not self.button_config.expected_scene_after_click:
            return True
        
        try:
            # 等待预期场景出现
            expected_scene = GameScene(self.button_config.expected_scene_after_click)
            
            # 简单的场景验证（实际实现可能需要更复杂的逻辑）
            start_time = time.time()
            timeout = self.config.verification_timeout
            
            while time.time() - start_time < timeout:
                current_state = self.perception.get_game_state()
                if current_state.current_scene == expected_scene:
                    self.logger.info(f"点击验证成功，已切换到场景: {expected_scene.value}")
                    return True
                time.sleep(0.1)
            
            self.logger.warning(f"点击验证失败，未能切换到预期场景: {expected_scene.value}")
            return False
            
        except Exception as e:
            self.logger.error(f"点击验证过程出错: {e}")
            return False

    def long_press(self, duration: Optional[float] = None) -> bool:
        """
        长按操作
        
        Args:
            duration: 长按持续时间，默认使用配置值
            
        Returns:
            是否成功
        """
        if duration is None:
            duration = self.button_config.long_press_duration
        
        self.logger.info(f"执行长按: {self.config.template_name}, 持续时间: {duration}秒")
        
        try:
            # 获取按钮位置
            position = self.get_position()
            if not position:
                self.logger.error("无法获取按钮位置，长按失败")
                return False
            
            # 执行长按（需要行动控制器支持）
            if hasattr(self.action, 'long_press'):
                return self.action.long_press(position.x, position.y, duration)
            else:
                self.logger.warning("行动控制器不支持长按操作")
                return False
                
        except Exception as e:
            self.logger.error(f"长按操作失败: {e}")
            return False

    def get_click_stats(self) -> dict:
        """获取点击统计"""
        return {
            'total_clicks': self._click_count,
            'last_click_time': self._last_click_time,
            'double_click_enabled': self.button_config.double_click_enabled,
            'click_delay': self.button_config.click_delay
        }

    def reset_click_stats(self):
        """重置点击统计"""
        self._click_count = 0
        self._last_click_time = 0.0


class EnhancedButton(Button):
    """增强按钮类 - 提供更多高级功能"""

    def __init__(self, config: ButtonConfig, perception_module, action_controller,
                 custom_click_behavior: Optional[Callable] = None):
        """
        初始化增强按钮
        
        Args:
            config: 按钮配置
            perception_module: 感知模块
            action_controller: 行动控制器
            custom_click_behavior: 自定义点击行为
        """
        super().__init__(config, perception_module, action_controller)
        self.custom_click_behavior = custom_click_behavior
        
        # 增强功能状态
        self._hover_enabled = False
        self._animation_detection = False

    def click_with_custom_behavior(self) -> bool:
        """使用自定义行为的点击"""
        if not self.custom_click_behavior:
            return self.click()
        
        try:
            self.logger.info(f"执行自定义点击行为: {self.config.template_name}")
            
            # 执行预检查
            if not self._pre_click_validation():
                return False
            
            # 执行自定义行为
            result = self.custom_click_behavior()
            
            # 记录结果
            if result:
                self._post_click_processing(time.time())
            
            return result
            
        except Exception as e:
            self.logger.error(f"自定义点击行为失败: {e}")
            return False

    def hover(self, duration: float = 1.0) -> bool:
        """
        悬停操作
        
        Args:
            duration: 悬停持续时间
            
        Returns:
            是否成功
        """
        try:
            position = self.get_position()
            if not position:
                return False
            
            self.logger.debug(f"悬停在按钮: {self.config.template_name}")
            
            # 执行悬停（需要行动控制器支持）
            if hasattr(self.action, 'hover'):
                return self.action.hover(position.x, position.y, duration)
            else:
                self.logger.warning("行动控制器不支持悬停操作")
                return False
                
        except Exception as e:
            self.logger.error(f"悬停操作失败: {e}")
            return False

    def wait_for_clickable(self, timeout: float = 10.0) -> bool:
        """
        等待按钮变为可点击状态
        
        Args:
            timeout: 等待超时时间
            
        Returns:
            是否变为可点击
        """
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if self.is_visible() and self.is_enabled():
                return True
            time.sleep(0.1)
        
        return False
