"""
自动化引擎
提供重复任务自动执行、条件触发和规则引擎功能
"""

import time
import json
from typing import List, Dict, Any, Optional, Callable, Union
from enum import Enum
from dataclasses import dataclass, field
from collections import defaultdict
import threading
import asyncio

from ....utils.logger import get_logger
from ....core.data_structures import GameScene
from ..utils.performance_monitor import measure_block


class StepType(Enum):
    """步骤类型枚举"""
    ACTION = "action"              # 执行行动
    CONDITION = "condition"        # 条件判断
    LOOP = "loop"                 # 循环
    WAIT = "wait"                 # 等待
    DECISION = "decision"         # 决策
    VALIDATION = "validation"     # 验证


class ExecutionStatus(Enum):
    """执行状态枚举"""
    PENDING = "pending"           # 等待执行
    RUNNING = "running"           # 正在执行
    SUCCESS = "success"           # 执行成功
    FAILED = "failed"             # 执行失败
    SKIPPED = "skipped"           # 跳过执行
    CANCELLED = "cancelled"       # 取消执行


class TriggerType(Enum):
    """触发类型枚举"""
    MANUAL = "manual"             # 手动触发
    SCHEDULED = "scheduled"       # 定时触发
    CONDITION = "condition"       # 条件触发
    EVENT = "event"               # 事件触发


@dataclass
class WorkflowStep:
    """工作流步骤"""
    id: str
    name: str
    step_type: StepType
    action: str
    parameters: Dict[str, Any] = field(default_factory=dict)
    conditions: List[Dict[str, Any]] = field(default_factory=list)
    retry_count: int = 3
    timeout: float = 30.0
    on_success: Optional[str] = None
    on_failure: Optional[str] = None
    description: str = ""


@dataclass
class ExecutionResult:
    """执行结果"""
    step_id: str
    status: ExecutionStatus
    start_time: float
    end_time: Optional[float] = None
    duration: Optional[float] = None
    result_data: Dict[str, Any] = field(default_factory=dict)
    error_message: Optional[str] = None
    retry_count: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)


class AutomationEngine:
    """自动化引擎"""
    
    def __init__(self, max_concurrent_workflows: int = 5):
        """
        初始化自动化引擎
        
        Args:
            max_concurrent_workflows: 最大并发工作流数量
        """
        self.max_concurrent_workflows = max_concurrent_workflows
        self.logger = get_logger("AutomationEngine")
        
        # 执行状态
        self._running_workflows: Dict[str, Dict[str, Any]] = {}
        self._execution_history: List[ExecutionResult] = []
        self._workflow_queue: List[Dict[str, Any]] = []
        
        # 注册的行动处理器
        self._action_handlers: Dict[str, Callable] = {}
        self._condition_evaluators: Dict[str, Callable] = {}
        
        # 规则引擎
        self._rules: List[Dict[str, Any]] = []
        self._triggers: Dict[str, Dict[str, Any]] = {}
        
        # 统计数据
        self._stats = {
            "total_executions": 0,
            "successful_executions": 0,
            "failed_executions": 0,
            "average_execution_time": 0.0,
            "active_workflows": 0
        }
        
        # 线程控制
        self._executor_thread: Optional[threading.Thread] = None
        self._stop_event = threading.Event()
        self._running = False
        
        # 注册默认处理器
        self._register_default_handlers()
        
        self.logger.info("自动化引擎初始化完成")
    
    def start(self):
        """启动自动化引擎"""
        if self._running:
            self.logger.warning("自动化引擎已在运行")
            return
        
        self._running = True
        self._stop_event.clear()
        
        # 启动执行线程
        self._executor_thread = threading.Thread(target=self._execution_loop, daemon=True)
        self._executor_thread.start()
        
        self.logger.info("自动化引擎已启动")
    
    def stop(self):
        """停止自动化引擎"""
        if not self._running:
            return
        
        self._running = False
        self._stop_event.set()
        
        # 等待执行线程结束
        if self._executor_thread and self._executor_thread.is_alive():
            self._executor_thread.join(timeout=5.0)
        
        # 取消所有运行中的工作流
        for workflow_id in list(self._running_workflows.keys()):
            self._cancel_workflow(workflow_id)
        
        self.logger.info("自动化引擎已停止")
    
    def execute_workflow(self, steps: List[WorkflowStep], workflow_id: Optional[str] = None,
                        priority: int = 0, context: Dict[str, Any] = None) -> str:
        """
        执行工作流
        
        Args:
            steps: 工作流步骤列表
            workflow_id: 工作流ID
            priority: 优先级
            context: 执行上下文
            
        Returns:
            工作流ID
        """
        try:
            if not workflow_id:
                workflow_id = f"workflow_{int(time.time() * 1000)}"
            
            workflow = {
                "id": workflow_id,
                "steps": steps,
                "priority": priority,
                "context": context or {},
                "created_time": time.time(),
                "status": ExecutionStatus.PENDING
            }
            
            # 检查并发限制
            if len(self._running_workflows) >= self.max_concurrent_workflows:
                # 添加到队列
                self._workflow_queue.append(workflow)
                self.logger.info(f"工作流{workflow_id}已添加到队列")
            else:
                # 直接执行
                self._start_workflow_execution(workflow)
            
            return workflow_id
            
        except Exception as e:
            self.logger.error(f"执行工作流失败: {e}")
            return ""
    
    def _execution_loop(self):
        """执行循环"""
        while self._running and not self._stop_event.is_set():
            try:
                # 处理队列中的工作流
                self._process_workflow_queue()
                
                # 检查运行中的工作流
                self._check_running_workflows()
                
                # 检查触发器
                self._check_triggers()
                
                # 短暂休眠
                time.sleep(0.1)
                
            except Exception as e:
                self.logger.error(f"执行循环异常: {e}")
                time.sleep(1.0)
    
    def _process_workflow_queue(self):
        """处理工作流队列"""
        if not self._workflow_queue:
            return
        
        # 检查是否有空闲槽位
        available_slots = self.max_concurrent_workflows - len(self._running_workflows)
        if available_slots <= 0:
            return
        
        # 按优先级排序
        self._workflow_queue.sort(key=lambda w: w["priority"], reverse=True)
        
        # 启动工作流
        for _ in range(min(available_slots, len(self._workflow_queue))):
            workflow = self._workflow_queue.pop(0)
            self._start_workflow_execution(workflow)
    
    def _start_workflow_execution(self, workflow: Dict[str, Any]):
        """开始工作流执行"""
        workflow_id = workflow["id"]
        workflow["status"] = ExecutionStatus.RUNNING
        workflow["start_time"] = time.time()
        workflow["current_step"] = 0
        workflow["execution_results"] = []
        
        self._running_workflows[workflow_id] = workflow
        self._stats["active_workflows"] = len(self._running_workflows)
        
        self.logger.info(f"开始执行工作流: {workflow_id}")
    
    def _check_running_workflows(self):
        """检查运行中的工作流"""
        completed_workflows = []
        
        for workflow_id, workflow in self._running_workflows.items():
            try:
                if self._execute_next_step(workflow):
                    # 工作流完成
                    completed_workflows.append(workflow_id)
                    
            except Exception as e:
                self.logger.error(f"工作流{workflow_id}执行异常: {e}")
                workflow["status"] = ExecutionStatus.FAILED
                workflow["error_message"] = str(e)
                completed_workflows.append(workflow_id)
        
        # 清理完成的工作流
        for workflow_id in completed_workflows:
            self._complete_workflow(workflow_id)
    
    def _execute_next_step(self, workflow: Dict[str, Any]) -> bool:
        """
        执行下一步
        
        Returns:
            是否工作流完成
        """
        steps = workflow["steps"]
        current_step_idx = workflow["current_step"]
        
        if current_step_idx >= len(steps):
            # 所有步骤完成
            workflow["status"] = ExecutionStatus.SUCCESS
            return True
        
        step = steps[current_step_idx]
        
        # 执行步骤
        result = self._execute_step(step, workflow["context"])
        workflow["execution_results"].append(result)
        
        # 根据执行结果决定下一步
        if result.status == ExecutionStatus.SUCCESS:
            if step.on_success:
                # 跳转到指定步骤
                next_step_idx = self._find_step_index(steps, step.on_success)
                workflow["current_step"] = next_step_idx if next_step_idx >= 0 else current_step_idx + 1
            else:
                workflow["current_step"] = current_step_idx + 1
        elif result.status == ExecutionStatus.FAILED:
            if step.on_failure:
                # 跳转到失败处理步骤
                next_step_idx = self._find_step_index(steps, step.on_failure)
                workflow["current_step"] = next_step_idx if next_step_idx >= 0 else len(steps)
            else:
                # 工作流失败
                workflow["status"] = ExecutionStatus.FAILED
                return True
        else:
            # 其他状态，继续下一步
            workflow["current_step"] = current_step_idx + 1
        
        return False
    
    def _execute_step(self, step: WorkflowStep, context: Dict[str, Any]) -> ExecutionResult:
        """执行单个步骤"""
        result = ExecutionResult(
            step_id=step.id,
            status=ExecutionStatus.RUNNING,
            start_time=time.time()
        )
        
        try:
            with measure_block(f"step_execution_{step.step_type.value}"):
                # 检查前置条件
                if step.conditions and not self._evaluate_conditions(step.conditions, context):
                    result.status = ExecutionStatus.SKIPPED
                    result.end_time = time.time()
                    result.duration = result.end_time - result.start_time
                    return result
                
                # 根据步骤类型执行
                if step.step_type == StepType.ACTION:
                    success = self._execute_action(step, context, result)
                elif step.step_type == StepType.CONDITION:
                    success = self._execute_condition(step, context, result)
                elif step.step_type == StepType.WAIT:
                    success = self._execute_wait(step, context, result)
                elif step.step_type == StepType.LOOP:
                    success = self._execute_loop(step, context, result)
                elif step.step_type == StepType.DECISION:
                    success = self._execute_decision(step, context, result)
                elif step.step_type == StepType.VALIDATION:
                    success = self._execute_validation(step, context, result)
                else:
                    success = False
                    result.error_message = f"未知的步骤类型: {step.step_type}"
                
                result.status = ExecutionStatus.SUCCESS if success else ExecutionStatus.FAILED
                
        except Exception as e:
            result.status = ExecutionStatus.FAILED
            result.error_message = str(e)
            self.logger.error(f"步骤{step.id}执行失败: {e}")
        
        finally:
            result.end_time = time.time()
            result.duration = result.end_time - result.start_time
            
            # 记录执行历史
            self._execution_history.append(result)
            self._stats["total_executions"] += 1
            
            if result.status == ExecutionStatus.SUCCESS:
                self._stats["successful_executions"] += 1
            elif result.status == ExecutionStatus.FAILED:
                self._stats["failed_executions"] += 1
            
            # 更新平均执行时间
            if self._stats["total_executions"] > 0:
                total_time = sum(r.duration or 0 for r in self._execution_history[-100:])  # 最近100次
                count = min(100, len(self._execution_history))
                self._stats["average_execution_time"] = total_time / count
        
        return result
    
    def _execute_action(self, step: WorkflowStep, context: Dict[str, Any], result: ExecutionResult) -> bool:
        """执行行动步骤"""
        action = step.action
        parameters = step.parameters
        
        if action in self._action_handlers:
            handler = self._action_handlers[action]
            try:
                # 合并上下文和参数
                merged_params = {**context, **parameters}
                action_result = handler(merged_params)
                
                result.result_data["action_result"] = action_result
                return bool(action_result)
                
            except Exception as e:
                result.error_message = f"行动处理器执行失败: {e}"
                return False
        else:
            result.error_message = f"未找到行动处理器: {action}"
            return False
    
    def _execute_condition(self, step: WorkflowStep, context: Dict[str, Any], result: ExecutionResult) -> bool:
        """执行条件步骤"""
        conditions = step.conditions or [{"type": "always_true"}]
        condition_result = self._evaluate_conditions(conditions, context)
        
        result.result_data["condition_result"] = condition_result
        return condition_result
    
    def _execute_wait(self, step: WorkflowStep, context: Dict[str, Any], result: ExecutionResult) -> bool:
        """执行等待步骤"""
        wait_time = step.parameters.get("duration", 1.0)
        
        try:
            time.sleep(wait_time)
            result.result_data["wait_duration"] = wait_time
            return True
        except Exception as e:
            result.error_message = f"等待执行失败: {e}"
            return False
    
    def _execute_loop(self, step: WorkflowStep, context: Dict[str, Any], result: ExecutionResult) -> bool:
        """执行循环步骤"""
        # 简化实现：循环执行指定次数
        loop_count = step.parameters.get("count", 1)
        loop_action = step.parameters.get("action", "")
        
        success_count = 0
        
        for i in range(loop_count):
            if loop_action in self._action_handlers:
                try:
                    loop_context = {**context, "loop_index": i}
                    loop_result = self._action_handlers[loop_action](loop_context)
                    if loop_result:
                        success_count += 1
                except Exception as e:
                    self.logger.error(f"循环第{i}次执行失败: {e}")
        
        result.result_data["loop_count"] = loop_count
        result.result_data["success_count"] = success_count
        
        return success_count > 0
    
    def _execute_decision(self, step: WorkflowStep, context: Dict[str, Any], result: ExecutionResult) -> bool:
        """执行决策步骤"""
        # 简化实现：基于条件做决策
        decision_conditions = step.parameters.get("conditions", [])
        
        for condition in decision_conditions:
            if self._evaluate_single_condition(condition, context):
                decision_action = condition.get("action", "")
                if decision_action in self._action_handlers:
                    try:
                        decision_result = self._action_handlers[decision_action](context)
                        result.result_data["decision"] = decision_action
                        result.result_data["decision_result"] = decision_result
                        return bool(decision_result)
                    except Exception as e:
                        result.error_message = f"决策执行失败: {e}"
                        return False
        
        # 没有匹配的条件
        result.result_data["decision"] = "no_match"
        return True
    
    def _execute_validation(self, step: WorkflowStep, context: Dict[str, Any], result: ExecutionResult) -> bool:
        """执行验证步骤"""
        validation_type = step.parameters.get("type", "scene_state")
        
        if validation_type == "scene_state":
            # 验证场景状态
            expected_scene = step.parameters.get("expected_scene", "")
            current_scene = context.get("current_scene", "")
            
            is_valid = current_scene == expected_scene
            result.result_data["validation_type"] = validation_type
            result.result_data["expected"] = expected_scene
            result.result_data["actual"] = current_scene
            result.result_data["is_valid"] = is_valid
            
            return is_valid
        
        # 其他验证类型
        return True
    
    def _evaluate_conditions(self, conditions: List[Dict[str, Any]], context: Dict[str, Any]) -> bool:
        """评估条件列表"""
        if not conditions:
            return True
        
        # 默认使用AND逻辑
        for condition in conditions:
            if not self._evaluate_single_condition(condition, context):
                return False
        
        return True
    
    def _evaluate_single_condition(self, condition: Dict[str, Any], context: Dict[str, Any]) -> bool:
        """评估单个条件"""
        condition_type = condition.get("type", "")
        
        if condition_type == "always_true":
            return True
        elif condition_type == "always_false":
            return False
        elif condition_type == "context_value":
            key = condition.get("key", "")
            expected_value = condition.get("value", "")
            actual_value = context.get(key, "")
            return actual_value == expected_value
        elif condition_type == "custom":
            evaluator_name = condition.get("evaluator", "")
            if evaluator_name in self._condition_evaluators:
                try:
                    return self._condition_evaluators[evaluator_name](condition, context)
                except Exception as e:
                    self.logger.error(f"条件评估器{evaluator_name}执行失败: {e}")
                    return False
        
        return False
    
    def _register_default_handlers(self):
        """注册默认处理器"""
        # 基础行动处理器
        self._action_handlers["log_message"] = self._handle_log_message
        self._action_handlers["set_context"] = self._handle_set_context
        self._action_handlers["sleep"] = self._handle_sleep
        
        # 基础条件评估器
        self._condition_evaluators["time_based"] = self._evaluate_time_condition
        self._condition_evaluators["context_based"] = self._evaluate_context_condition
    
    def _handle_log_message(self, params: Dict[str, Any]) -> bool:
        """处理日志消息"""
        message = params.get("message", "自动化执行")
        level = params.get("level", "info")
        
        if level == "debug":
            self.logger.debug(message)
        elif level == "warning":
            self.logger.warning(message)
        elif level == "error":
            self.logger.error(message)
        else:
            self.logger.info(message)
        
        return True
    
    def _handle_set_context(self, params: Dict[str, Any]) -> bool:
        """处理设置上下文"""
        key = params.get("key", "")
        value = params.get("value", "")
        
        if key:
            params[key] = value
            return True
        
        return False
    
    def _handle_sleep(self, params: Dict[str, Any]) -> bool:
        """处理休眠"""
        duration = params.get("duration", 1.0)
        try:
            time.sleep(duration)
            return True
        except Exception:
            return False
    
    def _evaluate_time_condition(self, condition: Dict[str, Any], context: Dict[str, Any]) -> bool:
        """评估时间条件"""
        current_time = time.time()
        start_time = condition.get("start_time", 0)
        end_time = condition.get("end_time", float('inf'))
        
        return start_time <= current_time <= end_time
    
    def _evaluate_context_condition(self, condition: Dict[str, Any], context: Dict[str, Any]) -> bool:
        """评估上下文条件"""
        key = condition.get("key", "")
        operator = condition.get("operator", "==")
        value = condition.get("value", "")
        
        if key not in context:
            return False
        
        actual_value = context[key]
        
        if operator == "==":
            return actual_value == value
        elif operator == "!=":
            return actual_value != value
        elif operator == ">":
            return actual_value > value
        elif operator == "<":
            return actual_value < value
        elif operator == ">=":
            return actual_value >= value
        elif operator == "<=":
            return actual_value <= value
        elif operator == "in":
            return actual_value in value
        elif operator == "not_in":
            return actual_value not in value
        
        return False

    def get_recent_executions(self, count: int = 10) -> List[ExecutionResult]:
        """获取最近的执行结果"""
        return self._execution_history[-count:] if self._execution_history else []

    def clear_execution_history(self):
        """清除执行历史"""
        self._execution_history.clear()
        self._stats["total_executions"] = 0
        self._stats["successful_executions"] = 0
        self._stats["failed_executions"] = 0
        self._stats["average_execution_time"] = 0.0

        self.logger.info("执行历史已清除")

    def create_simple_workflow(self, actions: List[str], workflow_id: Optional[str] = None) -> str:
        """
        创建简单工作流

        Args:
            actions: 行动列表
            workflow_id: 工作流ID

        Returns:
            工作流ID
        """
        steps = []

        for i, action in enumerate(actions):
            step = WorkflowStep(
                id=f"step_{i+1}",
                name=f"执行{action}",
                step_type=StepType.ACTION,
                action=action,
                description=f"自动执行{action}行动"
            )
            steps.append(step)

        return self.execute_workflow(steps, workflow_id)

    def __del__(self):
        """析构函数"""
        if self._running:
            self.stop()

    def _find_step_index(self, steps: List[WorkflowStep], step_id: str) -> int:
        """查找步骤索引"""
        for i, step in enumerate(steps):
            if step.id == step_id:
                return i
        return -1

    def _complete_workflow(self, workflow_id: str):
        """完成工作流"""
        if workflow_id not in self._running_workflows:
            return

        workflow = self._running_workflows.pop(workflow_id)
        workflow["end_time"] = time.time()
        workflow["duration"] = workflow["end_time"] - workflow["start_time"]

        self._stats["active_workflows"] = len(self._running_workflows)

        status = workflow["status"]
        self.logger.info(f"工作流{workflow_id}完成，状态: {status.value}")

        # 触发完成事件
        self._trigger_workflow_event("workflow_completed", workflow)

    def _cancel_workflow(self, workflow_id: str):
        """取消工作流"""
        if workflow_id in self._running_workflows:
            workflow = self._running_workflows[workflow_id]
            workflow["status"] = ExecutionStatus.CANCELLED
            self._complete_workflow(workflow_id)

    def _check_triggers(self):
        """检查触发器"""
        current_time = time.time()

        for trigger_id, trigger in self._triggers.items():
            try:
                if self._should_trigger(trigger, current_time):
                    self._execute_trigger(trigger_id, trigger)
            except Exception as e:
                self.logger.error(f"检查触发器{trigger_id}失败: {e}")

    def _should_trigger(self, trigger: Dict[str, Any], current_time: float) -> bool:
        """检查是否应该触发"""
        trigger_type = TriggerType(trigger.get("type", "manual"))

        if trigger_type == TriggerType.SCHEDULED:
            next_run_time = trigger.get("next_run_time", 0)
            return current_time >= next_run_time
        elif trigger_type == TriggerType.CONDITION:
            conditions = trigger.get("conditions", [])
            context = trigger.get("context", {})
            return self._evaluate_conditions(conditions, context)

        return False

    def _execute_trigger(self, trigger_id: str, trigger: Dict[str, Any]):
        """执行触发器"""
        workflow_template = trigger.get("workflow_template", {})
        steps = workflow_template.get("steps", [])

        if steps:
            workflow_id = f"triggered_{trigger_id}_{int(time.time())}"
            self.execute_workflow(steps, workflow_id, context=trigger.get("context", {}))

        # 更新下次运行时间（如果是定时触发）
        if trigger.get("type") == "scheduled":
            interval = trigger.get("interval", 3600)  # 默认1小时
            trigger["next_run_time"] = time.time() + interval

    def _trigger_workflow_event(self, event_type: str, workflow: Dict[str, Any]):
        """触发工作流事件"""
        # 简化实现：记录事件日志
        self.logger.debug(f"工作流事件: {event_type}, 工作流: {workflow['id']}")

    def register_action_handler(self, action_name: str, handler: Callable):
        """注册行动处理器"""
        self._action_handlers[action_name] = handler
        self.logger.debug(f"注册行动处理器: {action_name}")

    def register_condition_evaluator(self, evaluator_name: str, evaluator: Callable):
        """注册条件评估器"""
        self._condition_evaluators[evaluator_name] = evaluator
        self.logger.debug(f"注册条件评估器: {evaluator_name}")

    def add_trigger(self, trigger_id: str, trigger_type: TriggerType,
                   workflow_template: Dict[str, Any], **kwargs):
        """添加触发器"""
        trigger = {
            "id": trigger_id,
            "type": trigger_type.value,
            "workflow_template": workflow_template,
            "created_time": time.time(),
            **kwargs
        }

        # 设置定时触发的下次运行时间
        if trigger_type == TriggerType.SCHEDULED:
            interval = kwargs.get("interval", 3600)
            trigger["next_run_time"] = time.time() + interval

        self._triggers[trigger_id] = trigger
        self.logger.info(f"添加触发器: {trigger_id}, 类型: {trigger_type.value}")

    def remove_trigger(self, trigger_id: str):
        """移除触发器"""
        if trigger_id in self._triggers:
            del self._triggers[trigger_id]
            self.logger.info(f"移除触发器: {trigger_id}")

    def get_workflow_status(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """获取工作流状态"""
        if workflow_id in self._running_workflows:
            workflow = self._running_workflows[workflow_id]
            return {
                "id": workflow["id"],
                "status": workflow["status"].value,
                "current_step": workflow["current_step"],
                "total_steps": len(workflow["steps"]),
                "start_time": workflow["start_time"],
                "duration": time.time() - workflow["start_time"],
                "execution_results": len(workflow["execution_results"])
            }

        return None

    def cancel_workflow(self, workflow_id: str) -> bool:
        """取消工作流"""
        if workflow_id in self._running_workflows:
            self._cancel_workflow(workflow_id)
            return True

        # 从队列中移除
        for i, workflow in enumerate(self._workflow_queue):
            if workflow["id"] == workflow_id:
                self._workflow_queue.pop(i)
                self.logger.info(f"从队列中取消工作流: {workflow_id}")
                return True

        return False

    def get_execution_stats(self) -> Dict[str, Any]:
        """获取执行统计"""
        return {
            "total_executions": self._stats["total_executions"],
            "successful_executions": self._stats["successful_executions"],
            "failed_executions": self._stats["failed_executions"],
            "success_rate": (self._stats["successful_executions"] / max(1, self._stats["total_executions"])),
            "average_execution_time": self._stats["average_execution_time"],
            "active_workflows": self._stats["active_workflows"],
            "queued_workflows": len(self._workflow_queue),
            "registered_actions": len(self._action_handlers),
            "registered_conditions": len(self._condition_evaluators),
            "active_triggers": len(self._triggers)
        }
