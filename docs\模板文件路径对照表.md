# Gakumasu-Bot 模板文件路径对照表

## 概述

本文档详细说明了Gakumasu-Bot项目中每个游戏场景和UI元素对应的模板文件路径。所有模板文件都存储在 `assets/templates/` 目录下。

## 模板文件状态说明

- ✅ **已存在** - 模板文件已创建并可用
- 📋 **需要创建** - 模板文件尚未创建，需要用户截图制作
- ⚠️ **可选** - 可选模板，不影响基本功能

## 游戏场景模板文件

### 1. 主菜单场景 (MAIN_MENU)

#### 必需模板
| 模板名称 | 文件路径 | 状态 | 用途说明 |
|---------|----------|------|----------|
| `main_menu_full` | `assets/templates/main_menu_full.png` | ✅ | 完整主菜单截图，主要识别模板 |
| `main_menu_logo` | `assets/templates/main_menu_logo.png` | ✅ | 主菜单标识图标，备用识别模板 |

#### 可选模板
| 模板名称 | 文件路径 | 状态 | 用途说明 |
|---------|----------|------|----------|
| `produce_button` | `assets/templates/produce_button.png` | ✅ | 育成按钮，用于导航到育成界面 |
| `part_time_job_button` | `assets/templates/part_time_job_button.png` | 📋 | 打工按钮 |
| `daily_tasks_button` | `assets/templates/daily_tasks_button.png` | 📋 | 日常任务按钮 |

### 2. 育成准备界面 (PRODUCE_SETUP)

#### 必需模板
| 模板名称 | 文件路径 | 状态 | 用途说明 |
|---------|----------|------|----------|
| `produce_setup_title` | `assets/templates/produce_setup_title.png` | 📋 | 育成准备界面标题 |
| `idol_selection` | `assets/templates/idol_selection.png` | 📋 | 偶像选择区域 |

#### 可选模板
| 模板名称 | 文件路径 | 状态 | 用途说明 |
|---------|----------|------|----------|
| `support_card_selection` | `assets/templates/support_card_selection.png` | 📋 | 支援卡选择区域 |
| `start_produce_button` | `assets/templates/start_produce_button.png` | 📋 | 开始育成按钮 |

### 3. 育成主界面 (PRODUCE_MAIN)

#### 必需模板
| 模板名称 | 文件路径 | 状态 | 用途说明 |
|---------|----------|------|----------|
| `week_indicator` | `assets/templates/week_indicator.png` | ✅ | 周数指示器 |
| `stamina_bar` | `assets/templates/stamina_bar.png` | ✅ | 体力条显示 |

#### 可选模板
| 模板名称 | 文件路径 | 状态 | 用途说明 |
|---------|----------|------|----------|
| `lesson_buttons` | `assets/templates/lesson_buttons.png` | 📋 | 课程按钮组 |
| `rest_button` | `assets/templates/rest_button.png` | 📋 | 休息按钮 |
| `outing_button` | `assets/templates/outing_button.png` | 📋 | 外出按钮 |

### 4. 育成战斗界面 (PRODUCE_BATTLE)

#### 必需模板
| 模板名称 | 文件路径 | 状态 | 用途说明 |
|---------|----------|------|----------|
| `battle_ui` | `assets/templates/battle_ui.png` | 📋 | 战斗界面UI |
| `card_hand` | `assets/templates/card_hand.png` | 📋 | 手牌区域 |

#### 可选模板
| 模板名称 | 文件路径 | 状态 | 用途说明 |
|---------|----------|------|----------|
| `score_display` | `assets/templates/score_display.png` | 📋 | 分数显示区域 |
| `turn_indicator` | `assets/templates/turn_indicator.png` | 📋 | 回合指示器 |

### 5. 考试界面 (PRODUCE_EXAM)

#### 必需模板
| 模板名称 | 文件路径 | 状态 | 用途说明 |
|---------|----------|------|----------|
| `exam_title` | `assets/templates/exam_title.png` | 📋 | 考试界面标题 |
| `exam_ui` | `assets/templates/exam_ui.png` | 📋 | 考试界面UI |

#### 可选模板
| 模板名称 | 文件路径 | 状态 | 用途说明 |
|---------|----------|------|----------|
| `exam_score` | `assets/templates/exam_score.png` | 📋 | 考试分数显示 |
| `exam_cards` | `assets/templates/exam_cards.png` | 📋 | 考试卡牌区域 |

### 6. 育成结果界面 (PRODUCE_RESULT)

#### 必需模板
| 模板名称 | 文件路径 | 状态 | 用途说明 |
|---------|----------|------|----------|
| `result_title` | `assets/templates/result_title.png` | 📋 | 结果界面标题 |
| `final_score` | `assets/templates/final_score.png` | 📋 | 最终分数显示 |

#### 可选模板
| 模板名称 | 文件路径 | 状态 | 用途说明 |
|---------|----------|------|----------|
| `idol_stats` | `assets/templates/idol_stats.png` | 📋 | 偶像属性统计 |
| `result_buttons` | `assets/templates/result_buttons.png` | 📋 | 结果界面按钮组 |

### 7. 打工界面 (PART_TIME_JOB)

#### 必需模板
| 模板名称 | 文件路径 | 状态 | 用途说明 |
|---------|----------|------|----------|
| `part_time_job_title` | `assets/templates/part_time_job_title.png` | 📋 | 打工界面标题 |

#### 可选模板
| 模板名称 | 文件路径 | 状态 | 用途说明 |
|---------|----------|------|----------|
| `job_selection` | `assets/templates/job_selection.png` | 📋 | 工作选择区域 |
| `job_timer` | `assets/templates/job_timer.png` | 📋 | 工作计时器 |

### 8. 日常任务界面 (DAILY_TASKS)

#### 必需模板
| 模板名称 | 文件路径 | 状态 | 用途说明 |
|---------|----------|------|----------|
| `daily_tasks_title` | `assets/templates/daily_tasks_title.png` | 📋 | 日常任务标题 |

#### 可选模板
| 模板名称 | 文件路径 | 状态 | 用途说明 |
|---------|----------|------|----------|
| `task_list` | `assets/templates/task_list.png` | 📋 | 任务列表区域 |
| `reward_buttons` | `assets/templates/reward_buttons.png` | 📋 | 奖励按钮组 |

## 详细按钮模板文件

### 课程相关按钮

| 按钮名称 | 文件路径 | 状态 | 功能说明 |
|---------|----------|------|----------|
| `vocal_lesson_button` | `assets/templates/vocal_lesson_button.png` | 📋 | 声乐课程按钮 |
| `dance_lesson_button` | `assets/templates/dance_lesson_button.png` | 📋 | 舞蹈课程按钮 |
| `visual_lesson_button` | `assets/templates/visual_lesson_button.png` | 📋 | 视觉课程按钮 |
| `mental_lesson_button` | `assets/templates/mental_lesson_button.png` | 📋 | 精神课程按钮 |

### 行动相关按钮

| 按钮名称 | 文件路径 | 状态 | 功能说明 |
|---------|----------|------|----------|
| `rest_button` | `assets/templates/rest_button.png` | 📋 | 休息按钮 |
| `outing_button` | `assets/templates/outing_button.png` | 📋 | 外出按钮 |

### 通用控制按钮

| 按钮名称 | 文件路径 | 状态 | 功能说明 |
|---------|----------|------|----------|
| `confirm_button` | `assets/templates/confirm_button.png` | 📋 | 确认按钮 |
| `cancel_button` | `assets/templates/cancel_button.png` | 📋 | 取消按钮 |
| `back_button` | `assets/templates/back_button.png` | 📋 | 返回按钮 |
| `menu_button` | `assets/templates/menu_button.png` | 📋 | 菜单按钮 |

## 状态显示元素模板

### 属性显示

| 元素名称 | 文件路径 | 状态 | 功能说明 |
|---------|----------|------|----------|
| `vocal_stat` | `assets/templates/vocal_stat.png` | 📋 | 声乐属性显示 |
| `dance_stat` | `assets/templates/dance_stat.png` | 📋 | 舞蹈属性显示 |
| `visual_stat` | `assets/templates/visual_stat.png` | 📋 | 视觉属性显示 |
| `mental_stat` | `assets/templates/mental_stat.png` | 📋 | 精神属性显示 |
| `stamina_stat` | `assets/templates/stamina_stat.png` | 📋 | 体力数值显示 |
| `motivation_stat` | `assets/templates/motivation_stat.png` | 📋 | 干劲状态显示 |

### 信息显示

| 元素名称 | 文件路径 | 状态 | 功能说明 |
|---------|----------|------|----------|
| `username_label` | `assets/templates/username_label.png` | 📋 | 用户名显示 |
| `level_label` | `assets/templates/level_label.png` | 📋 | 等级显示 |
| `currency_label` | `assets/templates/currency_label.png` | 📋 | 货币显示 |
| `selected_idol_name` | `assets/templates/selected_idol_name.png` | 📋 | 选中偶像名称 |
| `support_count_label` | `assets/templates/support_count_label.png` | 📋 | 支援卡数量 |
| `turn_counter` | `assets/templates/turn_counter.png` | 📋 | 回合计数器 |

## DMM Player 相关模板

| 模板名称 | 文件路径 | 状态 | 用途说明 |
|---------|----------|------|----------|
| `dmm_gakumasu_icon` | `assets/templates/dmm_gakumasu_icon.png` | 📋 | DMM Player中的游戏图标 |
| `dmm_player_ui` | `assets/templates/dmm_player_ui.png` | 📋 | DMM Player界面 |

## 模板文件制作指南

### 1. 截图要求
- **分辨率**: 推荐1920x1080，与游戏实际分辨率一致
- **格式**: PNG格式，保证图像质量
- **内容**: 截取清晰、完整的UI元素
- **背景**: 尽量避免动态背景干扰

### 2. 命名规范
- 使用小写字母和下划线
- 名称要具有描述性
- 按钮类型添加`_button`后缀
- 状态显示添加`_stat`或`_label`后缀

### 3. 文件大小建议
- 小按钮: 50x50 - 200x100 像素
- 大按钮: 200x100 - 400x200 像素
- 界面标题: 300x100 - 600x200 像素
- 完整界面: 保持原始截图尺寸

### 4. 质量要求
- 图像清晰，无模糊
- 颜色准确，对比度适中
- 避免包含动态元素（如闪烁效果）
- 确保在不同光照条件下都能识别

## 模板文件优先级

### 高优先级（必须创建）
1. `main_menu_full.png` - 主菜单完整截图
2. `produce_setup_title.png` - 育成准备标题
3. `start_produce_button.png` - 开始育成按钮
4. `vocal_lesson_button.png` - 声乐课程按钮
5. `dance_lesson_button.png` - 舞蹈课程按钮
6. `visual_lesson_button.png` - 视觉课程按钮
7. `mental_lesson_button.png` - 精神课程按钮
8. `rest_button.png` - 休息按钮
9. `confirm_button.png` - 确认按钮
10. `cancel_button.png` - 取消按钮

### 中优先级（建议创建）
1. `battle_ui.png` - 战斗界面
2. `card_hand.png` - 手牌区域
3. `exam_title.png` - 考试标题
4. `result_title.png` - 结果标题
5. `back_button.png` - 返回按钮

### 低优先级（可选创建）
1. 各种状态显示元素
2. 信息标签元素
3. 装饰性UI元素

## 使用示例

### 在代码中引用模板
```python
# 场景识别中的模板引用
scene_rules = {
    GameScene.MAIN_MENU: {
        "required_templates": ["main_menu_full", "main_menu_logo"],
        "optional_templates": ["produce_button"]
    }
}

# UI配置中的模板引用
ui_elements = {
    "produce_button": {
        "template_name": "produce_button",  # 对应 assets/templates/produce_button.png
        "confidence_threshold": 0.8
    }
}
```

### 模板文件检查脚本
```python
import os

def check_template_files():
    template_dir = "assets/templates"
    required_templates = [
        "main_menu_full.png",
        "produce_setup_title.png", 
        "start_produce_button.png"
    ]
    
    for template in required_templates:
        path = os.path.join(template_dir, template)
        if os.path.exists(path):
            print(f"✅ {template}")
        else:
            print(f"📋 {template} - 需要创建")
```

## 注意事项

1. **版本兼容性**: 游戏更新可能导致UI变化，需要更新对应模板
2. **语言版本**: 日语版和中文版可能需要不同的模板文件
3. **分辨率适配**: 不同分辨率下可能需要调整模板或使用多尺度匹配
4. **性能考虑**: 模板文件过大会影响匹配速度，建议适当裁剪
5. **备份管理**: 建议对模板文件进行版本管理和备份
