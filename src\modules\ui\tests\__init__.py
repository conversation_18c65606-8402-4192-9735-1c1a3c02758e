"""
UI模块测试包
提供UI架构的单元测试和集成测试
"""

# 测试工具导入
from .test_utils import (
    MockPerceptionModule, MockActionController, 
    create_test_config, create_test_scene_config,
    UITestCase, SceneTestCase
)

# 测试数据导入
from .test_data import (
    TEST_UI_CONFIGS, TEST_SCENE_CONFIGS, 
    MOCK_TEMPLATES, MOCK_GAME_STATES
)

__all__ = [
    # 测试工具
    'MockPerceptionModule',
    'MockActionController', 
    'create_test_config',
    'create_test_scene_config',
    'UITestCase',
    'SceneTestCase',
    
    # 测试数据
    'TEST_UI_CONFIGS',
    'TEST_SCENE_CONFIGS',
    'MOCK_TEMPLATES', 
    'MOCK_GAME_STATES'
]
