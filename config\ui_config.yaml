# UI元素配置文件
# 定义通用UI元素的默认配置和特殊配置

ui_elements:
  # 通用按钮配置
  default_button:
    type: "button"
    confidence_threshold: 0.8
    timeout: 5.0
    retry_count: 3
    enabled: true
    double_click_enabled: false
    long_press_duration: 1.0
    click_delay: 0.1
    
  # 增强按钮配置
  default_enhanced_button:
    type: "enhanced_button"
    confidence_threshold: 0.8
    timeout: 5.0
    retry_count: 3
    enabled: true
    double_click_enabled: false
    long_press_duration: 1.0
    click_delay: 0.1
    verify_click_result: true
    
  # 输入框配置
  default_input_field:
    type: "input_field"
    confidence_threshold: 0.8
    timeout: 5.0
    retry_count: 3
    enabled: true
    clear_before_input: true
    input_delay: 0.1
    max_length: 100
    input_method: "direct"
    verify_input_result: false
    read_back_text: false
    
  # 标签配置
  default_label:
    type: "label"
    confidence_threshold: 0.8
    timeout: 5.0
    retry_count: 3
    enabled: true
    text_recognition_enabled: true
    ocr_language: "ja"
    ocr_confidence_threshold: 0.7
    text_cleanup_enabled: true
    
  # 增强标签配置
  default_enhanced_label:
    type: "enhanced_label"
    confidence_threshold: 0.8
    timeout: 5.0
    retry_count: 3
    enabled: true
    text_recognition_enabled: true
    ocr_language: "ja"
    ocr_confidence_threshold: 0.7
    text_cleanup_enabled: true
    
  # 特定UI元素配置
  produce_button:
    type: "enhanced_button"
    template_name: "produce_button"
    confidence_threshold: 0.8
    timeout: 5.0
    retry_count: 3
    enabled: true
    position:
      x: 500
      y: 400
    double_click_enabled: false
    expected_scene_after_click: "produce_setup"
    verify_click_result: true
    click_behavior: "navigate_to_produce"
    
  start_produce_button:
    type: "enhanced_button"
    template_name: "start_produce_button"
    confidence_threshold: 0.9
    timeout: 5.0
    retry_count: 3
    enabled: true
    position:
      x: 960
      y: 800
    double_click_enabled: false
    expected_scene_after_click: "produce_main"
    verify_click_result: true
    click_behavior: "start_produce"
    
  vocal_lesson_button:
    type: "enhanced_button"
    template_name: "vocal_lesson_button"
    confidence_threshold: 0.85
    timeout: 5.0
    retry_count: 3
    enabled: true
    position:
      x: 200
      y: 300
    verify_click_result: true
    click_behavior: "take_lesson"
    
  dance_lesson_button:
    type: "enhanced_button"
    template_name: "dance_lesson_button"
    confidence_threshold: 0.85
    timeout: 5.0
    retry_count: 3
    enabled: true
    position:
      x: 400
      y: 300
    verify_click_result: true
    click_behavior: "take_lesson"
    
  visual_lesson_button:
    type: "enhanced_button"
    template_name: "visual_lesson_button"
    confidence_threshold: 0.85
    timeout: 5.0
    retry_count: 3
    enabled: true
    position:
      x: 600
      y: 300
    verify_click_result: true
    click_behavior: "take_lesson"
    
  mental_lesson_button:
    type: "enhanced_button"
    template_name: "mental_lesson_button"
    confidence_threshold: 0.85
    timeout: 5.0
    retry_count: 3
    enabled: true
    position:
      x: 800
      y: 300
    verify_click_result: true
    click_behavior: "take_lesson"
    
  rest_button:
    type: "enhanced_button"
    template_name: "rest_button"
    confidence_threshold: 0.85
    timeout: 5.0
    retry_count: 3
    enabled: true
    position:
      x: 1000
      y: 300
    verify_click_result: true
    click_behavior: "take_rest"
    
  outing_button:
    type: "enhanced_button"
    template_name: "outing_button"
    confidence_threshold: 0.85
    timeout: 5.0
    retry_count: 3
    enabled: true
    position:
      x: 1200
      y: 300
    verify_click_result: true
    click_behavior: "go_outing"
    
  # 信息显示元素
  main_menu_logo:
    type: "label"
    template_name: "main_menu_logo"
    confidence_threshold: 0.9
    timeout: 3.0
    text_recognition_enabled: false
    
  produce_setup_title:
    type: "enhanced_label"
    template_name: "produce_setup_title"
    confidence_threshold: 0.9
    timeout: 3.0
    text_recognition_enabled: true
    ocr_language: "ja"
    
  produce_main_ui:
    type: "label"
    template_name: "produce_main_ui"
    confidence_threshold: 0.9
    timeout: 3.0
    text_recognition_enabled: false
    
  username_label:
    type: "enhanced_label"
    template_name: "username_label"
    confidence_threshold: 0.7
    timeout: 3.0
    text_recognition_enabled: true
    ocr_language: "ja"
    
  level_label:
    type: "label"
    template_name: "level_label"
    confidence_threshold: 0.8
    timeout: 3.0
    text_recognition_enabled: true
    ocr_language: "en"
    
  currency_label:
    type: "label"
    template_name: "currency_label"
    confidence_threshold: 0.8
    timeout: 3.0
    text_recognition_enabled: true
    ocr_language: "en"
    
  selected_idol_name:
    type: "enhanced_label"
    template_name: "selected_idol_name"
    confidence_threshold: 0.8
    timeout: 3.0
    text_recognition_enabled: true
    ocr_language: "ja"
    
  support_count_label:
    type: "label"
    template_name: "support_count_label"
    confidence_threshold: 0.8
    timeout: 3.0
    text_recognition_enabled: true
    ocr_language: "en"
    
  turn_counter:
    type: "enhanced_label"
    template_name: "turn_counter"
    confidence_threshold: 0.8
    timeout: 3.0
    text_recognition_enabled: true
    ocr_language: "en"
    
  # 属性显示元素
  vocal_stat:
    type: "enhanced_label"
    template_name: "vocal_stat"
    confidence_threshold: 0.8
    timeout: 3.0
    text_recognition_enabled: true
    ocr_language: "en"
    
  dance_stat:
    type: "enhanced_label"
    template_name: "dance_stat"
    confidence_threshold: 0.8
    timeout: 3.0
    text_recognition_enabled: true
    ocr_language: "en"
    
  visual_stat:
    type: "enhanced_label"
    template_name: "visual_stat"
    confidence_threshold: 0.8
    timeout: 3.0
    text_recognition_enabled: true
    ocr_language: "en"
    
  mental_stat:
    type: "enhanced_label"
    template_name: "mental_stat"
    confidence_threshold: 0.8
    timeout: 3.0
    text_recognition_enabled: true
    ocr_language: "en"
    
  stamina_stat:
    type: "enhanced_label"
    template_name: "stamina_stat"
    confidence_threshold: 0.8
    timeout: 3.0
    text_recognition_enabled: true
    ocr_language: "en"
    
  motivation_stat:
    type: "label"
    template_name: "motivation_stat"
    confidence_threshold: 0.8
    timeout: 3.0
    text_recognition_enabled: false
    
  # 通用控制按钮
  confirm_button:
    type: "button"
    template_name: "confirm_button"
    confidence_threshold: 0.9
    timeout: 3.0
    retry_count: 3
    enabled: true
    position:
      x: 800
      y: 700
      
  cancel_button:
    type: "button"
    template_name: "cancel_button"
    confidence_threshold: 0.9
    timeout: 3.0
    retry_count: 3
    enabled: true
    position:
      x: 600
      y: 700
      
  back_button:
    type: "button"
    template_name: "back_button"
    confidence_threshold: 0.8
    timeout: 3.0
    retry_count: 3
    enabled: true
    position:
      x: 100
      y: 100
      
  menu_button:
    type: "button"
    template_name: "menu_button"
    confidence_threshold: 0.8
    timeout: 3.0
    retry_count: 3
    enabled: true
    position:
      x: 1820
      y: 100

# 全局配置
global_settings:
  default_confidence_threshold: 0.8
  default_timeout: 5.0
  default_retry_count: 3
  cache_enabled: true
  cache_ttl: 30.0
  performance_monitoring: true
  error_recovery_enabled: true
  max_error_retry: 3
  error_recovery_delay: 1.0
