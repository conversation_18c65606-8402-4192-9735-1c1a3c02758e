"""
MCTS（蒙特卡洛树搜索）算法引擎
实现基于MCTS的智能决策算法
"""

import math
import random
import time
from typing import List, Optional, Dict, Any
from dataclasses import dataclass
from copy import deepcopy

from ...utils.logger import get_logger
from ...core.data_structures import GameState, Card, Action, ActionType
from .heuristic_evaluator import HeuristicEvaluator, EvaluationResult


@dataclass
class MCTSResult:
    """MCTS搜索结果"""
    best_action: Optional[Action]           # 最佳行动
    best_score: float                       # 最佳分数
    search_time: float                      # 搜索时间
    iterations: int                         # 迭代次数
    confidence: float                       # 置信度
    alternative_actions: List[Action]       # 备选行动
    metadata: Dict[str, Any] = None         # 额外信息
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class MCTSNode:
    """MCTS树节点"""
    
    def __init__(self, game_state: GameState, action: Optional[Action] = None, 
                 parent: Optional['MCTSNode'] = None):
        """
        初始化MCTS节点
        
        Args:
            game_state: 游戏状态
            action: 导致此状态的行动
            parent: 父节点
        """
        self.game_state = game_state
        self.action = action
        self.parent = parent
        self.children: List['MCTSNode'] = []
        
        # MCTS统计信息
        self.visits = 0
        self.total_reward = 0.0
        self.average_reward = 0.0
        
        # 节点状态
        self.is_fully_expanded = False
        self.possible_actions: List[Action] = []
        self.untried_actions: List[Action] = []
    
    def is_leaf(self) -> bool:
        """检查是否为叶子节点"""
        return len(self.children) == 0
    
    def is_terminal(self) -> bool:
        """检查是否为终端节点"""
        # 简化的终端条件判断
        return (self.game_state.stamina <= 0 or 
                self.game_state.current_week >= 52 or
                len(self.possible_actions) == 0)
    
    def add_child(self, child_state: GameState, action: Action) -> 'MCTSNode':
        """添加子节点"""
        child = MCTSNode(child_state, action, self)
        self.children.append(child)
        return child
    
    def update(self, reward: float):
        """更新节点统计信息"""
        self.visits += 1
        self.total_reward += reward
        self.average_reward = self.total_reward / self.visits
    
    def ucb1_score(self, exploration_constant: float = math.sqrt(2)) -> float:
        """计算UCB1分数"""
        if self.visits == 0:
            return float('inf')
        
        if self.parent is None or self.parent.visits == 0:
            return self.average_reward
        
        exploitation = self.average_reward
        exploration = exploration_constant * math.sqrt(
            math.log(self.parent.visits) / self.visits
        )
        
        return exploitation + exploration
    
    def best_child(self, exploration_constant: float = 0.0) -> Optional['MCTSNode']:
        """选择最佳子节点"""
        if not self.children:
            return None
        
        return max(self.children, key=lambda child: child.ucb1_score(exploration_constant))
    
    def most_visited_child(self) -> Optional['MCTSNode']:
        """选择访问次数最多的子节点"""
        if not self.children:
            return None
        
        return max(self.children, key=lambda child: child.visits)


class MCTSEngine:
    """MCTS算法引擎"""
    
    def __init__(self, heuristic_evaluator: Optional[HeuristicEvaluator] = None):
        """
        初始化MCTS引擎
        
        Args:
            heuristic_evaluator: 启发式评分器
        """
        self.logger = get_logger("MCTSEngine")
        self.heuristic_evaluator = heuristic_evaluator or HeuristicEvaluator()
        
        # MCTS参数配置
        self.max_iterations = 1000
        self.max_time = 10.0  # 最大搜索时间（秒）
        self.exploration_constant = math.sqrt(2)
        self.simulation_depth = 10  # 模拟深度
        
        # 性能统计
        self.total_searches = 0
        self.total_search_time = 0.0
        
        self.logger.info("MCTS引擎初始化完成")
    
    def search(self, initial_state: GameState, 
               possible_actions: List[Action],
               max_iterations: Optional[int] = None,
               max_time: Optional[float] = None) -> MCTSResult:
        """
        执行MCTS搜索
        
        Args:
            initial_state: 初始游戏状态
            possible_actions: 可能的行动列表
            max_iterations: 最大迭代次数
            max_time: 最大搜索时间
            
        Returns:
            MCTS搜索结果
        """
        start_time = time.time()
        iterations = max_iterations or self.max_iterations
        time_limit = max_time or self.max_time
        
        try:
            self.logger.info(f"开始MCTS搜索，最大迭代: {iterations}, 时间限制: {time_limit}s")
            
            # 创建根节点
            root = MCTSNode(initial_state)
            root.possible_actions = possible_actions
            root.untried_actions = possible_actions.copy()
            
            iteration_count = 0
            
            # MCTS主循环
            while (iteration_count < iterations and 
                   time.time() - start_time < time_limit):
                
                # 1. 选择阶段
                node = self._select(root)
                
                # 2. 扩展阶段
                if not node.is_terminal() and node.untried_actions:
                    node = self._expand(node)
                
                # 3. 模拟阶段
                reward = self._simulate(node)
                
                # 4. 反向传播阶段
                self._backpropagate(node, reward)
                
                iteration_count += 1
            
            # 选择最佳行动
            best_child = root.most_visited_child()
            best_action = best_child.action if best_child else None
            best_score = best_child.average_reward if best_child else 0.0
            
            # 计算置信度
            confidence = self._calculate_confidence(root, iteration_count)
            
            # 获取备选行动
            alternative_actions = self._get_alternative_actions(root)
            
            search_time = time.time() - start_time
            
            # 更新统计信息
            self.total_searches += 1
            self.total_search_time += search_time
            
            result = MCTSResult(
                best_action=best_action,
                best_score=best_score,
                search_time=search_time,
                iterations=iteration_count,
                confidence=confidence,
                alternative_actions=alternative_actions,
                metadata={
                    "root_visits": root.visits,
                    "tree_depth": self._calculate_tree_depth(root),
                    "nodes_created": self._count_nodes(root)
                }
            )
            
            self.logger.info(f"MCTS搜索完成: {iteration_count}次迭代, {search_time:.2f}s, 最佳分数: {best_score:.3f}")
            return result
            
        except Exception as e:
            self.logger.error(f"MCTS搜索失败: {e}")
            return MCTSResult(
                best_action=possible_actions[0] if possible_actions else None,
                best_score=0.0,
                search_time=time.time() - start_time,
                iterations=0,
                confidence=0.1,
                alternative_actions=[]
            )
    
    def _select(self, node: MCTSNode) -> MCTSNode:
        """选择阶段：从根节点向下选择到叶子节点"""
        current = node
        
        while not current.is_leaf() and not current.is_terminal():
            if current.untried_actions:
                # 如果还有未尝试的行动，返回当前节点进行扩展
                return current
            else:
                # 选择最佳子节点继续向下
                current = current.best_child(self.exploration_constant)
                if current is None:
                    break
        
        return current
    
    def _expand(self, node: MCTSNode) -> MCTSNode:
        """扩展阶段：为节点添加一个新的子节点"""
        if not node.untried_actions:
            return node
        
        # 随机选择一个未尝试的行动
        action = random.choice(node.untried_actions)
        node.untried_actions.remove(action)
        
        # 模拟执行行动后的游戏状态
        new_state = self._simulate_action(node.game_state, action)
        
        # 创建新的子节点
        child = node.add_child(new_state, action)
        
        # 为子节点生成可能的行动
        child.possible_actions = self._generate_possible_actions(new_state)
        child.untried_actions = child.possible_actions.copy()
        
        return child
    
    def _simulate(self, node: MCTSNode) -> float:
        """模拟阶段：从当前节点开始随机模拟到终端状态"""
        current_state = deepcopy(node.game_state)
        depth = 0
        total_reward = 0.0
        
        while depth < self.simulation_depth and not self._is_terminal_state(current_state):
            # 生成可能的行动
            possible_actions = self._generate_possible_actions(current_state)
            
            if not possible_actions:
                break
            
            # 随机选择行动（可以使用启发式改进）
            action = self._select_simulation_action(possible_actions, current_state)
            
            # 模拟执行行动
            current_state = self._simulate_action(current_state, action)
            
            # 计算即时奖励
            reward = self._calculate_reward(current_state, action)
            total_reward += reward * (0.9 ** depth)  # 折扣因子
            
            depth += 1
        
        # 计算最终状态的价值
        final_value = self._evaluate_final_state(current_state)
        total_reward += final_value * (0.9 ** depth)
        
        return total_reward
    
    def _backpropagate(self, node: MCTSNode, reward: float):
        """反向传播阶段：向上更新所有祖先节点的统计信息"""
        current = node
        
        while current is not None:
            current.update(reward)
            current = current.parent
    
    def _simulate_action(self, state: GameState, action: Action) -> GameState:
        """模拟执行行动后的游戏状态"""
        # 创建新状态（简化模拟）
        new_state = deepcopy(state)
        
        # 根据行动类型更新状态
        if action.action_type == ActionType.CLICK:
            # 模拟点击操作的效果
            if "card" in action.description.lower():
                # 假设是使用卡牌
                new_state.stamina = max(0, new_state.stamina - 10)
                new_state.score += random.randint(50, 200)
        
        elif action.action_type == ActionType.KEYPRESS:
            # 模拟按键操作的效果
            if action.target == "space":
                new_state.vigor = min(100, new_state.vigor + 5)
        
        # 更新时间戳
        new_state.timestamp = state.timestamp
        
        return new_state
    
    def _generate_possible_actions(self, state: GameState) -> List[Action]:
        """生成给定状态下的可能行动"""
        actions = []
        
        # 基于游戏场景生成行动
        if state.current_scene.value == "produce_battle":
            # 战斗场景：使用手牌
            for i, card in enumerate(state.hand[:5]):  # 限制手牌数量
                action = Action(
                    action_type=ActionType.CLICK,
                    target=(100 + i * 120, 500),  # 模拟卡牌位置
                    description=f"使用卡牌: {card.name_jp}"
                )
                actions.append(action)
        
        elif state.current_scene.value == "produce_main":
            # 主界面：选择课程或休息
            actions.extend([
                Action(ActionType.CLICK, (200, 300), "选择声乐课程"),
                Action(ActionType.CLICK, (300, 300), "选择舞蹈课程"),
                Action(ActionType.CLICK, (400, 300), "选择视觉课程"),
                Action(ActionType.CLICK, (500, 300), "休息")
            ])
        
        # 如果没有特定行动，添加默认行动
        if not actions:
            actions.append(Action(ActionType.KEYPRESS, "space", "等待"))
        
        return actions
    
    def _select_simulation_action(self, actions: List[Action], state: GameState) -> Action:
        """在模拟中选择行动（可以使用启发式）"""
        # 简单的随机选择，可以改进为基于启发式的选择
        if len(actions) == 1:
            return actions[0]
        
        # 使用启发式评分器选择较好的行动
        best_action = actions[0]
        best_score = 0.0
        
        for action in actions[:3]:  # 限制评估数量以提高性能
            try:
                eval_result = self.heuristic_evaluator.evaluate_action_sequence([action], state)
                if eval_result.total_score > best_score:
                    best_score = eval_result.total_score
                    best_action = action
            except:
                continue
        
        return best_action
    
    def _calculate_reward(self, state: GameState, action: Action) -> float:
        """计算即时奖励"""
        reward = 0.0
        
        # 基于状态变化计算奖励
        if state.score > 0:
            reward += state.score / 1000.0
        
        # 基于资源状态计算奖励
        if state.stamina > 50:
            reward += 0.1
        
        if state.vigor > 50:
            reward += 0.05
        
        return min(reward, 1.0)
    
    def _evaluate_final_state(self, state: GameState) -> float:
        """评估最终状态的价值"""
        try:
            eval_result = self.heuristic_evaluator.evaluate_game_state(state)
            return eval_result.total_score
        except:
            return 0.5
    
    def _is_terminal_state(self, state: GameState) -> bool:
        """检查是否为终端状态"""
        return (state.stamina <= 0 or 
                state.current_week >= 52)
    
    def _calculate_confidence(self, root: MCTSNode, iterations: int) -> float:
        """计算搜索结果的置信度"""
        if root.visits == 0:
            return 0.0
        
        # 基于访问次数和迭代次数计算置信度
        visit_ratio = root.visits / max(iterations, 1)
        base_confidence = min(visit_ratio, 1.0)
        
        # 基于最佳子节点的访问次数调整
        best_child = root.most_visited_child()
        if best_child:
            child_confidence = best_child.visits / max(root.visits, 1)
            base_confidence = (base_confidence + child_confidence) / 2
        
        return base_confidence
    
    def _get_alternative_actions(self, root: MCTSNode) -> List[Action]:
        """获取备选行动"""
        alternatives = []
        
        # 按访问次数排序子节点
        sorted_children = sorted(root.children, key=lambda x: x.visits, reverse=True)
        
        # 取前3个作为备选
        for child in sorted_children[1:4]:  # 跳过最佳选择
            if child.action:
                alternatives.append(child.action)
        
        return alternatives
    
    def _calculate_tree_depth(self, root: MCTSNode) -> int:
        """计算树的深度"""
        if not root.children:
            return 0
        
        max_depth = 0
        for child in root.children:
            child_depth = self._calculate_tree_depth(child)
            max_depth = max(max_depth, child_depth)
        
        return max_depth + 1
    
    def _count_nodes(self, root: MCTSNode) -> int:
        """计算树中的节点数量"""
        count = 1
        for child in root.children:
            count += self._count_nodes(child)
        return count
    
    def get_engine_info(self) -> Dict[str, Any]:
        """获取引擎信息"""
        avg_search_time = (self.total_search_time / self.total_searches
                          if self.total_searches > 0 else 0.0)

        return {
            "max_iterations": self.max_iterations,
            "max_time": self.max_time,
            "exploration_constant": self.exploration_constant,
            "simulation_depth": self.simulation_depth,
            "total_searches": self.total_searches,
            "total_search_time": self.total_search_time,
            "average_search_time": avg_search_time
        }

    def configure(self, max_iterations: Optional[int] = None,
                  max_time: Optional[float] = None,
                  exploration_constant: Optional[float] = None,
                  simulation_depth: Optional[int] = None):
        """
        配置MCTS引擎参数

        Args:
            max_iterations: 最大迭代次数
            max_time: 最大搜索时间
            exploration_constant: 探索常数
            simulation_depth: 模拟深度
        """
        if max_iterations is not None:
            self.max_iterations = max_iterations
        if max_time is not None:
            self.max_time = max_time
        if exploration_constant is not None:
            self.exploration_constant = exploration_constant
        if simulation_depth is not None:
            self.simulation_depth = simulation_depth

        self.logger.info(f"MCTS引擎参数已更新: iterations={self.max_iterations}, "
                        f"time={self.max_time}, exploration={self.exploration_constant}")

    def reset_statistics(self):
        """重置统计信息"""
        self.total_searches = 0
        self.total_search_time = 0.0
        self.logger.info("MCTS引擎统计信息已重置")
