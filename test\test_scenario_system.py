"""
剧本选择系统测试
测试剧本和难度数据结构、管理器和工厂类的功能
"""

import unittest
import tempfile
import shutil
import os
from unittest.mock import Mock, patch
from datetime import datetime

from .base_test import BaseTestCase
from src.core.data_structures import (
    ScenarioType, DifficultyLevel, ScenarioInfo, 
    DifficultyInfo, ScenarioSelection
)
from src.modules.scenario.scenario_manager import ScenarioManager
from src.modules.scenario.scenario_factory import ScenarioFactory


class TestScenarioDataStructures(BaseTestCase):
    """剧本数据结构测试"""
    
    def test_scenario_type_enum(self):
        """测试剧本类型枚举"""
        self.assertEqual(ScenarioType.NIA.value, "nia")
        self.assertEqual(ScenarioType.HAJIME.value, "hajime")
        self.assertEqual(ScenarioType.UNKNOWN.value, "unknown")
    
    def test_difficulty_level_enum(self):
        """测试难度等级枚举"""
        self.assertEqual(DifficultyLevel.NORMAL.value, "normal")
        self.assertEqual(DifficultyLevel.MASTER.value, "master")
        self.assertEqual(DifficultyLevel.EASY.value, "easy")
        self.assertEqual(DifficultyLevel.HARD.value, "hard")
        self.assertEqual(DifficultyLevel.EXTREME.value, "extreme")
        self.assertEqual(DifficultyLevel.UNKNOWN.value, "unknown")
    
    def test_scenario_info_creation(self):
        """测试剧本信息创建"""
        scenario = ScenarioInfo(
            scenario_type=ScenarioType.NIA,
            name="test_scenario",
            display_name="测试剧本",
            description="这是一个测试剧本"
        )
        
        self.assertEqual(scenario.scenario_type, ScenarioType.NIA)
        self.assertEqual(scenario.name, "test_scenario")
        self.assertEqual(scenario.display_name, "测试剧本")
        self.assertEqual(scenario.description, "这是一个测试剧本")
        self.assertTrue(scenario.is_available)
        self.assertEqual(scenario.version, "1.0.0")
    
    def test_scenario_info_validation(self):
        """测试剧本信息验证"""
        # 测试空名称
        with self.assertRaises(ValueError):
            ScenarioInfo(
                scenario_type=ScenarioType.NIA,
                name="",
                display_name="测试",
                description="测试"
            )
    
    def test_scenario_info_auto_fill(self):
        """测试剧本信息自动填充"""
        scenario = ScenarioInfo(
            scenario_type=ScenarioType.NIA,
            name="test_scenario",
            display_name="",
            description=""
        )
        
        self.assertEqual(scenario.display_name, "test_scenario")
        self.assertEqual(scenario.description, "test_scenario剧本")
    
    def test_scenario_info_serialization(self):
        """测试剧本信息序列化"""
        scenario = ScenarioInfo(
            scenario_type=ScenarioType.NIA,
            name="test_scenario",
            display_name="测试剧本",
            description="这是一个测试剧本",
            special_attributes={"test": "value"},
            rewards={"exp": 1000}
        )
        
        # 测试转换为字典
        data = scenario.to_dict()
        self.assertEqual(data["scenario_type"], "nia")
        self.assertEqual(data["name"], "test_scenario")
        self.assertEqual(data["special_attributes"]["test"], "value")
        
        # 测试从字典创建
        new_scenario = ScenarioInfo.from_dict(data)
        self.assertEqual(new_scenario.scenario_type, ScenarioType.NIA)
        self.assertEqual(new_scenario.name, "test_scenario")
        self.assertEqual(new_scenario.special_attributes["test"], "value")
    
    def test_difficulty_info_creation(self):
        """测试难度信息创建"""
        difficulty = DifficultyInfo(
            difficulty_level=DifficultyLevel.NORMAL,
            name="normal",
            display_name="普通",
            description="普通难度",
            reward_multiplier=1.0,
            experience_multiplier=1.0
        )
        
        self.assertEqual(difficulty.difficulty_level, DifficultyLevel.NORMAL)
        self.assertEqual(difficulty.name, "normal")
        self.assertEqual(difficulty.display_name, "普通")
        self.assertEqual(difficulty.reward_multiplier, 1.0)
        self.assertEqual(difficulty.experience_multiplier, 1.0)
        self.assertTrue(difficulty.is_available)
        self.assertEqual(difficulty.order, 0)
    
    def test_difficulty_info_validation(self):
        """测试难度信息验证"""
        # 测试空名称
        with self.assertRaises(ValueError):
            DifficultyInfo(
                difficulty_level=DifficultyLevel.NORMAL,
                name="",
                display_name="测试",
                description="测试"
            )
        
        # 测试无效倍率
        with self.assertRaises(ValueError):
            DifficultyInfo(
                difficulty_level=DifficultyLevel.NORMAL,
                name="test",
                display_name="测试",
                description="测试",
                reward_multiplier=0
            )
        
        with self.assertRaises(ValueError):
            DifficultyInfo(
                difficulty_level=DifficultyLevel.NORMAL,
                name="test",
                display_name="测试",
                description="测试",
                experience_multiplier=-1
            )
    
    def test_difficulty_info_serialization(self):
        """测试难度信息序列化"""
        difficulty = DifficultyInfo(
            difficulty_level=DifficultyLevel.MASTER,
            name="master",
            display_name="大师",
            description="大师难度",
            reward_multiplier=1.5,
            experience_multiplier=1.3,
            special_effects={"bonus": True}
        )
        
        # 测试转换为字典
        data = difficulty.to_dict()
        self.assertEqual(data["difficulty_level"], "master")
        self.assertEqual(data["reward_multiplier"], 1.5)
        self.assertEqual(data["special_effects"]["bonus"], True)
        
        # 测试从字典创建
        new_difficulty = DifficultyInfo.from_dict(data)
        self.assertEqual(new_difficulty.difficulty_level, DifficultyLevel.MASTER)
        self.assertEqual(new_difficulty.reward_multiplier, 1.5)
        self.assertEqual(new_difficulty.special_effects["bonus"], True)
    
    def test_scenario_selection_creation(self):
        """测试剧本选择创建"""
        scenario = ScenarioInfo(
            scenario_type=ScenarioType.NIA,
            name="test_scenario",
            display_name="测试剧本",
            description="测试"
        )
        
        difficulty = DifficultyInfo(
            difficulty_level=DifficultyLevel.NORMAL,
            name="normal",
            display_name="普通",
            description="普通难度"
        )
        
        selection = ScenarioSelection(
            scenario_info=scenario,
            difficulty_info=difficulty,
            custom_settings={"test": "value"}
        )
        
        self.assertEqual(selection.scenario_info, scenario)
        self.assertEqual(selection.difficulty_info, difficulty)
        self.assertEqual(selection.custom_settings["test"], "value")
    
    def test_scenario_selection_validation(self):
        """测试剧本选择验证"""
        # 不可用的剧本
        scenario = ScenarioInfo(
            scenario_type=ScenarioType.NIA,
            name="test_scenario",
            display_name="测试剧本",
            description="测试",
            is_available=False
        )
        
        difficulty = DifficultyInfo(
            difficulty_level=DifficultyLevel.NORMAL,
            name="normal",
            display_name="普通",
            description="普通难度"
        )
        
        with self.assertRaises(ValueError):
            ScenarioSelection(scenario_info=scenario, difficulty_info=difficulty)
        
        # 不可用的难度
        scenario.is_available = True
        difficulty.is_available = False
        
        with self.assertRaises(ValueError):
            ScenarioSelection(scenario_info=scenario, difficulty_info=difficulty)
    
    def test_scenario_selection_multipliers(self):
        """测试剧本选择倍率计算"""
        scenario = ScenarioInfo(
            scenario_type=ScenarioType.NIA,
            name="test_scenario",
            display_name="测试剧本",
            description="测试",
            special_attributes={
                "reward_multiplier": 1.2,
                "experience_multiplier": 1.1
            }
        )
        
        difficulty = DifficultyInfo(
            difficulty_level=DifficultyLevel.MASTER,
            name="master",
            display_name="大师",
            description="大师难度",
            reward_multiplier=1.5,
            experience_multiplier=1.3
        )
        
        selection = ScenarioSelection(
            scenario_info=scenario,
            difficulty_info=difficulty,
            custom_settings={
                "reward_multiplier": 1.1,
                "experience_multiplier": 1.05
            }
        )
        
        # 测试总倍率计算 (1.5 * 1.2 * 1.1 = 1.98)
        expected_reward = 1.5 * 1.2 * 1.1
        expected_experience = 1.3 * 1.1 * 1.05
        
        self.assertAlmostEqual(selection.get_total_reward_multiplier(), expected_reward, places=2)
        self.assertAlmostEqual(selection.get_total_experience_multiplier(), expected_experience, places=2)
    
    def test_scenario_selection_serialization(self):
        """测试剧本选择序列化"""
        scenario = ScenarioInfo(
            scenario_type=ScenarioType.NIA,
            name="test_scenario",
            display_name="测试剧本",
            description="测试"
        )
        
        difficulty = DifficultyInfo(
            difficulty_level=DifficultyLevel.NORMAL,
            name="normal",
            display_name="普通",
            description="普通难度"
        )
        
        selection = ScenarioSelection(
            scenario_info=scenario,
            difficulty_info=difficulty,
            custom_settings={"test": "value"}
        )
        
        # 测试转换为字典
        data = selection.to_dict()
        self.assertIn("scenario_info", data)
        self.assertIn("difficulty_info", data)
        self.assertIn("total_reward_multiplier", data)
        self.assertIn("total_experience_multiplier", data)
        
        # 测试从字典创建
        new_selection = ScenarioSelection.from_dict(data)
        self.assertEqual(new_selection.scenario_info.name, "test_scenario")
        self.assertEqual(new_selection.difficulty_info.name, "normal")
        self.assertEqual(new_selection.custom_settings["test"], "value")


class TestScenarioFactory(BaseTestCase):
    """剧本工厂测试"""
    
    def test_create_scenario_info(self):
        """测试创建剧本信息"""
        scenario = ScenarioFactory.create_scenario_info(
            scenario_type=ScenarioType.NIA,
            name="test_scenario",
            display_name="测试剧本",
            description="测试描述"
        )
        
        self.assertEqual(scenario.scenario_type, ScenarioType.NIA)
        self.assertEqual(scenario.name, "test_scenario")
        self.assertEqual(scenario.display_name, "测试剧本")
        self.assertEqual(scenario.description, "测试描述")
    
    def test_create_difficulty_info(self):
        """测试创建难度信息"""
        difficulty = ScenarioFactory.create_difficulty_info(
            difficulty_level=DifficultyLevel.MASTER,
            name="master",
            display_name="大师",
            description="大师难度",
            reward_multiplier=1.5
        )
        
        self.assertEqual(difficulty.difficulty_level, DifficultyLevel.MASTER)
        self.assertEqual(difficulty.name, "master")
        self.assertEqual(difficulty.reward_multiplier, 1.5)
    
    def test_create_nia_scenario(self):
        """测试创建NIA剧本"""
        scenario = ScenarioFactory.create_nia_scenario()
        
        self.assertEqual(scenario.scenario_type, ScenarioType.NIA)
        self.assertEqual(scenario.name, "nia_scenario")
        self.assertEqual(scenario.display_name, "NIA剧本")
        self.assertIn("focus_stats", scenario.special_attributes)
        self.assertIn("vocal", scenario.special_attributes["focus_stats"])
    
    def test_create_hajime_scenario(self):
        """测试创建HAJIME剧本"""
        scenario = ScenarioFactory.create_hajime_scenario()
        
        self.assertEqual(scenario.scenario_type, ScenarioType.HAJIME)
        self.assertEqual(scenario.name, "hajime_scenario")
        self.assertEqual(scenario.display_name, "HAJIME剧本")
        self.assertEqual(scenario.special_attributes["reward_multiplier"], 1.1)
    
    def test_create_normal_difficulty(self):
        """测试创建普通难度"""
        difficulty = ScenarioFactory.create_normal_difficulty()
        
        self.assertEqual(difficulty.difficulty_level, DifficultyLevel.NORMAL)
        self.assertEqual(difficulty.name, "normal")
        self.assertEqual(difficulty.reward_multiplier, 1.0)
        self.assertEqual(difficulty.order, 1)
        self.assertEqual(difficulty.color_theme, "green")
    
    def test_create_master_difficulty(self):
        """测试创建大师难度"""
        difficulty = ScenarioFactory.create_master_difficulty()
        
        self.assertEqual(difficulty.difficulty_level, DifficultyLevel.MASTER)
        self.assertEqual(difficulty.name, "master")
        self.assertEqual(difficulty.reward_multiplier, 1.5)
        self.assertEqual(difficulty.order, 2)
        self.assertEqual(difficulty.color_theme, "red")
    
    def test_create_quick_selection(self):
        """测试快速创建剧本选择"""
        selection = ScenarioFactory.create_quick_selection(
            scenario_type=ScenarioType.NIA,
            difficulty_level=DifficultyLevel.NORMAL,
            custom_settings={"test": "value"}
        )
        
        self.assertEqual(selection.scenario_info.scenario_type, ScenarioType.NIA)
        self.assertEqual(selection.difficulty_info.difficulty_level, DifficultyLevel.NORMAL)
        self.assertEqual(selection.custom_settings["test"], "value")
    
    def test_create_quick_selection_invalid(self):
        """测试快速创建无效剧本选择"""
        with self.assertRaises(ValueError):
            ScenarioFactory.create_quick_selection(
                scenario_type=ScenarioType.UNKNOWN,
                difficulty_level=DifficultyLevel.NORMAL
            )
        
        with self.assertRaises(ValueError):
            ScenarioFactory.create_quick_selection(
                scenario_type=ScenarioType.NIA,
                difficulty_level=DifficultyLevel.UNKNOWN
            )
    
    def test_get_default_scenarios(self):
        """测试获取默认剧本列表"""
        scenarios = ScenarioFactory.get_default_scenarios()
        
        self.assertEqual(len(scenarios), 2)
        scenario_types = [s.scenario_type for s in scenarios]
        self.assertIn(ScenarioType.NIA, scenario_types)
        self.assertIn(ScenarioType.HAJIME, scenario_types)
    
    def test_get_default_difficulties(self):
        """测试获取默认难度列表"""
        difficulties = ScenarioFactory.get_default_difficulties()
        
        self.assertEqual(len(difficulties), 2)
        difficulty_levels = [d.difficulty_level for d in difficulties]
        self.assertIn(DifficultyLevel.NORMAL, difficulty_levels)
        self.assertIn(DifficultyLevel.MASTER, difficulty_levels)
    
    def test_create_from_config(self):
        """测试从配置创建剧本选择"""
        config = {
            "scenario": {
                "scenario_type": "nia",
                "name": "test_scenario",
                "display_name": "测试剧本",
                "description": "测试"
            },
            "difficulty": {
                "difficulty_level": "normal",
                "name": "normal",
                "display_name": "普通",
                "description": "普通难度"
            },
            "custom_settings": {
                "test": "value"
            }
        }
        
        selection = ScenarioFactory.create_from_config(config)
        
        self.assertEqual(selection.scenario_info.scenario_type, ScenarioType.NIA)
        self.assertEqual(selection.difficulty_info.difficulty_level, DifficultyLevel.NORMAL)
        self.assertEqual(selection.custom_settings["test"], "value")


class TestScenarioManager(BaseTestCase):
    """剧本管理器测试"""

    def setUp(self):
        """测试前置设置"""
        super().setUp()
        # 创建临时配置目录
        self.temp_config_dir = tempfile.mkdtemp()
        self.scenario_manager = ScenarioManager(config_path=self.temp_config_dir)

    def tearDown(self):
        """测试后置清理"""
        super().tearDown()
        # 清理临时目录
        if os.path.exists(self.temp_config_dir):
            shutil.rmtree(self.temp_config_dir)

    def test_manager_initialization(self):
        """测试管理器初始化"""
        self.assertIsNotNone(self.scenario_manager)
        self.assertTrue(os.path.exists(self.temp_config_dir))

        # 检查是否创建了默认配置文件
        scenarios_file = os.path.join(self.temp_config_dir, "scenarios.yaml")
        difficulties_file = os.path.join(self.temp_config_dir, "difficulties.yaml")
        self.assertTrue(os.path.exists(scenarios_file))
        self.assertTrue(os.path.exists(difficulties_file))

    def test_get_available_scenarios(self):
        """测试获取可用剧本"""
        scenarios = self.scenario_manager.get_available_scenarios()

        self.assertGreater(len(scenarios), 0)
        for scenario in scenarios:
            self.assertTrue(scenario.is_available)

    def test_get_available_difficulties(self):
        """测试获取可用难度"""
        difficulties = self.scenario_manager.get_available_difficulties()

        self.assertGreater(len(difficulties), 0)
        for difficulty in difficulties:
            self.assertTrue(difficulty.is_available)

        # 检查排序
        orders = [d.order for d in difficulties]
        self.assertEqual(orders, sorted(orders))

    def test_get_scenario(self):
        """测试获取指定剧本"""
        scenario = self.scenario_manager.get_scenario(ScenarioType.NIA)
        self.assertIsNotNone(scenario)
        self.assertEqual(scenario.scenario_type, ScenarioType.NIA)

        # 测试不存在的剧本
        unknown_scenario = self.scenario_manager.get_scenario(ScenarioType.UNKNOWN)
        self.assertIsNone(unknown_scenario)

    def test_get_difficulty(self):
        """测试获取指定难度"""
        difficulty = self.scenario_manager.get_difficulty(DifficultyLevel.NORMAL)
        self.assertIsNotNone(difficulty)
        self.assertEqual(difficulty.difficulty_level, DifficultyLevel.NORMAL)

        # 测试不存在的难度
        unknown_difficulty = self.scenario_manager.get_difficulty(DifficultyLevel.UNKNOWN)
        self.assertIsNone(unknown_difficulty)

    def test_validate_selection(self):
        """测试验证剧本选择"""
        # 测试有效选择
        is_valid, message = self.scenario_manager.validate_selection(
            ScenarioType.NIA, DifficultyLevel.NORMAL
        )
        self.assertTrue(is_valid)
        self.assertEqual(message, "选择有效")

        # 测试无效剧本
        is_valid, message = self.scenario_manager.validate_selection(
            ScenarioType.UNKNOWN, DifficultyLevel.NORMAL
        )
        self.assertFalse(is_valid)
        self.assertIn("不存在", message)

        # 测试无效难度
        is_valid, message = self.scenario_manager.validate_selection(
            ScenarioType.NIA, DifficultyLevel.UNKNOWN
        )
        self.assertFalse(is_valid)
        self.assertIn("不存在", message)

    def test_create_selection(self):
        """测试创建剧本选择"""
        selection = self.scenario_manager.create_selection(
            ScenarioType.NIA,
            DifficultyLevel.NORMAL,
            custom_settings={"test": "value"}
        )

        self.assertIsNotNone(selection)
        self.assertEqual(selection.scenario_info.scenario_type, ScenarioType.NIA)
        self.assertEqual(selection.difficulty_info.difficulty_level, DifficultyLevel.NORMAL)
        self.assertEqual(selection.custom_settings["test"], "value")

        # 检查是否保存为当前选择
        current = self.scenario_manager.get_current_selection()
        self.assertEqual(current, selection)

    def test_create_invalid_selection(self):
        """测试创建无效剧本选择"""
        with self.assertRaises(ValueError):
            self.scenario_manager.create_selection(
                ScenarioType.UNKNOWN,
                DifficultyLevel.NORMAL
            )

    def test_clear_selection(self):
        """测试清除选择"""
        # 先创建选择
        self.scenario_manager.create_selection(
            ScenarioType.NIA,
            DifficultyLevel.NORMAL
        )
        self.assertIsNotNone(self.scenario_manager.get_current_selection())

        # 清除选择
        self.scenario_manager.clear_selection()
        self.assertIsNone(self.scenario_manager.get_current_selection())

    def test_add_scenario(self):
        """测试添加剧本"""
        new_scenario = ScenarioInfo(
            scenario_type=ScenarioType.UNKNOWN,
            name="custom_scenario",
            display_name="自定义剧本",
            description="测试用自定义剧本"
        )

        self.scenario_manager.add_scenario(new_scenario)

        retrieved = self.scenario_manager.get_scenario(ScenarioType.UNKNOWN)
        self.assertIsNotNone(retrieved)
        self.assertEqual(retrieved.name, "custom_scenario")

    def test_add_difficulty(self):
        """测试添加难度"""
        new_difficulty = DifficultyInfo(
            difficulty_level=DifficultyLevel.UNKNOWN,
            name="custom_difficulty",
            display_name="自定义难度",
            description="测试用自定义难度"
        )

        self.scenario_manager.add_difficulty(new_difficulty)

        retrieved = self.scenario_manager.get_difficulty(DifficultyLevel.UNKNOWN)
        self.assertIsNotNone(retrieved)
        self.assertEqual(retrieved.name, "custom_difficulty")

    def test_get_selection_summary(self):
        """测试获取选择摘要"""
        # 测试无选择情况
        summary = self.scenario_manager.get_selection_summary()
        self.assertFalse(summary["has_selection"])

        # 创建选择后测试
        self.scenario_manager.create_selection(
            ScenarioType.NIA,
            DifficultyLevel.MASTER,
            custom_settings={"bonus": 1.2}
        )

        summary = self.scenario_manager.get_selection_summary()
        self.assertTrue(summary["has_selection"])
        self.assertEqual(summary["scenario"]["type"], "nia")
        self.assertEqual(summary["difficulty"]["level"], "master")
        self.assertIn("multipliers", summary)
        self.assertIn("reward", summary["multipliers"])
        self.assertIn("experience", summary["multipliers"])


class TestScenarioSystemIntegration(BaseTestCase):
    """剧本系统集成测试"""

    def setUp(self):
        """测试前置设置"""
        super().setUp()
        self.temp_config_dir = tempfile.mkdtemp()
        self.scenario_manager = ScenarioManager(config_path=self.temp_config_dir)

    def tearDown(self):
        """测试后置清理"""
        super().tearDown()
        if os.path.exists(self.temp_config_dir):
            shutil.rmtree(self.temp_config_dir)

    def test_complete_workflow(self):
        """测试完整工作流程"""
        # 1. 获取可用剧本和难度
        scenarios = self.scenario_manager.get_available_scenarios()
        difficulties = self.scenario_manager.get_available_difficulties()

        self.assertGreater(len(scenarios), 0)
        self.assertGreater(len(difficulties), 0)

        # 2. 验证选择
        scenario_type = scenarios[0].scenario_type
        difficulty_level = difficulties[0].difficulty_level

        is_valid, _ = self.scenario_manager.validate_selection(
            scenario_type, difficulty_level
        )
        self.assertTrue(is_valid)

        # 3. 创建选择
        selection = self.scenario_manager.create_selection(
            scenario_type,
            difficulty_level,
            custom_settings={"test_mode": True}
        )

        self.assertIsNotNone(selection)

        # 4. 获取摘要
        summary = self.scenario_manager.get_selection_summary()
        self.assertTrue(summary["has_selection"])

        # 5. 清除选择
        self.scenario_manager.clear_selection()
        self.assertIsNone(self.scenario_manager.get_current_selection())

    def test_factory_manager_integration(self):
        """测试工厂和管理器集成"""
        # 使用工厂创建剧本和难度
        scenario = ScenarioFactory.create_nia_scenario()
        difficulty = ScenarioFactory.create_master_difficulty()

        # 添加到管理器
        self.scenario_manager.add_scenario(scenario)
        self.scenario_manager.add_difficulty(difficulty)

        # 验证添加成功
        retrieved_scenario = self.scenario_manager.get_scenario(ScenarioType.NIA)
        retrieved_difficulty = self.scenario_manager.get_difficulty(DifficultyLevel.MASTER)

        self.assertEqual(retrieved_scenario.name, scenario.name)
        self.assertEqual(retrieved_difficulty.name, difficulty.name)

        # 创建选择
        selection = self.scenario_manager.create_selection(
            ScenarioType.NIA,
            DifficultyLevel.MASTER
        )

        # 验证倍率计算
        expected_reward = difficulty.reward_multiplier * scenario.special_attributes.get("reward_multiplier", 1.0)
        self.assertAlmostEqual(selection.get_total_reward_multiplier(), expected_reward, places=2)


if __name__ == '__main__':
    unittest.main()
