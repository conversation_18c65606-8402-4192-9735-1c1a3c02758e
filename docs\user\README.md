# 用户手册

## 欢迎使用Gakumasu-Bot UI模块

本手册将帮助您快速上手并充分利用Gakumasu-Bot UI模块的各项功能。

## 目录

- [快速开始](#快速开始)
- [基本概念](#基本概念)
- [功能介绍](#功能介绍)
- [使用指南](#使用指南)
- [常见问题](#常见问题)
- [技术支持](#技术支持)

## 快速开始

### 系统要求

在开始使用之前，请确保您的系统满足以下要求：

- **操作系统**：Windows 10/11 (64位)
- **内存**：至少8GB RAM（推荐16GB）
- **显示器**：1920x1080分辨率或更高
- **游戏版本**：学园偶像大师 v1.0或更高版本
- **Python**：3.8或更高版本（如果需要自定义开发）

### 安装步骤

1. **下载程序**
   - 从官方网站下载最新版本的安装包
   - 或从GitHub仓库克隆源代码

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **配置设置**
   - 运行配置向导：`python setup_wizard.py`
   - 或手动编辑配置文件：`config/ui_config.yaml`

4. **验证安装**
   ```bash
   python -m src.modules.ui.test_installation
   ```

### 首次运行

1. **启动游戏**
   - 确保学园偶像大师游戏已启动
   - 游戏应处于主菜单界面

2. **运行UI模块**
   ```bash
   python main.py
   ```

3. **验证连接**
   - 程序会自动检测游戏窗口
   - 在控制台中查看连接状态

## 基本概念

### UI元素

UI元素是游戏界面中可以交互的组件，包括：

- **按钮 (Button)**：可点击的界面元素
- **输入框 (InputField)**：可输入文本的区域
- **标签 (Label)**：显示文本信息的区域
- **滑块 (Slider)**：可拖动调节数值的控件
- **下拉框 (Dropdown)**：可选择选项的菜单

### 游戏场景

游戏场景代表游戏的不同界面状态：

- **主菜单场景**：游戏主界面
- **训练场景**：偶像训练界面
- **比赛场景**：比赛相关界面
- **设置场景**：游戏设置界面

### 自动化工作流

自动化工作流是预定义的操作序列，可以：

- 自动执行重复性任务
- 根据条件选择不同的操作路径
- 记录和回放操作序列
- 优化操作效率

## 功能介绍

### 1. 智能场景识别

系统能够自动识别当前游戏场景，并提供相应的操作选项：

- **实时场景检测**：持续监控游戏界面变化
- **场景切换跟踪**：记录场景转换历史
- **异常场景处理**：处理未知或异常场景

### 2. UI元素自动定位

精确定位游戏界面中的各种UI元素：

- **视觉识别**：基于图像识别技术
- **文本识别**：OCR文字识别功能
- **自适应定位**：适应不同分辨率和界面变化

### 3. 智能决策辅助

提供AI辅助决策功能：

- **策略推荐**：基于历史数据推荐最优策略
- **风险评估**：评估不同选择的风险和收益
- **个性化建议**：根据用户偏好提供定制建议

### 4. 自动化执行

支持复杂的自动化操作：

- **工作流定义**：可视化定义操作流程
- **条件分支**：根据条件执行不同操作
- **循环控制**：支持重复执行操作
- **异常处理**：自动处理执行过程中的异常

### 5. 性能监控

实时监控系统性能：

- **响应时间监控**：监控操作响应时间
- **内存使用监控**：监控内存使用情况
- **错误率统计**：统计操作成功率
- **性能优化建议**：提供性能优化建议

## 使用指南

### 基本操作

#### 1. 启动系统

```bash
# 启动主程序
python main.py

# 启动调试模式
python main.py --debug

# 指定配置文件
python main.py --config custom_config.yaml
```

#### 2. 场景操作

```python
from src.modules.ui.scenes.scene_manager import SceneManager
from src.core.data_structures import GameScene

# 创建场景管理器
scene_manager = SceneManager()

# 检测当前场景
current_scene = scene_manager.detect_current_scene()
print(f"当前场景: {current_scene}")

# 切换到训练场景
success = scene_manager.switch_to_scene(GameScene.TRAINING)
if success:
    print("成功切换到训练场景")
```

#### 3. UI元素操作

```python
from src.modules.ui.elements.button import Button
from src.modules.ui.elements.input_field import InputField

# 创建按钮并点击
start_button = Button("开始训练按钮")
if start_button.is_visible():
    start_button.click()

# 创建输入框并输入文本
name_input = InputField("角色名称输入框")
name_input.input_text("我的偶像", clear_first=True)
```

### 高级功能

#### 1. 自定义工作流

```python
from src.modules.ui.intelligence.automation_engine import AutomationEngine
from src.modules.ui.intelligence.workflow_manager import WorkflowManager

# 创建自动化引擎
automation = AutomationEngine()

# 定义工作流步骤
steps = [
    {"action": "click_button", "target": "训练按钮"},
    {"action": "select_course", "target": "声乐课程"},
    {"action": "confirm_selection", "target": "确认按钮"}
]

# 执行工作流
workflow_id = automation.execute_workflow(steps)
```

#### 2. 智能决策

```python
from src.modules.ui.intelligence.decision_assistant import DecisionAssistant

# 创建决策助手
assistant = DecisionAssistant()

# 获取当前状态
current_state = {
    "stamina": 80,
    "vocal_level": 45,
    "dance_level": 38,
    "visual_level": 52
}

# 获取推荐行动
recommendation = assistant.get_recommendation(current_state)
print(f"推荐行动: {recommendation}")
```

#### 3. 性能监控

```python
from src.modules.ui.optimization.memory_optimizer import MemoryOptimizer
from src.modules.ui.optimization.rendering_optimizer import RenderingOptimizer

# 内存优化
memory_optimizer = MemoryOptimizer()
memory_optimizer.start_monitoring()

# 渲染优化
rendering_optimizer = RenderingOptimizer()
rendering_optimizer.start_rendering()

# 获取性能报告
memory_report = memory_optimizer.get_optimization_report()
rendering_report = rendering_optimizer.get_optimization_report()
```

### 配置管理

#### 1. 基本配置

编辑 `config/ui_config.yaml`：

```yaml
# UI元素配置
ui_elements:
  default_timeout: 5.0
  confidence_threshold: 0.8
  retry_count: 3

# 场景配置
scenes:
  transition_timeout: 30.0
  auto_detect: true

# 性能配置
performance:
  enable_caching: true
  cache_duration: 60.0
  max_memory_usage: 1024  # MB
```

#### 2. 高级配置

```yaml
# 智能化功能配置
intelligence:
  decision_assistant:
    enabled: true
    learning_rate: 0.01
    model_path: "models/decision_model.pkl"
  
  automation_engine:
    enabled: true
    max_concurrent_workflows: 5
    workflow_timeout: 300.0

# 优化配置
optimization:
  memory_optimizer:
    monitoring_interval: 30.0
    cleanup_threshold: 0.8
  
  rendering_optimizer:
    target_fps: 60.0
    enable_batching: true
```

### 故障排除

#### 常见问题及解决方案

**问题1：无法检测到游戏窗口**

解决方案：
1. 确保游戏已启动并处于前台
2. 检查游戏窗口标题是否正确
3. 尝试以管理员权限运行程序

**问题2：UI元素识别失败**

解决方案：
1. 检查游戏分辨率设置
2. 调整置信度阈值
3. 更新UI元素配置文件

**问题3：操作响应缓慢**

解决方案：
1. 启用性能优化功能
2. 增加系统内存
3. 关闭不必要的后台程序

**问题4：自动化流程中断**

解决方案：
1. 检查网络连接状态
2. 增加操作超时时间
3. 启用错误恢复功能

#### 日志分析

查看日志文件了解详细错误信息：

```bash
# 查看主日志
tail -f logs/ui_module.log

# 查看错误日志
tail -f logs/error.log

# 查看性能日志
tail -f logs/performance.log
```

#### 调试模式

启用调试模式获取更多信息：

```bash
# 启用调试模式
python main.py --debug --log-level DEBUG

# 保存调试截图
python main.py --debug --save-screenshots
```

## 最佳实践

### 1. 配置优化

- 根据您的硬件配置调整性能参数
- 定期更新UI元素配置以适应游戏更新
- 使用配置模板快速部署到多台设备

### 2. 工作流设计

- 将复杂任务分解为简单步骤
- 添加适当的错误处理和重试机制
- 使用条件分支处理不同情况

### 3. 性能优化

- 启用缓存功能减少重复计算
- 合理设置超时时间避免长时间等待
- 定期清理日志文件和临时文件

### 4. 安全使用

- 不要在重要账号上使用未经测试的自动化脚本
- 定期备份配置文件和重要数据
- 遵守游戏服务条款和使用规则

## 更新和维护

### 版本更新

1. **检查更新**
   ```bash
   python update_checker.py
   ```

2. **下载更新**
   - 从官方网站下载最新版本
   - 或使用Git拉取最新代码

3. **应用更新**
   ```bash
   python update_installer.py
   ```

### 配置迁移

升级版本时，使用配置迁移工具：

```bash
python config_migrator.py --from-version 1.0 --to-version 2.0
```

### 数据备份

定期备份重要数据：

```bash
# 备份配置文件
python backup_tool.py --backup-config

# 备份用户数据
python backup_tool.py --backup-data

# 完整备份
python backup_tool.py --full-backup
```

## 技术支持

### 获取帮助

如果您遇到问题或需要帮助，可以通过以下方式获取支持：

1. **查看文档**
   - [技术文档](../README.md)
   - [API参考](../api/README.md)
   - [开发指南](../development/README.md)

2. **社区支持**
   - GitHub Issues：报告bug和功能请求
   - 讨论区：与其他用户交流经验
   - Wiki：查看社区贡献的教程和技巧

3. **联系我们**
   - 邮件支持：<EMAIL>
   - 技术咨询：<EMAIL>
   - 商务合作：<EMAIL>

### 反馈建议

我们欢迎您的反馈和建议：

- **功能建议**：通过GitHub Issues提交功能请求
- **Bug报告**：详细描述问题和复现步骤
- **文档改进**：指出文档中的错误或不清楚的地方
- **用户体验**：分享您的使用体验和改进建议

### 贡献代码

如果您有编程经验，欢迎贡献代码：

1. Fork项目仓库
2. 创建功能分支
3. 编写代码和测试
4. 提交Pull Request
5. 参与代码审查

详细信息请参考[贡献指南](../CONTRIBUTING.md)。

---

感谢您使用Gakumasu-Bot UI模块！我们致力于为您提供最好的自动化体验。
