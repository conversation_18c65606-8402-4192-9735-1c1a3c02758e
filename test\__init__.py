"""
测试模块
提供完整的测试框架，包括单元测试、集成测试和性能测试
"""

import sys
import os
import unittest
import pytest
from typing import Dict, Any, List

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 测试配置
TEST_CONFIG = {
    "test_data_dir": os.path.join(os.path.dirname(__file__), "data"),
    "temp_dir": os.path.join(os.path.dirname(__file__), "temp"),
    "log_level": "DEBUG",
    "timeout": 30.0,
    "performance_threshold": {
        "ui_element_creation": 0.1,  # 秒
        "scene_transition": 0.5,
        "memory_usage": 100 * 1024 * 1024,  # 100MB
    }
}

# 创建必要的目录
os.makedirs(TEST_CONFIG["test_data_dir"], exist_ok=True)
os.makedirs(TEST_CONFIG["temp_dir"], exist_ok=True)

__all__ = ['TEST_CONFIG']
