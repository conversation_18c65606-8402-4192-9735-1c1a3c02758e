# 截图工具前端UI设计方案

**版本：** 1.0  
**日期：** 2025年7月27日  
**目标：** 为截图数据收集工具设计完整的前端用户界面

## 一、界面设计概览

### 1.1 整体布局
```
┌─────────────────────────────────────────────────────────────┐
│                    Gakumasu-Bot 控制面板                    │
├─────────────────────────────────────────────────────────────┤
│ 首页 | 截图工具 ← 新增                                      │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────┐  ┌─────────────────────┐  ┌─────────────┐  │
│  │             │  │                     │  │             │  │
│  │   控制面板   │  │      预览区域        │  │  历史记录    │  │
│  │             │  │                     │  │             │  │
│  │ - 截图模式   │  │   游戏窗口实时预览   │  │ - 截图列表   │  │
│  │ - 区域选择   │  │   区域选择覆盖层     │  │ - 文件预览   │  │
│  │ - 快速操作   │  │   预览控制工具       │  │ - 批量操作   │  │
│  │ - 任务状态   │  │                     │  │             │  │
│  │             │  │                     │  │             │  │
│  └─────────────┘  └─────────────────────┘  └─────────────┘  │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 组件架构
```
ScreenshotTool.vue (主页面)
├── ControlPanel.vue (左侧控制面板)
│   ├── ModeSelector.vue (模式选择器)
│   ├── RegionSelector.vue (区域选择器)
│   ├── QuickActions.vue (快速操作)
│   └── TaskStatus.vue (任务状态)
├── PreviewArea.vue (中间预览区域)
│   ├── GamePreview.vue (游戏预览)
│   ├── RegionOverlay.vue (区域覆盖层)
│   └── PreviewControls.vue (预览控制)
└── HistoryPanel.vue (右侧历史面板)
    ├── HistoryList.vue (历史列表)
    ├── FilePreview.vue (文件预览)
    └── BatchOperations.vue (批量操作)
```

## 二、核心组件设计

### 2.1 主页面组件 (ScreenshotTool.vue)

#### 2.1.1 组件结构
```vue
<template>
  <div class="screenshot-tool">
    <div class="tool-header">
      <h2>游戏截图数据收集工具</h2>
      <div class="connection-status">
        <el-tag :type="connectionStatus === 'connected' ? 'success' : 'danger'">
          {{ connectionStatus === 'connected' ? '已连接' : '未连接' }}
        </el-tag>
      </div>
    </div>
    
    <div class="tool-content">
      <div class="left-panel">
        <ControlPanel 
          :config="screenshotConfig"
          :loading="loading"
          @config-change="handleConfigChange"
          @capture="handleCapture"
        />
      </div>
      
      <div class="center-panel">
        <PreviewArea
          :preview-url="previewUrl"
          :selected-region="selectedRegion"
          :loading="previewLoading"
          @region-select="handleRegionSelect"
          @refresh="refreshPreview"
        />
      </div>
      
      <div class="right-panel">
        <HistoryPanel
          :history="screenshotHistory"
          :loading="historyLoading"
          @delete="handleDelete"
          @download="handleDownload"
          @refresh="refreshHistory"
        />
      </div>
    </div>
  </div>
</template>
```

#### 2.1.2 状态管理
```javascript
export default {
  name: 'ScreenshotTool',
  data() {
    return {
      // 连接状态
      connectionStatus: 'disconnected',
      websocket: null,
      
      // 截图配置
      screenshotConfig: {
        mode: 'window', // 'fullscreen', 'window', 'region'
        quality: 90,
        format: 'png'
      },
      
      // 预览相关
      previewUrl: null,
      previewLoading: false,
      selectedRegion: null,
      
      // 历史记录
      screenshotHistory: [],
      historyLoading: false,
      
      // 全局状态
      loading: false
    }
  }
}
```

### 2.2 控制面板组件 (ControlPanel.vue)

#### 2.2.1 模式选择器
```vue
<template>
  <div class="control-panel">
    <el-card header="截图模式">
      <el-radio-group v-model="config.mode" @change="onModeChange">
        <el-radio label="fullscreen">
          <el-icon><FullScreen /></el-icon>
          全屏截图
        </el-radio>
        <el-radio label="window">
          <el-icon><Monitor /></el-icon>
          窗口截图
        </el-radio>
        <el-radio label="region">
          <el-icon><Crop /></el-icon>
          区域截图
        </el-radio>
      </el-radio-group>
    </el-card>
    
    <el-card header="截图设置" class="mt-3">
      <el-form label-width="80px">
        <el-form-item label="图片质量">
          <el-slider 
            v-model="config.quality" 
            :min="10" 
            :max="100" 
            show-input
          />
        </el-form-item>
        <el-form-item label="图片格式">
          <el-select v-model="config.format">
            <el-option label="PNG" value="png" />
            <el-option label="JPEG" value="jpeg" />
          </el-select>
        </el-form-item>
      </el-form>
    </el-card>
    
    <el-card header="快速操作" class="mt-3">
      <div class="quick-actions">
        <el-button 
          type="primary" 
          :loading="loading"
          @click="$emit('capture')"
          block
        >
          <el-icon><Camera /></el-icon>
          立即截图
        </el-button>
        <el-button 
          type="success" 
          @click="startPreview"
          block
          class="mt-2"
        >
          <el-icon><VideoPlay /></el-icon>
          开始预览
        </el-button>
      </div>
    </el-card>
  </div>
</template>
```

### 2.3 预览区域组件 (PreviewArea.vue)

#### 2.3.1 游戏预览实现
```vue
<template>
  <div class="preview-area">
    <el-card header="游戏窗口预览">
      <div class="preview-container" v-loading="loading">
        <div class="preview-canvas-wrapper">
          <canvas 
            ref="previewCanvas"
            class="preview-canvas"
            @mousedown="startRegionSelect"
            @mousemove="updateRegionSelect"
            @mouseup="endRegionSelect"
          ></canvas>
          
          <!-- 区域选择覆盖层 -->
          <div 
            v-if="isSelecting || selectedRegion"
            class="region-overlay"
            :style="regionOverlayStyle"
          >
            <div class="region-info">
              {{ regionInfo }}
            </div>
          </div>
        </div>
        
        <div class="preview-controls">
          <el-button-group>
            <el-button @click="refreshPreview">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button @click="clearSelection">
              <el-icon><Close /></el-icon>
              清除选择
            </el-button>
            <el-button @click="fitToWindow">
              <el-icon><FullScreen /></el-icon>
              适应窗口
            </el-button>
          </el-button-group>
          
          <div class="preview-info">
            <span>更新时间: {{ lastUpdate }}</span>
            <span>FPS: {{ fps }}</span>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>
```

#### 2.3.2 区域选择逻辑
```javascript
export default {
  name: 'PreviewArea',
  data() {
    return {
      isSelecting: false,
      selectionStart: null,
      currentRegion: null,
      lastUpdate: null,
      fps: 0
    }
  },
  
  methods: {
    startRegionSelect(event) {
      if (this.config.mode !== 'region') return
      
      const rect = this.$refs.previewCanvas.getBoundingClientRect()
      const x = event.clientX - rect.left
      const y = event.clientY - rect.top
      
      this.isSelecting = true
      this.selectionStart = { x, y }
      this.currentRegion = { x, y, width: 0, height: 0 }
    },
    
    updateRegionSelect(event) {
      if (!this.isSelecting) return
      
      const rect = this.$refs.previewCanvas.getBoundingClientRect()
      const x = event.clientX - rect.left
      const y = event.clientY - rect.top
      
      this.currentRegion = {
        x: Math.min(this.selectionStart.x, x),
        y: Math.min(this.selectionStart.y, y),
        width: Math.abs(x - this.selectionStart.x),
        height: Math.abs(y - this.selectionStart.y)
      }
    },
    
    endRegionSelect() {
      if (!this.isSelecting) return
      
      this.isSelecting = false
      this.selectedRegion = { ...this.currentRegion }
      this.$emit('region-select', this.selectedRegion)
    }
  }
}
```

### 2.4 历史记录组件 (HistoryPanel.vue)

#### 2.4.1 历史列表实现
```vue
<template>
  <div class="history-panel">
    <el-card header="截图历史">
      <div class="history-controls">
        <el-input 
          v-model="searchText" 
          placeholder="搜索截图..."
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <el-button-group class="mt-2">
          <el-button @click="refreshHistory">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
          <el-button @click="clearAll">
            <el-icon><Delete /></el-icon>
            清空
          </el-button>
        </el-button-group>
      </div>
      
      <div class="history-list" v-loading="loading">
        <div 
          v-for="item in filteredHistory" 
          :key="item.id"
          class="history-item"
          @click="selectItem(item)"
        >
          <div class="item-thumbnail">
            <img :src="item.thumbnail" :alt="item.filename" />
          </div>
          <div class="item-info">
            <div class="item-name">{{ item.filename }}</div>
            <div class="item-meta">
              <span>{{ formatFileSize(item.size) }}</span>
              <span>{{ formatTime(item.created_at) }}</span>
            </div>
          </div>
          <div class="item-actions">
            <el-button-group size="small">
              <el-button @click.stop="downloadItem(item)">
                <el-icon><Download /></el-icon>
              </el-button>
              <el-button @click.stop="deleteItem(item)">
                <el-icon><Delete /></el-icon>
              </el-button>
            </el-button-group>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>
```

## 三、API 集成设计

### 3.1 API 服务类
```javascript
// frontend/src/services/screenshotApi.js
export class ScreenshotApiService {
  constructor(baseURL) {
    this.baseURL = baseURL
  }
  
  // 获取预览图像
  async getPreview() {
    const response = await fetch(`${this.baseURL}/api/v1/screenshot/preview`)
    return response.blob()
  }
  
  // 执行截图
  async captureScreenshot(config) {
    const response = await fetch(`${this.baseURL}/api/v1/screenshot/capture`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(config)
    })
    return response.json()
  }
  
  // 获取历史记录
  async getHistory() {
    const response = await fetch(`${this.baseURL}/api/v1/screenshot/history`)
    return response.json()
  }
  
  // 删除截图
  async deleteScreenshot(id) {
    const response = await fetch(`${this.baseURL}/api/v1/screenshot/${id}`, {
      method: 'DELETE'
    })
    return response.json()
  }
}
```

### 3.2 WebSocket 集成
```javascript
// WebSocket 消息处理
handleWebSocketMessage(data) {
  switch (data.type) {
    case 'screenshot_preview':
      this.updatePreview(data.image_data)
      break
    case 'screenshot_completed':
      this.onScreenshotCompleted(data.result)
      break
    case 'screenshot_error':
      this.onScreenshotError(data.error)
      break
  }
}
```

## 四、样式设计

### 4.1 主题色彩
```scss
// 截图工具专用色彩
$screenshot-primary: #409eff;
$screenshot-success: #67c23a;
$screenshot-warning: #e6a23c;
$screenshot-danger: #f56c6c;
$screenshot-info: #909399;

// 背景色彩
$bg-primary: #ffffff;
$bg-secondary: #f5f7fa;
$bg-dark: #2d3748;
```

### 4.2 布局样式
```scss
.screenshot-tool {
  height: 100vh;
  display: flex;
  flex-direction: column;
  
  .tool-content {
    flex: 1;
    display: flex;
    gap: 16px;
    padding: 16px;
    
    .left-panel {
      width: 300px;
      flex-shrink: 0;
    }
    
    .center-panel {
      flex: 1;
      min-width: 0;
    }
    
    .right-panel {
      width: 300px;
      flex-shrink: 0;
    }
  }
}
```

---

**实施准备**：前端UI设计方案已完成，等待用户确认后开始具体的开发实施工作。
