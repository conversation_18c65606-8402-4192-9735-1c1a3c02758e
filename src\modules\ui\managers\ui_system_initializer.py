"""
UI系统初始化器
负责初始化整个UI架构系统，包括工厂、管理器和配置加载
"""

import os
from pathlib import Path
from typing import Optional, Dict, Any

from ....utils.logger import get_logger
from ....core.data_structures import GameScene
from .scene_factory import EnhancedSceneFactory
from .scene_manager import EnhancedSceneManager
from .config_loader import UIConfigLoader
from .legacy_adapter import LegacyCompatibilityAdapter


class UISystemInitializer:
    """UI系统初始化器"""
    
    def __init__(self, config_dir: str = "config"):
        """
        初始化UI系统初始化器
        
        Args:
            config_dir: 配置文件目录
        """
        self.config_dir = Path(config_dir)
        self.logger = get_logger("UISystemInitializer")
        
        # 系统组件
        self.config_loader: Optional[UIConfigLoader] = None
        self.scene_factory: Optional[EnhancedSceneFactory] = None
        self.scene_manager: Optional[EnhancedSceneManager] = None
        self.legacy_adapter: Optional[LegacyCompatibilityAdapter] = None
        
        # 初始化状态
        self._initialized = False
        self._initialization_errors = []
    
    def initialize_ui_system(self, perception_module, action_controller) -> bool:
        """
        初始化UI系统
        
        Args:
            perception_module: 感知模块
            action_controller: 行动控制器
            
        Returns:
            是否初始化成功
        """
        try:
            self.logger.info("开始初始化UI系统")
            
            # 1. 初始化配置加载器
            if not self._initialize_config_loader():
                return False
            
            # 2. 初始化场景工厂
            if not self._initialize_scene_factory(perception_module, action_controller):
                return False
            
            # 3. 初始化场景管理器
            if not self._initialize_scene_manager():
                return False
            
            # 4. 初始化兼容性适配器
            if not self._initialize_legacy_adapter():
                return False
            
            # 5. 验证系统完整性
            if not self._verify_system_integrity():
                return False
            
            self._initialized = True
            self.logger.info("UI系统初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"UI系统初始化失败: {e}")
            self._initialization_errors.append(str(e))
            return False
    
    def _initialize_config_loader(self) -> bool:
        """初始化配置加载器"""
        try:
            self.logger.info("初始化配置加载器")
            
            # 检查配置目录
            if not self.config_dir.exists():
                self.logger.warning(f"配置目录不存在，创建目录: {self.config_dir}")
                self.config_dir.mkdir(parents=True, exist_ok=True)
            
            # 创建配置加载器
            self.config_loader = UIConfigLoader(str(self.config_dir))
            
            # 验证配置文件
            if not self._verify_config_files():
                self.logger.warning("配置文件验证失败，但继续初始化")
            
            self.logger.info("配置加载器初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"配置加载器初始化失败: {e}")
            self._initialization_errors.append(f"配置加载器: {e}")
            return False
    
    def _verify_config_files(self) -> bool:
        """验证配置文件"""
        required_files = [
            "scene_config.yaml",
            "ui_config.yaml"
        ]
        
        missing_files = []
        for file_name in required_files:
            file_path = self.config_dir / file_name
            if not file_path.exists():
                missing_files.append(file_name)
        
        if missing_files:
            self.logger.warning(f"缺少配置文件: {missing_files}")
            return False
        
        return True
    
    def _initialize_scene_factory(self, perception_module, action_controller) -> bool:
        """初始化场景工厂"""
        try:
            self.logger.info("初始化场景工厂")
            
            # 创建增强场景工厂
            self.scene_factory = EnhancedSceneFactory(
                perception_module, 
                action_controller, 
                self.config_loader
            )
            
            # 注册所有可用场景
            self.scene_factory.register_all_available_scenes()
            
            # 启用缓存和预加载
            self.scene_factory.set_cache_enabled(True)
            
            # 预加载核心场景
            core_scenes = [GameScene.MAIN_MENU, GameScene.PRODUCE_SETUP, GameScene.PRODUCE_MAIN]
            self.scene_factory.preload_scenes(core_scenes)
            
            # 获取注册信息
            scenes_info = self.scene_factory.get_registered_scenes_info()
            available_scenes = [info for info in scenes_info.values() if info['is_available']]
            
            self.logger.info(f"场景工厂初始化完成，可用场景: {len(available_scenes)}个")
            
            return True
            
        except Exception as e:
            self.logger.error(f"场景工厂初始化失败: {e}")
            self._initialization_errors.append(f"场景工厂: {e}")
            return False
    
    def _initialize_scene_manager(self) -> bool:
        """初始化场景管理器"""
        try:
            self.logger.info("初始化场景管理器")
            
            # 创建增强场景管理器
            self.scene_manager = EnhancedSceneManager(self.scene_factory)
            
            # 启用智能导航和路径学习
            self.scene_manager.set_navigation_optimization(True)
            self.scene_manager.set_path_learning(True)
            
            # 设置场景检查间隔
            self.scene_manager.set_scene_check_interval(1.0)
            
            self.logger.info("场景管理器初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"场景管理器初始化失败: {e}")
            self._initialization_errors.append(f"场景管理器: {e}")
            return False
    
    def _initialize_legacy_adapter(self) -> bool:
        """初始化兼容性适配器"""
        try:
            self.logger.info("初始化兼容性适配器")
            
            # 创建兼容性适配器
            self.legacy_adapter = LegacyCompatibilityAdapter(self.scene_manager)
            
            self.logger.info("兼容性适配器初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"兼容性适配器初始化失败: {e}")
            self._initialization_errors.append(f"兼容性适配器: {e}")
            return False
    
    def _verify_system_integrity(self) -> bool:
        """验证系统完整性"""
        try:
            self.logger.info("验证UI系统完整性")
            
            # 检查所有组件是否已初始化
            components = {
                "配置加载器": self.config_loader,
                "场景工厂": self.scene_factory,
                "场景管理器": self.scene_manager,
                "兼容性适配器": self.legacy_adapter
            }
            
            missing_components = []
            for name, component in components.items():
                if component is None:
                    missing_components.append(name)
            
            if missing_components:
                self.logger.error(f"缺少组件: {missing_components}")
                return False
            
            # 验证场景工厂功能
            if not self._verify_scene_factory():
                return False
            
            # 验证场景管理器功能
            if not self._verify_scene_manager():
                return False
            
            self.logger.info("UI系统完整性验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"系统完整性验证失败: {e}")
            self._initialization_errors.append(f"完整性验证: {e}")
            return False
    
    def _verify_scene_factory(self) -> bool:
        """验证场景工厂功能"""
        try:
            # 检查是否有可用场景
            supported_scenes = self.scene_factory.get_supported_scenes()
            if not supported_scenes:
                self.logger.error("场景工厂没有注册任何场景")
                return False
            
            # 尝试创建一个场景
            test_scene_type = supported_scenes[0]
            test_scene = self.scene_factory.create_scene(test_scene_type)
            if test_scene is None:
                self.logger.error(f"无法创建测试场景: {test_scene_type.value}")
                return False
            
            self.logger.debug(f"场景工厂验证通过，支持{len(supported_scenes)}个场景")
            return True
            
        except Exception as e:
            self.logger.error(f"场景工厂验证失败: {e}")
            return False
    
    def _verify_scene_manager(self) -> bool:
        """验证场景管理器功能"""
        try:
            # 检查场景管理器状态
            stats = self.scene_manager.get_manager_stats()
            if stats is None:
                self.logger.error("无法获取场景管理器状态")
                return False
            
            self.logger.debug(f"场景管理器验证通过: {stats}")
            return True
            
        except Exception as e:
            self.logger.error(f"场景管理器验证失败: {e}")
            return False
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        status = {
            "initialized": self._initialized,
            "initialization_errors": self._initialization_errors.copy(),
            "components": {
                "config_loader": self.config_loader is not None,
                "scene_factory": self.scene_factory is not None,
                "scene_manager": self.scene_manager is not None,
                "legacy_adapter": self.legacy_adapter is not None
            }
        }
        
        if self._initialized:
            # 添加详细状态信息
            if self.scene_factory:
                status["scene_factory_stats"] = self.scene_factory.get_creation_stats()
                status["cache_stats"] = self.scene_factory.get_cache_stats()
            
            if self.scene_manager:
                status["scene_manager_stats"] = self.scene_manager.get_manager_stats()
            
            if self.legacy_adapter:
                status["legacy_adapter_stats"] = self.legacy_adapter.get_usage_stats()
        
        return status
    
    def shutdown_system(self):
        """关闭UI系统"""
        try:
            self.logger.info("关闭UI系统")
            
            # 清理场景管理器
            if self.scene_manager:
                self.scene_manager.clear_cache()
                self.scene_manager.clear_navigation_history()
            
            # 清理场景工厂
            if self.scene_factory:
                self.scene_factory.clear_cache()
                self.scene_factory.reset_stats()
            
            # 清理配置加载器
            if self.config_loader:
                self.config_loader.clear_cache()
            
            # 重置状态
            self._initialized = False
            self._initialization_errors.clear()
            
            self.logger.info("UI系统已关闭")
            
        except Exception as e:
            self.logger.error(f"关闭UI系统失败: {e}")
    
    def is_initialized(self) -> bool:
        """检查系统是否已初始化"""
        return self._initialized
    
    def get_initialization_errors(self) -> list:
        """获取初始化错误"""
        return self._initialization_errors.copy()
