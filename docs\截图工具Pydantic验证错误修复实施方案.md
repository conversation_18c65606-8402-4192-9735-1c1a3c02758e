# 截图工具Pydantic验证错误修复实施方案

## 一、修复目标

解决截图历史记录API中的Pydantic验证错误，确保 `image_size` 字段能正确处理元组输入并转换为期望的 `ImageSize` 实例格式。

## 二、技术方案

### 2.1 核心修复策略

采用**双层修复**方案：
1. **立即修复**：在Web API层添加数据转换逻辑
2. **长期优化**：为ImageSize模型添加Pydantic验证器

### 2.2 修复范围

**主要文件**：
- `src/web/main.py` - API端点数据转换
- `src/web/models.py` - ImageSize模型验证器
- `test/test_screenshot_fix.py` - 修复验证测试

## 三、详细实施步骤

### 步骤1：修复Web API数据转换

**文件**: `src/web/main.py`

**修改位置**: `get_screenshot_history` 函数 (第903-913行)

**修改内容**:
```python
@app.get("/api/v1/screenshot/history", response_model=ScreenshotHistoryResponse)
async def get_screenshot_history(limit: int = 100):
    """获取截图历史记录"""
    try:
        records = await api_adapter.get_screenshot_history(limit)
        
        # 转换数据格式，处理image_size字段
        converted_records = []
        for record in records:
            # 处理image_size字段：从元组转换为ImageSize实例
            if isinstance(record.get('image_size'), tuple):
                width, height = record['image_size']
                record['image_size'] = ImageSize(width=width, height=height)
            elif isinstance(record.get('image_size'), dict):
                record['image_size'] = ImageSize(**record['image_size'])
            
            converted_records.append(ScreenshotRecord(**record))
        
        return ScreenshotHistoryResponse(
            total=len(converted_records),
            records=converted_records
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

### 步骤2：添加ImageSize验证器

**文件**: `src/web/models.py`

**修改位置**: `ImageSize` 类定义 (第81-84行)

**修改内容**:
```python
from pydantic import BaseModel, Field, validator
from typing import Union, Tuple

class ImageSize(BaseModel):
    """图像尺寸模型"""
    width: int = Field(ge=0, description="宽度")
    height: int = Field(ge=0, description="高度")
    
    @validator('*', pre=True)
    @classmethod
    def convert_tuple_to_imagesize(cls, v, field):
        """支持从元组创建ImageSize实例"""
        if field.name == 'image_size' and isinstance(v, tuple) and len(v) == 2:
            width, height = v
            return cls(width=width, height=height)
        return v
    
    @classmethod
    def from_tuple(cls, size_tuple: Tuple[int, int]) -> 'ImageSize':
        """从元组创建ImageSize实例的便捷方法"""
        if not isinstance(size_tuple, tuple) or len(size_tuple) != 2:
            raise ValueError("size_tuple must be a tuple of (width, height)")
        width, height = size_tuple
        return cls(width=width, height=height)
```

### 步骤3：添加数据转换工具函数

**文件**: `src/web/main.py`

**添加位置**: 在类定义之前

**添加内容**:
```python
def convert_screenshot_record_data(record_dict: Dict[str, Any]) -> Dict[str, Any]:
    """
    转换截图记录数据格式
    
    Args:
        record_dict: 原始记录字典
        
    Returns:
        转换后的记录字典
    """
    converted = record_dict.copy()
    
    # 处理image_size字段
    image_size = converted.get('image_size')
    if isinstance(image_size, tuple) and len(image_size) == 2:
        width, height = image_size
        converted['image_size'] = ImageSize(width=width, height=height)
    elif isinstance(image_size, dict):
        converted['image_size'] = ImageSize(**image_size)
    elif not isinstance(image_size, ImageSize):
        # 如果不是预期的类型，设置默认值
        converted['image_size'] = ImageSize(width=0, height=0)
    
    return converted
```

### 步骤4：创建测试文件

**文件**: `test/test_screenshot_fix.py`

**内容**:
```python
import pytest
import asyncio
from datetime import datetime
from src.web.models import ImageSize, ScreenshotRecord
from src.web.main import convert_screenshot_record_data

def test_imagesize_from_tuple():
    """测试从元组创建ImageSize"""
    # 测试正常情况
    size = ImageSize.from_tuple((1920, 1080))
    assert size.width == 1920
    assert size.height == 1080
    
    # 测试异常情况
    with pytest.raises(ValueError):
        ImageSize.from_tuple((1920,))  # 长度不对
    
    with pytest.raises(ValueError):
        ImageSize.from_tuple("invalid")  # 类型不对

def test_screenshot_record_conversion():
    """测试截图记录数据转换"""
    # 模拟原始数据（包含元组格式的image_size）
    raw_data = {
        "id": "test-123",
        "filename": "test.png",
        "filepath": "/path/to/test.png",
        "thumbnail_path": "/path/to/thumb.png",
        "timestamp": datetime.now(),
        "file_size": 1024000,
        "image_size": (1920, 1080),  # 元组格式
        "config": {"mode": "window", "format": "png"}
    }
    
    # 转换数据
    converted = convert_screenshot_record_data(raw_data)
    
    # 验证转换结果
    assert isinstance(converted['image_size'], ImageSize)
    assert converted['image_size'].width == 1920
    assert converted['image_size'].height == 1080
    
    # 测试创建ScreenshotRecord实例
    record = ScreenshotRecord(**converted)
    assert record.image_size.width == 1920
    assert record.image_size.height == 1080

def test_screenshot_record_with_dict_imagesize():
    """测试字典格式的image_size"""
    raw_data = {
        "id": "test-456",
        "filename": "test2.png",
        "filepath": "/path/to/test2.png",
        "thumbnail_path": None,
        "timestamp": datetime.now(),
        "file_size": 512000,
        "image_size": {"width": 1280, "height": 720},  # 字典格式
        "config": {"mode": "region", "format": "jpg"}
    }
    
    converted = convert_screenshot_record_data(raw_data)
    record = ScreenshotRecord(**converted)
    
    assert record.image_size.width == 1280
    assert record.image_size.height == 720

if __name__ == "__main__":
    print("🧪 运行截图修复测试...")
    
    test_imagesize_from_tuple()
    print("✅ ImageSize元组转换测试通过")
    
    test_screenshot_record_conversion()
    print("✅ 截图记录转换测试通过")
    
    test_screenshot_record_with_dict_imagesize()
    print("✅ 字典格式image_size测试通过")
    
    print("🎉 所有测试通过！")
```

## 四、实施顺序

1. **第一步**：修改 `src/web/models.py` 添加ImageSize验证器
2. **第二步**：修改 `src/web/main.py` 添加数据转换逻辑
3. **第三步**：创建并运行测试文件验证修复效果
4. **第四步**：测试完成后清理测试文件

## 五、验证方法

### 5.1 单元测试
运行测试文件验证各个组件功能正常

### 5.2 集成测试
启动Web服务，调用截图历史记录API验证修复效果

### 5.3 回归测试
确保修复不影响其他截图相关功能

## 六、回滚方案

如果修复出现问题，可以：
1. 恢复 `src/web/main.py` 的原始版本
2. 恢复 `src/web/models.py` 的原始版本
3. 删除测试文件

## 七、预期结果

修复完成后：
- 截图历史记录API能正常返回数据
- 支持元组和字典两种image_size输入格式
- 保持API响应格式的一致性
- 提供清晰的错误处理机制
