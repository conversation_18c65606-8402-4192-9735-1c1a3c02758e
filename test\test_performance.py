"""
性能测试
测试系统各组件的性能指标，包括响应时间、内存使用、并发处理等
"""

import unittest
import time
import threading
import gc
from concurrent.futures import ThreadPoolExecutor, as_completed
from unittest.mock import Mock, patch

from .base_test import PerformanceTestCase
from src.modules.ui.elements.button import Button
from src.modules.ui.elements.input_field import InputField
from src.modules.ui.scenes.training_scene import TrainingScene
from src.modules.ui.scenes.scene_manager import SceneManager
from src.modules.ui.optimization.memory_optimizer import MemoryOptimizer, CacheManager
from src.modules.ui.optimization.rendering_optimizer import RenderingOptimizer
from src.modules.ui.config.ui_element_config import UIElementConfig
from src.modules.ui.config.scene_config import SceneConfig


class TestUIElementPerformance(PerformanceTestCase):
    """UI元素性能测试"""
    
    def setUp(self):
        super().setUp()
        self.config = UIElementConfig(confidence_threshold=0.8, timeout=5.0)
    
    def test_button_creation_performance(self):
        """测试按钮创建性能"""
        def create_button():
            return Button("perf_button", self.config)
        
        result = self.benchmark(create_button, iterations=1000)
        
        # 验证性能指标
        self.assertLess(result['avg_time'], 0.001, "按钮创建平均时间应小于1ms")
        self.assertLess(result['max_time'], 0.01, "按钮创建最大时间应小于10ms")
    
    def test_input_field_creation_performance(self):
        """测试输入框创建性能"""
        def create_input():
            return InputField("perf_input", self.config)
        
        result = self.benchmark(create_input, iterations=1000)
        
        self.assertLess(result['avg_time'], 0.001, "输入框创建平均时间应小于1ms")
        self.assertLess(result['max_time'], 0.01, "输入框创建最大时间应小于10ms")
    
    def test_ui_element_interaction_performance(self):
        """测试UI元素交互性能"""
        button = Button("perf_button", self.config)
        
        # Mock快速响应
        with patch('src.modules.ui.perception.ui_perception.UIPerception') as mock_perception:
            with patch('src.modules.ui.actions.action_controller.ActionController') as mock_action:
                mock_perception.return_value.is_element_visible.return_value = True
                mock_action.return_value.click.return_value = True
                
                def click_button():
                    return button.click()
                
                result = self.benchmark(click_button, iterations=100)
                
                self.assertLess(result['avg_time'], 0.1, "按钮点击平均时间应小于100ms")
    
    def test_concurrent_ui_operations(self):
        """测试并发UI操作性能"""
        buttons = [Button(f"button_{i}", self.config) for i in range(10)]
        
        with patch('src.modules.ui.perception.ui_perception.UIPerception') as mock_perception:
            with patch('src.modules.ui.actions.action_controller.ActionController') as mock_action:
                mock_perception.return_value.is_element_visible.return_value = True
                mock_action.return_value.click.return_value = True
                
                def concurrent_clicks():
                    with ThreadPoolExecutor(max_workers=5) as executor:
                        futures = [executor.submit(button.click) for button in buttons]
                        results = [future.result() for future in as_completed(futures)]
                        return all(results)
                
                start_time = time.time()
                result = concurrent_clicks()
                end_time = time.time()
                
                self.assertTrue(result)
                self.assertLess(end_time - start_time, 2.0, "并发点击应在2秒内完成")
    
    def test_memory_usage_ui_elements(self):
        """测试UI元素内存使用"""
        def create_many_elements():
            elements = []
            for i in range(100):
                button = Button(f"button_{i}", self.config)
                input_field = InputField(f"input_{i}", self.config)
                elements.extend([button, input_field])
            return elements
        
        memory_result = self.memory_profile(create_many_elements)
        
        # 验证内存使用合理
        self.assertLess(memory_result['peak_memory'], 10 * 1024 * 1024, "创建200个UI元素内存使用应小于10MB")


class TestScenePerformance(PerformanceTestCase):
    """场景性能测试"""
    
    def setUp(self):
        super().setUp()
        self.scene_config = SceneConfig(scene_id="perf_scene", timeout=10.0)
    
    def test_scene_creation_performance(self):
        """测试场景创建性能"""
        def create_scene():
            return TrainingScene(self.scene_config)
        
        result = self.benchmark(create_scene, iterations=100)
        
        self.assertLess(result['avg_time'], 0.01, "场景创建平均时间应小于10ms")
        self.assertLess(result['max_time'], 0.1, "场景创建最大时间应小于100ms")
    
    def test_scene_detection_performance(self):
        """测试场景检测性能"""
        scene = TrainingScene(self.scene_config)
        
        with patch('src.modules.ui.perception.ui_perception.UIPerception') as mock_perception:
            mock_perception.return_value.detect_scene.return_value = "training"
            
            def detect_scene():
                return scene.is_scene_ready()
            
            result = self.benchmark(detect_scene, iterations=100)
            
            self.assertLess(result['avg_time'], 0.05, "场景检测平均时间应小于50ms")
    
    def test_scene_transition_performance(self):
        """测试场景转换性能"""
        scene_manager = SceneManager()
        
        # 创建多个场景
        scenes = {}
        for i in range(5):
            scene = Mock()
            scene.is_scene_ready.return_value = True
            scene.activate.return_value = True
            scenes[f"scene_{i}"] = scene
            scene_manager.register_scene(f"scene_{i}", scene)
        
        with patch('src.modules.ui.perception.ui_perception.UIPerception') as mock_perception:
            mock_perception.return_value.detect_scene.return_value = "scene_0"
            
            def switch_scenes():
                results = []
                for scene_id in scenes.keys():
                    start_time = time.time()
                    result = scene_manager.switch_to_scene(scene_id)
                    end_time = time.time()
                    results.append((result, end_time - start_time))
                return results
            
            transitions = switch_scenes()
            
            # 验证所有转换都成功且快速
            for success, duration in transitions:
                self.assertTrue(success)
                self.assertLess(duration, 0.5, f"场景转换时间应小于500ms，实际: {duration:.3f}s")
    
    def test_scene_memory_usage(self):
        """测试场景内存使用"""
        def create_scene_manager_with_scenes():
            scene_manager = SceneManager()
            
            # 创建多个场景
            for i in range(20):
                scene = TrainingScene(SceneConfig(scene_id=f"scene_{i}"))
                scene_manager.register_scene(f"scene_{i}", scene)
            
            return scene_manager
        
        memory_result = self.memory_profile(create_scene_manager_with_scenes)
        
        self.assertLess(memory_result['peak_memory'], 50 * 1024 * 1024, "20个场景内存使用应小于50MB")


class TestOptimizationPerformance(PerformanceTestCase):
    """优化组件性能测试"""
    
    def test_memory_optimizer_performance(self):
        """测试内存优化器性能"""
        optimizer = MemoryOptimizer()
        
        def run_memory_optimization():
            optimizer.optimize_memory_usage()
            return optimizer.get_memory_stats()
        
        result = self.benchmark(run_memory_optimization, iterations=10)
        
        self.assertLess(result['avg_time'], 0.1, "内存优化平均时间应小于100ms")
    
    def test_cache_manager_performance(self):
        """测试缓存管理器性能"""
        cache = CacheManager(max_size=1024*1024)  # 1MB缓存
        
        # 测试缓存写入性能
        def cache_write_test():
            for i in range(100):
                cache.put(f"key_{i}", f"value_{i}" * 100)
        
        write_result = self.benchmark(cache_write_test, iterations=10)
        self.assertLess(write_result['avg_time'], 0.1, "缓存写入性能测试")
        
        # 测试缓存读取性能
        def cache_read_test():
            for i in range(100):
                cache.get(f"key_{i}")
        
        read_result = self.benchmark(cache_read_test, iterations=100)
        self.assertLess(read_result['avg_time'], 0.01, "缓存读取性能测试")
    
    def test_rendering_optimizer_performance(self):
        """测试渲染优化器性能"""
        optimizer = RenderingOptimizer(target_fps=60.0)
        
        # Mock渲染任务
        def mock_render_task():
            time.sleep(0.001)  # 模拟1ms渲染时间
        
        # 创建渲染任务
        tasks = []
        for i in range(100):
            task = optimizer.create_render_task(f"element_{i}", mock_render_task)
            tasks.append(task)
        
        def process_render_tasks():
            for task in tasks:
                optimizer.add_render_task(task)
            
            # 等待处理完成
            time.sleep(0.5)
            
            return optimizer.get_rendering_stats()
        
        start_time = time.time()
        stats = process_render_tasks()
        end_time = time.time()
        
        processing_time = end_time - start_time
        self.assertLess(processing_time, 2.0, "100个渲染任务处理时间应小于2秒")
        
        # 验证FPS
        if stats.current_fps > 0:
            self.assertGreater(stats.current_fps, 30, "渲染FPS应大于30")


class TestConcurrencyPerformance(PerformanceTestCase):
    """并发性能测试"""
    
    def test_concurrent_ui_element_access(self):
        """测试并发UI元素访问"""
        button = Button("concurrent_button")
        
        with patch('src.modules.ui.perception.ui_perception.UIPerception') as mock_perception:
            mock_perception.return_value.is_element_visible.return_value = True
            
            def concurrent_visibility_check():
                results = []
                
                def check_visibility():
                    return button.is_visible()
                
                with ThreadPoolExecutor(max_workers=10) as executor:
                    futures = [executor.submit(check_visibility) for _ in range(50)]
                    results = [future.result() for future in as_completed(futures)]
                
                return all(results)
            
            start_time = time.time()
            result = concurrent_visibility_check()
            end_time = time.time()
            
            self.assertTrue(result)
            self.assertLess(end_time - start_time, 1.0, "50个并发可见性检查应在1秒内完成")
    
    def test_scene_manager_thread_safety(self):
        """测试场景管理器线程安全"""
        scene_manager = SceneManager()
        
        # 注册场景
        for i in range(5):
            scene = Mock()
            scene.is_scene_ready.return_value = True
            scene.activate.return_value = True
            scene_manager.register_scene(f"scene_{i}", scene)
        
        with patch('src.modules.ui.perception.ui_perception.UIPerception') as mock_perception:
            mock_perception.return_value.detect_scene.return_value = "scene_0"
            
            def concurrent_scene_switches():
                results = []
                
                def switch_to_random_scene(scene_id):
                    return scene_manager.switch_to_scene(scene_id)
                
                with ThreadPoolExecutor(max_workers=5) as executor:
                    futures = []
                    for i in range(20):
                        scene_id = f"scene_{i % 5}"
                        futures.append(executor.submit(switch_to_random_scene, scene_id))
                    
                    results = [future.result() for future in as_completed(futures)]
                
                return results
            
            start_time = time.time()
            results = concurrent_scene_switches()
            end_time = time.time()
            
            # 验证所有操作都成功
            self.assertTrue(all(results), "所有并发场景切换都应成功")
            self.assertLess(end_time - start_time, 2.0, "20个并发场景切换应在2秒内完成")
    
    def test_memory_optimizer_concurrent_access(self):
        """测试内存优化器并发访问"""
        optimizer = MemoryOptimizer()
        
        def concurrent_memory_operations():
            results = []
            
            def memory_operation():
                # 创建缓存管理器
                cache = optimizer.create_cache_manager(f"cache_{threading.current_thread().ident}")
                
                # 执行缓存操作
                for i in range(10):
                    cache.put(f"key_{i}", f"value_{i}")
                    cache.get(f"key_{i}")
                
                return True
            
            with ThreadPoolExecutor(max_workers=5) as executor:
                futures = [executor.submit(memory_operation) for _ in range(10)]
                results = [future.result() for future in as_completed(futures)]
            
            return all(results)
        
        start_time = time.time()
        result = concurrent_memory_operations()
        end_time = time.time()
        
        self.assertTrue(result)
        self.assertLess(end_time - start_time, 2.0, "并发内存操作应在2秒内完成")


class TestStressTest(PerformanceTestCase):
    """压力测试"""
    
    def test_ui_element_stress(self):
        """UI元素压力测试"""
        elements = []
        
        # 创建大量UI元素
        start_time = time.time()
        for i in range(1000):
            button = Button(f"stress_button_{i}")
            input_field = InputField(f"stress_input_{i}")
            elements.extend([button, input_field])
        creation_time = time.time() - start_time
        
        self.assertLess(creation_time, 5.0, "创建2000个UI元素应在5秒内完成")
        
        # 测试内存使用
        import psutil
        import os
        process = psutil.Process(os.getpid())
        memory_usage = process.memory_info().rss
        
        # 清理元素
        elements.clear()
        gc.collect()
        
        memory_after_cleanup = process.memory_info().rss
        memory_freed = memory_usage - memory_after_cleanup
        
        self.assertGreater(memory_freed, 0, "清理后应释放内存")
    
    def test_scene_manager_stress(self):
        """场景管理器压力测试"""
        scene_manager = SceneManager()
        
        # 注册大量场景
        start_time = time.time()
        for i in range(100):
            scene = Mock()
            scene.is_scene_ready.return_value = True
            scene.activate.return_value = True
            scene_manager.register_scene(f"stress_scene_{i}", scene)
        registration_time = time.time() - start_time
        
        self.assertLess(registration_time, 1.0, "注册100个场景应在1秒内完成")
        
        # 测试场景信息获取
        start_time = time.time()
        info = scene_manager.get_scene_info()
        info_time = time.time() - start_time
        
        self.assertLess(info_time, 0.1, "获取场景信息应在100ms内完成")
        self.assertEqual(len(info["registered_scenes"]), 100)


if __name__ == '__main__':
    unittest.main()
