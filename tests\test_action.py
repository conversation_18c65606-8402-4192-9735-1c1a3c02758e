"""
测试行动模块
"""

import pytest
import time
from unittest.mock import Mock, patch, MagicMock
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.modules.action import (
    ActionController, InputSimulator, GameLauncher, ActionVerifier,
    InputSimulationError, GameLaunchError, VerificationError
)
from src.modules.action.action_verifier import VerificationResult
from src.core.data_structures import Action, ActionType, GameScene


class TestInputSimulator:
    """测试InputSimulator类"""
    
    def setup_method(self):
        """测试前准备"""
        with patch('pydirectinput.FAILSAFE'), \
             patch('pydirectinput.PAUSE'):
            self.simulator = InputSimulator()
    
    def test_input_simulator_init(self):
        """测试InputSimulator初始化"""
        assert self.simulator.mouse_speed == 1.0
        assert self.simulator.click_duration == 0.1
        assert self.simulator.position_variance == 3
        assert self.simulator.min_delay == 0.05
    
    @patch('pydirectinput.position')
    @patch('pydirectinput.moveTo')
    def test_move_mouse_smooth(self, mock_moveTo, mock_position):
        """测试平滑鼠标移动"""
        mock_position.return_value = (100, 100)
        
        result = self.simulator.move_mouse_smooth(200, 200, duration=0.1)
        
        assert result is True
        assert mock_moveTo.called
    
    @patch('pydirectinput.position')
    @patch('pydirectinput.moveTo')
    @patch('pydirectinput.mouseDown')
    @patch('pydirectinput.mouseUp')
    @patch('time.sleep')
    def test_click(self, mock_sleep, mock_mouseUp, mock_mouseDown, mock_moveTo, mock_position):
        """测试点击操作"""
        mock_position.return_value = (100, 100)
        
        result = self.simulator.click(150, 150)
        
        assert result is True
        mock_mouseDown.assert_called_with(button='left')
        mock_mouseUp.assert_called_with(button='left')
    
    @patch('pydirectinput.keyDown')
    @patch('pydirectinput.keyUp')
    @patch('time.sleep')
    def test_key_press(self, mock_sleep, mock_keyUp, mock_keyDown):
        """测试按键操作"""
        result = self.simulator.key_press('space')
        
        assert result is True
        mock_keyDown.assert_called_with('space')
        mock_keyUp.assert_called_with('space')
    
    @patch('pydirectinput.keyDown')
    @patch('pydirectinput.keyUp')
    @patch('time.sleep')
    def test_key_press_combination(self, mock_sleep, mock_keyUp, mock_keyDown):
        """测试组合按键"""
        result = self.simulator.key_press(['ctrl', 'c'])
        
        assert result is True
        # 检查按键按下顺序
        assert mock_keyDown.call_count == 2
        assert mock_keyUp.call_count == 2
    
    def test_execute_action_click(self):
        """测试执行点击操作"""
        action = Action(
            action_type=ActionType.CLICK,
            target=(100, 200),
            description="测试点击"
        )
        
        with patch.object(self.simulator, 'click', return_value=True) as mock_click:
            result = self.simulator.execute_action(action)
            
            assert result is True
            mock_click.assert_called_once_with(100, 200)
    
    def test_execute_action_keypress(self):
        """测试执行按键操作"""
        action = Action(
            action_type=ActionType.KEYPRESS,
            target='enter',
            description="测试按键"
        )
        
        with patch.object(self.simulator, 'key_press', return_value=True) as mock_key_press:
            result = self.simulator.execute_action(action)
            
            assert result is True
            mock_key_press.assert_called_once_with('enter')
    
    def test_get_simulator_info(self):
        """测试获取模拟器信息"""
        with patch.object(self.simulator, 'get_mouse_position', return_value=(100, 200)):
            info = self.simulator.get_simulator_info()
            
            assert "mouse_speed" in info
            assert "click_duration" in info
            assert "current_mouse_position" in info
            assert info["current_mouse_position"] == (100, 200)


class TestGameLauncher:
    """测试GameLauncher类"""
    
    def setup_method(self):
        """测试前准备"""
        # 创建临时的DMM Player路径
        with patch.object(Path, 'exists', return_value=True):
            self.launcher = GameLauncher("C:/fake/dmm/player.exe")
    
    def test_game_launcher_init(self):
        """测试GameLauncher初始化"""
        assert self.launcher.dmm_player_path == Path("C:/fake/dmm/player.exe")
        assert self.launcher.launch_timeout == 60.0
        assert self.launcher.retry_attempts == 3
    
    def test_game_launcher_init_invalid_path(self):
        """测试无效DMM路径初始化"""
        with patch.object(Path, 'exists', return_value=False):
            with pytest.raises(GameLaunchError):
                GameLauncher("C:/invalid/path.exe")
    
    @patch('subprocess.run')
    def test_is_dmm_running_true(self, mock_run):
        """测试DMM运行状态检查 - 运行中"""
        mock_run.return_value.stdout = "DMMGamePlayer.exe    1234 Console"
        
        result = self.launcher.is_dmm_running()
        
        assert result is True
    
    @patch('subprocess.run')
    def test_is_dmm_running_false(self, mock_run):
        """测试DMM运行状态检查 - 未运行"""
        mock_run.return_value.stdout = "No tasks are running"
        
        result = self.launcher.is_dmm_running()
        
        assert result is False
    
    @patch('win32gui.EnumWindows')
    @patch('win32gui.IsWindowVisible')
    @patch('win32gui.GetWindowText')
    def test_is_game_running_true(self, mock_get_text, mock_is_visible, mock_enum):
        """测试游戏运行状态检查 - 运行中"""
        def enum_callback(callback, param):
            callback(12345, param)
            return True
        
        mock_enum.side_effect = enum_callback
        mock_is_visible.return_value = True
        mock_get_text.return_value = "gakumas - Main Window"
        
        result = self.launcher.is_game_running()
        
        assert result is True
    
    @patch('subprocess.Popen')
    @patch('time.sleep')
    def test_launch_dmm_player_success(self, mock_sleep, mock_popen):
        """测试成功启动DMM Player"""
        with patch.object(self.launcher, 'is_dmm_running', side_effect=[False, True]):
            result = self.launcher.launch_dmm_player()
            
            assert result is True
            mock_popen.assert_called_once()
    
    @patch('subprocess.Popen')
    @patch('time.sleep')
    def test_launch_dmm_player_already_running(self, mock_sleep, mock_popen):
        """测试DMM Player已运行"""
        with patch.object(self.launcher, 'is_dmm_running', return_value=True):
            result = self.launcher.launch_dmm_player()
            
            assert result is True
            mock_popen.assert_not_called()
    
    def test_get_launcher_info(self):
        """测试获取启动器信息"""
        with patch.object(self.launcher, 'is_dmm_running', return_value=True), \
             patch.object(self.launcher, 'is_game_running', return_value=False):
            
            info = self.launcher.get_launcher_info()
            
            assert "dmm_player_path" in info
            assert "launch_timeout" in info
            assert "dmm_running" in info
            assert "game_running" in info
            assert info["dmm_running"] is True
            assert info["game_running"] is False


class TestActionVerifier:
    """测试ActionVerifier类"""
    
    def setup_method(self):
        """测试前准备"""
        self.mock_perception = Mock()
        self.verifier = ActionVerifier(self.mock_perception)
    
    def test_action_verifier_init(self):
        """测试ActionVerifier初始化"""
        assert self.verifier.perception_module == self.mock_perception
        assert self.verifier.default_timeout == 10.0
        assert self.verifier.max_retries == 3
    
    def test_verify_scene_change_success(self):
        """测试场景切换验证成功"""
        self.mock_perception.get_current_scene.return_value = GameScene.MAIN_MENU
        
        result = self.verifier.verify_scene_change(GameScene.MAIN_MENU, timeout=1.0)
        
        assert result == VerificationResult.SUCCESS
    
    def test_verify_scene_change_timeout(self):
        """测试场景切换验证超时"""
        self.mock_perception.get_current_scene.return_value = GameScene.UNKNOWN
        
        result = self.verifier.verify_scene_change(GameScene.MAIN_MENU, timeout=0.1)
        
        assert result == VerificationResult.TIMEOUT
    
    def test_verify_ui_element_present_success(self):
        """测试UI元素存在验证成功"""
        mock_match = Mock()
        mock_match.confidence = 0.9
        self.mock_perception.find_ui_element.return_value = mock_match
        
        result = self.verifier.verify_ui_element_present("test_button", timeout=1.0)
        
        assert result == VerificationResult.SUCCESS
    
    def test_verify_ui_element_present_timeout(self):
        """测试UI元素存在验证超时"""
        self.mock_perception.find_ui_element.return_value = None
        
        result = self.verifier.verify_ui_element_present("test_button", timeout=0.1)
        
        assert result == VerificationResult.TIMEOUT
    
    def test_verify_custom_condition_success(self):
        """测试自定义条件验证成功"""
        condition_func = Mock(return_value=True)
        
        result = self.verifier.verify_custom_condition(condition_func, timeout=1.0)
        
        assert result == VerificationResult.SUCCESS
        condition_func.assert_called()
    
    def test_verify_custom_condition_timeout(self):
        """测试自定义条件验证超时"""
        condition_func = Mock(return_value=False)
        
        result = self.verifier.verify_custom_condition(condition_func, timeout=0.1)
        
        assert result == VerificationResult.TIMEOUT
    
    def test_get_verifier_info(self):
        """测试获取验证器信息"""
        info = self.verifier.get_verifier_info()
        
        assert "default_timeout" in info
        assert "check_interval" in info
        assert "max_retries" in info
        assert "verification_rules" in info
        assert "perception_module_available" in info
        assert info["perception_module_available"] is True


class TestActionController:
    """测试ActionController类"""
    
    def setup_method(self):
        """测试前准备"""
        self.mock_perception = Mock()
        
        with patch.object(Path, 'exists', return_value=True):
            self.controller = ActionController(
                dmm_player_path="C:/fake/dmm/player.exe",
                perception_module=self.mock_perception
            )
    
    def test_action_controller_init(self):
        """测试ActionController初始化"""
        assert self.controller.perception_module == self.mock_perception
        assert self.controller.game_window_title == "gakumas"
        assert self.controller.enable_auto_recovery is True
        assert self.controller.recovery_attempts == 3
    
    def test_execute_action_success(self):
        """测试成功执行操作"""
        action = Action(
            action_type=ActionType.CLICK,
            target=(100, 200),
            description="测试点击"
        )
        
        with patch.object(self.controller.input_simulator, 'execute_action', return_value=True):
            result = self.controller.execute_action(action, verify=False)
            
            assert result is True
            assert len(self.controller.action_history) == 1
    
    def test_execute_action_failure(self):
        """测试操作执行失败"""
        action = Action(
            action_type=ActionType.CLICK,
            target=(100, 200),
            description="测试点击"
        )
        
        with patch.object(self.controller.input_simulator, 'execute_action', return_value=False):
            result = self.controller.execute_action(action, verify=False)
            
            assert result is False
    
    def test_execute_action_sequence_success(self):
        """测试成功执行操作序列"""
        actions = [
            Action(ActionType.CLICK, (100, 100), "点击1"),
            Action(ActionType.CLICK, (200, 200), "点击2")
        ]
        
        with patch.object(self.controller, 'execute_action', return_value=True), \
             patch('time.sleep'):
            
            result = self.controller.execute_action_sequence(actions)
            
            assert result is True
    
    def test_execute_action_sequence_partial_failure(self):
        """测试操作序列部分失败"""
        actions = [
            Action(ActionType.CLICK, (100, 100), "点击1"),
            Action(ActionType.CLICK, (200, 200), "点击2")
        ]
        
        with patch.object(self.controller, 'execute_action', side_effect=[True, False]), \
             patch('time.sleep'):
            
            result = self.controller.execute_action_sequence(actions, stop_on_failure=True)
            
            assert result is False
    
    def test_click_ui_element_success(self):
        """测试成功点击UI元素"""
        mock_match = Mock()
        mock_match.center_x = 150
        mock_match.center_y = 250
        mock_match.confidence = 0.9
        
        self.mock_perception.find_ui_element.return_value = mock_match
        
        with patch.object(self.controller, 'execute_action', return_value=True):
            result = self.controller.click_ui_element("test_button")
            
            assert result is True
    
    def test_click_ui_element_not_found(self):
        """测试UI元素未找到"""
        self.mock_perception.find_ui_element.return_value = None
        
        result = self.controller.click_ui_element("test_button", timeout=0.1)
        
        assert result is False
    
    def test_launch_game(self):
        """测试启动游戏"""
        with patch.object(self.controller.game_launcher, 'launch_game_complete', return_value=True):
            result = self.controller.launch_game()
            
            assert result is True
    
    def test_close_game(self):
        """测试关闭游戏"""
        with patch.object(self.controller.game_launcher, 'close_game', return_value=True):
            result = self.controller.close_game()
            
            assert result is True
    
    def test_get_action_history(self):
        """测试获取操作历史"""
        # 添加一些历史记录
        self.controller.action_history = [
            {"description": "操作1", "success": True},
            {"description": "操作2", "success": False}
        ]
        
        history = self.controller.get_action_history(limit=5)
        
        assert len(history) == 2
        assert history[0]["description"] == "操作1"
        assert history[1]["description"] == "操作2"
    
    def test_get_controller_info(self):
        """测试获取控制器信息"""
        info = self.controller.get_controller_info()
        
        assert "game_window_title" in info
        assert "enable_auto_recovery" in info
        assert "action_history_size" in info
        assert "input_simulator_info" in info
        assert "game_launcher_info" in info
        assert "action_verifier_info" in info


if __name__ == "__main__":
    pytest.main([__file__])
