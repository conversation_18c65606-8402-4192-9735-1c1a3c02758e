#!/usr/bin/env python3
"""
Gakumasu-Bot GUI启动器
一键启动前后端服务的便捷工具

版本: 1.0.0
作者: Gakumasu-Bot Development Team
日期: 2025-07-26
"""

import sys
import os
import time
import socket
import signal
import argparse
import subprocess
import webbrowser
import threading
from pathlib import Path
from typing import Optional, Dict, Any
import requests

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils.logger import setup_logger, get_logger


class GUILauncher:
    """GUI启动器主类"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化启动器"""
        self.backend_process: Optional[subprocess.Popen] = None
        self.frontend_process: Optional[subprocess.Popen] = None
        self.running = False
        self.logger = None
        
        # 默认配置
        self.config = {
            'backend': {
                'host': '127.0.0.1',
                'port': 8000,
                'module': 'src.web.main:app',
                'reload': True
            },
            'frontend': {
                'host': '127.0.0.1', 
                'port': 3000,
                'command': 'npm run dev',
                'working_dir': 'frontend'
            },
            'browser': {
                'auto_open': True,
                'url': 'http://localhost:3000',
                'delay': 3  # 启动后等待时间
            }
        }
        
        # 更新配置
        if config:
            self._update_config(config)
    
    def _update_config(self, config: Dict[str, Any]):
        """更新配置"""
        for section, values in config.items():
            if section in self.config:
                self.config[section].update(values)
            else:
                self.config[section] = values
    
    def setup_logging(self, log_level: str = 'INFO'):
        """设置日志"""
        self.logger = setup_logger(log_level=log_level)
        self.logger.info("GUI启动器初始化完成")
    
    def is_port_in_use(self, port: int, host: str = 'localhost') -> bool:
        """检查端口是否被占用"""
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            try:
                s.bind((host, port))
                return False
            except OSError:
                return True
    
    def find_available_port(self, start_port: int, max_attempts: int = 10) -> int:
        """查找可用端口"""
        for i in range(max_attempts):
            port = start_port + i
            if not self.is_port_in_use(port):
                return port
        raise RuntimeError(f"无法找到可用端口 (起始端口: {start_port})")
    
    def check_ports(self):
        """检查并分配端口"""
        backend_port = self.config['backend']['port']
        frontend_port = self.config['frontend']['port']
        
        # 检查后端端口
        if self.is_port_in_use(backend_port):
            new_port = self.find_available_port(backend_port)
            self.logger.warning(f"后端端口 {backend_port} 被占用，使用端口 {new_port}")
            self.config['backend']['port'] = new_port
        else:
            self.logger.info(f"后端端口 {backend_port} 可用")
        
        # 检查前端端口
        if self.is_port_in_use(frontend_port):
            new_port = self.find_available_port(frontend_port)
            self.logger.warning(f"前端端口 {frontend_port} 被占用，使用端口 {new_port}")
            self.config['frontend']['port'] = new_port
            # 更新浏览器URL
            self.config['browser']['url'] = f"http://localhost:{new_port}"
        else:
            self.logger.info(f"前端端口 {frontend_port} 可用")
    
    def start_backend(self) -> subprocess.Popen:
        """启动后端服务"""
        cmd = [
            'uvicorn',
            self.config['backend']['module'],
            '--host', self.config['backend']['host'],
            '--port', str(self.config['backend']['port'])
        ]

        if self.config['backend']['reload']:
            cmd.append('--reload')

        self.logger.info(f"启动后端服务: {' '.join(cmd)}")

        try:
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,  # 合并stderr到stdout
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            # 启动一个线程来读取和记录输出
            def log_output():
                for line in iter(process.stdout.readline, ''):
                    if line.strip():
                        self.logger.info(f"Backend: {line.strip()}")
                process.stdout.close()

            threading.Thread(target=log_output, daemon=True).start()

            return process
        except FileNotFoundError:
            self.logger.error("uvicorn 未找到，请确保已安装 FastAPI 和 uvicorn")
            raise
    
    def start_frontend(self) -> subprocess.Popen:
        """启动前端服务"""
        frontend_dir = Path(self.config['frontend']['working_dir'])
        if not frontend_dir.exists():
            raise FileNotFoundError(f"前端目录不存在: {frontend_dir}")
        
        # 设置环境变量
        env = os.environ.copy()
        env['VITE_API_BASE_URL'] = f"http://{self.config['backend']['host']}:{self.config['backend']['port']}"
        
        self.logger.info(f"启动前端服务: {self.config['frontend']['command']}")
        
        try:
            process = subprocess.Popen(
                self.config['frontend']['command'],
                cwd=frontend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                shell=True,
                env=env
            )
            return process
        except Exception as e:
            self.logger.error(f"启动前端服务失败: {e}")
            raise
    
    def wait_for_service(self, url: str, timeout: int = 30) -> bool:
        """等待服务就绪"""
        self.logger.info(f"等待服务就绪: {url}")
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                response = requests.get(url, timeout=1)
                if response.status_code == 200:
                    self.logger.info(f"服务就绪: {url}")
                    return True
            except requests.RequestException:
                pass
            time.sleep(1)
        
        self.logger.error(f"服务启动超时: {url}")
        return False
    
    def open_browser(self):
        """打开浏览器"""
        if not self.config['browser']['auto_open']:
            return
        
        url = self.config['browser']['url']
        delay = self.config['browser']['delay']
        
        def _open_browser():
            time.sleep(delay)
            self.logger.info(f"自动打开浏览器: {url}")
            try:
                webbrowser.open(url)
            except Exception as e:
                self.logger.error(f"打开浏览器失败: {e}")
        
        threading.Thread(target=_open_browser, daemon=True).start()
    
    def monitor_processes(self):
        """监控进程状态"""
        while self.running:
            if self.backend_process and self.backend_process.poll() is not None:
                self.logger.error("后端服务意外退出")
                self.stop()
                break
            if self.frontend_process and self.frontend_process.poll() is not None:
                self.logger.error("前端服务意外退出")
                self.stop()
                break
            time.sleep(1)
    
    def start(self, mode: str = 'development'):
        """启动所有服务"""
        try:
            self.logger.info(f"启动模式: {mode}")
            self.running = True
            
            # 检查端口
            self.check_ports()
            
            # 启动后端服务
            self.backend_process = self.start_backend()
            
            # 等待后端服务就绪
            backend_url = f"http://{self.config['backend']['host']}:{self.config['backend']['port']}/health"
            if not self.wait_for_service(backend_url):
                raise RuntimeError("后端服务启动失败")
            
            # 启动前端服务
            self.frontend_process = self.start_frontend()
            
            # 等待前端服务就绪
            frontend_url = f"http://{self.config['frontend']['host']}:{self.config['frontend']['port']}"
            if not self.wait_for_service(frontend_url):
                raise RuntimeError("前端服务启动失败")
            
            # # 打开浏览器
            # self.open_browser()
            
            self.logger.info("所有服务启动完成，按 Ctrl+C 退出")
            
            # 启动进程监控
            monitor_thread = threading.Thread(target=self.monitor_processes, daemon=True)
            monitor_thread.start()
            
            # 等待用户中断
            try:
                while self.running:
                    time.sleep(1)
            except KeyboardInterrupt:
                self.logger.info("收到退出信号")
                self.stop()
        
        except Exception as e:
            self.logger.error(f"启动失败: {e}")
            self.stop()
            raise
    
    def stop(self):
        """停止所有服务"""
        self.logger.info("正在停止所有服务...")
        self.running = False
        
        # 停止前端服务
        if self.frontend_process:
            try:
                self.frontend_process.terminate()
                self.frontend_process.wait(timeout=5)
                self.logger.info("前端服务已停止")
            except subprocess.TimeoutExpired:
                self.frontend_process.kill()
                self.logger.warning("强制终止前端服务")
            except Exception as e:
                self.logger.error(f"停止前端服务失败: {e}")
        
        # 停止后端服务
        if self.backend_process:
            try:
                self.backend_process.terminate()
                self.backend_process.wait(timeout=5)
                self.logger.info("后端服务已停止")
            except subprocess.TimeoutExpired:
                self.backend_process.kill()
                self.logger.warning("强制终止后端服务")
            except Exception as e:
                self.logger.error(f"停止后端服务失败: {e}")
        
        self.logger.info("所有服务已停止")


def print_banner():
    """打印启动横幅"""
    banner = """
===================================================
== Gakumasu-Bot GUI启动器 v1.0 - 一键启动前后端 ==
===================================================
    """
    print(banner)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Gakumasu-Bot GUI启动器')
    parser.add_argument('--mode', default='development', 
                       choices=['development', 'production'],
                       help='启动模式')
    parser.add_argument('--backend-port', type=int, default=8000,
                       help='后端端口')
    parser.add_argument('--frontend-port', type=int, default=3000,
                       help='前端端口')
    parser.add_argument('--no-browser', action='store_true',
                       help='不自动打开浏览器')
    parser.add_argument('--log-level', default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别')
    
    args = parser.parse_args()
    
    # 打印横幅
    print_banner()
    
    # 创建配置
    config = {
        'backend': {'port': args.backend_port},
        'frontend': {'port': args.frontend_port},
        'browser': {'auto_open': not args.no_browser}
    }
    
    # 创建启动器
    launcher = GUILauncher(config)
    launcher.setup_logging(args.log_level)
    
    # 设置信号处理
    def signal_handler(signum, frame):
        launcher.stop()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        launcher.start(args.mode)
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
