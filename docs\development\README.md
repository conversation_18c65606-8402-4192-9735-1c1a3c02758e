# 开发指南

## 概述

本指南提供了Gakumasu-Bot UI模块的完整开发指导，包括环境搭建、开发流程、代码规范和最佳实践。

## 开发环境搭建

### 系统要求

- **操作系统**：Windows 10/11 (64位)
- **Python版本**：3.8 或更高版本
- **内存**：8GB RAM (推荐16GB)
- **存储空间**：至少2GB可用空间
- **显示器**：1920x1080或更高分辨率

### 必需软件

1. **Python 3.8+**
   - 从 [python.org](https://www.python.org/) 下载并安装
   - 确保添加到系统PATH

2. **Git**
   - 从 [git-scm.com](https://git-scm.com/) 下载并安装

3. **IDE/编辑器** (推荐)
   - PyCharm Professional/Community
   - Visual Studio Code
   - Sublime Text

### 项目设置

#### 1. 克隆项目

```bash
git clone https://github.com/your-repo/gakumasu-bot.git
cd gakumasu-bot
```

#### 2. 创建虚拟环境

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate
```

#### 3. 安装依赖

```bash
# 安装基础依赖
pip install -r requirements.txt

# 安装开发依赖
pip install -r requirements-dev.txt
```

#### 4. 配置环境变量

创建 `.env` 文件：

```env
# 开发模式
DEBUG=True

# 日志级别
LOG_LEVEL=DEBUG

# 游戏路径 (可选)
GAME_PATH=C:\Program Files\Game\gakumasu.exe

# 测试配置
TEST_DATA_PATH=./test/data
TEMP_PATH=./test/temp
```

#### 5. 验证安装

```bash
# 运行测试
python -m pytest test/ -v

# 检查代码风格
flake8 src/

# 类型检查
mypy src/
```

## 开发流程

### 1. 分支管理

我们使用Git Flow工作流：

- `main` - 主分支，包含稳定的生产代码
- `develop` - 开发分支，包含最新的开发代码
- `feature/*` - 功能分支，用于开发新功能
- `hotfix/*` - 热修复分支，用于紧急修复
- `release/*` - 发布分支，用于准备新版本

#### 创建功能分支

```bash
# 从develop分支创建功能分支
git checkout develop
git pull origin develop
git checkout -b feature/new-ui-element

# 开发完成后合并回develop
git checkout develop
git merge feature/new-ui-element
git push origin develop
```

### 2. 代码提交规范

使用约定式提交格式：

```
<type>(<scope>): <description>

[optional body]

[optional footer]
```

**类型 (type)：**
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

**示例：**
```bash
git commit -m "feat(ui): add new slider input component"
git commit -m "fix(scene): resolve training scene detection issue"
git commit -m "docs(api): update button class documentation"
```

### 3. 代码审查

所有代码变更都需要通过Pull Request进行审查：

1. 创建Pull Request
2. 填写详细的描述和变更说明
3. 指定审查者
4. 通过CI/CD检查
5. 获得至少一个审查者的批准
6. 合并到目标分支

## 代码规范

### 1. Python代码风格

遵循PEP 8规范，使用以下工具确保代码质量：

- **flake8** - 代码风格检查
- **black** - 代码格式化
- **isort** - 导入排序
- **mypy** - 类型检查

#### 配置文件

**setup.cfg:**
```ini
[flake8]
max-line-length = 88
extend-ignore = E203, W503
exclude = venv, .git, __pycache__

[isort]
profile = black
multi_line_output = 3
line_length = 88

[mypy]
python_version = 3.8
warn_return_any = True
warn_unused_configs = True
disallow_untyped_defs = True
```

### 2. 命名规范

- **类名**：使用PascalCase，如 `UIElement`, `SceneManager`
- **函数/方法名**：使用snake_case，如 `get_element_bounds`, `is_visible`
- **变量名**：使用snake_case，如 `element_id`, `confidence_threshold`
- **常量名**：使用UPPER_SNAKE_CASE，如 `DEFAULT_TIMEOUT`, `MAX_RETRY_COUNT`
- **私有成员**：使用单下划线前缀，如 `_internal_method`

### 3. 文档字符串

使用Google风格的文档字符串：

```python
def create_ui_element(element_type: str, element_id: str, **kwargs) -> BaseUIElement:
    """创建UI元素实例。
    
    Args:
        element_type: 元素类型，如 'button', 'input', 'label'
        element_id: 元素唯一标识符
        **kwargs: 其他配置参数
        
    Returns:
        BaseUIElement: 创建的UI元素实例
        
    Raises:
        ValueError: 当element_type不支持时
        TypeError: 当参数类型不正确时
        
    Example:
        >>> button = create_ui_element('button', 'start_btn', timeout=10.0)
        >>> print(button.element_id)
        'start_btn'
    """
```

### 4. 类型注解

所有公共API都必须包含类型注解：

```python
from typing import Optional, Dict, List, Any, Union

class UIElement:
    def __init__(self, element_id: str, config: Optional[UIElementConfig] = None) -> None:
        self.element_id = element_id
        self.config = config or UIElementConfig()
    
    def get_bounds(self) -> Tuple[int, int, int, int]:
        """返回元素边界 (x, y, width, height)"""
        pass
    
    def set_properties(self, properties: Dict[str, Any]) -> bool:
        """设置元素属性"""
        pass
```

## 测试指南

### 1. 测试结构

```
test/
├── __init__.py
├── base_test.py           # 测试基类
├── test_ui_elements.py    # UI元素测试
├── test_scenes.py         # 场景测试
├── test_performance.py    # 性能测试
├── test_integration.py    # 集成测试
└── data/                  # 测试数据
    ├── test_images/
    └── test_configs/
```

### 2. 测试类型

#### 单元测试
测试单个类或方法的功能：

```python
import unittest
from unittest.mock import Mock, patch
from src.modules.ui.elements.button import Button

class TestButton(unittest.TestCase):
    def setUp(self):
        self.button = Button("test_button")
    
    def test_button_creation(self):
        """测试按钮创建"""
        self.assertEqual(self.button.element_id, "test_button")
        self.assertIsNotNone(self.button.config)
    
    @patch('src.modules.ui.perception.ui_perception.UIPerception')
    def test_button_click(self, mock_perception):
        """测试按钮点击"""
        mock_perception.return_value.is_element_visible.return_value = True
        
        result = self.button.click()
        self.assertTrue(result)
```

#### 集成测试
测试多个组件的协作：

```python
class TestSceneIntegration(unittest.TestCase):
    def test_training_workflow(self):
        """测试完整的训练工作流"""
        scene_manager = SceneManager()
        training_scene = TrainingScene()
        
        # 注册场景
        scene_manager.register_scene(GameScene.TRAINING, training_scene)
        
        # 切换到训练场景
        success = scene_manager.switch_to_scene(GameScene.TRAINING)
        self.assertTrue(success)
        
        # 开始训练
        success = training_scene.start_vocal_lesson()
        self.assertTrue(success)
```

#### 性能测试
测试系统性能指标：

```python
class TestPerformance(unittest.TestCase):
    def test_ui_element_creation_performance(self):
        """测试UI元素创建性能"""
        import time
        
        start_time = time.time()
        elements = [Button(f"button_{i}") for i in range(1000)]
        end_time = time.time()
        
        creation_time = end_time - start_time
        self.assertLess(creation_time, 1.0, "创建1000个按钮应在1秒内完成")
```

### 3. 运行测试

```bash
# 运行所有测试
python -m pytest

# 运行特定测试文件
python -m pytest test/test_ui_elements.py

# 运行特定测试方法
python -m pytest test/test_ui_elements.py::TestButton::test_button_click

# 生成覆盖率报告
python -m pytest --cov=src --cov-report=html

# 运行性能测试
python -m pytest test/test_performance.py -v
```

## 调试指南

### 1. 日志配置

```python
import logging
from src.utils.logger import get_logger

# 获取模块日志器
logger = get_logger("MyModule")

# 设置日志级别
logger.setLevel(logging.DEBUG)

# 记录不同级别的日志
logger.debug("调试信息")
logger.info("一般信息")
logger.warning("警告信息")
logger.error("错误信息")
logger.critical("严重错误")
```

### 2. 调试工具

#### 使用pdb调试器

```python
import pdb

def problematic_function():
    x = 10
    y = 0
    pdb.set_trace()  # 设置断点
    result = x / y   # 这里会出错
    return result
```

#### 使用IDE调试器

在PyCharm或VS Code中设置断点，使用图形化调试界面。

### 3. 性能分析

```python
import cProfile
import pstats

def profile_function():
    # 要分析的代码
    pass

# 性能分析
cProfile.run('profile_function()', 'profile_stats')
stats = pstats.Stats('profile_stats')
stats.sort_stats('cumulative').print_stats(10)
```

## 最佳实践

### 1. 代码组织

- 保持模块职责单一
- 使用依赖注入减少耦合
- 遵循SOLID原则
- 合理使用设计模式

### 2. 错误处理

```python
class UIElementError(Exception):
    """UI元素异常基类"""
    pass

def safe_ui_operation():
    try:
        # UI操作
        result = perform_ui_action()
        return result
    except UIElementError as e:
        logger.error(f"UI操作失败: {e}")
        return None
    except Exception as e:
        logger.critical(f"未预期的错误: {e}")
        raise
```

### 3. 配置管理

```python
from dataclasses import dataclass
from typing import Optional

@dataclass
class ModuleConfig:
    timeout: float = 5.0
    retry_count: int = 3
    debug_mode: bool = False
    
    @classmethod
    def from_file(cls, config_path: str) -> 'ModuleConfig':
        """从配置文件加载"""
        # 实现配置加载逻辑
        pass
    
    def validate(self) -> bool:
        """验证配置有效性"""
        return self.timeout > 0 and self.retry_count >= 0
```

### 4. 资源管理

```python
from contextlib import contextmanager

@contextmanager
def ui_element_context(element_id: str):
    """UI元素上下文管理器"""
    element = create_ui_element(element_id)
    try:
        yield element
    finally:
        element.cleanup()

# 使用示例
with ui_element_context("button_1") as button:
    button.click()
```

## 发布流程

### 1. 版本管理

使用语义化版本控制 (SemVer)：

- `MAJOR.MINOR.PATCH`
- `2.1.0` - 主版本.次版本.修订版本

### 2. 发布检查清单

- [ ] 所有测试通过
- [ ] 代码覆盖率 > 80%
- [ ] 文档更新完成
- [ ] 变更日志更新
- [ ] 版本号更新
- [ ] 性能测试通过

### 3. 发布命令

```bash
# 创建发布分支
git checkout -b release/2.1.0

# 更新版本号
# 编辑 src/__init__.py 中的 __version__

# 提交变更
git commit -m "chore: bump version to 2.1.0"

# 合并到main分支
git checkout main
git merge release/2.1.0

# 创建标签
git tag -a v2.1.0 -m "Release version 2.1.0"

# 推送到远程
git push origin main --tags
```

## 常见问题

### Q: 如何添加新的UI元素类型？

A: 继承`BaseUIElement`类并实现`interact()`方法，然后在工厂类中注册新类型。

### Q: 如何优化UI识别性能？

A: 使用缓存、调整置信度阈值、优化图像预处理流程。

### Q: 如何处理游戏界面变化？

A: 使用配置文件管理UI元素位置，实现自适应识别算法。

### Q: 如何调试UI识别问题？

A: 启用调试模式，查看截图和识别结果，调整识别参数。

## 贡献指南

欢迎贡献代码！请遵循以下步骤：

1. Fork项目
2. 创建功能分支
3. 编写代码和测试
4. 提交Pull Request
5. 等待代码审查

详细信息请参考 [CONTRIBUTING.md](../CONTRIBUTING.md)。
