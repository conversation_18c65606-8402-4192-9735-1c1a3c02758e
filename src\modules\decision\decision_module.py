"""
决策模块主类
整合启发式评分、MCTS算法、事件处理等功能，提供统一的决策接口
"""

import time
from typing import List, Optional, Dict, Any, Union
from datetime import datetime

from ...utils.logger import get_logger
from ...core.data_structures import (
    GameState, Action, ActionType, UserStrategy, Card, GameScene, GakumasuBotException
)
from .heuristic_evaluator import HeuristicEvaluator, EvaluationResult
from .mcts_engine import MCTSEngine, MCTSResult
from .event_handler import EventHandler, EventType, EventDecision


class DecisionError(GakumasuBotException):
    """决策错误"""
    pass


class DecisionModule:
    """决策模块主类"""
    
    def __init__(self, user_strategy: Optional[UserStrategy] = None):
        """
        初始化决策模块
        
        Args:
            user_strategy: 用户策略配置
        """
        self.logger = get_logger("DecisionModule")
        self.user_strategy = user_strategy or UserStrategy()
        
        # 初始化子模块
        self.heuristic_evaluator = HeuristicEvaluator(self.user_strategy)
        self.mcts_engine = MCTSEngine(self.heuristic_evaluator)
        self.event_handler = EventHandler(self.user_strategy)
        
        # 决策配置
        self.decision_mode = "hybrid"  # hybrid, heuristic_only, mcts_only
        self.enable_mcts = self.user_strategy.behavior.enable_mcts
        self.mcts_time_limit = 5.0
        self.confidence_threshold = 0.6
        
        # 决策历史
        self.decision_history: List[Dict[str, Any]] = []
        self.max_history_size = 50
        
        # 性能统计
        self.total_decisions = 0
        self.total_decision_time = 0.0
        self.mcts_usage_count = 0
        self.heuristic_usage_count = 0
        
        self.logger.info(f"决策模块初始化完成，模式: {self.decision_mode}, MCTS: {self.enable_mcts}")
    
    def make_decision(self, game_state: GameState, 
                     available_actions: Optional[List[Action]] = None,
                     context: Optional[Dict[str, Any]] = None) -> Action:
        """
        做出游戏决策
        
        Args:
            game_state: 当前游戏状态
            available_actions: 可用行动列表
            context: 额外上下文信息
            
        Returns:
            选择的行动
            
        Raises:
            DecisionError: 决策失败时抛出
        """
        start_time = time.time()
        
        try:
            self.logger.info(f"开始决策，场景: {game_state.current_scene.value}")
            
            # 验证输入
            if not game_state.is_valid():
                raise DecisionError("游戏状态无效")
            
            # 识别当前事件类型
            event_type = self.event_handler.identify_event(game_state)
            self.logger.debug(f"识别事件类型: {event_type.value}")
            
            # 生成可用行动（如果未提供）
            if available_actions is None:
                available_actions = self._generate_available_actions(game_state, event_type)
            
            if not available_actions:
                raise DecisionError("没有可用的行动选项")
            
            # 选择决策方法
            decision_method = self._select_decision_method(game_state, event_type, available_actions)
            
            # 执行决策
            if decision_method == "event_handler":
                chosen_action = self._decide_with_event_handler(game_state, event_type, available_actions)
            elif decision_method == "mcts":
                chosen_action = self._decide_with_mcts(game_state, available_actions)
            elif decision_method == "heuristic":
                chosen_action = self._decide_with_heuristic(game_state, available_actions)
            else:
                chosen_action = self._decide_hybrid(game_state, available_actions, event_type)
            
            # 记录决策
            decision_time = time.time() - start_time
            self._record_decision(game_state, chosen_action, decision_method, decision_time, event_type)
            
            self.logger.info(f"决策完成: {chosen_action.description}, 方法: {decision_method}, 耗时: {decision_time:.3f}s")
            return chosen_action
            
        except Exception as e:
            self.logger.error(f"决策失败: {e}")
            # 返回安全的默认行动
            default_action = self._get_safe_default_action(game_state)
            decision_time = time.time() - start_time
            self._record_decision(game_state, default_action, "fallback", decision_time, EventType.UNKNOWN_EVENT)
            return default_action
    
    def evaluate_game_state(self, game_state: GameState) -> EvaluationResult:
        """
        评估游戏状态
        
        Args:
            game_state: 游戏状态
            
        Returns:
            评估结果
        """
        return self.heuristic_evaluator.evaluate_game_state(game_state)
    
    def evaluate_card(self, card: Card, game_state: GameState) -> EvaluationResult:
        """
        评估卡牌价值
        
        Args:
            card: 卡牌
            game_state: 游戏状态
            
        Returns:
            评估结果
        """
        return self.heuristic_evaluator.evaluate_card(card, game_state)
    
    def evaluate_action_sequence(self, actions: List[Action], 
                                game_state: GameState) -> EvaluationResult:
        """
        评估行动序列
        
        Args:
            actions: 行动序列
            game_state: 游戏状态
            
        Returns:
            评估结果
        """
        return self.heuristic_evaluator.evaluate_action_sequence(actions, game_state)
    
    def _select_decision_method(self, game_state: GameState, 
                               event_type: EventType,
                               available_actions: List[Action]) -> str:
        """选择决策方法"""
        # 特定事件优先使用事件处理器
        if event_type in [EventType.STORY_EVENT, EventType.SPECIAL_EVENT]:
            return "event_handler"
        
        # 简单场景使用启发式
        if len(available_actions) <= 2:
            return "heuristic"
        
        # 复杂决策场景
        if self.enable_mcts and len(available_actions) > 3:
            return "mcts"
        
        # 默认使用混合模式
        return "hybrid"
    
    def _decide_with_event_handler(self, game_state: GameState, 
                                  event_type: EventType,
                                  available_actions: List[Action]) -> Action:
        """使用事件处理器决策"""
        event_decision = self.event_handler.handle_event(event_type, game_state)
        
        # 验证推荐行动是否在可用行动中
        recommended_action = event_decision.recommended_action
        
        # 如果推荐行动不在可用列表中，选择最相似的
        if not self._is_action_available(recommended_action, available_actions):
            recommended_action = self._find_similar_action(recommended_action, available_actions)
        
        return recommended_action
    
    def _decide_with_mcts(self, game_state: GameState, 
                         available_actions: List[Action]) -> Action:
        """使用MCTS算法决策"""
        self.mcts_usage_count += 1
        
        mcts_result = self.mcts_engine.search(
            game_state, 
            available_actions,
            max_time=self.mcts_time_limit
        )
        
        if mcts_result.best_action and mcts_result.confidence > self.confidence_threshold:
            return mcts_result.best_action
        else:
            # MCTS结果不可靠，回退到启发式
            self.logger.warning(f"MCTS结果不可靠 (置信度: {mcts_result.confidence:.3f})，回退到启发式")
            return self._decide_with_heuristic(game_state, available_actions)
    
    def _decide_with_heuristic(self, game_state: GameState, 
                              available_actions: List[Action]) -> Action:
        """使用启发式算法决策"""
        self.heuristic_usage_count += 1
        
        best_action = available_actions[0]
        best_score = 0.0
        
        for action in available_actions:
            try:
                # 评估单个行动
                eval_result = self.heuristic_evaluator.evaluate_action_sequence([action], game_state)
                
                if eval_result.total_score > best_score:
                    best_score = eval_result.total_score
                    best_action = action
                    
            except Exception as e:
                self.logger.debug(f"评估行动失败: {action.description}, 错误: {e}")
                continue
        
        return best_action
    
    def _decide_hybrid(self, game_state: GameState, 
                      available_actions: List[Action],
                      event_type: EventType) -> Action:
        """使用混合决策方法"""
        # 首先尝试事件处理器
        if event_type != EventType.UNKNOWN_EVENT:
            try:
                event_decision = self.event_handler.handle_event(event_type, game_state)
                if event_decision.confidence > 0.7:
                    return event_decision.recommended_action
            except Exception as e:
                self.logger.debug(f"事件处理器决策失败: {e}")
        
        # 然后尝试MCTS（如果启用且适用）
        if self.enable_mcts and len(available_actions) > 2:
            try:
                mcts_result = self.mcts_engine.search(
                    game_state, 
                    available_actions,
                    max_time=min(self.mcts_time_limit, 3.0)  # 混合模式下限制MCTS时间
                )
                
                if mcts_result.best_action and mcts_result.confidence > 0.6:
                    self.mcts_usage_count += 1
                    return mcts_result.best_action
                    
            except Exception as e:
                self.logger.debug(f"MCTS决策失败: {e}")
        
        # 最后使用启发式
        return self._decide_with_heuristic(game_state, available_actions)
    
    def _generate_available_actions(self, game_state: GameState, 
                                   event_type: EventType) -> List[Action]:
        """生成可用行动列表"""
        actions = []
        
        # 基于场景生成行动
        if game_state.current_scene == GameScene.PRODUCE_MAIN:
            actions.extend([
                Action(ActionType.CLICK, (200, 300), "声乐课程"),
                Action(ActionType.CLICK, (300, 300), "舞蹈课程"),
                Action(ActionType.CLICK, (400, 300), "视觉课程"),
                Action(ActionType.CLICK, (500, 300), "休息")
            ])
        
        elif game_state.current_scene == GameScene.PRODUCE_BATTLE:
            # 基于手牌生成行动
            for i, card in enumerate(game_state.hand[:5]):
                actions.append(Action(
                    ActionType.CLICK,
                    (100 + i * 120, 500),
                    f"使用卡牌: {card.name_jp}"
                ))
        
        elif game_state.current_scene == GameScene.MAIN_MENU:
            actions.extend([
                Action(ActionType.CLICK, (400, 300), "开始育成"),
                Action(ActionType.CLICK, (400, 400), "打工"),
                Action(ActionType.CLICK, (400, 500), "日常任务")
            ])
        
        # 如果没有生成任何行动，添加默认行动
        if not actions:
            actions.append(Action(ActionType.KEYPRESS, "space", "等待"))
        
        return actions
    
    def _is_action_available(self, action: Action, available_actions: List[Action]) -> bool:
        """检查行动是否在可用列表中"""
        for available_action in available_actions:
            if (action.action_type == available_action.action_type and
                action.target == available_action.target):
                return True
        return False
    
    def _find_similar_action(self, target_action: Action, 
                           available_actions: List[Action]) -> Action:
        """查找最相似的可用行动"""
        # 优先选择相同类型的行动
        same_type_actions = [a for a in available_actions 
                           if a.action_type == target_action.action_type]
        
        if same_type_actions:
            return same_type_actions[0]
        
        # 否则返回第一个可用行动
        return available_actions[0]
    
    def _get_safe_default_action(self, game_state: GameState) -> Action:
        """获取安全的默认行动"""
        # 基于场景返回安全的默认行动
        if game_state.current_scene == GameScene.PRODUCE_MAIN:
            return Action(ActionType.CLICK, (500, 300), "休息")
        elif game_state.current_scene == GameScene.PRODUCE_BATTLE:
            return Action(ActionType.KEYPRESS, "space", "跳过")
        else:
            return Action(ActionType.KEYPRESS, "space", "等待")
    
    def _record_decision(self, game_state: GameState, action: Action, 
                        method: str, decision_time: float, event_type: EventType):
        """记录决策历史"""
        record = {
            "timestamp": datetime.now().isoformat(),
            "scene": game_state.current_scene.value,
            "event_type": event_type.value,
            "action_type": action.action_type.value,
            "action_description": action.description,
            "decision_method": method,
            "decision_time": decision_time,
            "stamina": game_state.stamina,
            "vigor": game_state.vigor,
            "week": game_state.current_week
        }
        
        self.decision_history.append(record)
        
        # 限制历史记录大小
        if len(self.decision_history) > self.max_history_size:
            self.decision_history.pop(0)
        
        # 更新统计信息
        self.total_decisions += 1
        self.total_decision_time += decision_time
    
    def get_decision_statistics(self) -> Dict[str, Any]:
        """获取决策统计信息"""
        avg_decision_time = (self.total_decision_time / self.total_decisions 
                           if self.total_decisions > 0 else 0.0)
        
        return {
            "total_decisions": self.total_decisions,
            "total_decision_time": self.total_decision_time,
            "average_decision_time": avg_decision_time,
            "mcts_usage_count": self.mcts_usage_count,
            "heuristic_usage_count": self.heuristic_usage_count,
            "mcts_usage_ratio": (self.mcts_usage_count / max(self.total_decisions, 1)),
            "decision_history_size": len(self.decision_history)
        }
    
    def get_recent_decisions(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最近的决策记录"""
        return self.decision_history[-limit:] if self.decision_history else []
    
    def configure(self, decision_mode: Optional[str] = None,
                  enable_mcts: Optional[bool] = None,
                  mcts_time_limit: Optional[float] = None,
                  confidence_threshold: Optional[float] = None):
        """
        配置决策模块参数
        
        Args:
            decision_mode: 决策模式
            enable_mcts: 是否启用MCTS
            mcts_time_limit: MCTS时间限制
            confidence_threshold: 置信度阈值
        """
        if decision_mode is not None:
            self.decision_mode = decision_mode
        if enable_mcts is not None:
            self.enable_mcts = enable_mcts
        if mcts_time_limit is not None:
            self.mcts_time_limit = mcts_time_limit
        if confidence_threshold is not None:
            self.confidence_threshold = confidence_threshold
        
        self.logger.info(f"决策模块配置已更新: mode={self.decision_mode}, "
                        f"mcts={self.enable_mcts}, time_limit={self.mcts_time_limit}")
    
    def reset_statistics(self):
        """重置统计信息"""
        self.total_decisions = 0
        self.total_decision_time = 0.0
        self.mcts_usage_count = 0
        self.heuristic_usage_count = 0
        self.decision_history.clear()
        
        # 重置子模块统计
        self.mcts_engine.reset_statistics()
        self.event_handler.reset_statistics()
        
        self.logger.info("决策模块统计信息已重置")
    
    def get_module_info(self) -> Dict[str, Any]:
        """获取模块信息"""
        return {
            "decision_mode": self.decision_mode,
            "enable_mcts": self.enable_mcts,
            "mcts_time_limit": self.mcts_time_limit,
            "confidence_threshold": self.confidence_threshold,
            "user_strategy_target": self.user_strategy.produce_goal.target,
            "statistics": self.get_decision_statistics(),
            "heuristic_evaluator_info": self.heuristic_evaluator.get_evaluator_info(),
            "mcts_engine_info": self.mcts_engine.get_engine_info(),
            "event_handler_info": self.event_handler.get_handler_info()
        }
