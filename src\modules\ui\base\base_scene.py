"""
场景抽象基类
使用传统Class实现复杂场景管理逻辑
"""

import time
from abc import ABC, abstractmethod
from typing import Dict, Optional, Any, Protocol, List
from collections import defaultdict

from ....utils.logger import get_logger
from ....core.data_structures import GameScene
from ..config.scene_config import SceneConfig
from .base_ui_element import BaseUIElement


class SceneInterface(Protocol):
    """场景接口协议"""
    
    def is_current_scene(self) -> bool:
        """检查是否为当前场景"""
        ...
    
    def get_ui_element(self, name: str) -> Optional[BaseUIElement]:
        """获取UI元素"""
        ...
    
    def wait_for_scene(self, timeout: float = 10.0) -> bool:
        """等待场景出现"""
        ...


class BaseScene(ABC):
    """场景抽象基类 - 使用传统Class"""

    def __init__(self, config: SceneConfig, perception_module, action_controller):
        """
        初始化场景
        
        Args:
            config: 场景配置
            perception_module: 感知模块
            action_controller: 行动控制器
        """
        self.config = config
        self.perception = perception_module
        self.action = action_controller
        self.ui_elements: Dict[str, BaseUIElement] = {}
        self.logger = get_logger(f"Scene.{self.__class__.__name__}")
        
        # 复杂状态管理
        self._scene_cache: Dict[str, Any] = {}
        self._navigation_history: List[Dict[str, Any]] = []
        self._error_recovery_count = 0
        self._last_recognition_time = 0.0
        self._performance_metrics = defaultdict(list)
        
        # 初始化UI元素
        self._init_ui_elements()

    @abstractmethod
    def _init_ui_elements(self):
        """初始化UI元素（抽象方法）"""
        pass

    def is_current_scene(self) -> bool:
        """
        检查是否为当前场景
        
        Returns:
            是否为当前场景
        """
        try:
            start_time = time.time()
            
            # 使用缓存检查
            if self.config.cache_enabled:
                cached_result = self._get_cached_scene_recognition()
                if cached_result is not None:
                    return cached_result
            
            # 执行实际识别
            result = self._recognize_scene_direct()
            
            # 记录性能
            duration = time.time() - start_time
            self._record_performance('scene_recognition', duration, result)
            
            # 更新缓存
            if self.config.cache_enabled:
                self._update_scene_recognition_cache(result)
            
            return result
            
        except Exception as e:
            self.logger.error(f"场景识别失败: {e}")
            return False

    def _recognize_scene_direct(self) -> bool:
        """直接场景识别"""
        try:
            # 检查场景指示器
            for indicator in self.config.scene_indicators:
                match_result = self.perception.find_ui_element(indicator)
                if (match_result and 
                    match_result.confidence >= self.config.recognition_confidence):
                    self.logger.debug(f"场景识别成功，指示器: {indicator}")
                    return True
            
            self.logger.debug(f"场景识别失败，未找到有效指示器")
            return False
            
        except Exception as e:
            self.logger.error(f"场景识别过程出错: {e}")
            return False

    def _get_cached_scene_recognition(self) -> Optional[bool]:
        """获取缓存的场景识别结果"""
        current_time = time.time()
        cache_key = "scene_recognition"
        
        if cache_key in self._scene_cache:
            cached_time, cached_result = self._scene_cache[cache_key]
            # 场景识别缓存时间较短，因为场景可能快速变化
            if current_time - cached_time < 0.5:  # 0.5秒缓存
                return cached_result
        
        return None

    def _update_scene_recognition_cache(self, result: bool):
        """更新场景识别缓存"""
        current_time = time.time()
        self._scene_cache["scene_recognition"] = (current_time, result)
        self._last_recognition_time = current_time

    def get_ui_element(self, name: str) -> Optional[BaseUIElement]:
        """
        获取UI元素
        
        Args:
            name: UI元素名称
            
        Returns:
            UI元素实例，如果不存在返回None
        """
        element = self.ui_elements.get(name)
        if not element:
            self.logger.warning(f"未找到UI元素: {name}")
        return element

    def wait_for_scene(self, timeout: float = None) -> bool:
        """
        等待场景出现
        
        Args:
            timeout: 等待超时时间，默认使用配置中的值
            
        Returns:
            是否成功等待到场景
        """
        if timeout is None:
            timeout = self.config.recognition_timeout
        
        start_time = time.time()
        self.logger.info(f"等待场景 {self.config.scene_name} 出现，超时时间: {timeout}秒")
        
        while time.time() - start_time < timeout:
            if self.is_current_scene():
                elapsed_time = time.time() - start_time
                self.logger.info(f"场景 {self.config.scene_name} 出现，等待时间: {elapsed_time:.2f}秒")
                return True
            
            time.sleep(0.1)  # 短暂等待后重试
        
        self.logger.warning(f"等待场景 {self.config.scene_name} 超时")
        return False

    def navigate_to_scene(self, target_scene: GameScene, timeout: float = 30.0) -> bool:
        """
        导航到目标场景
        
        Args:
            target_scene: 目标场景
            timeout: 导航超时时间
            
        Returns:
            是否成功导航
        """
        start_time = time.time()
        self.logger.info(f"从 {self.config.scene_name} 导航到 {target_scene.value}")
        
        try:
            # 记录导航开始
            navigation_record = {
                'from_scene': self.config.scene_type,
                'to_scene': target_scene,
                'start_time': start_time,
                'success': False
            }
            
            # 执行导航逻辑（子类实现）
            success = self._execute_navigation(target_scene, timeout)
            
            # 记录导航结果
            navigation_record['success'] = success
            navigation_record['end_time'] = time.time()
            navigation_record['duration'] = navigation_record['end_time'] - start_time
            self._navigation_history.append(navigation_record)
            
            if success:
                self.logger.info(f"导航成功，耗时: {navigation_record['duration']:.2f}秒")
            else:
                self.logger.error(f"导航失败，耗时: {navigation_record['duration']:.2f}秒")
            
            return success
            
        except Exception as e:
            self.logger.error(f"导航过程出错: {e}")
            return False

    @abstractmethod
    def _execute_navigation(self, target_scene: GameScene, timeout: float) -> bool:
        """
        执行导航逻辑（抽象方法，子类实现）
        
        Args:
            target_scene: 目标场景
            timeout: 超时时间
            
        Returns:
            是否成功导航
        """
        pass

    def _record_performance(self, operation: str, duration: float, success: bool):
        """记录性能指标"""
        self._performance_metrics[operation].append({
            'duration': duration,
            'success': success,
            'timestamp': time.time()
        })
        
        # 保持最近100条记录
        if len(self._performance_metrics[operation]) > 100:
            self._performance_metrics[operation] = self._performance_metrics[operation][-100:]

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        stats = {}
        
        for operation, records in self._performance_metrics.items():
            if not records:
                continue
                
            durations = [r['duration'] for r in records]
            success_count = sum(1 for r in records if r['success'])
            
            stats[operation] = {
                'total_operations': len(records),
                'success_rate': success_count / len(records),
                'average_duration': sum(durations) / len(durations),
                'min_duration': min(durations),
                'max_duration': max(durations)
            }
        
        return stats

    def get_navigation_history(self) -> List[Dict[str, Any]]:
        """获取导航历史"""
        return self._navigation_history.copy()

    def clear_cache(self):
        """清理缓存"""
        self._scene_cache.clear()
        for element in self.ui_elements.values():
            element.clear_cache()

    def reset_performance_stats(self):
        """重置性能统计"""
        self._performance_metrics.clear()
        self._navigation_history.clear()
        self._error_recovery_count = 0

    def get_scene_info(self) -> Dict[str, Any]:
        """获取场景信息"""
        return {
            'scene_type': self.config.scene_type.value,
            'scene_name': self.config.scene_name,
            'ui_elements_count': len(self.ui_elements),
            'ui_elements': list(self.ui_elements.keys()),
            'last_recognition_time': self._last_recognition_time,
            'error_recovery_count': self._error_recovery_count,
            'cache_enabled': self.config.cache_enabled
        }

    def __str__(self) -> str:
        """字符串表示"""
        return f"{self.__class__.__name__}(scene='{self.config.scene_name}')"

    def __repr__(self) -> str:
        """详细字符串表示"""
        return (f"{self.__class__.__name__}("
                f"scene_type={self.config.scene_type.value}, "
                f"scene_name='{self.config.scene_name}', "
                f"ui_elements={len(self.ui_elements)})")
