"""
育成考试场景实现
处理育成过程中的各种考试类型、考试准备和成绩评估
"""

import time
from typing import List, Dict, Any, Optional, Tuple
from enum import Enum

from .....core.data_structures import GameScene
from ...config.scene_config import SceneConfig
from ...config.ui_element_config import ButtonConfig, LabelConfig
from ..base_game_scene import BaseGameScene
from ...utils.performance_monitor import measure_block
from ...utils.scene_optimizer import get_scene_optimizer


class ExamType(Enum):
    """考试类型枚举"""
    VOCAL_EXAM = "vocal_exam"          # 声乐考试
    DANCE_EXAM = "dance_exam"          # 舞蹈考试
    VISUAL_EXAM = "visual_exam"        # 视觉考试
    MENTAL_EXAM = "mental_exam"        # 精神考试
    COMPREHENSIVE = "comprehensive"     # 综合考试
    SPECIAL_EXAM = "special_exam"      # 特殊考试


class ExamDifficulty(Enum):
    """考试难度枚举"""
    BEGINNER = "beginner"      # 初级
    INTERMEDIATE = "intermediate"  # 中级
    ADVANCED = "advanced"      # 高级
    EXPERT = "expert"          # 专家级


class ExamPhase(Enum):
    """考试阶段枚举"""
    PREPARATION = "preparation"    # 准备阶段
    EXAM = "exam"                 # 考试阶段
    RESULT = "result"             # 结果阶段


class ProduceExamScene(BaseGameScene):
    """育成考试场景类"""
    
    def __init__(self, config: SceneConfig, perception_module, action_controller):
        """
        初始化育成考试场景
        
        Args:
            config: 场景配置
            perception_module: 感知模块
            action_controller: 行动控制器
        """
        super().__init__(config, perception_module, action_controller)
        
        # 考试状态
        self._exam_type: Optional[ExamType] = None
        self._exam_difficulty: Optional[ExamDifficulty] = None
        self._current_phase = ExamPhase.PREPARATION
        self._exam_progress = 0.0
        
        # 考试数据
        self._exam_score = 0
        self._max_score = 100
        self._time_remaining = 0
        self._total_time = 300  # 5分钟默认
        
        # 考试题目和答案
        self._current_question = 0
        self._total_questions = 0
        self._correct_answers = 0
        self._exam_answers = []
        
        # 考试要求
        self._required_stats = {
            "vocal": 0, "dance": 0, "visual": 0, "mental": 0
        }
        self._player_stats = {
            "vocal": 0, "dance": 0, "visual": 0, "mental": 0
        }
        
        # 性能监控
        self.optimizer = get_scene_optimizer()
        
        self.scene_logger.info("育成考试场景初始化完成")
    
    def _create_scene_ui_elements(self):
        """创建考试场景特定的UI元素"""
        try:
            with measure_block("exam_ui_creation"):
                # 场景标识元素
                self._create_exam_indicators()
                
                # 考试选择元素
                self._create_exam_selection_elements()
                
                # 考试控制元素
                self._create_exam_controls()
                
                # 题目显示元素
                self._create_question_displays()
                
                # 状态显示元素
                self._create_status_displays()
            
            self.scene_logger.info(f"考试场景UI元素创建完成，共{len(self.ui_elements)}个元素")
            
        except Exception as e:
            self.scene_logger.error(f"考试场景UI元素创建失败: {e}")
            raise
    
    def _create_exam_indicators(self):
        """创建考试标识元素"""
        # 考试场景标题
        self.ui_elements["exam_scene_title"] = self.ui_factory.create_enhanced_label(
            "exam_scene_title",
            confidence_threshold=0.9,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
        
        # 考试类型显示
        self.ui_elements["exam_type_label"] = self.ui_factory.create_enhanced_label(
            "exam_type_label",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
        
        # 考试难度显示
        self.ui_elements["exam_difficulty_label"] = self.ui_factory.create_label(
            "exam_difficulty_label",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
        
        # 考试阶段指示器
        self.ui_elements["exam_phase_indicator"] = self.ui_factory.create_label(
            "exam_phase_indicator",
            confidence_threshold=0.8,
            text_recognition_enabled=False
        )
    
    def _create_exam_selection_elements(self):
        """创建考试选择元素"""
        # 考试类型选择按钮
        for exam_type in ExamType:
            button_name = f"{exam_type.value}_button"
            self.ui_elements[button_name] = self.ui_factory.create_enhanced_button(
                button_name,
                confidence_threshold=0.8,
                timeout=5.0,
                verify_click_result=True
            )
        
        # 难度选择按钮
        for difficulty in ExamDifficulty:
            button_name = f"{difficulty.value}_difficulty_button"
            self.ui_elements[button_name] = self.ui_factory.create_button(
                button_name,
                confidence_threshold=0.8,
                timeout=3.0
            )
        
        # 考试开始按钮
        self.ui_elements["start_exam_button"] = self.ui_factory.create_enhanced_button(
            "start_exam_button",
            confidence_threshold=0.9,
            timeout=5.0,
            expected_scene_after_click="exam_active",
            verify_click_result=True
        )
    
    def _create_exam_controls(self):
        """创建考试控制元素"""
        # 答案选择按钮（A、B、C、D）
        for option in ['A', 'B', 'C', 'D']:
            button_name = f"answer_{option}_button"
            self.ui_elements[button_name] = self.ui_factory.create_enhanced_button(
                button_name,
                confidence_threshold=0.8,
                timeout=3.0,
                verify_click_result=True
            )
        
        # 确认答案按钮
        self.ui_elements["confirm_answer_button"] = self.ui_factory.create_enhanced_button(
            "confirm_answer_button",
            confidence_threshold=0.9,
            timeout=3.0,
            verify_click_result=True
        )
        
        # 下一题按钮
        self.ui_elements["next_question_button"] = self.ui_factory.create_button(
            "next_question_button",
            confidence_threshold=0.8,
            timeout=3.0
        )
        
        # 跳过按钮
        self.ui_elements["skip_question_button"] = self.ui_factory.create_button(
            "skip_question_button",
            confidence_threshold=0.8,
            timeout=3.0
        )
        
        # 提交考试按钮
        self.ui_elements["submit_exam_button"] = self.ui_factory.create_enhanced_button(
            "submit_exam_button",
            confidence_threshold=0.9,
            timeout=5.0,
            verify_click_result=True
        )
    
    def _create_question_displays(self):
        """创建题目显示元素"""
        # 题目文本
        self.ui_elements["question_text"] = self.ui_factory.create_enhanced_label(
            "question_text",
            confidence_threshold=0.7,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
        
        # 选项文本（A、B、C、D）
        for option in ['A', 'B', 'C', 'D']:
            label_name = f"option_{option}_text"
            self.ui_elements[label_name] = self.ui_factory.create_enhanced_label(
                label_name,
                confidence_threshold=0.7,
                text_recognition_enabled=True,
                ocr_language="ja"
            )
        
        # 题目图片（如果有）
        self.ui_elements["question_image"] = self.ui_factory.create_label(
            "question_image",
            confidence_threshold=0.8,
            text_recognition_enabled=False
        )
        
        # 题目提示
        self.ui_elements["question_hint"] = self.ui_factory.create_label(
            "question_hint",
            confidence_threshold=0.7,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
    
    def _create_status_displays(self):
        """创建状态显示元素"""
        # 题目进度
        self.ui_elements["question_progress"] = self.ui_factory.create_enhanced_label(
            "question_progress",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="en"
        )
        
        # 剩余时间
        self.ui_elements["time_remaining"] = self.ui_factory.create_enhanced_label(
            "time_remaining",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="en"
        )
        
        # 当前得分
        self.ui_elements["current_score"] = self.ui_factory.create_enhanced_label(
            "current_score",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="en"
        )
        
        # 正确率
        self.ui_elements["accuracy_rate"] = self.ui_factory.create_label(
            "accuracy_rate",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="en"
        )
        
        # 考试要求显示
        for stat in ["vocal", "dance", "visual", "mental"]:
            self.ui_elements[f"required_{stat}"] = self.ui_factory.create_label(
                f"required_{stat}",
                confidence_threshold=0.8,
                text_recognition_enabled=True,
                ocr_language="en"
            )
    
    def _get_critical_elements(self) -> List[str]:
        """获取考试场景的关键UI元素"""
        return [
            "exam_scene_title",
            "exam_type_label",
            "start_exam_button",
            "question_text",
            "time_remaining"
        ]
    
    def _verify_scene_specific_state(self) -> bool:
        """验证考试场景特定状态"""
        try:
            # 验证考试场景标题可见
            title_element = self.get_ui_element("exam_scene_title")
            if not title_element or not title_element.is_visible():
                self.scene_logger.debug("考试场景标题不可见")
                return False
            
            # 根据当前阶段验证相应元素
            if self._current_phase == ExamPhase.PREPARATION:
                return self._verify_preparation_elements()
            elif self._current_phase == ExamPhase.EXAM:
                return self._verify_exam_elements()
            
            return True
            
        except Exception as e:
            self.scene_logger.error(f"考试场景状态验证异常: {e}")
            return False
    
    def _verify_preparation_elements(self) -> bool:
        """验证准备阶段元素"""
        # 验证考试类型选择按钮
        exam_buttons = [f"{exam_type.value}_button" for exam_type in ExamType]
        visible_buttons = 0
        
        for button_name in exam_buttons:
            button = self.get_ui_element(button_name)
            if button and button.is_visible():
                visible_buttons += 1
        
        return visible_buttons >= 3  # 至少3个考试类型按钮可见
    
    def _verify_exam_elements(self) -> bool:
        """验证考试阶段元素"""
        # 验证题目文本可见
        question_element = self.get_ui_element("question_text")
        if not question_element or not question_element.is_visible():
            return False
        
        # 验证答案选项按钮
        answer_buttons = [f"answer_{option}_button" for option in ['A', 'B', 'C', 'D']]
        visible_answers = 0
        
        for button_name in answer_buttons:
            button = self.get_ui_element(button_name)
            if button and button.is_visible():
                visible_answers += 1
        
        return visible_answers >= 2  # 至少2个答案选项可见
    
    def select_exam_type(self, exam_type: ExamType) -> bool:
        """
        选择考试类型
        
        Args:
            exam_type: 考试类型
            
        Returns:
            是否选择成功
        """
        try:
            self.scene_logger.info(f"选择考试类型: {exam_type.value}")
            
            # 记录场景使用
            self.optimizer.record_ui_element_usage(GameScene.PRODUCE_EXAM, f"{exam_type.value}_button")
            
            button_name = f"{exam_type.value}_button"
            
            if not self.interact_with_element(button_name, "click"):
                self.scene_logger.error(f"点击{exam_type.value}考试按钮失败")
                return False
            
            # 等待考试信息加载
            time.sleep(1.0)
            
            self._exam_type = exam_type
            self._load_exam_requirements()
            
            self.scene_logger.info(f"考试类型选择成功: {exam_type.value}")
            return True
            
        except Exception as e:
            self.scene_logger.error(f"选择考试类型失败: {e}")
            return False
    
    def select_exam_difficulty(self, difficulty: ExamDifficulty) -> bool:
        """
        选择考试难度
        
        Args:
            difficulty: 考试难度
            
        Returns:
            是否选择成功
        """
        try:
            self.scene_logger.info(f"选择考试难度: {difficulty.value}")
            
            button_name = f"{difficulty.value}_difficulty_button"
            
            if not self.interact_with_element(button_name, "click"):
                self.scene_logger.error(f"点击{difficulty.value}难度按钮失败")
                return False
            
            time.sleep(0.5)
            
            self._exam_difficulty = difficulty
            self.scene_logger.info(f"考试难度选择成功: {difficulty.value}")
            return True
            
        except Exception as e:
            self.scene_logger.error(f"选择考试难度失败: {e}")
            return False
    
    def start_exam(self) -> bool:
        """开始考试"""
        try:
            self.scene_logger.info("开始考试")
            
            # 检查是否已选择考试类型
            if not self._exam_type:
                self.scene_logger.warning("未选择考试类型，使用默认综合考试")
                if not self.select_exam_type(ExamType.COMPREHENSIVE):
                    return False
            
            # 检查是否已选择难度
            if not self._exam_difficulty:
                self.scene_logger.warning("未选择考试难度，使用默认中级难度")
                if not self.select_exam_difficulty(ExamDifficulty.INTERMEDIATE):
                    return False
            
            # 点击开始考试按钮
            if not self.interact_with_element("start_exam_button", "click"):
                self.scene_logger.error("点击开始考试按钮失败")
                return False
            
            # 更新考试阶段
            self._current_phase = ExamPhase.EXAM
            
            # 等待考试界面加载
            time.sleep(2.0)
            
            # 初始化考试数据
            self._initialize_exam_data()
            
            # 验证考试界面
            if self._verify_exam_elements():
                self.scene_logger.info("考试开始成功")
                return True
            else:
                self.scene_logger.error("考试界面验证失败")
                return False
                
        except Exception as e:
            self.scene_logger.error(f"开始考试失败: {e}")
            return False
    
    def _load_exam_requirements(self):
        """加载考试要求"""
        try:
            # 读取考试要求的属性值
            for stat in ["vocal", "dance", "visual", "mental"]:
                req_element = self.get_ui_element(f"required_{stat}")
                if req_element:
                    req_text = req_element.read_text()
                    if req_text:
                        import re
                        req_match = re.search(r'\d+', req_text)
                        if req_match:
                            self._required_stats[stat] = int(req_match.group())
            
            self.scene_logger.debug(f"考试要求: {self._required_stats}")
            
        except Exception as e:
            self.scene_logger.error(f"加载考试要求失败: {e}")
    
    def _initialize_exam_data(self):
        """初始化考试数据"""
        try:
            # 读取题目总数
            progress_element = self.get_ui_element("question_progress")
            if progress_element:
                progress_text = progress_element.read_text()
                if progress_text:
                    import re
                    progress_match = re.search(r'(\d+)/(\d+)', progress_text)
                    if progress_match:
                        self._current_question = int(progress_match.group(1))
                        self._total_questions = int(progress_match.group(2))
            
            # 读取剩余时间
            time_element = self.get_ui_element("time_remaining")
            if time_element:
                time_text = time_element.read_text()
                if time_text:
                    import re
                    time_match = re.search(r'(\d+):(\d+)', time_text)
                    if time_match:
                        minutes = int(time_match.group(1))
                        seconds = int(time_match.group(2))
                        self._time_remaining = minutes * 60 + seconds
            
            self.scene_logger.debug(f"考试初始化: {self._current_question}/{self._total_questions}, 时间: {self._time_remaining}s")
            
        except Exception as e:
            self.scene_logger.error(f"初始化考试数据失败: {e}")
    
    def answer_question(self, option: str) -> bool:
        """
        回答题目
        
        Args:
            option: 答案选项 ('A', 'B', 'C', 'D')
            
        Returns:
            是否回答成功
        """
        try:
            if option not in ['A', 'B', 'C', 'D']:
                self.scene_logger.error(f"无效的答案选项: {option}")
                return False
            
            self.scene_logger.info(f"回答题目: {option}")
            
            button_name = f"answer_{option}_button"
            
            # 记录UI元素使用
            self.optimizer.record_ui_element_usage(GameScene.PRODUCE_EXAM, button_name)
            
            # 点击答案选项
            if not self.interact_with_element(button_name, "click"):
                self.scene_logger.error(f"点击答案{option}按钮失败")
                return False
            
            time.sleep(0.5)
            
            # 确认答案
            if not self.interact_with_element("confirm_answer_button", "click"):
                self.scene_logger.error("确认答案失败")
                return False
            
            # 记录答案
            self._exam_answers.append({
                "question": self._current_question,
                "answer": option,
                "timestamp": time.time()
            })
            
            # 等待结果反馈
            time.sleep(1.0)
            
            # 更新统计
            self._update_exam_progress()
            
            self.scene_logger.info(f"题目回答成功: {option}")
            return True
            
        except Exception as e:
            self.scene_logger.error(f"回答题目失败: {e}")
            return False
    
    def skip_question(self) -> bool:
        """跳过当前题目"""
        try:
            self.scene_logger.info("跳过当前题目")
            
            if not self.interact_with_element("skip_question_button", "click"):
                self.scene_logger.error("点击跳过按钮失败")
                return False
            
            # 记录跳过
            self._exam_answers.append({
                "question": self._current_question,
                "answer": "SKIP",
                "timestamp": time.time()
            })
            
            time.sleep(0.5)
            self._update_exam_progress()
            
            self.scene_logger.info("题目跳过成功")
            return True
            
        except Exception as e:
            self.scene_logger.error(f"跳过题目失败: {e}")
            return False
    
    def submit_exam(self) -> bool:
        """提交考试"""
        try:
            self.scene_logger.info("提交考试")
            
            if not self.interact_with_element("submit_exam_button", "click"):
                self.scene_logger.error("点击提交考试按钮失败")
                return False
            
            # 更新考试阶段
            self._current_phase = ExamPhase.RESULT
            
            # 等待结果处理
            time.sleep(3.0)
            
            # 读取最终成绩
            self._read_final_score()
            
            self.scene_logger.info("考试提交成功")
            return True
            
        except Exception as e:
            self.scene_logger.error(f"提交考试失败: {e}")
            return False
    
    def _update_exam_progress(self):
        """更新考试进度"""
        try:
            # 读取当前题目进度
            progress_element = self.get_ui_element("question_progress")
            if progress_element:
                progress_text = progress_element.read_text()
                if progress_text:
                    import re
                    progress_match = re.search(r'(\d+)/(\d+)', progress_text)
                    if progress_match:
                        self._current_question = int(progress_match.group(1))
                        self._total_questions = int(progress_match.group(2))
            
            # 读取当前得分
            score_element = self.get_ui_element("current_score")
            if score_element:
                score_text = score_element.read_text()
                if score_text:
                    import re
                    score_match = re.search(r'\d+', score_text)
                    if score_match:
                        self._exam_score = int(score_match.group())
            
            # 计算进度
            if self._total_questions > 0:
                self._exam_progress = self._current_question / self._total_questions
            
        except Exception as e:
            self.scene_logger.error(f"更新考试进度失败: {e}")
    
    def _read_final_score(self):
        """读取最终成绩"""
        try:
            score_element = self.get_ui_element("current_score")
            if score_element:
                score_text = score_element.read_text()
                if score_text:
                    import re
                    score_match = re.search(r'(\d+)/(\d+)', score_text)
                    if score_match:
                        self._exam_score = int(score_match.group(1))
                        self._max_score = int(score_match.group(2))
            
            # 计算正确答案数
            answered_questions = [a for a in self._exam_answers if a["answer"] != "SKIP"]
            self._correct_answers = int(self._exam_score / self._max_score * len(answered_questions))
            
        except Exception as e:
            self.scene_logger.error(f"读取最终成绩失败: {e}")
    
    def get_exam_status(self) -> Dict[str, Any]:
        """获取考试状态"""
        return {
            "exam_type": self._exam_type.value if self._exam_type else None,
            "exam_difficulty": self._exam_difficulty.value if self._exam_difficulty else None,
            "current_phase": self._current_phase.value,
            "exam_progress": self._exam_progress,
            "current_question": self._current_question,
            "total_questions": self._total_questions,
            "exam_score": self._exam_score,
            "max_score": self._max_score,
            "time_remaining": self._time_remaining,
            "correct_answers": self._correct_answers,
            "total_answers": len(self._exam_answers),
            "required_stats": self._required_stats.copy(),
            "scene_ready": self.is_scene_ready()
        }
    
    def is_exam_complete(self) -> bool:
        """检查考试是否完成"""
        return self._current_phase == ExamPhase.RESULT or self._exam_progress >= 1.0
    
    def get_exam_summary(self) -> Dict[str, Any]:
        """获取考试总结"""
        accuracy = 0.0
        if len(self._exam_answers) > 0:
            accuracy = self._correct_answers / len([a for a in self._exam_answers if a["answer"] != "SKIP"])
        
        return {
            "exam_status": self.get_exam_status(),
            "final_score": self._exam_score,
            "accuracy_rate": accuracy,
            "answers_given": len(self._exam_answers),
            "questions_skipped": len([a for a in self._exam_answers if a["answer"] == "SKIP"]),
            "exam_complete": self.is_exam_complete(),
            "passed": self._exam_score >= (self._max_score * 0.6)  # 60%及格
        }
