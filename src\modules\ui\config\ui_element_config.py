"""
UI元素配置类
使用Dataclass实现配置数据结构
"""

import time
from dataclasses import dataclass, field
from typing import Optional, Tuple, List, Dict, Any, Callable


@dataclass(frozen=True)
class Position:
    """位置值对象 - 不可变Dataclass"""
    x: int
    y: int

    def distance_to(self, other: 'Position') -> float:
        """计算到另一个位置的距离"""
        return ((self.x - other.x) ** 2 + (self.y - other.y) ** 2) ** 0.5

    def offset(self, dx: int, dy: int) -> 'Position':
        """返回偏移后的新位置"""
        return Position(self.x + dx, self.y + dy)


@dataclass(frozen=True)
class Size:
    """尺寸值对象 - 不可变Dataclass"""
    width: int
    height: int

    @property
    def area(self) -> int:
        """计算面积"""
        return self.width * self.height


@dataclass(frozen=True)
class Rectangle:
    """矩形区域值对象 - 不可变Dataclass"""
    position: Position
    size: Size

    @property
    def center(self) -> Position:
        """获取中心点位置"""
        return Position(
            self.position.x + self.size.width // 2,
            self.position.y + self.size.height // 2
        )

    def contains(self, point: Position) -> bool:
        """检查是否包含指定点"""
        return (self.position.x <= point.x <= self.position.x + self.size.width and
                self.position.y <= point.y <= self.position.y + self.size.height)


@dataclass
class MatchResult:
    """匹配结果 - 数据传输对象"""
    position: Position
    size: Size
    confidence: float
    template_name: str
    timestamp: float = field(default_factory=time.time)

    @property
    def center(self) -> Position:
        """获取匹配区域的中心点"""
        return Position(
            self.position.x + self.size.width // 2,
            self.position.y + self.size.height // 2
        )

    @property
    def rectangle(self) -> Rectangle:
        """获取匹配区域的矩形"""
        return Rectangle(self.position, self.size)


@dataclass
class UIElementConfig:
    """UI元素配置类 - 使用Dataclass"""
    template_name: str
    confidence_threshold: float = 0.8
    timeout: float = 5.0
    retry_count: int = 3
    enabled: bool = True
    position: Optional[Position] = None
    size: Optional[Size] = None
    
    # 性能优化选项
    cache_enabled: bool = True
    cache_ttl: float = 1.0  # 缓存生存时间（秒）
    
    # 验证选项
    verify_after_action: bool = True
    verification_timeout: float = 2.0

    def __post_init__(self):
        """配置验证"""
        if not (0 < self.confidence_threshold <= 1.0):
            raise ValueError("confidence_threshold must be between 0 and 1")
        
        if self.timeout <= 0:
            raise ValueError("timeout must be positive")
        
        if self.retry_count < 0:
            raise ValueError("retry_count must be non-negative")
        
        if self.cache_ttl <= 0:
            raise ValueError("cache_ttl must be positive")


@dataclass
class ButtonConfig(UIElementConfig):
    """按钮特定配置"""
    click_behavior: Optional[str] = None
    double_click_enabled: bool = False
    long_press_duration: float = 1.0
    click_delay: float = 0.1  # 点击后延迟时间
    
    # 验证相关
    expected_scene_after_click: Optional[str] = None
    verify_click_result: bool = True


@dataclass
class InputFieldConfig(UIElementConfig):
    """输入框特定配置"""
    clear_before_input: bool = True
    input_delay: float = 0.1  # 字符间输入延迟
    max_length: Optional[int] = None
    input_method: str = "direct"  # direct, clipboard, ime
    
    # 验证相关
    verify_input_result: bool = True
    read_back_text: bool = False  # 是否读取输入后的文本进行验证


@dataclass
class LabelConfig(UIElementConfig):
    """标签特定配置"""
    text_recognition_enabled: bool = True
    ocr_language: str = "ja"  # 默认日语
    text_preprocessing: bool = True
    expected_text_pattern: Optional[str] = None  # 正则表达式模式
    
    # OCR相关设置
    ocr_confidence_threshold: float = 0.7
    text_cleanup_enabled: bool = True


# 性能优化的配置类（使用__slots__）
@dataclass
class OptimizedUIElementConfig:
    """性能优化的UI元素配置类"""
    __slots__ = [
        'template_name', 'confidence_threshold', 'timeout', 'retry_count', 
        'enabled', 'position', 'cache_enabled', 'cache_ttl'
    ]
    
    template_name: str
    confidence_threshold: float = 0.8
    timeout: float = 5.0
    retry_count: int = 3
    enabled: bool = True
    position: Optional[Position] = None
    cache_enabled: bool = True
    cache_ttl: float = 1.0
