"""
渲染优化器
提供UI渲染性能优化、视口管理和帧率控制功能
"""

import time
import threading
from typing import Dict, Any, Optional, List, Callable, Tuple
from enum import Enum
from dataclasses import dataclass, field
from collections import deque

from ....utils.logger import get_logger
from ..utils.performance_monitor import measure_block


class RenderingMode(Enum):
    """渲染模式枚举"""
    IMMEDIATE = "immediate"       # 立即渲染
    BATCHED = "batched"          # 批量渲染
    DEFERRED = "deferred"        # 延迟渲染
    ADAPTIVE = "adaptive"        # 自适应渲染


class ViewportState(Enum):
    """视口状态枚举"""
    VISIBLE = "visible"          # 可见
    HIDDEN = "hidden"            # 隐藏
    PARTIALLY_VISIBLE = "partial" # 部分可见
    OFFSCREEN = "offscreen"      # 屏幕外


class PerformanceLevel(Enum):
    """性能级别枚举"""
    HIGH = "high"                # 高性能
    MEDIUM = "medium"            # 中等性能
    LOW = "low"                  # 低性能
    CRITICAL = "critical"        # 性能临界


@dataclass
class RenderingStats:
    """渲染统计信息"""
    frame_count: int = 0
    average_fps: float = 0.0
    current_fps: float = 0.0
    frame_time_ms: float = 0.0
    render_calls: int = 0
    batched_calls: int = 0
    skipped_frames: int = 0
    viewport_updates: int = 0
    performance_level: PerformanceLevel = PerformanceLevel.HIGH
    timestamp: float = field(default_factory=time.time)


@dataclass
class ViewportInfo:
    """视口信息"""
    x: int = 0
    y: int = 0
    width: int = 1920
    height: int = 1080
    scale: float = 1.0
    visible_area: Tuple[int, int, int, int] = (0, 0, 1920, 1080)  # x, y, width, height


@dataclass
class RenderTask:
    """渲染任务"""
    id: str
    element_id: str
    priority: int = 0
    render_func: Optional[Callable] = None
    params: Dict[str, Any] = field(default_factory=dict)
    created_time: float = field(default_factory=time.time)
    viewport_dependent: bool = True


class ViewportManager:
    """视口管理器"""
    
    def __init__(self, initial_viewport: ViewportInfo = None):
        """
        初始化视口管理器
        
        Args:
            initial_viewport: 初始视口信息
        """
        self.viewport = initial_viewport or ViewportInfo()
        self.logger = get_logger("ViewportManager")
        
        # 视口变化监听
        self.viewport_listeners: List[Callable] = []
        
        # 元素可见性跟踪
        self._element_visibility: Dict[str, ViewportState] = {}
        self._visible_elements: set = set()
        
        # 视口历史
        self._viewport_history: deque = deque(maxlen=100)
        
        self.logger.info("视口管理器初始化完成")
    
    def update_viewport(self, x: int = None, y: int = None, width: int = None, 
                       height: int = None, scale: float = None):
        """更新视口信息"""
        old_viewport = ViewportInfo(
            x=self.viewport.x,
            y=self.viewport.y,
            width=self.viewport.width,
            height=self.viewport.height,
            scale=self.viewport.scale
        )
        
        if x is not None:
            self.viewport.x = x
        if y is not None:
            self.viewport.y = y
        if width is not None:
            self.viewport.width = width
        if height is not None:
            self.viewport.height = height
        if scale is not None:
            self.viewport.scale = scale
        
        # 更新可见区域
        self.viewport.visible_area = (
            self.viewport.x,
            self.viewport.y,
            self.viewport.width,
            self.viewport.height
        )
        
        # 记录历史
        self._viewport_history.append(old_viewport)
        
        # 通知监听器
        for listener in self.viewport_listeners:
            try:
                listener(self.viewport, old_viewport)
            except Exception as e:
                self.logger.error(f"视口监听器执行失败: {e}")
        
        self.logger.debug(f"视口更新: {self.viewport.width}x{self.viewport.height} @ ({self.viewport.x}, {self.viewport.y})")
    
    def add_viewport_listener(self, listener: Callable):
        """添加视口变化监听器"""
        self.viewport_listeners.append(listener)
    
    def remove_viewport_listener(self, listener: Callable):
        """移除视口变化监听器"""
        if listener in self.viewport_listeners:
            self.viewport_listeners.remove(listener)
    
    def is_element_visible(self, element_bounds: Tuple[int, int, int, int]) -> ViewportState:
        """检查元素是否在视口内可见"""
        elem_x, elem_y, elem_width, elem_height = element_bounds
        vp_x, vp_y, vp_width, vp_height = self.viewport.visible_area
        
        # 检查是否完全在视口外
        if (elem_x + elem_width < vp_x or elem_x > vp_x + vp_width or
            elem_y + elem_height < vp_y or elem_y > vp_y + vp_height):
            return ViewportState.OFFSCREEN
        
        # 检查是否完全在视口内
        if (elem_x >= vp_x and elem_y >= vp_y and
            elem_x + elem_width <= vp_x + vp_width and
            elem_y + elem_height <= vp_y + vp_height):
            return ViewportState.VISIBLE
        
        # 部分可见
        return ViewportState.PARTIALLY_VISIBLE
    
    def update_element_visibility(self, element_id: str, bounds: Tuple[int, int, int, int]):
        """更新元素可见性"""
        visibility = self.is_element_visible(bounds)
        old_visibility = self._element_visibility.get(element_id)
        
        self._element_visibility[element_id] = visibility
        
        # 更新可见元素集合
        if visibility in [ViewportState.VISIBLE, ViewportState.PARTIALLY_VISIBLE]:
            self._visible_elements.add(element_id)
        else:
            self._visible_elements.discard(element_id)
        
        return visibility != old_visibility
    
    def get_visible_elements(self) -> List[str]:
        """获取可见元素列表"""
        return list(self._visible_elements)
    
    def get_viewport_stats(self) -> Dict[str, Any]:
        """获取视口统计"""
        return {
            "viewport": {
                "x": self.viewport.x,
                "y": self.viewport.y,
                "width": self.viewport.width,
                "height": self.viewport.height,
                "scale": self.viewport.scale
            },
            "visible_elements": len(self._visible_elements),
            "tracked_elements": len(self._element_visibility),
            "viewport_changes": len(self._viewport_history)
        }


class RenderingOptimizer:
    """渲染优化器"""
    
    def __init__(self, target_fps: float = 60.0, rendering_mode: RenderingMode = RenderingMode.ADAPTIVE):
        """
        初始化渲染优化器
        
        Args:
            target_fps: 目标帧率
            rendering_mode: 渲染模式
        """
        self.target_fps = target_fps
        self.rendering_mode = rendering_mode
        self.logger = get_logger("RenderingOptimizer")
        
        # 视口管理器
        self.viewport_manager = ViewportManager()
        
        # 渲染队列
        self._render_queue: deque = deque()
        self._batch_queue: List[RenderTask] = []
        self._deferred_queue: List[RenderTask] = []
        
        # 性能监控
        self._frame_times: deque = deque(maxlen=60)  # 保存最近60帧的时间
        self._last_frame_time = time.time()
        self._frame_count = 0
        self._render_calls = 0
        self._batched_calls = 0
        self._skipped_frames = 0
        
        # 渲染控制
        self._rendering = False
        self._render_thread: Optional[threading.Thread] = None
        self._render_lock = threading.RLock()
        
        # 性能阈值
        self.performance_thresholds = {
            PerformanceLevel.HIGH: 16.67,      # 60 FPS (16.67ms per frame)
            PerformanceLevel.MEDIUM: 33.33,    # 30 FPS
            PerformanceLevel.LOW: 66.67,       # 15 FPS
            PerformanceLevel.CRITICAL: 100.0   # 10 FPS
        }
        
        # 优化设置
        self.enable_culling = True          # 启用视锥剔除
        self.enable_batching = True         # 启用批量渲染
        self.enable_deferred = True         # 启用延迟渲染
        self.max_batch_size = 50           # 最大批量大小
        self.batch_timeout = 16.67         # 批量超时时间（ms）
        
        # 事件回调
        self.on_performance_change: Optional[Callable] = None
        self.on_frame_drop: Optional[Callable] = None
        
        self.logger.info(f"渲染优化器初始化完成，目标FPS: {target_fps}")
    
    def start_rendering(self):
        """开始渲染循环"""
        if self._rendering:
            return
        
        self._rendering = True
        self._render_thread = threading.Thread(target=self._rendering_loop, daemon=True)
        self._render_thread.start()
        
        self.logger.info("渲染循环已启动")
    
    def stop_rendering(self):
        """停止渲染循环"""
        self._rendering = False
        if self._render_thread and self._render_thread.is_alive():
            self._render_thread.join(timeout=1.0)
        
        self.logger.info("渲染循环已停止")
    
    def _rendering_loop(self):
        """渲染循环"""
        frame_time_target = 1.0 / self.target_fps
        
        while self._rendering:
            frame_start = time.time()
            
            try:
                # 处理渲染任务
                self._process_render_queue()
                
                # 更新性能统计
                self._update_performance_stats(frame_start)
                
                # 帧率控制
                frame_elapsed = time.time() - frame_start
                if frame_elapsed < frame_time_target:
                    time.sleep(frame_time_target - frame_elapsed)
                else:
                    # 帧时间超出目标，记录丢帧
                    self._skipped_frames += 1
                    if self.on_frame_drop:
                        self.on_frame_drop(frame_elapsed, frame_time_target)
                
            except Exception as e:
                self.logger.error(f"渲染循环异常: {e}")
                time.sleep(0.016)  # 16ms
    
    def _process_render_queue(self):
        """处理渲染队列"""
        with self._render_lock:
            if self.rendering_mode == RenderingMode.IMMEDIATE:
                self._process_immediate_rendering()
            elif self.rendering_mode == RenderingMode.BATCHED:
                self._process_batched_rendering()
            elif self.rendering_mode == RenderingMode.DEFERRED:
                self._process_deferred_rendering()
            elif self.rendering_mode == RenderingMode.ADAPTIVE:
                self._process_adaptive_rendering()
    
    def _process_immediate_rendering(self):
        """处理立即渲染"""
        while self._render_queue:
            task = self._render_queue.popleft()
            self._execute_render_task(task)
    
    def _process_batched_rendering(self):
        """处理批量渲染"""
        # 收集批量任务
        batch_start = time.time()
        while (self._render_queue and 
               len(self._batch_queue) < self.max_batch_size and
               (time.time() - batch_start) * 1000 < self.batch_timeout):
            task = self._render_queue.popleft()
            self._batch_queue.append(task)
        
        # 执行批量渲染
        if self._batch_queue:
            self._execute_batch_render(self._batch_queue)
            self._batch_queue.clear()
    
    def _process_deferred_rendering(self):
        """处理延迟渲染"""
        # 将任务移到延迟队列
        while self._render_queue:
            task = self._render_queue.popleft()
            self._deferred_queue.append(task)
        
        # 按优先级排序延迟队列
        self._deferred_queue.sort(key=lambda t: t.priority, reverse=True)
        
        # 执行高优先级任务
        executed = 0
        max_executions = min(10, len(self._deferred_queue))  # 每帧最多执行10个任务
        
        while executed < max_executions and self._deferred_queue:
            task = self._deferred_queue.pop(0)
            self._execute_render_task(task)
            executed += 1
    
    def _process_adaptive_rendering(self):
        """处理自适应渲染"""
        current_performance = self._get_current_performance_level()
        
        if current_performance == PerformanceLevel.HIGH:
            self._process_immediate_rendering()
        elif current_performance == PerformanceLevel.MEDIUM:
            self._process_batched_rendering()
        else:
            self._process_deferred_rendering()
    
    def _execute_render_task(self, task: RenderTask):
        """执行渲染任务"""
        try:
            # 视锥剔除检查
            if self.enable_culling and task.viewport_dependent:
                if not self._is_task_visible(task):
                    return
            
            # 执行渲染函数
            if task.render_func:
                with measure_block(f"render_task_{task.id}"):
                    task.render_func(**task.params)
            
            self._render_calls += 1
            
        except Exception as e:
            self.logger.error(f"执行渲染任务失败: {task.id}, {e}")
    
    def _execute_batch_render(self, tasks: List[RenderTask]):
        """执行批量渲染"""
        try:
            with measure_block("batch_render"):
                # 按类型分组任务
                task_groups = {}
                for task in tasks:
                    task_type = task.params.get("type", "default")
                    if task_type not in task_groups:
                        task_groups[task_type] = []
                    task_groups[task_type].append(task)
                
                # 批量执行同类型任务
                for task_type, group_tasks in task_groups.items():
                    self._execute_task_group(group_tasks)
                
                self._batched_calls += 1
                
        except Exception as e:
            self.logger.error(f"批量渲染失败: {e}")
    
    def _execute_task_group(self, tasks: List[RenderTask]):
        """执行任务组"""
        for task in tasks:
            self._execute_render_task(task)
    
    def _is_task_visible(self, task: RenderTask) -> bool:
        """检查任务是否在可见区域"""
        # 简化实现：检查元素是否在可见元素列表中
        return task.element_id in self.viewport_manager.get_visible_elements()
    
    def _update_performance_stats(self, frame_start: float):
        """更新性能统计"""
        current_time = time.time()
        frame_time = (current_time - frame_start) * 1000  # 转换为毫秒
        
        self._frame_times.append(frame_time)
        self._frame_count += 1
        self._last_frame_time = current_time
        
        # 检查性能级别变化
        new_performance_level = self._get_current_performance_level()
        if hasattr(self, '_last_performance_level'):
            if new_performance_level != self._last_performance_level:
                if self.on_performance_change:
                    self.on_performance_change(new_performance_level, self._last_performance_level)
        
        self._last_performance_level = new_performance_level
    
    def _get_current_performance_level(self) -> PerformanceLevel:
        """获取当前性能级别"""
        if not self._frame_times:
            return PerformanceLevel.HIGH
        
        avg_frame_time = sum(self._frame_times) / len(self._frame_times)
        
        for level, threshold in sorted(self.performance_thresholds.items(), 
                                     key=lambda x: x[1]):
            if avg_frame_time <= threshold:
                return level
        
        return PerformanceLevel.CRITICAL
    
    def add_render_task(self, task: RenderTask):
        """添加渲染任务"""
        with self._render_lock:
            self._render_queue.append(task)
    
    def create_render_task(self, element_id: str, render_func: Callable, 
                          priority: int = 0, **params) -> RenderTask:
        """创建渲染任务"""
        task_id = f"{element_id}_{int(time.time() * 1000000)}"
        
        return RenderTask(
            id=task_id,
            element_id=element_id,
            priority=priority,
            render_func=render_func,
            params=params
        )
    
    def schedule_render(self, element_id: str, render_func: Callable, 
                       priority: int = 0, **params):
        """调度渲染任务"""
        task = self.create_render_task(element_id, render_func, priority, **params)
        self.add_render_task(task)
    
    def clear_render_queue(self):
        """清空渲染队列"""
        with self._render_lock:
            self._render_queue.clear()
            self._batch_queue.clear()
            self._deferred_queue.clear()
    
    def get_rendering_stats(self) -> RenderingStats:
        """获取渲染统计"""
        current_time = time.time()
        
        # 计算FPS
        if self._frame_times:
            avg_frame_time = sum(self._frame_times) / len(self._frame_times)
            current_fps = 1000.0 / avg_frame_time if avg_frame_time > 0 else 0
            
            # 计算平均FPS（基于最近的帧时间）
            if len(self._frame_times) >= 2:
                time_span = (len(self._frame_times) - 1) * (avg_frame_time / 1000.0)
                average_fps = (len(self._frame_times) - 1) / time_span if time_span > 0 else 0
            else:
                average_fps = current_fps
        else:
            avg_frame_time = 0
            current_fps = 0
            average_fps = 0
        
        return RenderingStats(
            frame_count=self._frame_count,
            average_fps=average_fps,
            current_fps=current_fps,
            frame_time_ms=avg_frame_time,
            render_calls=self._render_calls,
            batched_calls=self._batched_calls,
            skipped_frames=self._skipped_frames,
            viewport_updates=len(self.viewport_manager._viewport_history),
            performance_level=self._get_current_performance_level()
        )
    
    def optimize_rendering(self):
        """优化渲染性能"""
        try:
            current_performance = self._get_current_performance_level()
            
            if current_performance in [PerformanceLevel.LOW, PerformanceLevel.CRITICAL]:
                # 启用所有优化
                self.enable_culling = True
                self.enable_batching = True
                self.enable_deferred = True
                
                # 调整批量大小
                if current_performance == PerformanceLevel.CRITICAL:
                    self.max_batch_size = 20
                    self.batch_timeout = 33.33  # 30 FPS
                else:
                    self.max_batch_size = 30
                    self.batch_timeout = 25.0
                
                # 切换到自适应模式
                self.rendering_mode = RenderingMode.ADAPTIVE
                
                self.logger.info(f"启用渲染优化，性能级别: {current_performance.value}")
            
            elif current_performance == PerformanceLevel.HIGH:
                # 高性能时可以使用立即渲染
                self.rendering_mode = RenderingMode.IMMEDIATE
                self.max_batch_size = 50
                self.batch_timeout = 16.67
            
        except Exception as e:
            self.logger.error(f"渲染优化失败: {e}")
    
    def get_optimization_report(self) -> Dict[str, Any]:
        """获取优化报告"""
        stats = self.get_rendering_stats()
        viewport_stats = self.viewport_manager.get_viewport_stats()
        
        return {
            "rendering_stats": {
                "frame_count": stats.frame_count,
                "average_fps": stats.average_fps,
                "current_fps": stats.current_fps,
                "frame_time_ms": stats.frame_time_ms,
                "render_calls": stats.render_calls,
                "batched_calls": stats.batched_calls,
                "skipped_frames": stats.skipped_frames,
                "performance_level": stats.performance_level.value
            },
            "viewport_stats": viewport_stats,
            "optimization_settings": {
                "rendering_mode": self.rendering_mode.value,
                "target_fps": self.target_fps,
                "enable_culling": self.enable_culling,
                "enable_batching": self.enable_batching,
                "enable_deferred": self.enable_deferred,
                "max_batch_size": self.max_batch_size,
                "batch_timeout": self.batch_timeout
            },
            "queue_status": {
                "render_queue": len(self._render_queue),
                "batch_queue": len(self._batch_queue),
                "deferred_queue": len(self._deferred_queue)
            }
        }
    
    def __del__(self):
        """析构函数"""
        self.stop_rendering()
