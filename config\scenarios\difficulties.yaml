# 难度配置文件
# 定义游戏中可用的难度等级和属性

difficulties:
  # 普通难度
  - difficulty_level: "normal"
    name: "normal"
    display_name: "普通"
    description: "标准难度，适合新手玩家学习游戏机制和基础策略"
    
    # 倍率设置
    reward_multiplier: 1.0            # 奖励倍率
    experience_multiplier: 1.0        # 经验倍率
    
    # 解锁条件
    unlock_conditions:
      min_level: 1                    # 最低等级要求
      required_achievements: []       # 无特殊要求
      tutorial_completed: false       # 不需要完成教程
    
    # 特殊效果
    special_effects:
      stamina_cost_reduction: 0.0     # 体力消耗减少
      failure_penalty_reduction: 0.1  # 失败惩罚减少10%
      hint_availability: true         # 提供提示
      auto_recovery_bonus: 0.05       # 自动恢复加成
      beginner_protection: true       # 新手保护
    
    # UI设置
    is_available: true
    order: 1                          # 显示顺序
    color_theme: "green"              # 主题色
    icon: "normal_difficulty.png"     # 图标文件

  # 大师难度
  - difficulty_level: "master"
    name: "master"
    display_name: "大师"
    description: "高难度模式，提供更多奖励和挑战，需要深度理解游戏机制"
    
    # 倍率设置
    reward_multiplier: 1.5            # 50%奖励加成
    experience_multiplier: 1.3        # 30%经验加成
    
    # 解锁条件
    unlock_conditions:
      min_level: 10                   # 需要10级
      required_achievements:          # 需要完成普通难度
        - "complete_normal_scenario"
        - "reach_score_threshold"
      tutorial_completed: true        # 必须完成教程
      min_completion_rate: 0.8        # 至少80%完成率
    
    # 特殊效果
    special_effects:
      stamina_cost_increase: 0.2      # 体力消耗增加20%
      failure_penalty_increase: 0.3   # 失败惩罚增加30%
      bonus_event_chance: 0.15        # 15%额外事件概率
      rare_reward_chance: 0.25        # 25%稀有奖励概率
      advanced_ai_opponents: true     # 启用高级AI对手
      time_pressure: true             # 时间压力
      limited_retries: 3              # 限制重试次数
    
    # UI设置
    is_available: true
    order: 2                          # 显示顺序
    color_theme: "red"                # 主题色
    icon: "master_difficulty.png"     # 图标文件

  # 简单难度（预留，当前不可用）
  - difficulty_level: "easy"
    name: "easy"
    display_name: "简单"
    description: "简化版难度，降低挑战性，专为休闲玩家设计"
    
    # 倍率设置
    reward_multiplier: 0.8            # 降低奖励
    experience_multiplier: 0.9        # 降低经验
    
    # 解锁条件
    unlock_conditions:
      min_level: 1
      required_achievements: []
    
    # 特殊效果
    special_effects:
      stamina_cost_reduction: 0.3     # 大幅减少体力消耗
      failure_penalty_reduction: 0.5  # 大幅减少失败惩罚
      auto_guidance: true             # 自动指导
      unlimited_retries: true         # 无限重试
      simplified_mechanics: true      # 简化机制
    
    # UI设置
    is_available: false               # 当前不可用
    order: 0
    color_theme: "blue"
    icon: "easy_difficulty.png"

  # 困难难度（预留，当前不可用）
  - difficulty_level: "hard"
    name: "hard"
    display_name: "困难"
    description: "极具挑战性的难度，为专家级玩家准备"
    
    # 倍率设置
    reward_multiplier: 2.0            # 双倍奖励
    experience_multiplier: 1.8        # 80%经验加成
    
    # 解锁条件
    unlock_conditions:
      min_level: 20
      required_achievements:
        - "master_multiple_scenarios"
        - "achieve_perfect_score"
      min_completion_rate: 0.95
    
    # 特殊效果
    special_effects:
      stamina_cost_increase: 0.5      # 大幅增加体力消耗
      failure_penalty_increase: 0.8   # 大幅增加失败惩罚
      no_hints: true                  # 无提示
      permadeath_risk: true           # 永久死亡风险
      extreme_time_pressure: true     # 极端时间压力
      limited_saves: 1                # 限制存档次数
    
    # UI设置
    is_available: false               # 当前不可用
    order: 3
    color_theme: "purple"
    icon: "hard_difficulty.png"

  # 极限难度（预留，当前不可用）
  - difficulty_level: "extreme"
    name: "extreme"
    display_name: "极限"
    description: "终极挑战难度，只有最顶尖的玩家才能征服"
    
    # 倍率设置
    reward_multiplier: 3.0            # 三倍奖励
    experience_multiplier: 2.5        # 2.5倍经验
    
    # 解锁条件
    unlock_conditions:
      min_level: 50
      required_achievements:
        - "complete_all_hard_scenarios"
        - "achieve_legendary_status"
      min_completion_rate: 0.99
      special_unlock_code: true       # 需要特殊解锁码
    
    # 特殊效果
    special_effects:
      stamina_cost_increase: 1.0      # 双倍体力消耗
      failure_penalty_increase: 1.5   # 2.5倍失败惩罚
      one_life_only: true             # 只有一条命
      no_auto_save: true              # 禁用自动保存
      hardcore_mode: true             # 硬核模式
      leaderboard_eligible: true      # 排行榜资格
    
    # UI设置
    is_available: false               # 当前不可用
    order: 4
    color_theme: "gold"
    icon: "extreme_difficulty.png"

# 全局难度设置
global_difficulty_settings:
  # 默认设置
  default_difficulty: "normal"
  allow_difficulty_change: true      # 允许中途更改难度
  difficulty_change_penalty: 0.1     # 更改难度的惩罚
  
  # 平衡设置
  auto_balance: false                 # 自动平衡
  dynamic_scaling: false             # 动态缩放
  
  # 统计设置
  track_performance: true            # 跟踪表现
  generate_reports: true             # 生成报告
  
  # 奖励设置
  difficulty_bonus_cap: 5.0          # 难度奖励上限
  experience_bonus_cap: 3.0          # 经验奖励上限

# 难度组合规则
difficulty_combinations:
  # 定义哪些剧本和难度的组合是允许的
  allowed_combinations:
    - scenario: "nia"
      difficulties: ["normal", "master"]
    - scenario: "hajime"
      difficulties: ["normal", "master"]
  
  # 推荐组合
  recommended_combinations:
    - scenario: "nia"
      difficulty: "normal"
      reason: "新手推荐"
    - scenario: "hajime"
      difficulty: "master"
      reason: "高级玩家挑战"

# 成就和解锁系统
achievement_system:
  # 难度相关成就
  difficulty_achievements:
    - id: "complete_normal_scenario"
      name: "普通难度完成者"
      description: "完成任意剧本的普通难度"
      unlock_condition: "complete_scenario_normal"
    
    - id: "master_multiple_scenarios"
      name: "大师级玩家"
      description: "完成多个剧本的大师难度"
      unlock_condition: "complete_scenarios_master_count_3"
    
    - id: "achieve_perfect_score"
      name: "完美表现"
      description: "在大师难度下获得满分"
      unlock_condition: "perfect_score_master"
