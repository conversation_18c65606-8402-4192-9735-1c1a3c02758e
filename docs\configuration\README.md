# 配置指南

## 概述

本指南详细介绍了Gakumasu-Bot UI模块的配置选项和最佳实践，帮助您根据需求优化系统性能和行为。

## 配置文件结构

系统使用YAML格式的配置文件，主要配置文件位于：

```
config/
├── ui_config.yaml          # 主配置文件
├── scenes/                 # 场景配置
│   ├── training_scene.yaml
│   ├── competition_scene.yaml
│   └── menu_scene.yaml
├── elements/               # UI元素配置
│   ├── buttons.yaml
│   ├── inputs.yaml
│   └── labels.yaml
└── optimization/           # 优化配置
    ├── memory_config.yaml
    └── rendering_config.yaml
```

## 主配置文件

### ui_config.yaml

```yaml
# 系统基础配置
system:
  debug_mode: false
  log_level: "INFO"
  log_file: "logs/ui_module.log"
  screenshot_on_error: true
  temp_directory: "temp/"

# UI元素默认配置
ui_elements:
  default_timeout: 5.0
  confidence_threshold: 0.8
  retry_count: 3
  cache_duration: 1.0
  verification_enabled: true

# 场景配置
scenes:
  transition_timeout: 30.0
  auto_detect: true
  detection_interval: 1.0
  max_transition_retries: 3

# 感知系统配置
perception:
  image_recognition:
    enabled: true
    confidence_threshold: 0.8
    preprocessing: true
  
  text_recognition:
    enabled: true
    language: "zh-CN"
    confidence_threshold: 0.7

# 行动控制配置
action_control:
  mouse:
    click_delay: 0.1
    double_click_interval: 0.3
    drag_speed: 1.0
  
  keyboard:
    typing_speed: 0.05
    key_press_duration: 0.1

# 性能配置
performance:
  enable_caching: true
  cache_size_limit: 100  # MB
  max_memory_usage: 1024  # MB
  gc_threshold: 0.8
  
  rendering:
    target_fps: 60
    enable_batching: true
    max_batch_size: 50
```

## 场景配置

### training_scene.yaml

```yaml
# 训练场景配置
scene_id: "training"
scene_type: "TRAINING"
timeout: 15.0
auto_detect: true

# UI元素定义
ui_elements:
  vocal_lesson_button:
    type: "button"
    confidence_threshold: 0.9
    timeout: 5.0
    template_path: "templates/vocal_button.png"
    
  dance_lesson_button:
    type: "button"
    confidence_threshold: 0.9
    timeout: 5.0
    template_path: "templates/dance_button.png"
    
  visual_lesson_button:
    type: "button"
    confidence_threshold: 0.9
    timeout: 5.0
    template_path: "templates/visual_button.png"
    
  mental_lesson_button:
    type: "button"
    confidence_threshold: 0.9
    timeout: 5.0
    template_path: "templates/mental_button.png"
    
  rest_button:
    type: "button"
    confidence_threshold: 0.8
    timeout: 3.0
    template_path: "templates/rest_button.png"
    
  stamina_label:
    type: "label"
    confidence_threshold: 0.7
    text_recognition: true
    region: [100, 50, 200, 30]  # x, y, width, height

# 场景检测配置
detection:
  primary_indicators:
    - element_id: "vocal_lesson_button"
      required: true
    - element_id: "dance_lesson_button"
      required: true
  
  secondary_indicators:
    - element_id: "stamina_label"
      required: false

# 行为配置
behavior:
  auto_rest_threshold: 20  # 体力低于20时自动休息
  preferred_training_order:
    - "vocal_lesson"
    - "dance_lesson"
    - "visual_lesson"
    - "mental_lesson"
```

## UI元素配置

### buttons.yaml

```yaml
# 按钮元素配置模板
button_defaults:
  confidence_threshold: 0.8
  timeout: 5.0
  retry_count: 3
  verify_click_result: true
  click_delay: 0.1

# 特定按钮配置
buttons:
  start_button:
    template_path: "templates/start_button.png"
    confidence_threshold: 0.9
    timeout: 10.0
    
  confirm_button:
    template_path: "templates/confirm_button.png"
    confidence_threshold: 0.85
    
  cancel_button:
    template_path: "templates/cancel_button.png"
    confidence_threshold: 0.8
    
  back_button:
    template_path: "templates/back_button.png"
    confidence_threshold: 0.8
    region: [50, 50, 100, 50]  # 限制搜索区域
```

### inputs.yaml

```yaml
# 输入框元素配置模板
input_defaults:
  timeout: 5.0
  verify_input_result: true
  clear_before_input: false
  typing_speed: 0.05

# 特定输入框配置
inputs:
  character_name_input:
    template_path: "templates/name_input.png"
    confidence_threshold: 0.8
    max_length: 20
    
  search_input:
    template_path: "templates/search_input.png"
    confidence_threshold: 0.7
    clear_before_input: true
    
  number_input:
    template_path: "templates/number_input.png"
    confidence_threshold: 0.8
    input_type: "numeric"
    min_value: 0
    max_value: 999
```

## 优化配置

### memory_config.yaml

```yaml
# 内存优化配置
memory_optimization:
  enabled: true
  monitoring_interval: 30.0  # 秒
  
  # 内存阈值
  thresholds:
    low: 0.3      # 30%
    normal: 0.6   # 60%
    high: 0.8     # 80%
    critical: 0.95 # 95%
  
  # 缓存配置
  cache_managers:
    ui_elements:
      max_size: 50  # MB
      policy: "LRU"
      ttl: 300      # 秒
    
    images:
      max_size: 100  # MB
      policy: "LFU"
      ttl: 600       # 秒
    
    text_recognition:
      max_size: 20   # MB
      policy: "TTL"
      ttl: 120       # 秒
  
  # 垃圾回收配置
  garbage_collection:
    auto_trigger: true
    trigger_threshold: 0.8
    force_interval: 300  # 秒
```

### rendering_config.yaml

```yaml
# 渲染优化配置
rendering_optimization:
  enabled: true
  target_fps: 60.0
  rendering_mode: "adaptive"  # immediate, batched, deferred, adaptive
  
  # 批处理配置
  batching:
    enabled: true
    max_batch_size: 50
    batch_timeout: 16.67  # 毫秒 (60 FPS)
  
  # 视口管理
  viewport:
    width: 1920
    height: 1080
    scale: 1.0
    culling_enabled: true
  
  # 性能阈值
  performance_thresholds:
    high: 16.67      # 60 FPS
    medium: 33.33    # 30 FPS
    low: 66.67       # 15 FPS
    critical: 100.0  # 10 FPS
```

## 智能化功能配置

### intelligence_config.yaml

```yaml
# AI决策助手配置
decision_assistant:
  enabled: true
  model_path: "models/decision_model.pkl"
  learning_rate: 0.01
  update_interval: 100  # 决策次数
  
  # 策略配置
  strategies:
    conservative:
      risk_tolerance: 0.2
      exploration_rate: 0.1
    
    balanced:
      risk_tolerance: 0.5
      exploration_rate: 0.3
    
    aggressive:
      risk_tolerance: 0.8
      exploration_rate: 0.5

# 自动化引擎配置
automation_engine:
  enabled: true
  max_concurrent_workflows: 5
  workflow_timeout: 300.0  # 秒
  
  # 工作流配置
  workflow_defaults:
    retry_count: 3
    retry_delay: 1.0
    timeout: 60.0
    
  # 调度配置
  scheduler:
    max_queue_size: 100
    priority_levels: 5
    execution_threads: 3
```

## 环境变量配置

创建 `.env` 文件：

```env
# 基础配置
DEBUG=false
LOG_LEVEL=INFO
CONFIG_PATH=config/ui_config.yaml

# 游戏配置
GAME_WINDOW_TITLE=学园偶像大师
GAME_PROCESS_NAME=gakumasu.exe
GAME_RESOLUTION=1920x1080

# 路径配置
TEMPLATES_PATH=templates/
LOGS_PATH=logs/
TEMP_PATH=temp/
MODELS_PATH=models/

# 性能配置
MAX_MEMORY_MB=1024
TARGET_FPS=60
CACHE_SIZE_MB=100

# 功能开关
ENABLE_AI_ASSISTANT=true
ENABLE_AUTOMATION=true
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_ERROR_RECOVERY=true
```

## 配置验证

### 验证配置文件

```python
from src.modules.ui.config.config_validator import ConfigValidator

# 验证主配置
validator = ConfigValidator()
is_valid, errors = validator.validate_config("config/ui_config.yaml")

if not is_valid:
    print("配置验证失败:")
    for error in errors:
        print(f"  - {error}")
else:
    print("配置验证通过")
```

### 配置测试工具

```bash
# 测试配置文件语法
python -m src.modules.ui.tools.config_tester config/ui_config.yaml

# 验证所有配置文件
python -m src.modules.ui.tools.config_validator --all

# 生成配置报告
python -m src.modules.ui.tools.config_reporter --output config_report.html
```

## 配置最佳实践

### 1. 分环境配置

```yaml
# config/environments/development.yaml
system:
  debug_mode: true
  log_level: "DEBUG"
  screenshot_on_error: true

ui_elements:
  default_timeout: 10.0  # 开发环境使用更长超时
  retry_count: 5

# config/environments/production.yaml
system:
  debug_mode: false
  log_level: "INFO"
  screenshot_on_error: false

ui_elements:
  default_timeout: 5.0
  retry_count: 3
```

### 2. 配置继承

```yaml
# config/base.yaml
ui_elements: &ui_defaults
  confidence_threshold: 0.8
  timeout: 5.0
  retry_count: 3

# config/custom.yaml
ui_elements:
  <<: *ui_defaults
  confidence_threshold: 0.9  # 覆盖默认值
```

### 3. 动态配置

```python
from src.modules.ui.config.dynamic_config import DynamicConfig

# 创建动态配置管理器
config = DynamicConfig("config/ui_config.yaml")

# 运行时修改配置
config.set("ui_elements.confidence_threshold", 0.9)
config.set("performance.target_fps", 30)

# 保存配置更改
config.save()
```

### 4. 配置备份和恢复

```bash
# 备份当前配置
python -m src.modules.ui.tools.config_backup --backup

# 恢复配置
python -m src.modules.ui.tools.config_backup --restore backup_20241201_120000

# 重置为默认配置
python -m src.modules.ui.tools.config_backup --reset
```

## 故障排除

### 常见配置问题

1. **配置文件语法错误**
   - 检查YAML语法
   - 验证缩进和格式
   - 使用配置验证工具

2. **路径配置错误**
   - 确保路径存在
   - 使用绝对路径或正确的相对路径
   - 检查文件权限

3. **参数值超出范围**
   - 检查数值参数的有效范围
   - 验证枚举值是否正确
   - 使用配置验证器检查

4. **性能配置不当**
   - 根据硬件配置调整参数
   - 监控系统资源使用
   - 使用性能分析工具

### 配置调试

```bash
# 启用配置调试模式
python main.py --config-debug

# 显示当前配置
python -m src.modules.ui.tools.config_viewer

# 比较配置差异
python -m src.modules.ui.tools.config_diff config1.yaml config2.yaml
```

## 配置迁移

### 版本升级配置迁移

```bash
# 迁移配置到新版本
python -m src.modules.ui.tools.config_migrator \
  --from-version 1.0 \
  --to-version 2.0 \
  --config config/ui_config.yaml
```

### 配置格式转换

```bash
# JSON转YAML
python -m src.modules.ui.tools.config_converter \
  --input config.json \
  --output config.yaml \
  --format yaml

# YAML转JSON
python -m src.modules.ui.tools.config_converter \
  --input config.yaml \
  --output config.json \
  --format json
```

通过合理配置这些参数，您可以根据具体需求和硬件环境优化系统性能，获得最佳的使用体验。
