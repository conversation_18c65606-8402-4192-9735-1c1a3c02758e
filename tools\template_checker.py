#!/usr/bin/env python3
"""
Gakumasu-Bot 模板文件检查工具
检查模板文件的存在状态，并提供创建指导
"""

import os
import sys
from pathlib import Path
from typing import Dict, List, Tuple

def get_template_definitions() -> Dict[str, Dict[str, any]]:
    """获取模板文件定义"""
    return {
        # 主菜单场景
        "main_menu_full.png": {
            "scene": "主菜单",
            "type": "必需",
            "priority": "高",
            "description": "完整主菜单截图，主要识别模板",
            "size_hint": "1920x1080 (完整截图)"
        },
        "main_menu_logo.png": {
            "scene": "主菜单", 
            "type": "必需",
            "priority": "中",
            "description": "主菜单标识图标，备用识别模板",
            "size_hint": "200x100"
        },
        "produce_button.png": {
            "scene": "主菜单",
            "type": "可选",
            "priority": "高",
            "description": "育成按钮，用于导航到育成界面",
            "size_hint": "200x80"
        },
        
        # 育成准备界面
        "produce_setup_title.png": {
            "scene": "育成准备",
            "type": "必需",
            "priority": "高",
            "description": "育成准备界面标题",
            "size_hint": "400x100"
        },
        "idol_selection.png": {
            "scene": "育成准备",
            "type": "必需", 
            "priority": "中",
            "description": "偶像选择区域",
            "size_hint": "600x400"
        },
        "start_produce_button.png": {
            "scene": "育成准备",
            "type": "可选",
            "priority": "高",
            "description": "开始育成按钮",
            "size_hint": "200x80"
        },
        
        # 育成主界面
        "week_indicator.png": {
            "scene": "育成主界面",
            "type": "必需",
            "priority": "中",
            "description": "周数指示器",
            "size_hint": "150x50"
        },
        "stamina_bar.png": {
            "scene": "育成主界面",
            "type": "必需",
            "priority": "中", 
            "description": "体力条显示",
            "size_hint": "200x30"
        },
        
        # 课程按钮
        "vocal_lesson_button.png": {
            "scene": "育成主界面",
            "type": "可选",
            "priority": "高",
            "description": "声乐课程按钮",
            "size_hint": "150x100"
        },
        "dance_lesson_button.png": {
            "scene": "育成主界面", 
            "type": "可选",
            "priority": "高",
            "description": "舞蹈课程按钮",
            "size_hint": "150x100"
        },
        "visual_lesson_button.png": {
            "scene": "育成主界面",
            "type": "可选", 
            "priority": "高",
            "description": "视觉课程按钮",
            "size_hint": "150x100"
        },
        "mental_lesson_button.png": {
            "scene": "育成主界面",
            "type": "可选",
            "priority": "高", 
            "description": "精神课程按钮",
            "size_hint": "150x100"
        },
        "rest_button.png": {
            "scene": "育成主界面",
            "type": "可选",
            "priority": "高",
            "description": "休息按钮", 
            "size_hint": "150x100"
        },
        
        # 战斗界面
        "battle_ui.png": {
            "scene": "育成战斗",
            "type": "必需",
            "priority": "中",
            "description": "战斗界面UI",
            "size_hint": "800x600"
        },
        "card_hand.png": {
            "scene": "育成战斗",
            "type": "必需",
            "priority": "中",
            "description": "手牌区域",
            "size_hint": "600x200"
        },
        
        # 通用按钮
        "confirm_button.png": {
            "scene": "通用",
            "type": "可选",
            "priority": "高",
            "description": "确认按钮",
            "size_hint": "120x50"
        },
        "cancel_button.png": {
            "scene": "通用",
            "type": "可选", 
            "priority": "高",
            "description": "取消按钮",
            "size_hint": "120x50"
        },
        "back_button.png": {
            "scene": "通用",
            "type": "可选",
            "priority": "中",
            "description": "返回按钮",
            "size_hint": "80x40"
        },
        
        # DMM Player
        "dmm_gakumasu_icon.png": {
            "scene": "DMM Player",
            "type": "可选",
            "priority": "低",
            "description": "DMM Player中的游戏图标",
            "size_hint": "64x64"
        }
    }

def check_template_files() -> Tuple[List[str], List[str], Dict[str, List[str]]]:
    """检查模板文件状态"""
    template_dir = Path("assets/templates")
    template_definitions = get_template_definitions()
    
    existing_files = []
    missing_files = []
    categorized_missing = {
        "高": [],
        "中": [],
        "低": []
    }
    
    # 确保模板目录存在
    template_dir.mkdir(parents=True, exist_ok=True)
    
    for template_file, info in template_definitions.items():
        file_path = template_dir / template_file
        if file_path.exists():
            existing_files.append(template_file)
        else:
            missing_files.append(template_file)
            categorized_missing[info["priority"]].append(template_file)
    
    return existing_files, missing_files, categorized_missing

def print_status_report():
    """打印状态报告"""
    print("🔍 Gakumasu-Bot 模板文件状态检查")
    print("=" * 60)
    
    existing_files, missing_files, categorized_missing = check_template_files()
    template_definitions = get_template_definitions()
    
    # 统计信息
    total_templates = len(template_definitions)
    existing_count = len(existing_files)
    missing_count = len(missing_files)
    completion_rate = (existing_count / total_templates) * 100
    
    print(f"\n📊 总体状态:")
    print(f"  总模板数: {total_templates}")
    print(f"  已存在: {existing_count}")
    print(f"  缺失: {missing_count}")
    print(f"  完成度: {completion_rate:.1f}%")
    
    # 已存在的文件
    if existing_files:
        print(f"\n✅ 已存在的模板文件 ({len(existing_files)}个):")
        for file in sorted(existing_files):
            info = template_definitions[file]
            print(f"  • {file:<30} [{info['scene']}] {info['description']}")
    
    # 缺失的文件（按优先级分类）
    if missing_files:
        print(f"\n📋 缺失的模板文件 ({len(missing_files)}个):")
        
        for priority in ["高", "中", "低"]:
            if categorized_missing[priority]:
                print(f"\n  🔴 {priority}优先级 ({len(categorized_missing[priority])}个):")
                for file in categorized_missing[priority]:
                    info = template_definitions[file]
                    print(f"    • {file:<30} [{info['scene']}]")
                    print(f"      {info['description']}")
                    print(f"      建议尺寸: {info['size_hint']}")
                    print()

def generate_creation_guide():
    """生成模板创建指南"""
    print("\n📋 模板文件创建指南")
    print("=" * 60)
    
    _, missing_files, categorized_missing = check_template_files()
    template_definitions = get_template_definitions()
    
    if not missing_files:
        print("🎉 所有模板文件都已存在！")
        return
    
    print("\n🎯 推荐创建顺序:")
    
    step = 1
    for priority in ["高", "中", "低"]:
        if categorized_missing[priority]:
            print(f"\n第{step}阶段 - {priority}优先级模板:")
            for file in categorized_missing[priority]:
                info = template_definitions[file]
                print(f"  {step}. {file}")
                print(f"     场景: {info['scene']}")
                print(f"     用途: {info['description']}")
                print(f"     建议尺寸: {info['size_hint']}")
                print(f"     文件路径: assets/templates/{file}")
                print()
                step += 1

def print_creation_instructions():
    """打印创建说明"""
    print("\n🛠️ 模板文件制作说明")
    print("=" * 60)
    
    instructions = [
        "1. 启动《学园偶像大师》游戏",
        "2. 进入对应的游戏界面",
        "3. 使用截图工具（如Snipping Tool）截取UI元素",
        "4. 保存为PNG格式到 assets/templates/ 目录",
        "5. 确保文件名与上述列表完全一致",
        "",
        "📸 截图技巧:",
        "  • 确保游戏界面清晰，无模糊",
        "  • 避免截取动态效果（如闪烁、动画）",
        "  • 尽量在标准光照条件下截图",
        "  • 按钮类元素要包含完整边框",
        "  • 文字类元素要确保文字清晰可读",
        "",
        "🔧 文件要求:",
        "  • 格式: PNG（推荐）或JPG",
        "  • 分辨率: 与游戏实际分辨率一致",
        "  • 文件大小: 通常小于1MB",
        "  • 命名: 严格按照列表中的文件名"
    ]
    
    for instruction in instructions:
        print(f"  {instruction}")

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == "--guide":
        generate_creation_guide()
        print_creation_instructions()
    else:
        print_status_report()
        
        _, missing_files, _ = check_template_files()
        if missing_files:
            print(f"\n💡 提示: 运行 'python tools/template_checker.py --guide' 查看详细创建指南")
        
        print(f"\n📖 详细信息请参考: docs/模板文件路径对照表.md")

if __name__ == "__main__":
    main()
