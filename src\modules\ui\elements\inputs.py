"""
输入框类实现
使用传统Class处理复杂输入逻辑
"""

import time
import re
from typing import Optional, List

from ....utils.logger import get_logger
from ..base.base_ui_element import BaseUIElement
from ..config.ui_element_config import InputFieldConfig


class InputField(BaseUIElement):
    """输入框类 - 使用传统Class处理复杂输入逻辑"""

    def __init__(self, config: InputFieldConfig, perception_module, action_controller):
        """
        初始化输入框
        
        Args:
            config: 输入框配置
            perception_module: 感知模块
            action_controller: 行动控制器
        """
        super().__init__(config, perception_module, action_controller)
        self.input_config = config  # 类型提示用的强类型引用
        
        # 输入框特定状态
        self._last_input_text = ""
        self._input_history: List[str] = []
        self._is_focused = False

    def input_text(self, text: str) -> bool:
        """
        在输入框中输入文本
        
        Args:
            text: 要输入的文本
            
        Returns:
            是否输入成功
        """
        start_time = time.time()
        
        try:
            self.logger.info(f"在输入框 {self.config.template_name} 中输入文本")
            
            # 预输入验证
            if not self._pre_input_validation(text):
                return False

            # 获得焦点
            if not self._ensure_focus():
                return False

            # 清空现有内容
            if self.input_config.clear_before_input:
                if not self._clear_content():
                    return False

            # 执行输入
            success = self._execute_input(text)

            # 后处理
            if success:
                self._post_input_processing(text, start_time)

            return success

        except Exception as e:
            self.logger.error(f"输入文本失败: {e}")
            self._record_performance('input_text', start_time, False, str(e))
            return False

    def _pre_input_validation(self, text: str) -> bool:
        """预输入验证"""
        if not self._pre_action_validation():
            return False

        # 检查文本长度
        if (self.input_config.max_length and 
            len(text) > self.input_config.max_length):
            self.logger.error(f"输入文本长度超过限制: {len(text)} > {self.input_config.max_length}")
            return False

        # 检查文本内容（可以添加更多验证规则）
        if not text.strip():
            self.logger.warning("输入文本为空")
            return False

        return True

    def _ensure_focus(self) -> bool:
        """确保输入框获得焦点"""
        try:
            # 点击输入框获得焦点
            if not self.click():
                self.logger.error("无法点击输入框获得焦点")
                return False

            # 等待焦点生效
            time.sleep(0.1)
            self._is_focused = True
            return True

        except Exception as e:
            self.logger.error(f"获得焦点失败: {e}")
            return False

    def click(self) -> bool:
        """点击输入框"""
        try:
            success = self.action.click_ui_element(
                self.config.template_name,
                confidence_threshold=self.config.confidence_threshold,
                timeout=self.config.timeout
            )
            
            if success:
                self._is_focused = True
                self.logger.debug(f"成功点击输入框: {self.config.template_name}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"点击输入框失败: {e}")
            return False

    def _clear_content(self) -> bool:
        """清空输入框内容"""
        try:
            self.logger.debug("清空输入框内容")
            
            # 全选 (Ctrl+A)
            if hasattr(self.action.input_simulator, 'key_combination'):
                self.action.input_simulator.key_combination(['ctrl', 'a'])
            elif hasattr(self.action.input_simulator, 'key_press'):
                self.action.input_simulator.key_press(['ctrl', 'a'])
            else:
                self.logger.warning("无法执行全选操作")
                return False
            
            time.sleep(0.05)
            
            # 删除 (Delete 或 Backspace)
            if hasattr(self.action.input_simulator, 'key_press'):
                self.action.input_simulator.key_press('delete')
            else:
                self.logger.warning("无法执行删除操作")
                return False
            
            time.sleep(0.05)
            return True

        except Exception as e:
            self.logger.error(f"清空内容失败: {e}")
            return False

    def _execute_input(self, text: str) -> bool:
        """执行文本输入"""
        try:
            if self.input_config.input_method == "direct":
                return self._input_direct(text)
            elif self.input_config.input_method == "clipboard":
                return self._input_via_clipboard(text)
            elif self.input_config.input_method == "ime":
                return self._input_via_ime(text)
            else:
                self.logger.error(f"不支持的输入方法: {self.input_config.input_method}")
                return False

        except Exception as e:
            self.logger.error(f"执行输入失败: {e}")
            return False

    def _input_direct(self, text: str) -> bool:
        """直接输入文本"""
        try:
            if hasattr(self.action.input_simulator, 'type_text'):
                # 逐字符输入，支持延迟
                if self.input_config.input_delay > 0:
                    for char in text:
                        self.action.input_simulator.type_text(char)
                        time.sleep(self.input_config.input_delay)
                else:
                    self.action.input_simulator.type_text(text)
                return True
            else:
                self.logger.error("输入模拟器不支持文本输入")
                return False

        except Exception as e:
            self.logger.error(f"直接输入失败: {e}")
            return False

    def _input_via_clipboard(self, text: str) -> bool:
        """通过剪贴板输入文本"""
        try:
            import pyperclip
            
            # 保存当前剪贴板内容
            original_clipboard = pyperclip.paste()
            
            # 设置新内容到剪贴板
            pyperclip.copy(text)
            
            # 粘贴 (Ctrl+V)
            if hasattr(self.action.input_simulator, 'key_combination'):
                self.action.input_simulator.key_combination(['ctrl', 'v'])
            else:
                self.logger.error("无法执行粘贴操作")
                return False
            
            time.sleep(0.1)
            
            # 恢复原剪贴板内容
            pyperclip.copy(original_clipboard)
            
            return True

        except ImportError:
            self.logger.error("pyperclip模块未安装，无法使用剪贴板输入")
            return False
        except Exception as e:
            self.logger.error(f"剪贴板输入失败: {e}")
            return False

    def _input_via_ime(self, text: str) -> bool:
        """通过IME输入文本（主要用于日语等）"""
        try:
            # 这里需要根据具体的IME实现
            # 暂时使用直接输入作为fallback
            self.logger.warning("IME输入方法尚未实现，使用直接输入")
            return self._input_direct(text)

        except Exception as e:
            self.logger.error(f"IME输入失败: {e}")
            return False

    def _post_input_processing(self, text: str, start_time: float):
        """输入后处理"""
        self._last_input_text = text
        self._input_history.append(text)
        
        # 保持历史记录在合理范围内
        if len(self._input_history) > 50:
            self._input_history = self._input_history[-50:]
        
        # 记录性能
        self._record_performance('input_text', start_time, True)
        
        # 执行后置处理
        self._post_action_processing(True)
        
        # 验证输入结果
        if self.input_config.verify_input_result:
            self._verify_input_result(text)

    def _verify_input_result(self, expected_text: str) -> bool:
        """验证输入结果"""
        if not self.input_config.read_back_text:
            return True
        
        try:
            # 读取输入框当前文本（需要OCR支持）
            actual_text = self._read_current_text()
            if actual_text is None:
                self.logger.warning("无法读取输入框文本进行验证")
                return False
            
            # 比较文本
            if actual_text.strip() == expected_text.strip():
                self.logger.info("输入验证成功")
                return True
            else:
                self.logger.warning(f"输入验证失败: 期望 '{expected_text}', 实际 '{actual_text}'")
                return False

        except Exception as e:
            self.logger.error(f"输入验证失败: {e}")
            return False

    def _read_current_text(self) -> Optional[str]:
        """读取输入框当前文本"""
        try:
            # 这需要OCR功能支持
            if hasattr(self.perception, 'read_text_from_region'):
                position = self.get_position()
                if position and self.config.size:
                    # 定义文本区域
                    region = {
                        'x': position.x - self.config.size.width // 2,
                        'y': position.y - self.config.size.height // 2,
                        'width': self.config.size.width,
                        'height': self.config.size.height
                    }
                    return self.perception.read_text_from_region(region)
            
            self.logger.warning("感知模块不支持文本读取")
            return None

        except Exception as e:
            self.logger.error(f"读取文本失败: {e}")
            return None

    def clear(self) -> bool:
        """清空输入框"""
        try:
            if not self._ensure_focus():
                return False
            
            return self._clear_content()

        except Exception as e:
            self.logger.error(f"清空输入框失败: {e}")
            return False

    def get_last_input(self) -> str:
        """获取最后输入的文本"""
        return self._last_input_text

    def get_input_history(self) -> List[str]:
        """获取输入历史"""
        return self._input_history.copy()

    def is_focused(self) -> bool:
        """检查是否有焦点"""
        return self._is_focused

    def lose_focus(self) -> bool:
        """失去焦点"""
        try:
            # 按Tab键或点击其他地方
            if hasattr(self.action.input_simulator, 'key_press'):
                self.action.input_simulator.key_press('tab')
                self._is_focused = False
                return True
            return False

        except Exception as e:
            self.logger.error(f"失去焦点失败: {e}")
            return False


class EnhancedInputField(InputField):
    """增强输入框类 - 提供更多高级功能"""

    def __init__(self, config: InputFieldConfig, perception_module, action_controller):
        super().__init__(config, perception_module, action_controller)
        
        # 增强功能
        self._auto_complete_enabled = False
        self._input_validation_patterns: List[str] = []

    def input_with_validation(self, text: str, pattern: str = None) -> bool:
        """
        带验证的输入
        
        Args:
            text: 输入文本
            pattern: 验证正则表达式
            
        Returns:
            是否输入成功且验证通过
        """
        if pattern and not re.match(pattern, text):
            self.logger.error(f"输入文本不符合验证模式: {pattern}")
            return False
        
        return self.input_text(text)

    def append_text(self, text: str) -> bool:
        """
        追加文本（不清空现有内容）
        
        Args:
            text: 要追加的文本
            
        Returns:
            是否成功
        """
        try:
            # 临时禁用清空功能
            original_clear_setting = self.input_config.clear_before_input
            self.input_config.clear_before_input = False
            
            # 移动到文本末尾
            if not self._ensure_focus():
                return False
            
            # Ctrl+End 移动到末尾
            if hasattr(self.action.input_simulator, 'key_combination'):
                self.action.input_simulator.key_combination(['ctrl', 'end'])
            
            # 输入文本
            result = self._execute_input(text)
            
            # 恢复设置
            self.input_config.clear_before_input = original_clear_setting
            
            return result

        except Exception as e:
            self.logger.error(f"追加文本失败: {e}")
            return False
