"""
性能监控工具
用于监控UI系统的性能指标和资源使用情况
"""

import time
import threading
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from collections import defaultdict, deque
import psutil
import gc

from ....utils.logger import get_logger


@dataclass
class PerformanceMetric:
    """性能指标数据类"""
    name: str
    start_time: float
    end_time: Optional[float] = None
    duration: Optional[float] = None
    memory_before: Optional[float] = None
    memory_after: Optional[float] = None
    memory_delta: Optional[float] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def finish(self):
        """完成性能测量"""
        self.end_time = time.time()
        self.duration = self.end_time - self.start_time
        
        if self.memory_before is not None:
            self.memory_after = self._get_memory_usage()
            self.memory_delta = self.memory_after - self.memory_before
    
    def _get_memory_usage(self) -> float:
        """获取当前内存使用量（MB）"""
        try:
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except Exception:
            return 0.0


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, max_history: int = 1000):
        """
        初始化性能监控器
        
        Args:
            max_history: 最大历史记录数量
        """
        self.max_history = max_history
        self.logger = get_logger("PerformanceMonitor")
        
        # 性能指标存储
        self._metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_history))
        self._active_metrics: Dict[str, PerformanceMetric] = {}
        
        # 统计数据
        self._stats: Dict[str, Dict[str, Any]] = defaultdict(dict)
        
        # 线程锁
        self._lock = threading.RLock()
        
        # 监控状态
        self._monitoring_enabled = True
        self._auto_gc_enabled = True
        
        self.logger.info("性能监控器初始化完成")
    
    def start_measurement(self, name: str, metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        开始性能测量
        
        Args:
            name: 测量名称
            metadata: 附加元数据
            
        Returns:
            测量ID
        """
        if not self._monitoring_enabled:
            return ""
        
        try:
            with self._lock:
                measurement_id = f"{name}_{int(time.time() * 1000000)}"
                
                metric = PerformanceMetric(
                    name=name,
                    start_time=time.time(),
                    memory_before=self._get_memory_usage(),
                    metadata=metadata or {}
                )
                
                self._active_metrics[measurement_id] = metric
                
                self.logger.debug(f"开始性能测量: {name} (ID: {measurement_id})")
                return measurement_id
                
        except Exception as e:
            self.logger.error(f"开始性能测量失败: {e}")
            return ""
    
    def end_measurement(self, measurement_id: str) -> Optional[PerformanceMetric]:
        """
        结束性能测量
        
        Args:
            measurement_id: 测量ID
            
        Returns:
            性能指标对象
        """
        if not self._monitoring_enabled or not measurement_id:
            return None
        
        try:
            with self._lock:
                if measurement_id not in self._active_metrics:
                    self.logger.warning(f"未找到活跃的测量: {measurement_id}")
                    return None
                
                metric = self._active_metrics.pop(measurement_id)
                metric.finish()
                
                # 存储到历史记录
                self._metrics[metric.name].append(metric)
                
                # 更新统计数据
                self._update_stats(metric)
                
                self.logger.debug(f"结束性能测量: {metric.name}, 耗时: {metric.duration:.3f}s")
                
                # 自动垃圾回收
                if self._auto_gc_enabled and len(self._active_metrics) == 0:
                    gc.collect()
                
                return metric
                
        except Exception as e:
            self.logger.error(f"结束性能测量失败: {e}")
            return None
    
    def measure_function(self, name: Optional[str] = None, metadata: Optional[Dict[str, Any]] = None):
        """
        函数性能测量装饰器
        
        Args:
            name: 测量名称，默认使用函数名
            metadata: 附加元数据
        """
        def decorator(func: Callable):
            def wrapper(*args, **kwargs):
                measurement_name = name or f"{func.__module__}.{func.__name__}"
                measurement_id = self.start_measurement(measurement_name, metadata)
                
                try:
                    result = func(*args, **kwargs)
                    return result
                finally:
                    self.end_measurement(measurement_id)
            
            return wrapper
        return decorator
    
    def measure_block(self, name: str, metadata: Optional[Dict[str, Any]] = None):
        """
        代码块性能测量上下文管理器
        
        Args:
            name: 测量名称
            metadata: 附加元数据
        """
        class MeasurementContext:
            def __init__(self, monitor: 'PerformanceMonitor', name: str, metadata: Optional[Dict[str, Any]]):
                self.monitor = monitor
                self.name = name
                self.metadata = metadata
                self.measurement_id = ""
            
            def __enter__(self):
                self.measurement_id = self.monitor.start_measurement(self.name, self.metadata)
                return self
            
            def __exit__(self, exc_type, exc_val, exc_tb):
                self.monitor.end_measurement(self.measurement_id)
        
        return MeasurementContext(self, name, metadata)
    
    def _update_stats(self, metric: PerformanceMetric):
        """更新统计数据"""
        try:
            name = metric.name
            stats = self._stats[name]
            
            # 初始化统计数据
            if 'count' not in stats:
                stats.update({
                    'count': 0,
                    'total_duration': 0.0,
                    'min_duration': float('inf'),
                    'max_duration': 0.0,
                    'avg_duration': 0.0,
                    'total_memory_delta': 0.0,
                    'min_memory_delta': float('inf'),
                    'max_memory_delta': float('-inf'),
                    'avg_memory_delta': 0.0
                })
            
            # 更新计数和总时间
            stats['count'] += 1
            stats['total_duration'] += metric.duration or 0.0
            
            # 更新时间统计
            if metric.duration:
                stats['min_duration'] = min(stats['min_duration'], metric.duration)
                stats['max_duration'] = max(stats['max_duration'], metric.duration)
                stats['avg_duration'] = stats['total_duration'] / stats['count']
            
            # 更新内存统计
            if metric.memory_delta is not None:
                stats['total_memory_delta'] += metric.memory_delta
                stats['min_memory_delta'] = min(stats['min_memory_delta'], metric.memory_delta)
                stats['max_memory_delta'] = max(stats['max_memory_delta'], metric.memory_delta)
                stats['avg_memory_delta'] = stats['total_memory_delta'] / stats['count']
            
        except Exception as e:
            self.logger.error(f"更新统计数据失败: {e}")
    
    def get_stats(self, name: Optional[str] = None) -> Dict[str, Any]:
        """
        获取性能统计数据
        
        Args:
            name: 指定测量名称，None表示获取所有统计
            
        Returns:
            统计数据字典
        """
        try:
            with self._lock:
                if name:
                    return self._stats.get(name, {}).copy()
                else:
                    return {k: v.copy() for k, v in self._stats.items()}
        except Exception as e:
            self.logger.error(f"获取统计数据失败: {e}")
            return {}
    
    def get_recent_metrics(self, name: str, count: int = 10) -> List[PerformanceMetric]:
        """
        获取最近的性能指标
        
        Args:
            name: 测量名称
            count: 获取数量
            
        Returns:
            性能指标列表
        """
        try:
            with self._lock:
                metrics = list(self._metrics.get(name, []))
                return metrics[-count:] if metrics else []
        except Exception as e:
            self.logger.error(f"获取最近指标失败: {e}")
            return []
    
    def clear_stats(self, name: Optional[str] = None):
        """
        清除统计数据
        
        Args:
            name: 指定测量名称，None表示清除所有统计
        """
        try:
            with self._lock:
                if name:
                    if name in self._stats:
                        del self._stats[name]
                    if name in self._metrics:
                        self._metrics[name].clear()
                else:
                    self._stats.clear()
                    self._metrics.clear()
                
                self.logger.info(f"清除统计数据: {name or '全部'}")
                
        except Exception as e:
            self.logger.error(f"清除统计数据失败: {e}")
    
    def _get_memory_usage(self) -> float:
        """获取当前内存使用量（MB）"""
        try:
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except Exception:
            return 0.0
    
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            
            return {
                'memory_usage_mb': memory_info.rss / 1024 / 1024,
                'memory_percent': process.memory_percent(),
                'cpu_percent': process.cpu_percent(),
                'num_threads': process.num_threads(),
                'active_measurements': len(self._active_metrics),
                'total_measurements': sum(len(metrics) for metrics in self._metrics.values()),
                'monitoring_enabled': self._monitoring_enabled
            }
        except Exception as e:
            self.logger.error(f"获取系统信息失败: {e}")
            return {}
    
    def set_monitoring_enabled(self, enabled: bool):
        """设置监控状态"""
        self._monitoring_enabled = enabled
        self.logger.info(f"性能监控{'启用' if enabled else '禁用'}")
    
    def set_auto_gc_enabled(self, enabled: bool):
        """设置自动垃圾回收"""
        self._auto_gc_enabled = enabled
        self.logger.info(f"自动垃圾回收{'启用' if enabled else '禁用'}")


# 全局性能监控器实例
_global_monitor: Optional[PerformanceMonitor] = None


def get_performance_monitor() -> PerformanceMonitor:
    """获取全局性能监控器实例"""
    global _global_monitor
    if _global_monitor is None:
        _global_monitor = PerformanceMonitor()
    return _global_monitor


def measure(name: Optional[str] = None, metadata: Optional[Dict[str, Any]] = None):
    """性能测量装饰器的便捷函数"""
    return get_performance_monitor().measure_function(name, metadata)


def measure_block(name: str, metadata: Optional[Dict[str, Any]] = None):
    """性能测量上下文管理器的便捷函数"""
    return get_performance_monitor().measure_block(name, metadata)
