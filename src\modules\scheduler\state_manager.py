"""
状态管理器
负责游戏状态的持久化、恢复和历史管理
"""

import json
import pickle
import os
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from datetime import datetime
from pathlib import Path
from threading import Lock

from ...utils.logger import get_logger
from ...core.data_structures import GameState, GakumasuBotException


@dataclass
class GameStateSnapshot:
    """游戏状态快照"""
    snapshot_id: str
    timestamp: datetime
    game_state: GameState
    metadata: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "snapshot_id": self.snapshot_id,
            "timestamp": self.timestamp.isoformat(),
            "game_state": self.game_state.to_dict(),
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'GameStateSnapshot':
        """从字典创建快照"""
        return cls(
            snapshot_id=data["snapshot_id"],
            timestamp=datetime.fromisoformat(data["timestamp"]),
            game_state=GameState.from_dict(data["game_state"]),
            metadata=data["metadata"]
        )


class StateManagerError(GakumasuBotException):
    """状态管理器错误"""
    pass


class StateManager:
    """状态管理器类"""
    
    def __init__(self, data_dir: str = "data/states"):
        """
        初始化状态管理器
        
        Args:
            data_dir: 数据存储目录
        """
        self.logger = get_logger("StateManager")
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # 状态存储
        self.current_state: Optional[GameState] = None
        self.state_history: List[GameStateSnapshot] = []
        self.max_history_size = 100
        
        # 文件路径
        self.current_state_file = self.data_dir / "current_state.json"
        self.history_file = self.data_dir / "state_history.json"
        self.backup_dir = self.data_dir / "backups"
        self.backup_dir.mkdir(exist_ok=True)
        
        # 线程安全
        self._lock = Lock()
        
        # 统计信息
        self.total_saves = 0
        self.total_loads = 0
        self.total_snapshots = 0
        
        # 加载现有状态
        self._load_current_state()
        self._load_state_history()
        
        self.logger.info(f"状态管理器初始化完成，数据目录: {self.data_dir}")
    
    def save_current_state(self, game_state: GameState, 
                          metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        保存当前游戏状态
        
        Args:
            game_state: 游戏状态
            metadata: 额外元数据
            
        Returns:
            是否保存成功
        """
        try:
            with self._lock:
                self.current_state = game_state
                
                # 准备保存数据
                save_data = {
                    "timestamp": datetime.now().isoformat(),
                    "game_state": game_state.to_dict(),
                    "metadata": metadata or {}
                }
                
                # 保存到文件
                with open(self.current_state_file, 'w', encoding='utf-8') as f:
                    json.dump(save_data, f, indent=2, ensure_ascii=False)
                
                self.total_saves += 1
                self.logger.debug("当前游戏状态已保存")
                return True
                
        except Exception as e:
            self.logger.error(f"保存当前状态失败: {e}")
            return False
    
    def load_current_state(self) -> Optional[GameState]:
        """
        加载当前游戏状态
        
        Returns:
            游戏状态，如果不存在则返回None
        """
        try:
            with self._lock:
                if self.current_state:
                    return self.current_state
                
                return self._load_current_state()
                
        except Exception as e:
            self.logger.error(f"加载当前状态失败: {e}")
            return None
    
    def create_snapshot(self, game_state: GameState, 
                       snapshot_id: Optional[str] = None,
                       metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        创建游戏状态快照
        
        Args:
            game_state: 游戏状态
            snapshot_id: 快照ID，如果不提供则自动生成
            metadata: 额外元数据
            
        Returns:
            快照ID
        """
        try:
            with self._lock:
                if not snapshot_id:
                    snapshot_id = f"snapshot_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                
                snapshot = GameStateSnapshot(
                    snapshot_id=snapshot_id,
                    timestamp=datetime.now(),
                    game_state=game_state,
                    metadata=metadata or {}
                )
                
                self.state_history.append(snapshot)
                self.total_snapshots += 1
                
                # 限制历史记录大小
                if len(self.state_history) > self.max_history_size:
                    self.state_history.pop(0)
                
                # 保存历史记录
                self._save_state_history()
                
                self.logger.info(f"创建状态快照: {snapshot_id}")
                return snapshot_id
                
        except Exception as e:
            self.logger.error(f"创建快照失败: {e}")
            raise StateManagerError(f"创建快照失败: {e}")
    
    def restore_snapshot(self, snapshot_id: str) -> Optional[GameState]:
        """
        恢复指定快照的游戏状态
        
        Args:
            snapshot_id: 快照ID
            
        Returns:
            恢复的游戏状态
        """
        try:
            with self._lock:
                for snapshot in self.state_history:
                    if snapshot.snapshot_id == snapshot_id:
                        self.current_state = snapshot.game_state
                        self.logger.info(f"恢复状态快照: {snapshot_id}")
                        return snapshot.game_state
                
                self.logger.warning(f"未找到快照: {snapshot_id}")
                return None
                
        except Exception as e:
            self.logger.error(f"恢复快照失败: {e}")
            return None
    
    def get_snapshot(self, snapshot_id: str) -> Optional[GameStateSnapshot]:
        """获取指定快照"""
        for snapshot in self.state_history:
            if snapshot.snapshot_id == snapshot_id:
                return snapshot
        return None
    
    def list_snapshots(self) -> List[GameStateSnapshot]:
        """获取所有快照列表"""
        return self.state_history.copy()
    
    def delete_snapshot(self, snapshot_id: str) -> bool:
        """删除指定快照"""
        try:
            with self._lock:
                for i, snapshot in enumerate(self.state_history):
                    if snapshot.snapshot_id == snapshot_id:
                        del self.state_history[i]
                        self._save_state_history()
                        self.logger.info(f"删除快照: {snapshot_id}")
                        return True
                
                return False
                
        except Exception as e:
            self.logger.error(f"删除快照失败: {e}")
            return False
    
    def create_backup(self, backup_name: Optional[str] = None) -> str:
        """
        创建完整备份
        
        Args:
            backup_name: 备份名称
            
        Returns:
            备份文件路径
        """
        try:
            if not backup_name:
                backup_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            backup_file = self.backup_dir / f"{backup_name}.json"
            
            backup_data = {
                "backup_name": backup_name,
                "created_at": datetime.now().isoformat(),
                "current_state": self.current_state.to_dict() if self.current_state else None,
                "state_history": [snapshot.to_dict() for snapshot in self.state_history],
                "statistics": self.get_manager_statistics()
            }
            
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"创建备份: {backup_file}")
            return str(backup_file)
            
        except Exception as e:
            self.logger.error(f"创建备份失败: {e}")
            raise StateManagerError(f"创建备份失败: {e}")
    
    def restore_backup(self, backup_file: str) -> bool:
        """
        恢复备份
        
        Args:
            backup_file: 备份文件路径
            
        Returns:
            是否恢复成功
        """
        try:
            with self._lock:
                with open(backup_file, 'r', encoding='utf-8') as f:
                    backup_data = json.load(f)
                
                # 恢复当前状态
                if backup_data.get("current_state"):
                    self.current_state = GameState.from_dict(backup_data["current_state"])
                
                # 恢复历史记录
                self.state_history = []
                for snapshot_data in backup_data.get("state_history", []):
                    snapshot = GameStateSnapshot.from_dict(snapshot_data)
                    self.state_history.append(snapshot)
                
                # 保存恢复的数据
                if self.current_state:
                    self.save_current_state(self.current_state)
                self._save_state_history()
                
                self.logger.info(f"恢复备份成功: {backup_file}")
                return True
                
        except Exception as e:
            self.logger.error(f"恢复备份失败: {e}")
            return False
    
    def cleanup_old_snapshots(self, keep_count: int = 50):
        """清理旧快照"""
        try:
            with self._lock:
                if len(self.state_history) > keep_count:
                    # 按时间排序，保留最新的
                    self.state_history.sort(key=lambda s: s.timestamp, reverse=True)
                    removed_count = len(self.state_history) - keep_count
                    self.state_history = self.state_history[:keep_count]
                    
                    self._save_state_history()
                    self.logger.info(f"清理了 {removed_count} 个旧快照")
                    
        except Exception as e:
            self.logger.error(f"清理快照失败: {e}")
    
    def _load_current_state(self) -> Optional[GameState]:
        """加载当前状态文件"""
        try:
            if not self.current_state_file.exists():
                return None
            
            with open(self.current_state_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.current_state = GameState.from_dict(data["game_state"])
            self.total_loads += 1
            
            self.logger.debug("加载当前状态成功")
            return self.current_state
            
        except Exception as e:
            self.logger.warning(f"加载当前状态失败: {e}")
            return None
    
    def _load_state_history(self):
        """加载状态历史"""
        try:
            if not self.history_file.exists():
                return
            
            with open(self.history_file, 'r', encoding='utf-8') as f:
                history_data = json.load(f)
            
            self.state_history = []
            for snapshot_data in history_data:
                snapshot = GameStateSnapshot.from_dict(snapshot_data)
                self.state_history.append(snapshot)
            
            self.logger.debug(f"加载状态历史成功，共 {len(self.state_history)} 个快照")
            
        except Exception as e:
            self.logger.warning(f"加载状态历史失败: {e}")
    
    def _save_state_history(self):
        """保存状态历史"""
        try:
            history_data = [snapshot.to_dict() for snapshot in self.state_history]
            
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(history_data, f, indent=2, ensure_ascii=False)
            
        except Exception as e:
            self.logger.error(f"保存状态历史失败: {e}")
    
    def get_manager_statistics(self) -> Dict[str, Any]:
        """获取管理器统计信息"""
        return {
            "total_saves": self.total_saves,
            "total_loads": self.total_loads,
            "total_snapshots": self.total_snapshots,
            "current_snapshots": len(self.state_history),
            "has_current_state": self.current_state is not None,
            "data_directory": str(self.data_dir)
        }
    
    def get_manager_info(self) -> Dict[str, Any]:
        """获取管理器信息"""
        return {
            "data_directory": str(self.data_dir),
            "current_state_exists": self.current_state is not None,
            "snapshot_count": len(self.state_history),
            "max_history_size": self.max_history_size,
            "statistics": self.get_manager_statistics()
        }
