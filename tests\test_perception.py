"""
测试感知模块
"""

import pytest
import numpy as np
import cv2
from unittest.mock import Mock, patch, MagicMock
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.modules.perception import (
    PerceptionModule, ScreenCapture, SceneRecognizer, 
    TemplateMatcher, MatchResult
)
from src.core.data_structures import GameScene, GameState


class TestMatchResult:
    """测试MatchResult类"""
    
    def test_match_result_creation(self):
        """测试MatchResult创建"""
        result = MatchResult(100, 200, 50, 30, 0.95, "test_template")
        
        assert result.x == 100
        assert result.y == 200
        assert result.width == 50
        assert result.height == 30
        assert result.confidence == 0.95
        assert result.template_name == "test_template"
        assert result.center_x == 125  # 100 + 50//2
        assert result.center_y == 215  # 200 + 30//2
    
    def test_match_result_repr(self):
        """测试MatchResult字符串表示"""
        result = MatchResult(10, 20, 30, 40, 0.8, "button")
        repr_str = repr(result)
        
        assert "MatchResult" in repr_str
        assert "button" in repr_str
        assert "0.800" in repr_str


class TestTemplateMatcher:
    """测试TemplateMatcher类"""
    
    def setup_method(self):
        """测试前准备"""
        self.matcher = TemplateMatcher("test_templates")
    
    def test_template_matcher_init(self):
        """测试TemplateMatcher初始化"""
        assert self.matcher.templates_dir == Path("test_templates")
        assert self.matcher.default_confidence_threshold == 0.8
        assert self.matcher.multi_scale_enabled is True
    
    @patch('cv2.imread')
    def test_load_template_success(self, mock_imread):
        """测试成功加载模板"""
        # 模拟cv2.imread返回值
        mock_template = np.zeros((100, 100, 3), dtype=np.uint8)
        mock_imread.return_value = mock_template
        
        # 模拟文件存在
        with patch.object(Path, 'exists', return_value=True):
            template = self.matcher.load_template("test_button")
        
        assert template is not None
        assert np.array_equal(template, mock_template)
        assert "test_button" in self.matcher._template_cache
    
    def test_load_template_not_found(self):
        """测试模板文件不存在"""
        with patch.object(Path, 'exists', return_value=False):
            template = self.matcher.load_template("nonexistent")
        
        assert template is None
    
    @patch('cv2.matchTemplate')
    @patch('cv2.minMaxLoc')
    def test_match_template_success(self, mock_minMaxLoc, mock_matchTemplate):
        """测试成功匹配模板"""
        # 准备测试数据
        test_image = np.zeros((200, 200, 3), dtype=np.uint8)
        test_template = np.zeros((50, 50, 3), dtype=np.uint8)
        
        # 模拟cv2函数返回值
        mock_matchTemplate.return_value = np.array([[0.9]])
        mock_minMaxLoc.return_value = (0.1, 0.9, (10, 20), (100, 150))
        
        # 模拟加载模板
        self.matcher._template_cache["test_template"] = test_template
        
        result = self.matcher.match_template(test_image, "test_template")
        
        assert result is not None
        assert result.x == 100
        assert result.y == 150
        assert result.confidence == 0.9
        assert result.template_name == "test_template"
    
    def test_get_template_info(self):
        """测试获取模板信息"""
        info = self.matcher.get_template_info()
        
        assert "templates_dir" in info
        assert "cached_templates" in info
        assert "default_confidence_threshold" in info
        assert "multi_scale_enabled" in info


class TestSceneRecognizer:
    """测试SceneRecognizer类"""
    
    def setup_method(self):
        """测试前准备"""
        self.recognizer = SceneRecognizer("test_templates")
    
    def test_scene_recognizer_init(self):
        """测试SceneRecognizer初始化"""
        assert self.recognizer.confidence_threshold == 0.7
        assert self.recognizer.min_matches_required == 1
        assert len(self.recognizer.scene_rules) > 0
    
    def test_scene_rules_structure(self):
        """测试场景规则结构"""
        rules = self.recognizer.scene_rules
        
        # 检查是否包含主要场景
        assert GameScene.MAIN_MENU in rules
        assert GameScene.PRODUCE_MAIN in rules
        assert GameScene.PRODUCE_BATTLE in rules
        
        # 检查规则结构
        for scene, rule in rules.items():
            assert "required_templates" in rule
            assert "optional_templates" in rule
            assert "min_required" in rule
            assert "description" in rule
    
    @patch.object(TemplateMatcher, 'match_multiple_templates')
    def test_recognize_scene_success(self, mock_match):
        """测试成功识别场景"""
        # 模拟匹配结果
        mock_matches = [
            MatchResult(10, 20, 30, 40, 0.9, "main_menu_logo"),
            MatchResult(50, 60, 70, 80, 0.85, "produce_button")
        ]
        mock_match.return_value = mock_matches
        
        test_image = np.zeros((200, 200, 3), dtype=np.uint8)
        scene = self.recognizer.recognize_scene(test_image)
        
        # 应该识别为主菜单（因为有main_menu_logo和produce_button）
        assert scene in [GameScene.MAIN_MENU, GameScene.UNKNOWN]  # 取决于具体的评分逻辑
    
    @patch.object(TemplateMatcher, 'match_multiple_templates')
    def test_recognize_scene_unknown(self, mock_match):
        """测试识别未知场景"""
        # 模拟没有匹配结果
        mock_match.return_value = []
        
        test_image = np.zeros((200, 200, 3), dtype=np.uint8)
        scene = self.recognizer.recognize_scene(test_image)
        
        assert scene == GameScene.UNKNOWN
    
    def test_get_recognition_info(self):
        """测试获取识别器信息"""
        info = self.recognizer.get_recognition_info()
        
        assert "supported_scenes" in info
        assert "confidence_threshold" in info
        assert "min_matches_required" in info
        assert "template_matcher_info" in info


class TestScreenCapture:
    """测试ScreenCapture类"""
    
    def setup_method(self):
        """测试前准备"""
        self.capture = ScreenCapture("Test Game")
    
    def test_screen_capture_init(self):
        """测试ScreenCapture初始化"""
        assert self.capture.game_window_title == "Test Game"
        assert self.capture._window_handle is None
        assert self.capture._window_check_interval == 5.0
    
    @patch('win32gui.EnumWindows')
    @patch('win32gui.IsWindowVisible')
    @patch('win32gui.GetWindowText')
    def test_find_game_window_success(self, mock_get_text, mock_is_visible, mock_enum):
        """测试成功找到游戏窗口"""
        # 模拟窗口枚举
        def enum_callback(callback, param):
            callback(12345, param)  # 模拟窗口句柄
            return True
        
        mock_enum.side_effect = enum_callback
        mock_is_visible.return_value = True
        mock_get_text.return_value = "Test Game - Main Window"
        
        handle = self.capture.find_game_window()
        
        assert handle == 12345
        assert self.capture._window_handle == 12345
    
    @patch('win32gui.EnumWindows')
    def test_find_game_window_not_found(self, mock_enum):
        """测试未找到游戏窗口"""
        # 模拟没有窗口
        mock_enum.side_effect = lambda callback, param: True
        
        handle = self.capture.find_game_window()
        
        assert handle is None
        assert self.capture._window_handle is None
    
    def test_get_window_info(self):
        """测试获取窗口信息"""
        info = self.capture.get_window_info()
        
        assert "window_handle" in info
        assert "window_title" in info
        assert "window_rect" in info
        assert "is_active" in info
        assert "last_check_time" in info


class TestPerceptionModule:
    """测试PerceptionModule类"""
    
    def setup_method(self):
        """测试前准备"""
        with patch.object(ScreenCapture, '__init__', return_value=None), \
             patch.object(SceneRecognizer, '__init__', return_value=None), \
             patch.object(TemplateMatcher, '__init__', return_value=None):
            
            self.perception = PerceptionModule()
            
            # 手动设置模拟的子模块
            self.perception.screen_capture = Mock()
            self.perception.scene_recognizer = Mock()
            self.perception.template_matcher = Mock()
    
    def test_perception_module_init(self):
        """测试PerceptionModule初始化"""
        assert self.perception.language == "ja"
        assert self.perception.screenshot_cache_duration == 0.5
        assert self.perception.debug_mode is False
    
    def test_get_current_scene(self):
        """测试获取当前场景"""
        # 模拟截图和场景识别
        mock_image = np.zeros((100, 100, 3), dtype=np.uint8)
        self.perception.screen_capture.capture_screen.return_value = mock_image
        self.perception.scene_recognizer.recognize_scene.return_value = GameScene.MAIN_MENU
        
        scene = self.perception.get_current_scene()
        
        assert scene == GameScene.MAIN_MENU
        self.perception.screen_capture.capture_screen.assert_called_once()
        self.perception.scene_recognizer.recognize_scene.assert_called_once_with(mock_image)
    
    def test_get_current_scene_with_provided_image(self):
        """测试使用提供的图像获取场景"""
        test_image = np.zeros((100, 100, 3), dtype=np.uint8)
        self.perception.scene_recognizer.recognize_scene.return_value = GameScene.PRODUCE_MAIN
        
        scene = self.perception.get_current_scene(test_image)
        
        assert scene == GameScene.PRODUCE_MAIN
        # 不应该调用capture_screen
        self.perception.screen_capture.capture_screen.assert_not_called()
        self.perception.scene_recognizer.recognize_scene.assert_called_once_with(test_image)
    
    def test_find_ui_element(self):
        """测试查找UI元素"""
        mock_image = np.zeros((100, 100, 3), dtype=np.uint8)
        mock_result = MatchResult(10, 20, 30, 40, 0.9, "test_button")
        
        self.perception.screen_capture.capture_screen.return_value = mock_image
        self.perception.template_matcher.match_template.return_value = mock_result
        
        result = self.perception.find_ui_element("test_button")
        
        assert result == mock_result
        self.perception.template_matcher.match_template.assert_called_once_with(mock_image, "test_button")
    
    def test_get_module_info(self):
        """测试获取模块信息"""
        # 模拟子模块的info方法
        self.perception.screen_capture.get_window_info.return_value = {"test": "screen"}
        self.perception.scene_recognizer.get_recognition_info.return_value = {"test": "scene"}
        self.perception.template_matcher.get_template_info.return_value = {"test": "template"}
        
        info = self.perception.get_module_info()
        
        assert "language" in info
        assert "debug_mode" in info
        assert "screenshot_cache_duration" in info
        assert "screen_capture_info" in info
        assert "scene_recognizer_info" in info
        assert "template_matcher_info" in info


if __name__ == "__main__":
    pytest.main([__file__])
