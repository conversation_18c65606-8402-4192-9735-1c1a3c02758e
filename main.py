#!/usr/bin/env python3
"""
Gakumasu-Bot 主程序入口
学园偶像大师自动化游玩程序

版本: 1.0.0
作者: Gakumasu-Bot Development Team
日期: 2025-06-19
"""

import sys
import os
import argparse
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils.logger import setup_logger, get_logger
from src.utils.config_loader import ConfigLoader
from src.core.data_structures import InvalidConfiguration


def print_banner():
    """打印程序启动横幅"""
    banner = """
===================================================
== Gakumasu-Bot v1.0 - 自动化偶像制作人 (Python 3.13) ==
===================================================
    """
    print(banner)


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 10):
        print("错误: 需要Python 3.10或更高版本")
        print(f"当前版本: {sys.version}")
        sys.exit(1)
    
    if sys.version_info >= (3, 13):
        print(f"✓ Python版本: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    else:
        print(f"警告: 推荐使用Python 3.13，当前版本: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")


def check_dependencies():
    """检查必要的依赖库"""
    required_packages = [
        'cv2', 'mss', 'easyocr', 'torch', 'ultralytics', 
        'pydirectinput', 'yaml', 'numpy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'cv2':
                import cv2
            elif package == 'yaml':
                import yaml
            else:
                __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("错误: 缺少以下必要的依赖库:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")
        sys.exit(1)
    
    print("✓ 所有依赖库检查通过")


def setup_directories():
    """创建必要的目录"""
    directories = ['logs', 'config', 'data', 'assets/templates']
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✓ 目录结构检查完成")


def initialize_config():
    """初始化配置"""
    config_loader = ConfigLoader()
    
    try:
        # 加载系统设置
        settings = config_loader.load_settings()
        print("✓ 系统设置加载完成")
        
        # 加载用户策略
        user_strategy = config_loader.load_user_strategy()
        print("✓ 用户策略加载完成")
        
        return config_loader, settings, user_strategy
        
    except InvalidConfiguration as e:
        print(f"配置错误: {e}")
        print("\n请检查配置文件:")
        print("1. 复制 config/settings.yaml.example 为 config/settings.yaml")
        print("2. 复制 config/user_strategy.yaml.example 为 config/user_strategy.yaml")
        print("3. 根据实际情况修改配置文件")
        sys.exit(1)


def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='Gakumasu-Bot - 学园偶像大师自动化程序')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--config-dir', default='config', help='配置文件目录')
    parser.add_argument('--log-level', default='INFO', 
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
                       help='日志级别')
    
    args = parser.parse_args()
    
    # 打印启动横幅
    print_banner()
    
    # 系统检查
    print("[INFO] 正在进行系统检查...")
    check_python_version()
    check_dependencies()
    setup_directories()
    
    # 初始化日志系统
    log_level = 'DEBUG' if args.debug else args.log_level
    logger = setup_logger(log_level=log_level)
    logger.info("Gakumasu-Bot 启动中...")
    
    try:
        # 初始化配置
        print("[INFO] 正在加载配置文件...")
        config_loader, settings, user_strategy = initialize_config()
        
        # 设置语言
        language = settings.get('system', {}).get('language', 'ja')
        logger.info(f"语言设置: {'日语' if language == 'ja' else '中文'}")
        
        # 加载知识库
        print("[INFO] 正在加载知识库...")
        cards_data = config_loader.load_cards_data()
        events_data = config_loader.load_events_data()
        logger.info(f"已加载 {len(cards_data)} 张卡牌数据")
        logger.info(f"已加载 {len(events_data)} 个事件数据")
        
        print("[INFO] 初始化完成!")
        print("\n可用命令:")
        print("  start    - 启动任务调度")
        print("  status   - 查看状态")
        print("  config   - 重新加载配置")
        print("  help     - 显示帮助")
        print("  exit     - 退出程序")
        
        # 进入交互模式
        interactive_mode(logger, config_loader, settings, user_strategy)
        
    except KeyboardInterrupt:
        logger.info("用户中断程序")
        print("\n程序已退出")
    except Exception as e:
        logger.error(f"程序启动失败: {e}", exc_info=True)
        print(f"启动失败: {e}")
        sys.exit(1)


def interactive_mode(logger, config_loader, settings, user_strategy):
    """交互模式"""
    while True:
        try:
            command = input("\n> ").strip().lower()
            
            if command == 'exit' or command == 'quit':
                logger.info("用户退出程序")
                break
            elif command == 'start':
                print("任务调度功能尚未实现，请等待后续版本")
                logger.info("用户尝试启动任务调度")
            elif command == 'status':
                print("状态查看功能尚未实现，请等待后续版本")
                logger.info("用户查看状态")
            elif command == 'config':
                try:
                    config_loader.clear_cache()
                    settings = config_loader.load_settings(force_reload=True)
                    user_strategy = config_loader.load_user_strategy(force_reload=True)
                    print("配置重新加载完成")
                    logger.info("配置重新加载完成")
                except Exception as e:
                    print(f"配置重新加载失败: {e}")
                    logger.error(f"配置重新加载失败: {e}")
            elif command == 'help':
                print("\n可用命令:")
                print("  start    - 启动任务调度")
                print("  status   - 查看状态")
                print("  config   - 重新加载配置")
                print("  help     - 显示帮助")
                print("  exit     - 退出程序")
            elif command == '':
                continue
            else:
                print(f"未知命令: {command}，输入 'help' 查看可用命令")
                
        except KeyboardInterrupt:
            break
        except EOFError:
            break


if __name__ == "__main__":
    main()
