<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gakumasu-Bot 控制面板</title>
    
    <!-- Vue.js 3 CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <!-- Element Plus UI库 -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    
    <!-- 图标库 -->
    <link rel="stylesheet" href="https://unpkg.com/@element-plus/icons-vue/dist/index.css">
    
    <!-- 自定义样式 -->
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
        }
        
        .main-container {
            min-height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        
        .header .subtitle {
            margin: 5px 0 0 0;
            font-size: 14px;
            opacity: 0.9;
        }
        
        .content {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .status-card {
            margin-bottom: 20px;
        }
        
        .control-buttons {
            margin-bottom: 20px;
        }
        
        .control-buttons .el-button {
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stat-item {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #409eff;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 14px;
            color: #666;
        }
        
        .task-table {
            margin-bottom: 20px;
        }
        
        .log-container {
            height: 300px;
            overflow-y: auto;
            background: #1e1e1e;
            color: #fff;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .log-entry {
            margin-bottom: 5px;
        }
        
        .log-timestamp {
            color: #888;
        }
        
        .log-level-info {
            color: #4CAF50;
        }
        
        .log-level-warning {
            color: #FF9800;
        }
        
        .log-level-error {
            color: #F44336;
        }
        
        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .footer {
            text-align: center;
            padding: 20px;
            color: #666;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 连接状态指示器 -->
        <div class="connection-status">
            <el-tag :type="connectionStatus === 'connected' ? 'success' : 'danger'" size="small">
                {{ connectionStatus === 'connected' ? '已连接' : '未连接' }}
            </el-tag>
        </div>
        
        <!-- 主容器 -->
        <div class="main-container">
            <!-- 头部 -->
            <div class="header">
                <h1>Gakumasu-Bot 控制面板</h1>
                <p class="subtitle">学园偶像大师自动化机器人 - Web控制界面</p>
            </div>
            
            <!-- 内容区域 -->
            <div class="content">
                <!-- 系统状态卡片 -->
                <el-card class="status-card" shadow="hover">
                    <template #header>
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span>系统状态</span>
                            <el-button size="small" @click="refreshStatus" :loading="loading">
                                刷新
                            </el-button>
                        </div>
                    </template>
                    
                    <!-- 统计数据网格 -->
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-value">{{ systemStatus.status || '未知' }}</div>
                            <div class="stat-label">系统状态</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">{{ formatUptime(systemStatus.uptime) }}</div>
                            <div class="stat-label">运行时间</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">{{ systemStatus.active_tasks || 0 }}</div>
                            <div class="stat-label">活跃任务</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">{{ systemStatus.completed_tasks || 0 }}</div>
                            <div class="stat-label">已完成任务</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">{{ systemStatus.failed_tasks || 0 }}</div>
                            <div class="stat-label">失败任务</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">{{ systemStatus.scheduler_status || '未知' }}</div>
                            <div class="stat-label">调度器状态</div>
                        </div>
                    </div>
                </el-card>
                
                <!-- 控制按钮 -->
                <el-card class="control-buttons" shadow="hover">
                    <template #header>
                        <span>系统控制</span>
                    </template>
                    
                    <el-button type="success" @click="controlSystem('start')" :loading="controlLoading">
                        启动系统
                    </el-button>
                    <el-button type="danger" @click="controlSystem('stop')" :loading="controlLoading">
                        停止系统
                    </el-button>
                    <el-button type="warning" @click="controlSystem('restart')" :loading="controlLoading">
                        重启系统
                    </el-button>
                    <el-button type="info" @click="controlSystem('pause')" :loading="controlLoading">
                        暂停系统
                    </el-button>
                    <el-button type="primary" @click="controlSystem('resume')" :loading="controlLoading">
                        恢复系统
                    </el-button>
                    
                    <el-divider></el-divider>
                    
                    <el-button type="primary" @click="showCreateTaskDialog = true">
                        创建育成任务
                    </el-button>
                    <el-button @click="refreshTasks">
                        刷新任务列表
                    </el-button>
                </el-card>
                
                <!-- 任务列表 -->
                <el-card class="task-table" shadow="hover">
                    <template #header>
                        <span>任务列表</span>
                    </template>
                    
                    <el-table :data="tasks" style="width: 100%" empty-text="暂无任务">
                        <el-table-column prop="name" label="任务名称" width="200"></el-table-column>
                        <el-table-column prop="status" label="状态" width="100">
                            <template #default="scope">
                                <el-tag :type="getStatusType(scope.row.status)" size="small">
                                    {{ getStatusText(scope.row.status) }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="priority" label="优先级" width="100"></el-table-column>
                        <el-table-column prop="progress" label="进度" width="120">
                            <template #default="scope">
                                {{ scope.row.progress || '未开始' }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="created_at" label="创建时间" width="180">
                            <template #default="scope">
                                {{ formatDateTime(scope.row.created_at) }}
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="120">
                            <template #default="scope">
                                <el-button size="small" type="text" @click="viewTaskDetails(scope.row)">
                                    详情
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-card>
                
                <!-- 实时日志 -->
                <el-card shadow="hover">
                    <template #header>
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span>实时日志</span>
                            <el-button size="small" @click="clearLogs">
                                清空日志
                            </el-button>
                        </div>
                    </template>
                    
                    <div class="log-container" ref="logContainer">
                        <div v-for="(log, index) in logs" :key="index" class="log-entry">
                            <span class="log-timestamp">{{ log.timestamp }}</span>
                            <span :class="'log-level-' + log.level">{{ log.message }}</span>
                        </div>
                    </div>
                </el-card>
            </div>
            
            <!-- 页脚 -->
            <div class="footer">
                <p>Gakumasu-Bot v1.0.0 - 基于Vue.js 3 + FastAPI构建</p>
            </div>
        </div>
        
        <!-- 创建任务对话框 -->
        <el-dialog v-model="showCreateTaskDialog" title="创建育成任务" width="500px">
            <el-form :model="newTask" label-width="100px">
                <el-form-item label="任务类型">
                    <el-select v-model="newTask.type" placeholder="请选择任务类型">
                        <el-option label="完整育成流程" value="produce"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="偶像选择">
                    <el-input v-model="newTask.strategy.produce_idol" placeholder="输入偶像名称（可选）"></el-input>
                </el-form-item>
                <el-form-item label="策略配置">
                    <el-input type="textarea" v-model="newTask.strategyJson" placeholder="JSON格式的策略配置（可选）" rows="4"></el-input>
                </el-form-item>
            </el-form>
            
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="showCreateTaskDialog = false">取消</el-button>
                    <el-button type="primary" @click="createTask" :loading="createTaskLoading">
                        创建任务
                    </el-button>
                </span>
            </template>
        </el-dialog>
    </div>
    
    <!-- Vue应用脚本 -->
    <script>
        // Vue应用配置
        const { createApp } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        const app = createApp({
            data() {
                return {
                    // 连接状态
                    connectionStatus: 'disconnected',
                    websocket: null,

                    // 系统状态
                    systemStatus: {},
                    loading: false,
                    controlLoading: false,

                    // 任务相关
                    tasks: [],
                    showCreateTaskDialog: false,
                    createTaskLoading: false,
                    newTask: {
                        type: 'produce',
                        strategy: {
                            produce_idol: ''
                        },
                        strategyJson: ''
                    },

                    // 日志
                    logs: [],
                    maxLogs: 100
                };
            },

            mounted() {
                this.initializeApp();
            },

            beforeUnmount() {
                if (this.websocket) {
                    this.websocket.close();
                }
            },

            methods: {
                // 初始化应用
                async initializeApp() {
                    await this.refreshStatus();
                    await this.refreshTasks();
                    this.connectWebSocket();
                    this.addLog('info', '应用初始化完成');
                },

                // 连接WebSocket
                connectWebSocket() {
                    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                    const wsUrl = `${protocol}//${window.location.host}/ws`;

                    this.websocket = new WebSocket(wsUrl);

                    this.websocket.onopen = () => {
                        this.connectionStatus = 'connected';
                        this.addLog('info', 'WebSocket连接已建立');
                    };

                    this.websocket.onmessage = (event) => {
                        const data = JSON.parse(event.data);
                        this.handleWebSocketMessage(data);
                    };

                    this.websocket.onclose = () => {
                        this.connectionStatus = 'disconnected';
                        this.addLog('warning', 'WebSocket连接已断开');

                        // 5秒后重连
                        setTimeout(() => {
                            this.connectWebSocket();
                        }, 5000);
                    };

                    this.websocket.onerror = (error) => {
                        this.addLog('error', 'WebSocket连接错误');
                    };
                },

                // 处理WebSocket消息
                handleWebSocketMessage(data) {
                    if (data.type === 'status_update') {
                        this.systemStatus = data.data;
                    } else if (data.type === 'system_status') {
                        this.addLog('info', `系统状态变更: ${data.data.status}`);
                        this.refreshStatus();
                    } else if (data.type === 'task_update') {
                        this.refreshTasks();
                    }
                },

                // 刷新系统状态
                async refreshStatus() {
                    this.loading = true;
                    try {
                        const response = await fetch('/api/v1/status');
                        if (response.ok) {
                            this.systemStatus = await response.json();
                        } else {
                            throw new Error('获取状态失败');
                        }
                    } catch (error) {
                        this.addLog('error', `获取系统状态失败: ${error.message}`);
                        ElMessage.error('获取系统状态失败');
                    } finally {
                        this.loading = false;
                    }
                },

                // 系统控制
                async controlSystem(action) {
                    this.controlLoading = true;
                    try {
                        const response = await fetch('/api/v1/control', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ action })
                        });

                        if (response.ok) {
                            const result = await response.json();
                            ElMessage.success(result.message || `${action}操作成功`);
                            this.addLog('info', `执行系统控制: ${action}`);
                            await this.refreshStatus();
                        } else {
                            throw new Error('控制操作失败');
                        }
                    } catch (error) {
                        this.addLog('error', `系统控制失败: ${error.message}`);
                        ElMessage.error('系统控制失败');
                    } finally {
                        this.controlLoading = false;
                    }
                },

                // 刷新任务列表
                async refreshTasks() {
                    try {
                        const response = await fetch('/api/v1/tasks');
                        if (response.ok) {
                            this.tasks = await response.json();
                        } else {
                            throw new Error('获取任务列表失败');
                        }
                    } catch (error) {
                        this.addLog('error', `获取任务列表失败: ${error.message}`);
                    }
                },

                // 创建任务
                async createTask() {
                    this.createTaskLoading = true;
                    try {
                        let strategy = this.newTask.strategy;

                        // 如果有JSON配置，尝试解析
                        if (this.newTask.strategyJson.trim()) {
                            try {
                                const jsonStrategy = JSON.parse(this.newTask.strategyJson);
                                strategy = { ...strategy, ...jsonStrategy };
                            } catch (e) {
                                ElMessage.error('策略配置JSON格式错误');
                                return;
                            }
                        }

                        const response = await fetch('/api/v1/tasks', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                task_type: this.newTask.type,
                                parameters: { strategy }
                            })
                        });

                        if (response.ok) {
                            const result = await response.json();
                            ElMessage.success('任务创建成功');
                            this.addLog('info', `创建任务: ${result.task_id}`);
                            this.showCreateTaskDialog = false;
                            this.resetNewTask();
                            await this.refreshTasks();
                        } else {
                            throw new Error('创建任务失败');
                        }
                    } catch (error) {
                        this.addLog('error', `创建任务失败: ${error.message}`);
                        ElMessage.error('创建任务失败');
                    } finally {
                        this.createTaskLoading = false;
                    }
                },

                // 重置新任务表单
                resetNewTask() {
                    this.newTask = {
                        type: 'produce',
                        strategy: {
                            produce_idol: ''
                        },
                        strategyJson: ''
                    };
                },

                // 查看任务详情
                viewTaskDetails(task) {
                    ElMessageBox.alert(
                        `任务ID: ${task.task_id}\n状态: ${task.status}\n创建时间: ${this.formatDateTime(task.created_at)}`,
                        '任务详情',
                        { confirmButtonText: '确定' }
                    );
                },

                // 添加日志
                addLog(level, message) {
                    const timestamp = new Date().toLocaleTimeString();
                    this.logs.push({
                        timestamp,
                        level,
                        message
                    });

                    // 限制日志数量
                    if (this.logs.length > this.maxLogs) {
                        this.logs.shift();
                    }

                    // 自动滚动到底部
                    this.$nextTick(() => {
                        const container = this.$refs.logContainer;
                        if (container) {
                            container.scrollTop = container.scrollHeight;
                        }
                    });
                },

                // 清空日志
                clearLogs() {
                    this.logs = [];
                },

                // 格式化运行时间
                formatUptime(seconds) {
                    if (!seconds) return '0秒';

                    const hours = Math.floor(seconds / 3600);
                    const minutes = Math.floor((seconds % 3600) / 60);
                    const secs = Math.floor(seconds % 60);

                    if (hours > 0) {
                        return `${hours}小时${minutes}分钟`;
                    } else if (minutes > 0) {
                        return `${minutes}分钟${secs}秒`;
                    } else {
                        return `${secs}秒`;
                    }
                },

                // 格式化日期时间
                formatDateTime(dateString) {
                    if (!dateString) return '';
                    return new Date(dateString).toLocaleString('zh-CN');
                },

                // 获取状态类型
                getStatusType(status) {
                    const statusMap = {
                        'pending': 'info',
                        'running': 'warning',
                        'completed': 'success',
                        'failed': 'danger',
                        'cancelled': 'info',
                        'paused': 'warning'
                    };
                    return statusMap[status] || 'info';
                },

                // 获取状态文本
                getStatusText(status) {
                    const statusMap = {
                        'pending': '等待中',
                        'running': '运行中',
                        'completed': '已完成',
                        'failed': '失败',
                        'cancelled': '已取消',
                        'paused': '已暂停'
                    };
                    return statusMap[status] || status;
                }
            }
        });

        // 使用Element Plus
        app.use(ElementPlus);

        // 挂载应用
        app.mount('#app');
    </script>
</body>
</html>
