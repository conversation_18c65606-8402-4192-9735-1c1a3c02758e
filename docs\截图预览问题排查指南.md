# 截图预览问题排查指南

## 问题分类与排查步骤

### 1. URL构建问题 🔗

#### 检查点
- [ ] 前端URL构建逻辑是否正确
- [ ] 路径分隔符处理是否完整
- [ ] 文件名提取是否准确

#### 排查方法
```javascript
// 在浏览器控制台检查URL构建
console.log('API返回的数据:', historyData);
console.log('构建的缩略图URL:', getThumbnailUrl(item));
console.log('构建的原图URL:', getFullImageUrl(item));
```

#### 常见问题
- Windows路径分隔符 `\` 未正确处理
- 完整路径未正确提取文件名
- URL前缀不匹配静态文件服务配置

### 2. 静态文件服务配置问题 🗂️

#### 检查点
- [ ] 后端静态文件服务是否正确挂载
- [ ] 目录路径是否存在
- [ ] 文件权限是否正确

#### 排查方法
```bash
# 检查目录结构
ls -la screenshots/
ls -la screenshots/thumbnails/

# 直接访问静态文件
curl -I http://127.0.0.1:8000/screenshots/filename.png
curl -I http://127.0.0.1:8000/screenshots/thumbnails/filename_thumb.png
```

#### 常见问题
- `screenshots` 目录不存在
- 静态文件服务未正确挂载到 `/screenshots` 路径
- 文件权限不足

### 3. 数据格式问题 📊

#### 检查点
- [ ] API返回的路径格式是否正确
- [ ] 路径标准化是否完整
- [ ] 缩略图路径是否存在

#### 排查方法
```javascript
// 检查API返回的数据格式
fetch('/api/v1/screenshot/history')
  .then(r => r.json())
  .then(data => {
    console.log('历史记录数据:', data);
    data.records.forEach(record => {
      console.log('文件名:', record.filename);
      console.log('文件路径:', record.filepath);
      console.log('缩略图路径:', record.thumbnail_path);
    });
  });
```

#### 常见问题
- `thumbnail_path` 包含完整系统路径
- 路径分隔符不统一
- 缺少必要的路径前缀

### 4. 网络请求问题 🌐

#### 检查点
- [ ] 图片请求是否发送
- [ ] HTTP状态码是否正确
- [ ] CORS配置是否正确

#### 排查方法
```javascript
// 监听图片加载事件
document.querySelectorAll('img').forEach(img => {
  img.addEventListener('load', () => {
    console.log('图片加载成功:', img.src);
  });
  img.addEventListener('error', (e) => {
    console.error('图片加载失败:', img.src, e);
  });
});
```

#### 常见问题
- 404 Not Found：文件不存在或路径错误
- 403 Forbidden：权限问题
- CORS错误：跨域配置问题

### 5. 前端组件问题 🎨

#### 检查点
- [ ] 图片元素是否正确渲染
- [ ] 错误处理是否生效
- [ ] 占位图是否显示

#### 排查方法
```javascript
// 检查DOM元素
document.querySelectorAll('.item-thumbnail img').forEach(img => {
  console.log('图片元素:', img);
  console.log('src属性:', img.src);
  console.log('是否有错误类:', img.classList.contains('image-load-error'));
});
```

## 完整排查流程

### 步骤1：验证API数据
```bash
curl http://127.0.0.1:8000/api/v1/screenshot/history | jq '.'
```

### 步骤2：验证静态文件服务
```bash
# 测试原图
curl -I http://127.0.0.1:8000/screenshots/screenshot_20250730_220149_ddde1861.png

# 测试缩略图
curl -I http://127.0.0.1:8000/screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
```

### 步骤3：检查前端URL构建
在浏览器开发者工具中：
1. 打开Network标签
2. 刷新页面
3. 查看图片请求的URL是否正确
4. 检查响应状态码

### 步骤4：验证文件系统
```bash
# 检查文件是否存在
ls -la screenshots/screenshot_20250730_220149_ddde1861.png
ls -la screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
```

## 修复建议

### 如果URL构建错误
```javascript
// 修复前端URL构建逻辑
function getThumbnailUrl(item) {
  if (!item.thumbnail_path) return getFullImageUrl(item);
  
  // 提取文件名
  const filename = item.thumbnail_path.split(/[/\\]/).pop();
  return `/screenshots/thumbnails/${filename}`;
}
```

### 如果静态文件服务问题
```python
# 检查后端配置
screenshots_dir = Path("screenshots")
if screenshots_dir.exists():
    app.mount("/screenshots", StaticFiles(directory=str(screenshots_dir)), name="screenshots")
else:
    print(f"警告：截图目录不存在: {screenshots_dir}")
```

### 如果路径格式问题
```python
# 修复路径标准化
def normalize_path(path_str):
    if not path_str:
        return None
    # 统一使用正斜杠
    normalized = path_str.replace('\\', '/')
    # 提取相对路径
    if normalized.startswith('screenshots/'):
        return normalized[12:]  # 移除 'screenshots/' 前缀
    return normalized
```

## 调试工具

### 前端调试代码
```javascript
// 添加到组件中用于调试
function debugImageUrls() {
  console.group('图片URL调试信息');
  historyData.value.records.forEach((item, index) => {
    console.log(`记录 ${index}:`, {
      filename: item.filename,
      filepath: item.filepath,
      thumbnail_path: item.thumbnail_path,
      thumbnailUrl: getThumbnailUrl(item),
      fullImageUrl: getFullImageUrl(item)
    });
  });
  console.groupEnd();
}
```

### 后端调试代码
```python
# 添加到路径转换函数中
def convert_screenshot_record_data(record_dict: Dict[str, Any]) -> Dict[str, Any]:
    converted = record_dict.copy()
    
    # 调试输出
    print(f"[DEBUG] 原始数据: {record_dict}")
    
    # ... 处理逻辑 ...
    
    print(f"[DEBUG] 转换后数据: {converted}")
    return converted
```

## 成功标准

修复完成后，应该满足以下条件：
- [ ] API返回正确的元数据格式
- [ ] 静态文件服务正常响应
- [ ] 前端能正确构建图片URL
- [ ] 缩略图和原图都能正常显示
- [ ] 浏览器控制台无相关错误
- [ ] 图片加载失败时显示占位图
