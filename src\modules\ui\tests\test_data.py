"""
测试数据
提供测试用的配置数据和模拟数据
"""

from ....core.data_structures import GameScene
from ..config.ui_element_config import Position, Size
from ..config.scene_config import SceneConfig, NavigationConfig, NavigationStep, NavigationStrategy


# 测试用UI元素配置
TEST_UI_CONFIGS = {
    "test_button": {
        "type": "button",
        "template_name": "test_button",
        "confidence_threshold": 0.8,
        "timeout": 5.0,
        "retry_count": 3,
        "enabled": True,
        "position": {"x": 100, "y": 200},
        "double_click_enabled": False,
        "long_press_duration": 1.0
    },
    
    "test_input": {
        "type": "input_field",
        "template_name": "test_input",
        "confidence_threshold": 0.8,
        "timeout": 5.0,
        "retry_count": 3,
        "enabled": True,
        "position": {"x": 300, "y": 400},
        "clear_before_input": True,
        "input_delay": 0.1,
        "max_length": 100,
        "input_method": "direct"
    },
    
    "test_label": {
        "type": "label",
        "template_name": "test_label",
        "confidence_threshold": 0.8,
        "timeout": 5.0,
        "retry_count": 3,
        "enabled": True,
        "position": {"x": 500, "y": 600},
        "text_recognition_enabled": True,
        "ocr_language": "ja",
        "ocr_confidence_threshold": 0.7
    },
    
    "enhanced_button": {
        "type": "enhanced_button",
        "template_name": "enhanced_button",
        "confidence_threshold": 0.85,
        "timeout": 8.0,
        "retry_count": 5,
        "enabled": True,
        "position": {"x": 700, "y": 800},
        "double_click_enabled": True,
        "long_press_duration": 2.0,
        "expected_scene_after_click": "produce_setup"
    }
}


# 测试用场景配置
TEST_SCENE_CONFIGS = {
    GameScene.MAIN_MENU: SceneConfig(
        scene_type=GameScene.MAIN_MENU,
        scene_name="测试主菜单",
        scene_indicators=["main_menu_logo", "main_menu_bg"],
        recognition_confidence=0.8,
        recognition_timeout=10.0,
        ui_elements={
            "produce_button": {
                "template_name": "produce_button",
                "position": {"x": 500, "y": 400},
                "confidence_threshold": 0.8
            },
            "part_time_job_button": {
                "template_name": "part_time_job_button",
                "position": {"x": 300, "y": 600},
                "confidence_threshold": 0.8
            }
        },
        navigation=NavigationConfig(
            strategy=NavigationStrategy.SMART,
            max_navigation_time=30.0,
            steps=[
                NavigationStep(
                    action_type="verify",
                    target="main_menu_logo",
                    timeout=5.0,
                    description="验证主菜单标志"
                )
            ]
        )
    ),
    
    GameScene.PRODUCE_SETUP: SceneConfig(
        scene_type=GameScene.PRODUCE_SETUP,
        scene_name="测试育成准备",
        scene_indicators=["produce_setup_title", "idol_selection_area"],
        recognition_confidence=0.8,
        recognition_timeout=10.0,
        ui_elements={
            "idol_selection_button": {
                "template_name": "idol_selection_button",
                "position": {"x": 300, "y": 500},
                "confidence_threshold": 0.8
            },
            "start_produce_button": {
                "template_name": "start_produce_button",
                "position": {"x": 960, "y": 800},
                "confidence_threshold": 0.8
            }
        },
        navigation=NavigationConfig(
            strategy=NavigationStrategy.DIRECT,
            max_navigation_time=20.0,
            steps=[
                NavigationStep(
                    action_type="click",
                    target="produce_button",
                    timeout=5.0,
                    description="点击育成按钮"
                ),
                NavigationStep(
                    action_type="wait",
                    target="produce_setup_title",
                    timeout=10.0,
                    description="等待育成准备界面"
                )
            ]
        )
    ),
    
    GameScene.PRODUCE_MAIN: SceneConfig(
        scene_type=GameScene.PRODUCE_MAIN,
        scene_name="测试育成主界面",
        scene_indicators=["produce_main_ui", "lesson_area"],
        recognition_confidence=0.8,
        recognition_timeout=10.0,
        ui_elements={
            "vocal_lesson_button": {
                "template_name": "vocal_lesson_button",
                "confidence_threshold": 0.8
            },
            "dance_lesson_button": {
                "template_name": "dance_lesson_button",
                "confidence_threshold": 0.8
            },
            "rest_button": {
                "template_name": "rest_button",
                "confidence_threshold": 0.8
            }
        },
        navigation=NavigationConfig(
            strategy=NavigationStrategy.STEP_BY_STEP,
            max_navigation_time=40.0,
            steps=[
                NavigationStep(
                    action_type="click",
                    target="produce_button",
                    timeout=5.0,
                    description="点击育成按钮"
                ),
                NavigationStep(
                    action_type="wait",
                    target="produce_setup_title",
                    timeout=10.0,
                    description="等待育成准备界面"
                ),
                NavigationStep(
                    action_type="click",
                    target="start_produce_button",
                    timeout=5.0,
                    description="点击开始育成"
                ),
                NavigationStep(
                    action_type="wait",
                    target="produce_main_ui",
                    timeout=15.0,
                    description="等待育成主界面"
                )
            ]
        )
    )
}


# 模拟模板数据
MOCK_TEMPLATES = {
    # 主菜单相关
    "main_menu_logo": {
        "position": Position(960, 100),
        "size": Size(200, 80),
        "confidence": 0.95
    },
    "main_menu_bg": {
        "position": Position(960, 540),
        "size": Size(1920, 1080),
        "confidence": 0.9
    },
    "produce_button": {
        "position": Position(500, 400),
        "size": Size(150, 60),
        "confidence": 0.85
    },
    "part_time_job_button": {
        "position": Position(300, 600),
        "size": Size(150, 60),
        "confidence": 0.85
    },
    "daily_tasks_button": {
        "position": Position(700, 600),
        "size": Size(150, 60),
        "confidence": 0.85
    },
    
    # 育成准备相关
    "produce_setup_title": {
        "position": Position(960, 100),
        "size": Size(300, 50),
        "confidence": 0.9
    },
    "idol_selection_area": {
        "position": Position(960, 400),
        "size": Size(800, 600),
        "confidence": 0.8
    },
    "idol_selection_button": {
        "position": Position(300, 500),
        "size": Size(120, 50),
        "confidence": 0.85
    },
    "support_card_button": {
        "position": Position(600, 500),
        "size": Size(120, 50),
        "confidence": 0.85
    },
    "start_produce_button": {
        "position": Position(960, 800),
        "size": Size(200, 80),
        "confidence": 0.9
    },
    
    # 育成主界面相关
    "produce_main_ui": {
        "position": Position(960, 100),
        "size": Size(400, 60),
        "confidence": 0.9
    },
    "lesson_area": {
        "position": Position(960, 400),
        "size": Size(1200, 800),
        "confidence": 0.8
    },
    "vocal_lesson_button": {
        "position": Position(200, 300),
        "size": Size(100, 100),
        "confidence": 0.85
    },
    "dance_lesson_button": {
        "position": Position(400, 300),
        "size": Size(100, 100),
        "confidence": 0.85
    },
    "visual_lesson_button": {
        "position": Position(600, 300),
        "size": Size(100, 100),
        "confidence": 0.85
    },
    "mental_lesson_button": {
        "position": Position(800, 300),
        "size": Size(100, 100),
        "confidence": 0.85
    },
    "rest_button": {
        "position": Position(1000, 300),
        "size": Size(100, 100),
        "confidence": 0.85
    },
    "outing_button": {
        "position": Position(1200, 300),
        "size": Size(100, 100),
        "confidence": 0.85
    },
    
    # 通用按钮
    "confirm_button": {
        "position": Position(800, 700),
        "size": Size(120, 50),
        "confidence": 0.9
    },
    "cancel_button": {
        "position": Position(600, 700),
        "size": Size(120, 50),
        "confidence": 0.9
    },
    "back_button": {
        "position": Position(100, 100),
        "size": Size(80, 40),
        "confidence": 0.85
    },
    "menu_button": {
        "position": Position(1820, 100),
        "size": Size(80, 40),
        "confidence": 0.85
    }
}


# 模拟游戏状态数据
MOCK_GAME_STATES = {
    GameScene.MAIN_MENU: {
        "current_scene": GameScene.MAIN_MENU,
        "scene_name": "主菜单",
        "available_actions": ["produce", "part_time_job", "daily_tasks"],
        "ui_elements_visible": ["main_menu_logo", "produce_button", "part_time_job_button", "daily_tasks_button"]
    },
    
    GameScene.PRODUCE_SETUP: {
        "current_scene": GameScene.PRODUCE_SETUP,
        "scene_name": "育成准备",
        "available_actions": ["select_idol", "select_support_cards", "start_produce"],
        "ui_elements_visible": ["produce_setup_title", "idol_selection_button", "support_card_button", "start_produce_button"]
    },
    
    GameScene.PRODUCE_MAIN: {
        "current_scene": GameScene.PRODUCE_MAIN,
        "scene_name": "育成主界面",
        "available_actions": ["vocal_lesson", "dance_lesson", "visual_lesson", "mental_lesson", "rest", "outing"],
        "ui_elements_visible": ["produce_main_ui", "vocal_lesson_button", "dance_lesson_button", "visual_lesson_button", "mental_lesson_button", "rest_button", "outing_button"]
    }
}


# 测试用OCR结果
MOCK_OCR_RESULTS = {
    # 位置格式: "x_y_width_height": "识别文本"
    "450_350_100_30": "ボーカル",  # 声乐
    "650_350_100_30": "ダンス",   # 舞蹈
    "850_350_100_30": "ビジュアル", # 视觉
    "1050_350_100_30": "メンタル", # 精神
    "1250_350_100_30": "休息",     # 休息
    "1450_350_100_30": "お出かけ", # 外出
    
    "910_750_100_30": "確認",      # 确认
    "550_750_100_30": "キャンセル", # 取消
    "50_50_60_30": "戻る",         # 返回
    "1770_50_60_30": "メニュー",   # 菜单
}


# 测试序列数据
TEST_SEQUENCES = {
    "main_menu_to_produce": [
        {"element": "produce_button", "action": "click", "delay": 0.5},
        {"element": "start_produce_button", "action": "wait_visible", "params": {"timeout": 10.0}},
        {"element": "start_produce_button", "action": "click", "delay": 1.0}
    ],
    
    "produce_vocal_lesson": [
        {"element": "vocal_lesson_button", "action": "click", "delay": 0.3},
        {"element": "confirm_button", "action": "wait_visible", "params": {"timeout": 5.0}},
        {"element": "confirm_button", "action": "click", "delay": 0.5}
    ],
    
    "form_input_test": [
        {"element": "test_input", "action": "click", "delay": 0.2},
        {"element": "test_input", "action": "input", "params": {"text": "测试文本"}, "delay": 0.5},
        {"element": "confirm_button", "action": "click", "delay": 0.3}
    ]
}


# 性能测试数据
PERFORMANCE_TEST_DATA = {
    "element_creation_benchmark": {
        "iterations": 1000,
        "element_types": ["button", "input_field", "label", "enhanced_button"],
        "expected_max_time": 0.01  # 每个元素创建不超过10ms
    },
    
    "scene_navigation_benchmark": {
        "iterations": 100,
        "navigation_paths": [
            (GameScene.MAIN_MENU, GameScene.PRODUCE_SETUP),
            (GameScene.PRODUCE_SETUP, GameScene.PRODUCE_MAIN),
            (GameScene.PRODUCE_MAIN, GameScene.MAIN_MENU)
        ],
        "expected_max_time": 5.0  # 每次导航不超过5秒
    },
    
    "ui_element_interaction_benchmark": {
        "iterations": 500,
        "interactions": ["click", "input_text", "read_text"],
        "expected_max_time": 0.1  # 每次交互不超过100ms
    }
}
