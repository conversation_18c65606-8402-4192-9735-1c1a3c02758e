"""
育成主界面场景实现
处理育成过程中的核心功能：课程选择、休息、外出等
"""

import time
from typing import List, Dict, Any, Optional, Tuple
from enum import Enum

from ....core.data_structures import GameScene
from ..config.scene_config import SceneConfig
from ..config.ui_element_config import ButtonConfig, LabelConfig
from .base_game_scene import BaseGameScene


class LessonType(Enum):
    """课程类型枚举"""
    VOCAL = "vocal"      # 声乐
    DANCE = "dance"      # 舞蹈
    VISUAL = "visual"    # 视觉
    MENTAL = "mental"    # 精神


class ActionType(Enum):
    """行动类型枚举"""
    LESSON = "lesson"    # 上课
    REST = "rest"        # 休息
    OUTING = "outing"    # 外出


class ProduceMainScene(BaseGameScene):
    """育成主界面场景类"""
    
    def __init__(self, config: SceneConfig, perception_module, action_controller):
        """
        初始化育成主界面场景
        
        Args:
            config: 场景配置
            perception_module: 感知模块
            action_controller: 行动控制器
        """
        super().__init__(config, perception_module, action_controller)
        
        # 育成状态
        self._current_turn = 0
        self._max_turns = 78  # 默认78回合
        self._current_stats = {
            "vocal": 0, "dance": 0, "visual": 0, "mental": 0,
            "stamina": 100, "motivation": 3
        }
        self._last_action = None
        self._action_history = []
        
        # 界面状态
        self._lesson_area_loaded = False
        self._stats_updated = False
        self._turn_info_visible = False
        
        self.scene_logger.info("育成主界面场景初始化完成")
    
    def _create_scene_ui_elements(self):
        """创建育成主界面特定的UI元素"""
        try:
            # 场景标识元素
            self._create_scene_indicators()
            
            # 课程选择元素
            self._create_lesson_elements()
            
            # 行动选择元素
            self._create_action_elements()
            
            # 状态显示元素
            self._create_status_elements()
            
            # 控制按钮
            self._create_control_buttons()
            
            # 事件和对话元素
            self._create_event_elements()
            
            self.scene_logger.info(f"育成主界面UI元素创建完成，共{len(self.ui_elements)}个元素")
            
        except Exception as e:
            self.scene_logger.error(f"育成主界面UI元素创建失败: {e}")
            raise
    
    def _create_scene_indicators(self):
        """创建场景标识元素"""
        # 育成主界面标识
        self.ui_elements["produce_main_ui"] = self.ui_factory.create_label(
            "produce_main_ui",
            confidence_threshold=0.9,
            text_recognition_enabled=False
        )
        
        # 课程区域
        self.ui_elements["lesson_area"] = self.ui_factory.create_label(
            "lesson_area",
            confidence_threshold=0.8,
            text_recognition_enabled=False
        )
        
        # 回合信息区域
        self.ui_elements["turn_info_area"] = self.ui_factory.create_label(
            "turn_info_area",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
    
    def _create_lesson_elements(self):
        """创建课程选择元素"""
        # 声乐课程按钮
        self.ui_elements["vocal_lesson_button"] = self.ui_factory.create_enhanced_button(
            "vocal_lesson_button",
            confidence_threshold=0.85,
            timeout=5.0,
            verify_click_result=True
        )
        
        # 舞蹈课程按钮
        self.ui_elements["dance_lesson_button"] = self.ui_factory.create_enhanced_button(
            "dance_lesson_button",
            confidence_threshold=0.85,
            timeout=5.0,
            verify_click_result=True
        )
        
        # 视觉课程按钮
        self.ui_elements["visual_lesson_button"] = self.ui_factory.create_enhanced_button(
            "visual_lesson_button",
            confidence_threshold=0.85,
            timeout=5.0,
            verify_click_result=True
        )
        
        # 精神课程按钮
        self.ui_elements["mental_lesson_button"] = self.ui_factory.create_enhanced_button(
            "mental_lesson_button",
            confidence_threshold=0.85,
            timeout=5.0,
            verify_click_result=True
        )
        
        # 课程详情显示
        self.ui_elements["lesson_details"] = self.ui_factory.create_label(
            "lesson_details",
            confidence_threshold=0.7,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
    
    def _create_action_elements(self):
        """创建行动选择元素"""
        # 休息按钮
        self.ui_elements["rest_button"] = self.ui_factory.create_enhanced_button(
            "rest_button",
            confidence_threshold=0.85,
            timeout=5.0,
            verify_click_result=True
        )
        
        # 外出按钮
        self.ui_elements["outing_button"] = self.ui_factory.create_enhanced_button(
            "outing_button",
            confidence_threshold=0.85,
            timeout=5.0,
            verify_click_result=True
        )
        
        # 特殊训练按钮
        self.ui_elements["special_training_button"] = self.ui_factory.create_button(
            "special_training_button",
            confidence_threshold=0.8,
            timeout=5.0
        )
        
        # 技能获取按钮
        self.ui_elements["skill_button"] = self.ui_factory.create_button(
            "skill_button",
            confidence_threshold=0.8,
            timeout=5.0
        )
    
    def _create_status_elements(self):
        """创建状态显示元素"""
        # 属性值显示
        for stat in ["vocal", "dance", "visual", "mental"]:
            self.ui_elements[f"{stat}_stat"] = self.ui_factory.create_enhanced_label(
                f"{stat}_stat",
                confidence_threshold=0.8,
                text_recognition_enabled=True,
                ocr_language="en"
            )
        
        # 体力显示
        self.ui_elements["stamina_stat"] = self.ui_factory.create_enhanced_label(
            "stamina_stat",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="en"
        )
        
        # 干劲显示
        self.ui_elements["motivation_stat"] = self.ui_factory.create_label(
            "motivation_stat",
            confidence_threshold=0.8,
            text_recognition_enabled=False
        )
        
        # 回合数显示
        self.ui_elements["turn_counter"] = self.ui_factory.create_enhanced_label(
            "turn_counter",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="en"
        )
        
        # 偶像状态显示
        self.ui_elements["idol_status"] = self.ui_factory.create_label(
            "idol_status",
            confidence_threshold=0.7,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
    
    def _create_control_buttons(self):
        """创建控制按钮"""
        # 确认按钮
        self.ui_elements["confirm_button"] = self.ui_factory.create_button(
            "confirm_button",
            confidence_threshold=0.9,
            timeout=3.0
        )
        
        # 取消按钮
        self.ui_elements["cancel_button"] = self.ui_factory.create_button(
            "cancel_button",
            confidence_threshold=0.8,
            timeout=3.0
        )
        
        # 菜单按钮
        self.ui_elements["menu_button"] = self.ui_factory.create_button(
            "menu_button",
            confidence_threshold=0.8,
            timeout=3.0
        )
        
        # 自动模式按钮
        self.ui_elements["auto_mode_button"] = self.ui_factory.create_button(
            "auto_mode_button",
            confidence_threshold=0.8,
            timeout=3.0
        )
    
    def _create_event_elements(self):
        """创建事件和对话元素"""
        # 事件对话框
        self.ui_elements["event_dialog"] = self.ui_factory.create_label(
            "event_dialog",
            confidence_threshold=0.7,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
        
        # 选择肢按钮
        for i in range(1, 4):  # 最多3个选择肢
            self.ui_elements[f"choice_{i}_button"] = self.ui_factory.create_button(
                f"choice_{i}_button",
                confidence_threshold=0.8,
                timeout=3.0
            )
        
        # 跳过按钮
        self.ui_elements["skip_button"] = self.ui_factory.create_button(
            "skip_button",
            confidence_threshold=0.8,
            timeout=3.0
        )
    
    def _get_critical_elements(self) -> List[str]:
        """获取育成主界面的关键UI元素"""
        return [
            "produce_main_ui",
            "lesson_area",
            "vocal_lesson_button",
            "dance_lesson_button",
            "visual_lesson_button",
            "mental_lesson_button",
            "rest_button",
            "outing_button"
        ]
    
    def _verify_scene_specific_state(self) -> bool:
        """验证育成主界面特定状态"""
        try:
            # 验证主界面标识可见
            main_ui = self.get_ui_element("produce_main_ui")
            if not main_ui or not main_ui.is_visible():
                self.scene_logger.debug("育成主界面标识不可见")
                return False
            
            # 验证课程区域可见
            lesson_area = self.get_ui_element("lesson_area")
            if not lesson_area or not lesson_area.is_visible():
                self.scene_logger.debug("课程区域不可见")
                return False
            
            # 验证至少有课程按钮可见
            lesson_buttons = ["vocal_lesson_button", "dance_lesson_button", 
                            "visual_lesson_button", "mental_lesson_button"]
            visible_lessons = 0
            
            for button_name in lesson_buttons:
                button = self.get_ui_element(button_name)
                if button and button.is_visible():
                    visible_lessons += 1
            
            if visible_lessons < 3:  # 至少3个课程按钮可见
                self.scene_logger.debug(f"可见课程按钮数量不足: {visible_lessons}/4")
                return False
            
            return True
            
        except Exception as e:
            self.scene_logger.error(f"育成主界面状态验证异常: {e}")
            return False
    
    def take_lesson(self, lesson_type: LessonType, force: bool = False) -> bool:
        """
        上课

        Args:
            lesson_type: 课程类型
            force: 是否强制执行（忽略状态检查）

        Returns:
            是否上课成功
        """
        try:
            self.scene_logger.info(f"开始上课: {lesson_type.value}")

            # 状态检查（除非强制执行）
            if not force:
                if not self._check_lesson_prerequisites(lesson_type):
                    return False

            # 获取对应的课程按钮
            button_name = f"{lesson_type.value}_lesson_button"

            # 验证按钮是否存在且可见
            lesson_button = self.get_ui_element(button_name)
            if not lesson_button:
                self.scene_logger.error(f"未找到{lesson_type.value}课程按钮")
                return False

            if not lesson_button.is_visible():
                self.scene_logger.error(f"{lesson_type.value}课程按钮不可见")
                return False

            # 点击课程按钮
            if not self.interact_with_element(button_name, "click"):
                self.scene_logger.error(f"点击{lesson_type.value}课程按钮失败")
                return False

            # 等待课程执行
            time.sleep(1.0)

            # 处理可能的确认对话框
            if self._handle_confirmation_dialog(timeout=5.0):
                # 等待课程结果
                success = self._wait_for_lesson_result()

                if success:
                    self._record_action(ActionType.LESSON, lesson_type.value)
                    self._update_turn_info()
                    self.scene_logger.info(f"{lesson_type.value}课程完成")
                    return True
                else:
                    self.scene_logger.error(f"{lesson_type.value}课程执行失败")
                    return False
            else:
                self.scene_logger.error("处理确认对话框失败")
                return False

        except Exception as e:
            self.scene_logger.error(f"上课失败: {e}")
            return False

    def _check_lesson_prerequisites(self, lesson_type: LessonType) -> bool:
        """
        检查上课前置条件

        Args:
            lesson_type: 课程类型

        Returns:
            是否满足前置条件
        """
        try:
            # 检查体力是否足够
            current_stamina = self._current_stats.get("stamina", 0)
            if current_stamina < 10:  # 假设上课需要至少10点体力
                self.scene_logger.warning(f"体力不足，无法上{lesson_type.value}课程")
                return False

            # 检查是否已经完成育成
            if self.is_produce_complete():
                self.scene_logger.warning("育成已完成，无法继续上课")
                return False

            # 检查场景状态
            if not self.verify_scene_state():
                self.scene_logger.warning("场景状态验证失败，无法上课")
                return False

            return True

        except Exception as e:
            self.scene_logger.error(f"检查上课前置条件失败: {e}")
            return False
    
    def take_rest(self) -> bool:
        """休息"""
        try:
            self.scene_logger.info("开始休息")
            
            # 点击休息按钮
            if not self.interact_with_element("rest_button", "click"):
                self.scene_logger.error("点击休息按钮失败")
                return False
            
            # 等待休息执行
            time.sleep(1.0)
            
            # 处理确认对话框
            if self._handle_confirmation_dialog():
                # 等待休息结果
                success = self._wait_for_action_result()
                
                if success:
                    self._record_action(ActionType.REST)
                    self._update_turn_info()
                    self.scene_logger.info("休息完成")
                    return True
                else:
                    self.scene_logger.error("休息执行失败")
                    return False
            else:
                self.scene_logger.error("处理休息确认对话框失败")
                return False
                
        except Exception as e:
            self.scene_logger.error(f"休息失败: {e}")
            return False
    
    def go_outing(self) -> bool:
        """外出"""
        try:
            self.scene_logger.info("开始外出")
            
            # 点击外出按钮
            if not self.interact_with_element("outing_button", "click"):
                self.scene_logger.error("点击外出按钮失败")
                return False
            
            # 等待外出选择界面
            time.sleep(1.5)
            
            # 处理外出选择（简化实现）
            if self._handle_outing_selection():
                # 等待外出结果
                success = self._wait_for_action_result()
                
                if success:
                    self._record_action(ActionType.OUTING)
                    self._update_turn_info()
                    self.scene_logger.info("外出完成")
                    return True
                else:
                    self.scene_logger.error("外出执行失败")
                    return False
            else:
                self.scene_logger.error("处理外出选择失败")
                return False
                
        except Exception as e:
            self.scene_logger.error(f"外出失败: {e}")
            return False
    
    def _handle_confirmation_dialog(self, timeout: float = 3.0) -> bool:
        """
        处理确认对话框

        Args:
            timeout: 等待确认按钮的超时时间

        Returns:
            是否成功处理确认对话框
        """
        try:
            start_time = time.time()

            # 等待确认按钮出现
            while time.time() - start_time < timeout:
                confirm_button = self.get_ui_element("confirm_button")
                if confirm_button and confirm_button.is_visible():
                    success = self.interact_with_element("confirm_button", "click")
                    if success:
                        self.scene_logger.debug("成功处理确认对话框")
                        return True
                    else:
                        self.scene_logger.warning("点击确认按钮失败")
                        return False

                time.sleep(0.1)

            # 如果没有确认对话框出现，认为不需要确认
            self.scene_logger.debug("未检测到确认对话框，继续执行")
            return True

        except Exception as e:
            self.scene_logger.error(f"处理确认对话框异常: {e}")
            return False
    
    def _handle_outing_selection(self) -> bool:
        """处理外出选择"""
        try:
            # 简化实现：选择第一个外出选项
            time.sleep(1.0)
            
            # 假设有外出选择按钮
            choice_button = self.get_ui_element("choice_1_button")
            if choice_button and choice_button.is_visible():
                return self.interact_with_element("choice_1_button", "click")
            
            # 如果没有选择，直接确认
            return self._handle_confirmation_dialog()
            
        except Exception as e:
            self.scene_logger.error(f"处理外出选择失败: {e}")
            return False
    
    def _wait_for_lesson_result(self) -> bool:
        """等待课程结果"""
        try:
            # 等待课程动画和结果显示
            time.sleep(3.0)
            
            # 检查是否有事件对话框
            if self._handle_event_dialog():
                return True
            
            # 如果没有事件，直接返回成功
            return True
            
        except Exception as e:
            self.scene_logger.error(f"等待课程结果失败: {e}")
            return False
    
    def _wait_for_action_result(self) -> bool:
        """等待行动结果"""
        try:
            # 等待行动动画和结果显示
            time.sleep(2.0)
            
            # 检查是否有事件对话框
            if self._handle_event_dialog():
                return True
            
            return True
            
        except Exception as e:
            self.scene_logger.error(f"等待行动结果失败: {e}")
            return False
    
    def _handle_event_dialog(self) -> bool:
        """处理事件对话框"""
        try:
            event_dialog = self.get_ui_element("event_dialog")
            if event_dialog and event_dialog.is_visible():
                self.scene_logger.info("检测到事件对话框")
                
                # 读取事件内容
                event_text = event_dialog.read_text()
                if event_text:
                    self.scene_logger.debug(f"事件内容: {event_text[:50]}...")
                
                # 处理事件选择
                return self._handle_event_choices()
            
            return True
            
        except Exception as e:
            self.scene_logger.error(f"处理事件对话框失败: {e}")
            return False
    
    def _handle_event_choices(self) -> bool:
        """处理事件选择"""
        try:
            # 检查是否有选择肢
            for i in range(1, 4):
                choice_button = self.get_ui_element(f"choice_{i}_button")
                if choice_button and choice_button.is_visible():
                    # 简化实现：选择第一个可见的选择肢
                    if self.interact_with_element(f"choice_{i}_button", "click"):
                        time.sleep(1.0)
                        return True
            
            # 如果没有选择肢，尝试点击确认或跳过
            if self.interact_with_element("confirm_button", "click"):
                return True
            
            if self.interact_with_element("skip_button", "click"):
                return True
            
            return True
            
        except Exception as e:
            self.scene_logger.error(f"处理事件选择失败: {e}")
            return False
    
    def _record_action(self, action_type: ActionType, detail: str = ""):
        """记录行动"""
        action_record = {
            "turn": self._current_turn,
            "action_type": action_type.value,
            "detail": detail,
            "timestamp": time.time()
        }
        
        self._action_history.append(action_record)
        self._last_action = action_record
        
        # 保持历史记录在合理范围内
        if len(self._action_history) > 100:
            self._action_history = self._action_history[-100:]
    
    def _update_turn_info(self):
        """更新回合信息"""
        try:
            # 读取回合数
            turn_counter = self.get_ui_element("turn_counter")
            if turn_counter:
                turn_text = turn_counter.read_text()
                if turn_text:
                    import re
                    turn_match = re.search(r'(\d+)/(\d+)', turn_text)
                    if turn_match:
                        self._current_turn = int(turn_match.group(1))
                        self._max_turns = int(turn_match.group(2))
            
            # 更新属性值
            self._update_stats()
            
            self.scene_logger.debug(f"回合信息更新: {self._current_turn}/{self._max_turns}")
            
        except Exception as e:
            self.scene_logger.error(f"更新回合信息失败: {e}")
    
    def _update_stats(self):
        """更新属性值"""
        try:
            # 更新各项属性
            for stat in ["vocal", "dance", "visual", "mental"]:
                stat_element = self.get_ui_element(f"{stat}_stat")
                if stat_element:
                    stat_text = stat_element.read_text()
                    if stat_text:
                        import re
                        stat_match = re.search(r'\d+', stat_text)
                        if stat_match:
                            self._current_stats[stat] = int(stat_match.group())
            
            # 更新体力
            stamina_element = self.get_ui_element("stamina_stat")
            if stamina_element:
                stamina_text = stamina_element.read_text()
                if stamina_text:
                    import re
                    stamina_match = re.search(r'(\d+)/(\d+)', stamina_text)
                    if stamina_match:
                        self._current_stats["stamina"] = int(stamina_match.group(1))
            
            self._stats_updated = True
            
        except Exception as e:
            self.scene_logger.error(f"更新属性值失败: {e}")
    
    def get_current_stats(self) -> Dict[str, int]:
        """获取当前属性值"""
        return self._current_stats.copy()
    
    def get_turn_info(self) -> Tuple[int, int]:
        """获取回合信息"""
        return self._current_turn, self._max_turns
    
    def get_action_history(self) -> List[Dict[str, Any]]:
        """获取行动历史"""
        return self._action_history.copy()
    
    def is_produce_complete(self) -> bool:
        """检查育成是否完成"""
        return self._current_turn >= self._max_turns
    
    def get_produce_progress(self) -> float:
        """获取育成进度"""
        if self._max_turns <= 0:
            return 0.0
        return min(1.0, self._current_turn / self._max_turns)

    def get_remaining_turns(self) -> int:
        """获取剩余回合数"""
        return max(0, self._max_turns - self._current_turn)

    def get_stamina_percentage(self) -> float:
        """获取体力百分比"""
        current_stamina = self._current_stats.get("stamina", 0)
        if current_stamina <= 0:
            return 0.0
        # 假设最大体力为100
        return min(1.0, current_stamina / 100.0)

    def is_low_stamina(self, threshold: float = 0.3) -> bool:
        """检查是否体力不足"""
        return self.get_stamina_percentage() < threshold

    def should_rest(self) -> bool:
        """建议是否应该休息"""
        return self.is_low_stamina() or self._current_stats.get("motivation", 3) < 2

    def get_recommended_action(self) -> str:
        """获取推荐行动"""
        try:
            # 如果体力不足，建议休息
            if self.is_low_stamina(0.2):
                return "rest"

            # 如果干劲不足，建议外出
            if self._current_stats.get("motivation", 3) < 2:
                return "outing"

            # 根据属性值推荐课程
            stats = self._current_stats
            min_stat = min(stats.get("vocal", 0), stats.get("dance", 0),
                          stats.get("visual", 0), stats.get("mental", 0))

            # 推荐最低的属性对应的课程
            for stat_name in ["vocal", "dance", "visual", "mental"]:
                if stats.get(stat_name, 0) == min_stat:
                    return f"{stat_name}_lesson"

            return "vocal_lesson"  # 默认推荐声乐课程

        except Exception as e:
            self.scene_logger.error(f"获取推荐行动失败: {e}")
            return "rest"

    def execute_recommended_action(self) -> bool:
        """执行推荐行动"""
        try:
            recommended = self.get_recommended_action()

            if recommended == "rest":
                return self.take_rest()
            elif recommended == "outing":
                return self.go_outing()
            elif recommended.endswith("_lesson"):
                lesson_type_str = recommended.replace("_lesson", "")
                lesson_type = LessonType(lesson_type_str)
                return self.take_lesson(lesson_type)
            else:
                self.scene_logger.warning(f"未知的推荐行动: {recommended}")
                return False

        except Exception as e:
            self.scene_logger.error(f"执行推荐行动失败: {e}")
            return False

    def get_scene_summary(self) -> Dict[str, Any]:
        """获取场景摘要信息"""
        return {
            "current_turn": self._current_turn,
            "max_turns": self._max_turns,
            "remaining_turns": self.get_remaining_turns(),
            "progress": self.get_produce_progress(),
            "stats": self._current_stats.copy(),
            "stamina_percentage": self.get_stamina_percentage(),
            "is_low_stamina": self.is_low_stamina(),
            "should_rest": self.should_rest(),
            "recommended_action": self.get_recommended_action(),
            "last_action": self._last_action,
            "total_actions": len(self._action_history),
            "scene_ready": self.is_scene_ready()
        }
