"""
测试导航框架的基本功能
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import unittest
from unittest.mock import Mock, MagicMock, patch
from datetime import datetime

from src.modules.scheduler.game_tasks import GameNavigationTasks, NavigationStep
from src.core.data_structures import GameState, GameScene, Action, ActionType
from src.utils.config_loader import ConfigLoader


class TestNavigationFramework(unittest.TestCase):
    """测试导航框架"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建模拟的模块
        self.mock_perception = Mock()
        self.mock_decision = Mock()
        self.mock_action = Mock()
        self.mock_config_loader = Mock()
        
        # 创建导航任务实例
        self.navigation_tasks = GameNavigationTasks(
            perception_module=self.mock_perception,
            decision_module=self.mock_decision,
            action_controller=self.mock_action,
            config_loader=self.mock_config_loader
        )
        
        # 设置默认的模拟返回值
        self.mock_config_loader.load_settings.return_value = {
            'navigation': {
                'ui_elements': {
                    'main_menu': {
                        'produce_button': {'x': 500, 'y': 400, 'template': 'produce_button.png'}
                    }
                },
                'timeouts': {
                    'scene_recognition': 10.0,
                    'action_execution': 5.0,
                    'scene_transition': 15.0
                },
                'retry_settings': {
                    'max_retries': 3,
                    'retry_delay': 2.0
                }
            }
        }
    
    def test_navigation_initialization(self):
        """测试导航任务初始化"""
        self.assertIsNotNone(self.navigation_tasks.perception)
        self.assertIsNotNone(self.navigation_tasks.decision)
        self.assertIsNotNone(self.navigation_tasks.action)
        self.assertIsNotNone(self.navigation_tasks.config_loader)
        
        self.assertEqual(self.navigation_tasks.current_step, NavigationStep.SCENE_RECOGNITION)
        self.assertEqual(len(self.navigation_tasks.navigation_history), 0)
    
    def test_load_navigation_config(self):
        """测试加载导航配置"""
        self.navigation_tasks._load_navigation_config()
        
        self.assertIsNotNone(self.navigation_tasks._navigation_config)
        self.assertIn('ui_elements', self.navigation_tasks._navigation_config)
        self.assertIn('timeouts', self.navigation_tasks._navigation_config)
        self.assertIn('retry_settings', self.navigation_tasks._navigation_config)
    
    def test_recognize_current_scene_success(self):
        """测试场景识别成功"""
        # 设置模拟返回值
        mock_game_state = GameState(
            current_scene=GameScene.MAIN_MENU,
            stamina=100,
            vigor=100,
            score=0,
            current_week=0,
            hand=[],
            deck_size=0,
            discard_size=0
        )
        self.mock_perception.get_game_state.return_value = mock_game_state
        
        # 执行测试
        result = self.navigation_tasks._recognize_current_scene()
        
        # 验证结果
        self.assertTrue(result)
        self.assertEqual(self.navigation_tasks._current_scene, GameScene.MAIN_MENU)
        self.mock_perception.get_game_state.assert_called_once()
    
    def test_recognize_current_scene_unknown(self):
        """测试场景识别为未知"""
        # 设置模拟返回值
        mock_game_state = GameState(
            current_scene=GameScene.UNKNOWN,
            stamina=100,
            vigor=100,
            score=0,
            current_week=0,
            hand=[],
            deck_size=0,
            discard_size=0
        )
        self.mock_perception.get_game_state.return_value = mock_game_state
        
        # 执行测试
        result = self.navigation_tasks._recognize_current_scene()
        
        # 验证结果
        self.assertFalse(result)
    
    def test_plan_navigation_path_main_menu_to_produce(self):
        """测试从主菜单到育成准备的路径规划"""
        # 设置前置条件
        self.navigation_tasks._current_scene = GameScene.MAIN_MENU
        self.navigation_tasks._load_navigation_config()
        
        # 执行测试
        result = self.navigation_tasks._plan_navigation_path()
        
        # 验证结果
        self.assertTrue(result)
        self.assertIsNotNone(self.navigation_tasks._navigation_path)
        self.assertEqual(len(self.navigation_tasks._navigation_path), 1)
        
        step = self.navigation_tasks._navigation_path[0]
        self.assertEqual(step['action_type'], 'click')
        self.assertEqual(step['target_element'], 'produce_button')
        self.assertEqual(step['expected_scene'], GameScene.PRODUCE_SETUP)
    
    def test_plan_navigation_path_already_at_target(self):
        """测试已经在目标场景的路径规划"""
        # 设置前置条件
        self.navigation_tasks._current_scene = GameScene.PRODUCE_SETUP
        self.navigation_tasks._load_navigation_config()
        
        # 执行测试
        result = self.navigation_tasks._plan_navigation_path()
        
        # 验证结果
        self.assertTrue(result)
        self.assertEqual(len(self.navigation_tasks._navigation_path), 0)
    
    def test_get_element_coordinates_from_config(self):
        """测试从配置获取元素坐标"""
        # 设置前置条件
        self.navigation_tasks._load_navigation_config()
        
        # 执行测试
        coords = self.navigation_tasks._get_element_coordinates('produce_button')
        
        # 验证结果
        self.assertIsNotNone(coords)
        self.assertEqual(coords, (500, 400))
    
    def test_get_element_coordinates_not_found(self):
        """测试获取不存在的元素坐标"""
        # 设置前置条件
        self.navigation_tasks._load_navigation_config()
        
        # 执行测试
        coords = self.navigation_tasks._get_element_coordinates('nonexistent_button')
        
        # 验证结果
        self.assertIsNone(coords)
    
    def test_execute_single_navigation_action_success(self):
        """测试执行单个导航操作成功"""
        # 设置前置条件
        self.navigation_tasks._load_navigation_config()
        self.mock_action.execute_and_verify.return_value = True
        
        step = {
            'action_type': 'click',
            'target_element': 'produce_button',
            'description': '点击育成按钮'
        }
        
        # 执行测试
        result = self.navigation_tasks._execute_single_navigation_action(step)
        
        # 验证结果
        self.assertTrue(result)
        self.mock_action.execute_and_verify.assert_called_once()
        
        # 验证传递给action的参数
        call_args = self.mock_action.execute_and_verify.call_args[0]
        action = call_args[0]
        self.assertEqual(action.action_type, ActionType.CLICK)
        self.assertEqual(action.target, (500, 400))
    
    def test_verify_navigation_result_success(self):
        """测试导航结果验证成功"""
        # 设置模拟返回值
        mock_game_state = GameState(
            current_scene=GameScene.PRODUCE_SETUP,
            stamina=100,
            vigor=100,
            score=0,
            current_week=0,
            hand=[],
            deck_size=0,
            discard_size=0
        )
        self.mock_perception.get_game_state.return_value = mock_game_state
        
        # 执行测试
        result = self.navigation_tasks._verify_navigation_result()
        
        # 验证结果
        self.assertTrue(result)
    
    def test_verify_navigation_result_failure(self):
        """测试导航结果验证失败"""
        # 设置模拟返回值
        mock_game_state = GameState(
            current_scene=GameScene.MAIN_MENU,  # 不是期望的场景
            stamina=100,
            vigor=100,
            score=0,
            current_week=0,
            hand=[],
            deck_size=0,
            discard_size=0
        )
        self.mock_perception.get_game_state.return_value = mock_game_state
        
        # 执行测试
        result = self.navigation_tasks._verify_navigation_result()
        
        # 验证结果
        self.assertFalse(result)
    
    @patch('time.sleep')  # 模拟sleep以加速测试
    def test_navigate_to_produce_start_success(self, mock_sleep):
        """测试完整导航流程成功"""
        # 设置模拟返回值
        mock_game_state = GameState(
            current_scene=GameScene.MAIN_MENU,
            stamina=100,
            vigor=100,
            score=0,
            current_week=0,
            hand=[],
            deck_size=0,
            discard_size=0
        )
        self.mock_perception.get_game_state.return_value = mock_game_state
        self.mock_action.execute_and_verify.return_value = True
        
        # 模拟场景切换
        def side_effect(*args, **kwargs):
            # 第一次调用返回主菜单，后续调用返回育成准备界面
            if self.mock_perception.get_game_state.call_count <= 2:
                return GameState(
                    current_scene=GameScene.MAIN_MENU,
                    stamina=100, vigor=100, score=0, current_week=0,
                    hand=[], deck_size=0, discard_size=0
                )
            else:
                return GameState(
                    current_scene=GameScene.PRODUCE_SETUP,
                    stamina=100, vigor=100, score=0, current_week=0,
                    hand=[], deck_size=0, discard_size=0
                )
        
        self.mock_perception.get_game_state.side_effect = side_effect
        
        # 执行测试
        result = self.navigation_tasks.navigate_to_produce_start()
        
        # 验证结果
        self.assertTrue(result)
        self.assertEqual(self.navigation_tasks.current_step, NavigationStep.COMPLETED)
        self.assertGreater(len(self.navigation_tasks.navigation_history), 0)


if __name__ == '__main__':
    unittest.main()
