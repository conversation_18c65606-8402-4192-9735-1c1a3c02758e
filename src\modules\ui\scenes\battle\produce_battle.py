"""
育成战斗场景实现
处理育成过程中的战斗准备、策略选择和实时战斗监控
"""

import time
from typing import List, Dict, Any, Optional, Tuple
from enum import Enum

from .....core.data_structures import GameScene
from ...config.scene_config import SceneConfig
from ...config.ui_element_config import ButtonConfig, LabelConfig
from ..base_game_scene import BaseGameScene
from ...utils.performance_monitor import measure_block
from ...utils.scene_optimizer import get_scene_optimizer


class BattleType(Enum):
    """战斗类型枚举"""
    TRAINING_BATTLE = "training_battle"    # 训练战斗
    COMPETITION = "competition"            # 比赛
    SPECIAL_EVENT = "special_event"        # 特殊事件战斗
    FINAL_BATTLE = "final_battle"          # 最终战斗


class BattleStrategy(Enum):
    """战斗策略枚举"""
    AGGRESSIVE = "aggressive"      # 激进策略
    DEFENSIVE = "defensive"        # 防守策略
    BALANCED = "balanced"          # 平衡策略
    ADAPTIVE = "adaptive"          # 自适应策略


class BattlePhase(Enum):
    """战斗阶段枚举"""
    PREPARATION = "preparation"    # 准备阶段
    BATTLE = "battle"             # 战斗阶段
    RESULT = "result"             # 结果阶段


class ProduceBattleScene(BaseGameScene):
    """育成战斗场景类"""
    
    def __init__(self, config: SceneConfig, perception_module, action_controller):
        """
        初始化育成战斗场景
        
        Args:
            config: 场景配置
            perception_module: 感知模块
            action_controller: 行动控制器
        """
        super().__init__(config, perception_module, action_controller)
        
        # 战斗状态
        self._battle_type: Optional[BattleType] = None
        self._battle_strategy: Optional[BattleStrategy] = None
        self._current_phase = BattlePhase.PREPARATION
        self._battle_progress = 0.0
        
        # 战斗数据
        self._player_stats = {
            "vocal": 0, "dance": 0, "visual": 0, "mental": 0,
            "stamina": 100, "motivation": 3
        }
        self._opponent_stats = {
            "vocal": 0, "dance": 0, "visual": 0, "mental": 0,
            "difficulty": 1
        }
        
        # 战斗历史
        self._battle_actions = []
        self._battle_events = []
        
        # 性能监控
        self.optimizer = get_scene_optimizer()
        
        self.scene_logger.info("育成战斗场景初始化完成")
    
    def _create_scene_ui_elements(self):
        """创建战斗场景特定的UI元素"""
        try:
            with measure_block("battle_ui_creation"):
                # 场景标识元素
                self._create_battle_indicators()
                
                # 战斗准备元素
                self._create_preparation_elements()
                
                # 战斗控制元素
                self._create_battle_controls()
                
                # 状态显示元素
                self._create_status_displays()
                
                # 战斗信息元素
                self._create_battle_info_elements()
            
            self.scene_logger.info(f"战斗场景UI元素创建完成，共{len(self.ui_elements)}个元素")
            
        except Exception as e:
            self.scene_logger.error(f"战斗场景UI元素创建失败: {e}")
            raise
    
    def _create_battle_indicators(self):
        """创建战斗标识元素"""
        # 战斗场景标题
        self.ui_elements["battle_scene_title"] = self.ui_factory.create_enhanced_label(
            "battle_scene_title",
            confidence_threshold=0.9,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
        
        # 战斗类型显示
        self.ui_elements["battle_type_label"] = self.ui_factory.create_label(
            "battle_type_label",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
        
        # 战斗阶段指示器
        self.ui_elements["battle_phase_indicator"] = self.ui_factory.create_label(
            "battle_phase_indicator",
            confidence_threshold=0.8,
            text_recognition_enabled=False
        )
    
    def _create_preparation_elements(self):
        """创建战斗准备元素"""
        # 策略选择按钮
        for strategy in BattleStrategy:
            button_name = f"{strategy.value}_strategy_button"
            self.ui_elements[button_name] = self.ui_factory.create_enhanced_button(
                button_name,
                confidence_threshold=0.8,
                timeout=5.0,
                verify_click_result=True
            )
        
        # 准备确认按钮
        self.ui_elements["preparation_confirm_button"] = self.ui_factory.create_enhanced_button(
            "preparation_confirm_button",
            confidence_threshold=0.9,
            timeout=5.0,
            verify_click_result=True
        )
        
        # 战斗开始按钮
        self.ui_elements["start_battle_button"] = self.ui_factory.create_enhanced_button(
            "start_battle_button",
            confidence_threshold=0.9,
            timeout=5.0,
            expected_scene_after_click="battle_active",
            verify_click_result=True
        )
    
    def _create_battle_controls(self):
        """创建战斗控制元素"""
        # 技能按钮
        for i in range(1, 5):  # 假设有4个技能
            skill_button = f"skill_{i}_button"
            self.ui_elements[skill_button] = self.ui_factory.create_enhanced_button(
                skill_button,
                confidence_threshold=0.8,
                timeout=3.0,
                verify_click_result=True
            )
        
        # 特殊行动按钮
        self.ui_elements["special_action_button"] = self.ui_factory.create_enhanced_button(
            "special_action_button",
            confidence_threshold=0.8,
            timeout=3.0,
            verify_click_result=True
        )
        
        # 自动战斗按钮
        self.ui_elements["auto_battle_button"] = self.ui_factory.create_button(
            "auto_battle_button",
            confidence_threshold=0.8,
            timeout=3.0
        )
        
        # 暂停按钮
        self.ui_elements["pause_button"] = self.ui_factory.create_button(
            "pause_button",
            confidence_threshold=0.8,
            timeout=3.0
        )
    
    def _create_status_displays(self):
        """创建状态显示元素"""
        # 玩家状态显示
        for stat in ["vocal", "dance", "visual", "mental"]:
            self.ui_elements[f"player_{stat}_stat"] = self.ui_factory.create_enhanced_label(
                f"player_{stat}_stat",
                confidence_threshold=0.8,
                text_recognition_enabled=True,
                ocr_language="en"
            )
        
        # 对手状态显示
        for stat in ["vocal", "dance", "visual", "mental"]:
            self.ui_elements[f"opponent_{stat}_stat"] = self.ui_factory.create_label(
                f"opponent_{stat}_stat",
                confidence_threshold=0.8,
                text_recognition_enabled=True,
                ocr_language="en"
            )
        
        # 战斗进度条
        self.ui_elements["battle_progress_bar"] = self.ui_factory.create_label(
            "battle_progress_bar",
            confidence_threshold=0.8,
            text_recognition_enabled=False
        )
        
        # 体力显示
        self.ui_elements["player_stamina"] = self.ui_factory.create_enhanced_label(
            "player_stamina",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="en"
        )
    
    def _create_battle_info_elements(self):
        """创建战斗信息元素"""
        # 战斗日志
        self.ui_elements["battle_log"] = self.ui_factory.create_enhanced_label(
            "battle_log",
            confidence_threshold=0.7,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
        
        # 当前回合显示
        self.ui_elements["current_turn_label"] = self.ui_factory.create_label(
            "current_turn_label",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="en"
        )
        
        # 战斗结果预览
        self.ui_elements["battle_result_preview"] = self.ui_factory.create_label(
            "battle_result_preview",
            confidence_threshold=0.7,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
    
    def _get_critical_elements(self) -> List[str]:
        """获取战斗场景的关键UI元素"""
        return [
            "battle_scene_title",
            "battle_phase_indicator",
            "start_battle_button",
            "player_vocal_stat",
            "battle_progress_bar"
        ]
    
    def _verify_scene_specific_state(self) -> bool:
        """验证战斗场景特定状态"""
        try:
            # 验证战斗场景标题可见
            title_element = self.get_ui_element("battle_scene_title")
            if not title_element or not title_element.is_visible():
                self.scene_logger.debug("战斗场景标题不可见")
                return False
            
            # 验证战斗阶段指示器
            phase_element = self.get_ui_element("battle_phase_indicator")
            if not phase_element or not phase_element.is_visible():
                self.scene_logger.debug("战斗阶段指示器不可见")
                return False
            
            # 根据当前阶段验证相应元素
            if self._current_phase == BattlePhase.PREPARATION:
                return self._verify_preparation_elements()
            elif self._current_phase == BattlePhase.BATTLE:
                return self._verify_battle_elements()
            
            return True
            
        except Exception as e:
            self.scene_logger.error(f"战斗场景状态验证异常: {e}")
            return False
    
    def _verify_preparation_elements(self) -> bool:
        """验证准备阶段元素"""
        strategy_buttons = [f"{s.value}_strategy_button" for s in BattleStrategy]
        visible_buttons = 0
        
        for button_name in strategy_buttons:
            button = self.get_ui_element(button_name)
            if button and button.is_visible():
                visible_buttons += 1
        
        return visible_buttons >= 2  # 至少2个策略按钮可见
    
    def _verify_battle_elements(self) -> bool:
        """验证战斗阶段元素"""
        skill_buttons = [f"skill_{i}_button" for i in range(1, 5)]
        visible_skills = 0
        
        for button_name in skill_buttons:
            button = self.get_ui_element(button_name)
            if button and button.is_visible():
                visible_skills += 1
        
        return visible_skills >= 2  # 至少2个技能按钮可见
    
    def select_battle_strategy(self, strategy: BattleStrategy) -> bool:
        """
        选择战斗策略
        
        Args:
            strategy: 战斗策略
            
        Returns:
            是否选择成功
        """
        try:
            self.scene_logger.info(f"选择战斗策略: {strategy.value}")
            
            # 记录场景使用
            self.optimizer.record_ui_element_usage(GameScene.PRODUCE_BATTLE, f"{strategy.value}_strategy_button")
            
            button_name = f"{strategy.value}_strategy_button"
            
            if not self.interact_with_element(button_name, "click"):
                self.scene_logger.error(f"点击{strategy.value}策略按钮失败")
                return False
            
            # 等待策略确认
            time.sleep(1.0)
            
            # 确认策略选择
            if self.interact_with_element("preparation_confirm_button", "click"):
                self._battle_strategy = strategy
                self.scene_logger.info(f"战斗策略选择成功: {strategy.value}")
                return True
            else:
                self.scene_logger.error("确认策略选择失败")
                return False
                
        except Exception as e:
            self.scene_logger.error(f"选择战斗策略失败: {e}")
            return False
    
    def start_battle(self) -> bool:
        """开始战斗"""
        try:
            self.scene_logger.info("开始战斗")
            
            # 检查是否已选择策略
            if not self._battle_strategy:
                self.scene_logger.warning("未选择战斗策略，使用默认平衡策略")
                if not self.select_battle_strategy(BattleStrategy.BALANCED):
                    return False
            
            # 点击开始战斗按钮
            if not self.interact_with_element("start_battle_button", "click"):
                self.scene_logger.error("点击开始战斗按钮失败")
                return False
            
            # 更新战斗阶段
            self._current_phase = BattlePhase.BATTLE
            
            # 等待战斗界面加载
            time.sleep(2.0)
            
            # 验证战斗界面
            if self._verify_battle_elements():
                self.scene_logger.info("战斗开始成功")
                return True
            else:
                self.scene_logger.error("战斗界面验证失败")
                return False
                
        except Exception as e:
            self.scene_logger.error(f"开始战斗失败: {e}")
            return False
    
    def use_skill(self, skill_number: int) -> bool:
        """
        使用技能
        
        Args:
            skill_number: 技能编号 (1-4)
            
        Returns:
            是否使用成功
        """
        try:
            if not 1 <= skill_number <= 4:
                self.scene_logger.error(f"无效的技能编号: {skill_number}")
                return False
            
            self.scene_logger.info(f"使用技能: {skill_number}")
            
            skill_button = f"skill_{skill_number}_button"
            
            # 记录UI元素使用
            self.optimizer.record_ui_element_usage(GameScene.PRODUCE_BATTLE, skill_button)
            
            if not self.interact_with_element(skill_button, "click"):
                self.scene_logger.error(f"点击技能{skill_number}按钮失败")
                return False
            
            # 等待技能执行
            time.sleep(1.5)
            
            # 记录战斗行动
            self._battle_actions.append({
                "type": "skill",
                "skill_number": skill_number,
                "timestamp": time.time()
            })
            
            self.scene_logger.info(f"技能{skill_number}使用成功")
            return True
            
        except Exception as e:
            self.scene_logger.error(f"使用技能失败: {e}")
            return False
    
    def enable_auto_battle(self) -> bool:
        """启用自动战斗"""
        try:
            self.scene_logger.info("启用自动战斗")
            
            if not self.interact_with_element("auto_battle_button", "click"):
                self.scene_logger.error("点击自动战斗按钮失败")
                return False
            
            time.sleep(0.5)
            self.scene_logger.info("自动战斗已启用")
            return True
            
        except Exception as e:
            self.scene_logger.error(f"启用自动战斗失败: {e}")
            return False
    
    def pause_battle(self) -> bool:
        """暂停战斗"""
        try:
            self.scene_logger.info("暂停战斗")
            
            if not self.interact_with_element("pause_button", "click"):
                self.scene_logger.error("点击暂停按钮失败")
                return False
            
            time.sleep(0.5)
            self.scene_logger.info("战斗已暂停")
            return True
            
        except Exception as e:
            self.scene_logger.error(f"暂停战斗失败: {e}")
            return False
    
    def get_battle_status(self) -> Dict[str, Any]:
        """获取战斗状态"""
        try:
            # 更新战斗进度
            self._update_battle_progress()
            
            return {
                "battle_type": self._battle_type.value if self._battle_type else None,
                "battle_strategy": self._battle_strategy.value if self._battle_strategy else None,
                "current_phase": self._current_phase.value,
                "battle_progress": self._battle_progress,
                "player_stats": self._player_stats.copy(),
                "opponent_stats": self._opponent_stats.copy(),
                "battle_actions_count": len(self._battle_actions),
                "battle_events_count": len(self._battle_events)
            }
            
        except Exception as e:
            self.scene_logger.error(f"获取战斗状态失败: {e}")
            return {}
    
    def _update_battle_progress(self):
        """更新战斗进度"""
        try:
            # 读取战斗进度条
            progress_element = self.get_ui_element("battle_progress_bar")
            if progress_element:
                # 这里可以通过图像识别或OCR来获取进度
                # 简化实现，基于战斗行动数量估算进度
                self._battle_progress = min(1.0, len(self._battle_actions) / 10.0)
            
            # 更新玩家状态
            self._update_player_stats()
            
        except Exception as e:
            self.scene_logger.error(f"更新战斗进度失败: {e}")
    
    def _update_player_stats(self):
        """更新玩家状态"""
        try:
            # 读取玩家属性值
            for stat in ["vocal", "dance", "visual", "mental"]:
                stat_element = self.get_ui_element(f"player_{stat}_stat")
                if stat_element:
                    stat_text = stat_element.read_text()
                    if stat_text:
                        import re
                        stat_match = re.search(r'\d+', stat_text)
                        if stat_match:
                            self._player_stats[stat] = int(stat_match.group())
            
            # 读取体力值
            stamina_element = self.get_ui_element("player_stamina")
            if stamina_element:
                stamina_text = stamina_element.read_text()
                if stamina_text:
                    import re
                    stamina_match = re.search(r'(\d+)/(\d+)', stamina_text)
                    if stamina_match:
                        self._player_stats["stamina"] = int(stamina_match.group(1))
            
        except Exception as e:
            self.scene_logger.error(f"更新玩家状态失败: {e}")
    
    def is_battle_complete(self) -> bool:
        """检查战斗是否完成"""
        return self._battle_progress >= 1.0 or self._current_phase == BattlePhase.RESULT
    
    def get_battle_summary(self) -> Dict[str, Any]:
        """获取战斗总结"""
        return {
            "battle_status": self.get_battle_status(),
            "total_actions": len(self._battle_actions),
            "battle_duration": time.time() - (self._battle_actions[0]["timestamp"] if self._battle_actions else time.time()),
            "strategy_used": self._battle_strategy.value if self._battle_strategy else "none",
            "scene_ready": self.is_scene_ready(),
            "battle_complete": self.is_battle_complete()
        }
