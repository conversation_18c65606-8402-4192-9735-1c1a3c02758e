# 游戏截图数据收集工具 API 参考

**版本：** 1.0  
**日期：** 2025年7月27日  
**基础URL：** `http://localhost:8000/api/v1`

## 一、API 概览

### 1.1 认证
当前版本不需要认证，所有 API 端点都可以直接访问。

### 1.2 响应格式
所有 API 响应都使用 JSON 格式，包含以下通用字段：
```json
{
  "success": true,
  "message": "操作成功",
  "data": {...},
  "timestamp": "2025-07-27T14:30:22.123456"
}
```

### 1.3 错误处理
错误响应格式：
```json
{
  "success": false,
  "error": "错误描述",
  "error_code": "ERROR_CODE",
  "timestamp": "2025-07-27T14:30:22.123456"
}
```

## 二、截图相关 API

### 2.1 获取截图预览

**端点：** `GET /screenshot/preview`

**描述：** 获取当前游戏窗口的预览图像

**响应：** 
- **Content-Type：** `image/jpeg`
- **Body：** JPEG 格式的图像数据

**示例：**
```bash
curl -X GET http://localhost:8000/api/v1/screenshot/preview \
  --output preview.jpg
```

### 2.2 执行截图

**端点：** `POST /screenshot/capture`

**描述：** 执行截图操作

**请求体：**
```json
{
  "config": {
    "mode": "window",
    "format": "png",
    "quality": 90,
    "region": {
      "x": 100,
      "y": 100,
      "width": 800,
      "height": 600
    },
    "save_to_disk": true,
    "filename_prefix": "screenshot"
  }
}
```

**参数说明：**
- `mode`: 截图模式 (`"fullscreen"` | `"window"` | `"region"`)
- `format`: 图片格式 (`"png"` | `"jpeg"` | `"bmp"`)
- `quality`: 图片质量 (1-100，仅对 JPEG 有效)
- `region`: 区域坐标 (仅在 region 模式下需要)
- `save_to_disk`: 是否保存到磁盘
- `filename_prefix`: 文件名前缀

**响应：**
```json
{
  "id": "uuid-string",
  "filename": "screenshot_20250127_143022_a1b2c3d4.png",
  "filepath": "/path/to/screenshot.png",
  "config": {...},
  "timestamp": "2025-07-27T14:30:22.123456",
  "file_size": 1024000,
  "image_size": {
    "width": 1920,
    "height": 1080
  },
  "success": true,
  "error_message": null
}
```

**示例：**
```bash
curl -X POST http://localhost:8000/api/v1/screenshot/capture \
  -H "Content-Type: application/json" \
  -d '{
    "config": {
      "mode": "window",
      "format": "png",
      "quality": 90,
      "save_to_disk": true,
      "filename_prefix": "test"
    }
  }'
```

### 2.3 获取截图历史

**端点：** `GET /screenshot/history`

**描述：** 获取截图历史记录

**查询参数：**
- `limit`: 返回记录数量限制 (默认: 100)

**响应：**
```json
{
  "total": 150,
  "records": [
    {
      "id": "uuid-string",
      "filename": "screenshot_20250127_143022_a1b2c3d4.png",
      "filepath": "/path/to/screenshot.png",
      "thumbnail_path": "/path/to/thumbnail.png",
      "timestamp": "2025-07-27T14:30:22.123456",
      "file_size": 1024000,
      "image_size": {
        "width": 1920,
        "height": 1080
      },
      "config": {
        "mode": "window",
        "format": "png",
        "quality": 90
      }
    }
  ]
}
```

**示例：**
```bash
curl -X GET "http://localhost:8000/api/v1/screenshot/history?limit=50"
```

### 2.4 删除截图

**端点：** `DELETE /screenshot/{screenshot_id}`

**描述：** 删除指定的截图

**路径参数：**
- `screenshot_id`: 截图 ID

**响应：**
```json
{
  "success": true,
  "message": "截图已删除"
}
```

**示例：**
```bash
curl -X DELETE http://localhost:8000/api/v1/screenshot/uuid-string
```

### 2.5 批量删除截图

**端点：** `POST /screenshot/batch-delete`

**描述：** 批量删除多个截图

**请求体：**
```json
{
  "ids": ["uuid-1", "uuid-2", "uuid-3"]
}
```

**响应：**
```json
{
  "total": 3,
  "success_count": 2,
  "failed_count": 1,
  "failed_ids": ["uuid-3"],
  "errors": ["截图 uuid-3 不存在"]
}
```

**示例：**
```bash
curl -X POST http://localhost:8000/api/v1/screenshot/batch-delete \
  -H "Content-Type: application/json" \
  -d '{"ids": ["uuid-1", "uuid-2"]}'
```

### 2.6 下载截图

**端点：** `GET /screenshot/{screenshot_id}/download`

**描述：** 下载指定的截图文件

**路径参数：**
- `screenshot_id`: 截图 ID

**响应：**
- **Content-Type：** `image/png` | `image/jpeg` | `image/bmp`
- **Content-Disposition：** `attachment; filename="screenshot.png"`
- **Body：** 图像文件数据

**示例：**
```bash
curl -X GET http://localhost:8000/api/v1/screenshot/uuid-string/download \
  --output downloaded_screenshot.png
```

### 2.7 获取截图统计

**端点：** `GET /screenshot/stats`

**描述：** 获取截图统计信息

**响应：**
```json
{
  "total_screenshots": 150,
  "total_size_bytes": 157286400,
  "total_size_mb": 150.0,
  "preview_active": false,
  "storage_directory": "/path/to/screenshots",
  "capture_stats": {
    "success_rate": 0.95,
    "average_size": 1048576,
    "formats": {
      "png": 100,
      "jpeg": 45,
      "bmp": 5
    }
  }
}
```

**示例：**
```bash
curl -X GET http://localhost:8000/api/v1/screenshot/stats
```

### 2.8 获取截图配置

**端点：** `GET /screenshot/config`

**描述：** 获取当前截图工具配置

**响应：**
```json
{
  "game_window": {
    "window_title": "gakumas",
    "enable_background_mode": true,
    "capture_method": "auto"
  },
  "storage": {
    "base_directory": "screenshots",
    "max_history_size": 1000,
    "auto_cleanup": true,
    "cleanup_days": 30
  },
  "preview_fps": 2,
  "default_quality": 90,
  "default_format": "png"
}
```

### 2.9 更新截图配置

**端点：** `POST /screenshot/config`

**描述：** 更新截图工具配置

**请求体：** 与获取配置响应格式相同

**响应：**
```json
{
  "success": true,
  "message": "配置已更新",
  "data": {...}
}
```

## 三、WebSocket API

### 3.1 连接端点
**URL：** `ws://localhost:8000/ws`

### 3.2 消息格式
所有 WebSocket 消息都使用 JSON 格式：
```json
{
  "type": "message_type",
  "data": {...},
  "timestamp": "2025-07-27T14:30:22.123456"
}
```

### 3.3 客户端消息

#### 3.3.1 心跳检测
```json
{
  "type": "ping"
}
```

#### 3.3.2 获取系统状态
```json
{
  "type": "get_status"
}
```

#### 3.3.3 启动预览流
```json
{
  "type": "start_preview"
}
```

#### 3.3.4 停止预览流
```json
{
  "type": "stop_preview"
}
```

#### 3.3.5 获取截图统计
```json
{
  "type": "get_screenshot_stats"
}
```

#### 3.3.6 获取截图历史
```json
{
  "type": "get_screenshot_history",
  "limit": 50
}
```

### 3.4 服务端消息

#### 3.4.1 连接建立
```json
{
  "type": "connection_established",
  "data": {
    "message": "WebSocket连接已建立",
    "server_version": "1.0.0"
  }
}
```

#### 3.4.2 心跳响应
```json
{
  "type": "pong",
  "timestamp": "2025-07-27T14:30:22.123456"
}
```

#### 3.4.3 预览数据
```json
{
  "type": "screenshot_preview",
  "data": {
    "image_data": "base64-encoded-jpeg-data",
    "timestamp": "2025-07-27T14:30:22.123456",
    "frame_count": 123,
    "fps": 2.1,
    "data_size": 51200
  }
}
```

#### 3.4.4 截图任务状态
```json
{
  "type": "screenshot_task_started",
  "data": {
    "task_id": "task-uuid",
    "config": {...}
  }
}
```

```json
{
  "type": "screenshot_task_completed",
  "data": {
    "task_id": "task-uuid",
    "result": {...}
  }
}
```

```json
{
  "type": "screenshot_task_failed",
  "data": {
    "task_id": "task-uuid",
    "error": "错误描述"
  }
}
```

## 四、系统相关 API

### 4.1 健康检查

**端点：** `GET /health`

**描述：** 检查系统健康状态

**响应：**
```json
{
  "status": "healthy",
  "timestamp": "2025-07-27T14:30:22.123456",
  "version": "1.0.0",
  "uptime": 3600.0,
  "components": {
    "screenshot_collector": "ok",
    "websocket": "ok",
    "storage": "ok"
  }
}
```

### 4.2 获取 WebSocket 统计

**端点：** `GET /websocket/stats`

**描述：** 获取 WebSocket 连接统计信息

**响应：**
```json
{
  "success": true,
  "data": {
    "total_connections": 10,
    "active_connections": 2,
    "messages_sent": 1500,
    "messages_received": 800,
    "errors": 5,
    "uptime_seconds": 3600.0
  }
}
```

## 五、错误代码参考

| HTTP 状态码 | 错误代码 | 描述 |
|------------|----------|------|
| 400 | INVALID_REQUEST | 请求参数无效 |
| 404 | NOT_FOUND | 资源不存在 |
| 500 | INTERNAL_ERROR | 内部服务器错误 |
| 500 | CAPTURE_FAILED | 截图捕获失败 |
| 500 | SAVE_FAILED | 文件保存失败 |
| 500 | WINDOW_NOT_FOUND | 游戏窗口未找到 |

## 六、使用示例

### 6.1 Python 示例
```python
import requests
import json

# 执行截图
config = {
    "mode": "window",
    "format": "png",
    "quality": 90,
    "save_to_disk": True,
    "filename_prefix": "api_test"
}

response = requests.post(
    "http://localhost:8000/api/v1/screenshot/capture",
    json={"config": config}
)

if response.status_code == 200:
    result = response.json()
    print(f"截图成功: {result['filename']}")
else:
    print(f"截图失败: {response.text}")
```

### 6.2 JavaScript 示例
```javascript
// 执行截图
async function captureScreenshot() {
  const config = {
    mode: 'window',
    format: 'png',
    quality: 90,
    save_to_disk: true,
    filename_prefix: 'js_test'
  };

  try {
    const response = await fetch('/api/v1/screenshot/capture', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ config })
    });

    const result = await response.json();
    
    if (result.success) {
      console.log('截图成功:', result.filename);
    } else {
      console.error('截图失败:', result.error_message);
    }
  } catch (error) {
    console.error('请求失败:', error);
  }
}
```

### 6.3 WebSocket 示例
```javascript
// WebSocket 连接
const ws = new WebSocket('ws://localhost:8000/ws');

ws.onopen = () => {
  console.log('WebSocket 连接已建立');
  
  // 启动预览
  ws.send(JSON.stringify({
    type: 'start_preview'
  }));
};

ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  
  if (data.type === 'screenshot_preview') {
    // 处理预览数据
    const imageUrl = 'data:image/jpeg;base64,' + data.data.image_data;
    document.getElementById('preview').src = imageUrl;
  }
};

ws.onclose = () => {
  console.log('WebSocket 连接已关闭');
};
```

---

**注意事项：**
1. 所有时间戳都使用 ISO 8601 格式
2. 文件大小以字节为单位
3. Base64 编码的图像数据不包含数据URL前缀
4. WebSocket 连接会自动处理重连和心跳检测
