# 图像识别调试控制台设计文档

## 文档信息

**项目名称**: Gakumasu-Bot 图像识别调试控制台  
**文档版本**: V1.0  
**创建日期**: 2025-08-05  
**创建人**: AI Assistant  

## 1. 项目背景与需求分析

### 1.1 项目背景

基于项目知识库分析，Gakumasu-Bot已具备完整的四层架构（感知-决策-行动-调度），其中感知模块包含：
- 屏幕捕获功能（ScreenCapture）
- 场景识别器（SceneRecognizer）
- 模板匹配器（TemplateMatcher）
- OCR文本识别（EasyOCR集成）

现有调试控制台已实现基础功能，但缺乏专门针对图像识别功能的深度调试和可视化展示。

### 1.2 功能需求

**核心需求**：
1. **游戏预览窗口**：实时显示游戏画面，叠加识别结果可视化
2. **识别功能控制面板**：手动触发各种识别功能，显示识别状态
3. **详细日志面板**：记录识别过程的每个步骤和中间结果
4. **后端调试接口**：提供单独调用识别功能的API接口

**技术需求**：
1. 基于现有Vue.js + FastAPI架构
2. 复用现有的WebSocket通信机制
3. 集成现有的感知模块功能
4. 保持与现有调试控制台的一致性

## 2. 系统架构设计

### 2.1 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                图像识别调试控制台架构                          │
├─────────────────────────────────────────────────────────────┤
│  前端界面层                                                   │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │  ImageRecognitionDebugConsole.vue (主组件)              │ │
│  │  ├── GamePreviewPanel.vue (游戏预览面板)                │ │
│  │  ├── RecognitionControlPanel.vue (识别控制面板)         │ │
│  │  ├── RecognitionLogViewer.vue (识别日志查看器)          │ │
│  │  └── RecognitionResultsPanel.vue (识别结果面板)        │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  API服务层                                                    │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │  FastAPI路由扩展                                         │ │
│  │  ├── /api/v1/debug/perception/* (感知调试接口)          │ │
│  │  ├── /api/v1/debug/recognition/* (识别调试接口)         │ │
│  │  └── WebSocket: /ws/debug/recognition (实时通信)        │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  业务逻辑层                                                   │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │  PerceptionDebugAdapter (感知调试适配器)                 │ │
│  │  ├── 屏幕捕获调试功能                                     │ │
│  │  ├── 场景识别调试功能                                     │ │
│  │  ├── 模板匹配调试功能                                     │ │
│  │  ├── OCR识别调试功能                                      │ │
│  │  └── 识别结果可视化处理                                   │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  核心模块层 (复用现有)                                         │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │  PerceptionModule (感知模块)                             │ │
│  │  ├── ScreenCapture (屏幕捕获)                           │ │
│  │  ├── SceneRecognizer (场景识别)                         │ │
│  │  ├── TemplateMatcher (模板匹配)                         │ │
│  │  └── OCR Reader (文本识别)                              │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 数据流设计

```
用户操作 → 前端控制面板 → API请求 → 感知调试适配器 → 感知模块
    ↓                                                    ↓
实时更新 ← WebSocket通信 ← 识别结果处理 ← 识别结果返回 ← 识别执行
    ↓
前端可视化展示 (预览窗口 + 日志面板 + 结果面板)
```

## 3. 功能模块设计

### 3.1 游戏预览面板 (GamePreviewPanel)

**功能特性**：
- 实时显示当前游戏画面
- 叠加显示场景识别结果（文字标注）
- 叠加显示UI元素识别结果（边框高亮）
- 支持识别区域的可视化标注
- 提供全屏查看和缩放功能

**技术实现**：
- 基于Canvas或SVG进行图像叠加渲染
- 使用WebSocket接收实时识别结果
- 支持多种标注样式（边框、文字、高亮区域）

### 3.2 识别控制面板 (RecognitionControlPanel)

**功能特性**：
- 手动触发屏幕捕获功能
- 手动触发场景识别功能
- 手动触发模板匹配功能
- 手动触发OCR文本识别功能
- 参数调节选项（阈值、模式切换等）
- 识别状态实时显示

**控制选项**：
- 识别置信度阈值调节
- 模板匹配方法选择
- OCR语言设置
- 调试模式开关
- 识别区域选择

### 3.3 识别日志查看器 (RecognitionLogViewer)

**功能特性**：
- 详细记录每个识别步骤
- 显示识别算法的中间结果
- 记录识别耗时和性能指标
- 支持日志级别过滤
- 支持关键词搜索和高亮

**日志内容**：
- 屏幕捕获日志（耗时、图像尺寸等）
- 场景识别日志（匹配结果、置信度等）
- 模板匹配日志（匹配位置、相似度等）
- OCR识别日志（识别文本、置信度等）
- 错误和异常日志

### 3.4 识别结果面板 (RecognitionResultsPanel)

**功能特性**：
- 显示当前识别状态和结果数据
- 展示识别性能统计信息
- 提供识别结果的详细信息
- 支持识别结果的导出功能

**结果展示**：
- 当前场景识别结果
- UI元素位置和属性
- OCR识别的文本内容
- 识别置信度和准确率
- 识别耗时统计

## 4. 后端接口设计

### 4.1 感知调试接口

```python
# 屏幕捕获调试
POST /api/v1/debug/perception/capture
GET  /api/v1/debug/perception/capture/status

# 场景识别调试
POST /api/v1/debug/perception/scene-recognition
GET  /api/v1/debug/perception/scene-recognition/results

# 模板匹配调试
POST /api/v1/debug/perception/template-matching
GET  /api/v1/debug/perception/template-matching/results

# OCR识别调试
POST /api/v1/debug/perception/ocr-recognition
GET  /api/v1/debug/perception/ocr-recognition/results
```

### 4.2 识别配置接口

```python
# 获取识别配置
GET  /api/v1/debug/perception/config

# 更新识别配置
PUT  /api/v1/debug/perception/config

# 重置识别配置
POST /api/v1/debug/perception/config/reset
```

### 4.3 WebSocket事件

```python
# 实时识别结果推送
"recognition_result_update"

# 识别状态变化通知
"recognition_status_change"

# 识别错误事件
"recognition_error_occurred"

# 识别性能统计更新
"recognition_stats_update"
```

## 5. 实施计划

### 5.1 开发阶段划分

**阶段1：后端调试接口开发** (预计4小时)
- 创建PerceptionDebugAdapter类
- 实现感知模块调试接口
- 扩展WebAPIAdapter类
- 添加FastAPI路由

**阶段2：前端组件开发** (预计6小时)
- 创建主调试控制台组件
- 实现游戏预览面板
- 实现识别控制面板
- 实现识别日志查看器
- 实现识别结果面板

**阶段3：集成测试与优化** (预计2小时)
- 前后端集成测试
- 功能验证和调试
- 性能优化
- 用户体验优化

### 5.2 技术风险评估

**风险点**：
1. 实时图像叠加渲染性能问题
2. WebSocket通信稳定性
3. 识别结果数据量过大导致的性能问题

**应对措施**：
1. 使用Canvas优化渲染，实现帧率控制
2. 实现WebSocket重连机制和错误处理
3. 实现识别结果数据的分页和缓存机制

## 6. 验收标准

### 6.1 功能验收标准

1. **游戏预览功能**：能够实时显示游戏画面，识别结果叠加显示正确
2. **识别控制功能**：各种识别功能能够手动触发，参数调节生效
3. **日志记录功能**：识别过程详细记录，日志过滤和搜索正常
4. **结果展示功能**：识别结果数据完整展示，性能统计准确

### 6.2 性能验收标准

1. **响应时间**：识别功能触发响应时间 < 500ms
2. **实时性**：游戏画面更新延迟 < 1秒
3. **稳定性**：连续运行2小时无崩溃
4. **资源占用**：前端内存占用 < 200MB

### 6.3 用户体验验收标准

1. **界面友好**：界面布局合理，操作直观
2. **信息清晰**：识别结果和日志信息清晰易懂
3. **操作便捷**：常用功能快捷访问，参数调节方便
4. **错误处理**：异常情况有明确提示和处理

---

**下一步行动**：等待用户确认设计方案后，开始实施开发工作。
