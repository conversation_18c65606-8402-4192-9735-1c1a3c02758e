"""
无障碍管理器
提供无障碍功能支持，包括屏幕阅读器、键盘导航、高对比度等
"""

import time
from typing import Dict, Any, Optional, List, Callable, Union
from enum import Enum
from dataclasses import dataclass, field

from ....utils.logger import get_logger


class AccessibilityFeature(Enum):
    """无障碍功能枚举"""
    SCREEN_READER = "screen_reader"           # 屏幕阅读器
    KEYBOARD_NAVIGATION = "keyboard_navigation" # 键盘导航
    HIGH_CONTRAST = "high_contrast"           # 高对比度
    LARGE_TEXT = "large_text"                 # 大字体
    VOICE_CONTROL = "voice_control"           # 语音控制
    MAGNIFIER = "magnifier"                   # 放大镜
    COLOR_BLIND_SUPPORT = "color_blind_support" # 色盲支持
    REDUCED_MOTION = "reduced_motion"         # 减少动画
    FOCUS_INDICATORS = "focus_indicators"     # 焦点指示器
    ALTERNATIVE_TEXT = "alternative_text"     # 替代文本


class AccessibilityLevel(Enum):
    """无障碍级别枚举"""
    NONE = "none"                 # 无
    BASIC = "basic"               # 基础
    ENHANCED = "enhanced"         # 增强
    FULL = "full"                 # 完整


class NavigationMode(Enum):
    """导航模式枚举"""
    MOUSE = "mouse"               # 鼠标导航
    KEYBOARD = "keyboard"         # 键盘导航
    VOICE = "voice"               # 语音导航
    GESTURE = "gesture"           # 手势导航


@dataclass
class AccessibilitySettings:
    """无障碍设置"""
    enabled_features: List[AccessibilityFeature] = field(default_factory=list)
    accessibility_level: AccessibilityLevel = AccessibilityLevel.BASIC
    navigation_mode: NavigationMode = NavigationMode.MOUSE
    
    # 视觉设置
    high_contrast_enabled: bool = False
    large_text_enabled: bool = False
    text_scale_factor: float = 1.0
    color_blind_mode: Optional[str] = None  # "protanopia", "deuteranopia", "tritanopia"
    
    # 动画设置
    reduced_motion_enabled: bool = False
    animation_duration_scale: float = 1.0
    
    # 音频设置
    screen_reader_enabled: bool = False
    voice_rate: float = 1.0
    voice_volume: float = 1.0
    
    # 键盘设置
    keyboard_navigation_enabled: bool = False
    tab_order_enabled: bool = True
    focus_indicators_enabled: bool = True
    
    # 其他设置
    alternative_text_enabled: bool = True
    magnifier_enabled: bool = False
    magnification_level: float = 2.0


@dataclass
class AccessibilityReport:
    """无障碍报告"""
    element_id: str
    issues: List[str] = field(default_factory=list)
    suggestions: List[str] = field(default_factory=list)
    compliance_level: AccessibilityLevel = AccessibilityLevel.NONE
    score: float = 0.0
    timestamp: float = field(default_factory=time.time)


class AccessibilityManager:
    """无障碍管理器"""
    
    def __init__(self):
        """初始化无障碍管理器"""
        self.logger = get_logger("AccessibilityManager")
        
        # 设置
        self.settings = AccessibilitySettings()
        
        # 元素注册
        self._registered_elements: Dict[str, Dict[str, Any]] = {}
        self._focus_order: List[str] = []
        self._current_focus_index = -1
        
        # 屏幕阅读器
        self._screen_reader_queue: List[str] = []
        self._is_speaking = False
        
        # 键盘导航
        self._keyboard_handlers: Dict[str, Callable] = {}
        self._navigation_shortcuts: Dict[str, Callable] = {}
        
        # 事件回调
        self.on_focus_change: Optional[Callable] = None
        self.on_accessibility_change: Optional[Callable] = None
        self.on_screen_reader_speak: Optional[Callable] = None
        
        # 注册默认键盘快捷键
        self._register_default_shortcuts()
        
        self.logger.info("无障碍管理器初始化完成")
    
    def enable_feature(self, feature: AccessibilityFeature):
        """启用无障碍功能"""
        if feature not in self.settings.enabled_features:
            self.settings.enabled_features.append(feature)
            
            # 应用功能设置
            self._apply_feature_settings(feature, True)
            
            if self.on_accessibility_change:
                self.on_accessibility_change(feature, True)
            
            self.logger.info(f"启用无障碍功能: {feature.value}")
    
    def disable_feature(self, feature: AccessibilityFeature):
        """禁用无障碍功能"""
        if feature in self.settings.enabled_features:
            self.settings.enabled_features.remove(feature)
            
            # 移除功能设置
            self._apply_feature_settings(feature, False)
            
            if self.on_accessibility_change:
                self.on_accessibility_change(feature, False)
            
            self.logger.info(f"禁用无障碍功能: {feature.value}")
    
    def _apply_feature_settings(self, feature: AccessibilityFeature, enabled: bool):
        """应用功能设置"""
        if feature == AccessibilityFeature.SCREEN_READER:
            self.settings.screen_reader_enabled = enabled
        elif feature == AccessibilityFeature.KEYBOARD_NAVIGATION:
            self.settings.keyboard_navigation_enabled = enabled
        elif feature == AccessibilityFeature.HIGH_CONTRAST:
            self.settings.high_contrast_enabled = enabled
        elif feature == AccessibilityFeature.LARGE_TEXT:
            self.settings.large_text_enabled = enabled
            if enabled:
                self.settings.text_scale_factor = 1.5
            else:
                self.settings.text_scale_factor = 1.0
        elif feature == AccessibilityFeature.REDUCED_MOTION:
            self.settings.reduced_motion_enabled = enabled
            if enabled:
                self.settings.animation_duration_scale = 0.1
            else:
                self.settings.animation_duration_scale = 1.0
        elif feature == AccessibilityFeature.FOCUS_INDICATORS:
            self.settings.focus_indicators_enabled = enabled
        elif feature == AccessibilityFeature.ALTERNATIVE_TEXT:
            self.settings.alternative_text_enabled = enabled
        elif feature == AccessibilityFeature.MAGNIFIER:
            self.settings.magnifier_enabled = enabled
    
    def register_element(self, element_id: str, element_info: Dict[str, Any]):
        """注册UI元素"""
        self._registered_elements[element_id] = {
            "type": element_info.get("type", "unknown"),
            "label": element_info.get("label", ""),
            "description": element_info.get("description", ""),
            "role": element_info.get("role", ""),
            "focusable": element_info.get("focusable", False),
            "tab_index": element_info.get("tab_index", 0),
            "aria_label": element_info.get("aria_label", ""),
            "aria_description": element_info.get("aria_description", ""),
            "keyboard_shortcuts": element_info.get("keyboard_shortcuts", []),
            "bounds": element_info.get("bounds", (0, 0, 0, 0)),
            "visible": element_info.get("visible", True),
            "enabled": element_info.get("enabled", True)
        }
        
        # 更新焦点顺序
        if element_info.get("focusable", False):
            self._update_focus_order()
        
        self.logger.debug(f"注册无障碍元素: {element_id}")
    
    def unregister_element(self, element_id: str):
        """注销UI元素"""
        if element_id in self._registered_elements:
            del self._registered_elements[element_id]
            
            # 从焦点顺序中移除
            if element_id in self._focus_order:
                self._focus_order.remove(element_id)
            
            self.logger.debug(f"注销无障碍元素: {element_id}")
    
    def _update_focus_order(self):
        """更新焦点顺序"""
        focusable_elements = [
            (element_id, info) for element_id, info in self._registered_elements.items()
            if info.get("focusable", False) and info.get("visible", True) and info.get("enabled", True)
        ]
        
        # 按tab_index排序
        focusable_elements.sort(key=lambda x: x[1].get("tab_index", 0))
        
        self._focus_order = [element_id for element_id, _ in focusable_elements]
    
    def set_focus(self, element_id: str) -> bool:
        """设置焦点"""
        if element_id not in self._registered_elements:
            return False
        
        element_info = self._registered_elements[element_id]
        if not element_info.get("focusable", False):
            return False
        
        # 更新焦点索引
        if element_id in self._focus_order:
            self._current_focus_index = self._focus_order.index(element_id)
        
        # 屏幕阅读器朗读
        if self.settings.screen_reader_enabled:
            self._speak_element(element_id)
        
        # 触发焦点变化事件
        if self.on_focus_change:
            self.on_focus_change(element_id)
        
        self.logger.debug(f"设置焦点: {element_id}")
        return True
    
    def navigate_next(self) -> Optional[str]:
        """导航到下一个元素"""
        if not self._focus_order:
            return None
        
        self._current_focus_index = (self._current_focus_index + 1) % len(self._focus_order)
        next_element = self._focus_order[self._current_focus_index]
        
        if self.set_focus(next_element):
            return next_element
        
        return None
    
    def navigate_previous(self) -> Optional[str]:
        """导航到上一个元素"""
        if not self._focus_order:
            return None
        
        self._current_focus_index = (self._current_focus_index - 1) % len(self._focus_order)
        prev_element = self._focus_order[self._current_focus_index]
        
        if self.set_focus(prev_element):
            return prev_element
        
        return None
    
    def _speak_element(self, element_id: str):
        """屏幕阅读器朗读元素"""
        if not self.settings.screen_reader_enabled:
            return
        
        element_info = self._registered_elements.get(element_id)
        if not element_info:
            return
        
        # 构建朗读文本
        text_parts = []
        
        # 优先使用aria_label
        if element_info.get("aria_label"):
            text_parts.append(element_info["aria_label"])
        elif element_info.get("label"):
            text_parts.append(element_info["label"])
        
        # 添加角色信息
        if element_info.get("role"):
            text_parts.append(element_info["role"])
        elif element_info.get("type"):
            text_parts.append(element_info["type"])
        
        # 添加描述
        if element_info.get("aria_description"):
            text_parts.append(element_info["aria_description"])
        elif element_info.get("description"):
            text_parts.append(element_info["description"])
        
        # 添加状态信息
        if not element_info.get("enabled", True):
            text_parts.append("禁用")
        
        speak_text = "，".join(text_parts)
        if speak_text:
            self.speak(speak_text)
    
    def speak(self, text: str, priority: bool = False):
        """屏幕阅读器朗读文本"""
        if not self.settings.screen_reader_enabled:
            return
        
        if priority:
            # 高优先级文本插入到队列前面
            self._screen_reader_queue.insert(0, text)
        else:
            self._screen_reader_queue.append(text)
        
        # 如果当前没有在朗读，开始朗读
        if not self._is_speaking:
            self._process_speech_queue()
    
    def _process_speech_queue(self):
        """处理朗读队列"""
        if not self._screen_reader_queue or self._is_speaking:
            return
        
        text = self._screen_reader_queue.pop(0)
        self._is_speaking = True
        
        # 调用朗读回调
        if self.on_screen_reader_speak:
            self.on_screen_reader_speak(text, self.settings.voice_rate, self.settings.voice_volume)
        
        # 模拟朗读完成（实际应该在朗读完成后调用）
        # 这里简化处理
        import threading
        def on_speech_complete():
            self._is_speaking = False
            self._process_speech_queue()
        
        # 估算朗读时间
        speech_duration = len(text) * 0.1 / self.settings.voice_rate
        threading.Timer(speech_duration, on_speech_complete).start()
    
    def stop_speech(self):
        """停止朗读"""
        self._screen_reader_queue.clear()
        self._is_speaking = False
    
    def _register_default_shortcuts(self):
        """注册默认键盘快捷键"""
        self._navigation_shortcuts = {
            "Tab": self.navigate_next,
            "Shift+Tab": self.navigate_previous,
            "Enter": self._activate_current_element,
            "Space": self._activate_current_element,
            "Escape": self._cancel_current_action,
            "F1": self._show_help,
            "Alt+F4": self._close_application
        }
    
    def register_keyboard_shortcut(self, key_combination: str, handler: Callable):
        """注册键盘快捷键"""
        self._navigation_shortcuts[key_combination] = handler
        self.logger.debug(f"注册键盘快捷键: {key_combination}")
    
    def handle_keyboard_input(self, key_combination: str) -> bool:
        """处理键盘输入"""
        if not self.settings.keyboard_navigation_enabled:
            return False
        
        if key_combination in self._navigation_shortcuts:
            try:
                handler = self._navigation_shortcuts[key_combination]
                result = handler()
                return result is not False
            except Exception as e:
                self.logger.error(f"键盘快捷键处理失败: {e}")
                return False
        
        return False
    
    def _activate_current_element(self):
        """激活当前焦点元素"""
        if self._current_focus_index >= 0 and self._current_focus_index < len(self._focus_order):
            element_id = self._focus_order[self._current_focus_index]
            self.logger.debug(f"激活元素: {element_id}")
            return True
        return False
    
    def _cancel_current_action(self):
        """取消当前操作"""
        self.logger.debug("取消当前操作")
        return True
    
    def _show_help(self):
        """显示帮助信息"""
        help_text = "可用的键盘快捷键：Tab - 下一个元素，Shift+Tab - 上一个元素，Enter/Space - 激活元素，Escape - 取消，F1 - 帮助"
        self.speak(help_text, priority=True)
        return True
    
    def _close_application(self):
        """关闭应用程序"""
        self.logger.info("请求关闭应用程序")
        return True
    
    def audit_accessibility(self, element_id: str = None) -> Union[AccessibilityReport, List[AccessibilityReport]]:
        """审计无障碍合规性"""
        if element_id:
            return self._audit_single_element(element_id)
        else:
            return self._audit_all_elements()
    
    def _audit_single_element(self, element_id: str) -> AccessibilityReport:
        """审计单个元素"""
        if element_id not in self._registered_elements:
            return AccessibilityReport(
                element_id=element_id,
                issues=["元素未注册"],
                compliance_level=AccessibilityLevel.NONE
            )
        
        element_info = self._registered_elements[element_id]
        issues = []
        suggestions = []
        score = 100.0
        
        # 检查标签
        if not element_info.get("label") and not element_info.get("aria_label"):
            issues.append("缺少标签")
            suggestions.append("添加label或aria-label属性")
            score -= 20
        
        # 检查描述
        if element_info.get("focusable") and not element_info.get("description") and not element_info.get("aria_description"):
            issues.append("可聚焦元素缺少描述")
            suggestions.append("添加description或aria-description属性")
            score -= 10
        
        # 检查键盘可访问性
        if element_info.get("focusable") and not element_info.get("keyboard_shortcuts"):
            issues.append("缺少键盘快捷键")
            suggestions.append("为可聚焦元素添加键盘快捷键")
            score -= 15
        
        # 检查角色
        if not element_info.get("role"):
            issues.append("缺少角色定义")
            suggestions.append("添加role属性")
            score -= 10
        
        # 确定合规级别
        if score >= 90:
            compliance_level = AccessibilityLevel.FULL
        elif score >= 70:
            compliance_level = AccessibilityLevel.ENHANCED
        elif score >= 50:
            compliance_level = AccessibilityLevel.BASIC
        else:
            compliance_level = AccessibilityLevel.NONE
        
        return AccessibilityReport(
            element_id=element_id,
            issues=issues,
            suggestions=suggestions,
            compliance_level=compliance_level,
            score=score
        )
    
    def _audit_all_elements(self) -> List[AccessibilityReport]:
        """审计所有元素"""
        reports = []
        for element_id in self._registered_elements:
            report = self._audit_single_element(element_id)
            reports.append(report)
        return reports
    
    def get_accessibility_summary(self) -> Dict[str, Any]:
        """获取无障碍摘要"""
        reports = self._audit_all_elements()
        
        # 统计合规级别
        compliance_stats = {level.value: 0 for level in AccessibilityLevel}
        total_score = 0
        
        for report in reports:
            compliance_stats[report.compliance_level.value] += 1
            total_score += report.score
        
        average_score = total_score / len(reports) if reports else 0
        
        return {
            "total_elements": len(self._registered_elements),
            "focusable_elements": len(self._focus_order),
            "enabled_features": [feature.value for feature in self.settings.enabled_features],
            "accessibility_level": self.settings.accessibility_level.value,
            "navigation_mode": self.settings.navigation_mode.value,
            "compliance_statistics": compliance_stats,
            "average_score": average_score,
            "screen_reader_enabled": self.settings.screen_reader_enabled,
            "keyboard_navigation_enabled": self.settings.keyboard_navigation_enabled,
            "high_contrast_enabled": self.settings.high_contrast_enabled,
            "large_text_enabled": self.settings.large_text_enabled,
            "reduced_motion_enabled": self.settings.reduced_motion_enabled
        }
    
    def export_accessibility_report(self, format_type: str = "json") -> Any:
        """导出无障碍报告"""
        try:
            reports = self._audit_all_elements()
            summary = self.get_accessibility_summary()
            
            if format_type == "json":
                import json
                
                export_data = {
                    "summary": summary,
                    "reports": [
                        {
                            "element_id": report.element_id,
                            "issues": report.issues,
                            "suggestions": report.suggestions,
                            "compliance_level": report.compliance_level.value,
                            "score": report.score,
                            "timestamp": report.timestamp
                        }
                        for report in reports
                    ]
                }
                
                return json.dumps(export_data, indent=2, ensure_ascii=False)
            
            elif format_type == "html":
                # 生成HTML报告
                html_content = f"""
                <!DOCTYPE html>
                <html lang="zh-CN">
                <head>
                    <meta charset="UTF-8">
                    <title>无障碍审计报告</title>
                    <style>
                        body {{ font-family: Arial, sans-serif; margin: 20px; }}
                        .summary {{ background: #f5f5f5; padding: 15px; border-radius: 5px; }}
                        .report {{ margin: 10px 0; padding: 10px; border: 1px solid #ddd; }}
                        .issues {{ color: #d32f2f; }}
                        .suggestions {{ color: #1976d2; }}
                    </style>
                </head>
                <body>
                    <h1>无障碍审计报告</h1>
                    <div class="summary">
                        <h2>摘要</h2>
                        <p>总元素数: {summary['total_elements']}</p>
                        <p>可聚焦元素数: {summary['focusable_elements']}</p>
                        <p>平均分数: {summary['average_score']:.1f}</p>
                    </div>
                """
                
                for report in reports:
                    html_content += f"""
                    <div class="report">
                        <h3>元素: {report.element_id}</h3>
                        <p>合规级别: {report.compliance_level.value}</p>
                        <p>分数: {report.score:.1f}</p>
                        <div class="issues">问题: {', '.join(report.issues) if report.issues else '无'}</div>
                        <div class="suggestions">建议: {', '.join(report.suggestions) if report.suggestions else '无'}</div>
                    </div>
                    """
                
                html_content += "</body></html>"
                return html_content
            
            return None
            
        except Exception as e:
            self.logger.error(f"导出无障碍报告失败: {e}")
            return None
    
    def apply_accessibility_preset(self, preset_name: str):
        """应用无障碍预设"""
        if preset_name == "visual_impairment":
            self.enable_feature(AccessibilityFeature.SCREEN_READER)
            self.enable_feature(AccessibilityFeature.KEYBOARD_NAVIGATION)
            self.enable_feature(AccessibilityFeature.HIGH_CONTRAST)
            self.enable_feature(AccessibilityFeature.LARGE_TEXT)
            self.enable_feature(AccessibilityFeature.FOCUS_INDICATORS)
            
        elif preset_name == "motor_impairment":
            self.enable_feature(AccessibilityFeature.KEYBOARD_NAVIGATION)
            self.enable_feature(AccessibilityFeature.VOICE_CONTROL)
            self.enable_feature(AccessibilityFeature.REDUCED_MOTION)
            self.enable_feature(AccessibilityFeature.FOCUS_INDICATORS)
            
        elif preset_name == "cognitive_impairment":
            self.enable_feature(AccessibilityFeature.REDUCED_MOTION)
            self.enable_feature(AccessibilityFeature.LARGE_TEXT)
            self.enable_feature(AccessibilityFeature.HIGH_CONTRAST)
            self.enable_feature(AccessibilityFeature.ALTERNATIVE_TEXT)
            
        elif preset_name == "hearing_impairment":
            self.enable_feature(AccessibilityFeature.ALTERNATIVE_TEXT)
            self.enable_feature(AccessibilityFeature.HIGH_CONTRAST)
            self.enable_feature(AccessibilityFeature.FOCUS_INDICATORS)
            
        self.logger.info(f"应用无障碍预设: {preset_name}")
