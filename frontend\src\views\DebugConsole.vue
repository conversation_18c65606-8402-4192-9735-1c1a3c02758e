<template>
  <div class="debug-console">
    <el-container class="console-container">
      <!-- 侧边栏 -->
      <el-aside width="250px" class="console-sidebar">
        <div class="sidebar-header">
          <h3>调试控制台</h3>
        </div>
        <el-menu
          :default-active="activeTab"
          class="sidebar-menu"
          @select="handleTabSelect"
        >
          <el-menu-item index="system-status">
            <el-icon><Monitor /></el-icon>
            <span>系统状态</span>
          </el-menu-item>
          <el-menu-item index="game-monitor">
            <el-icon><View /></el-icon>
            <span>游戏监控</span>
          </el-menu-item>
          <el-menu-item index="function-test">
            <el-icon><Tools /></el-icon>
            <span>功能测试</span>
          </el-menu-item>
          <el-menu-item index="log-viewer">
            <el-icon><Document /></el-icon>
            <span>日志查看</span>
          </el-menu-item>
          <el-menu-item index="config-manager">
            <el-icon><Setting /></el-icon>
            <span>配置管理</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 主内容区域 -->
      <el-main class="console-main">
        <!-- 系统状态面板 -->
        <div v-show="activeTab === 'system-status'" class="tab-content">
          <SystemStatusPanel />
        </div>

        <!-- 游戏监控面板 -->
        <div v-show="activeTab === 'game-monitor'" class="tab-content">
          <GameStateMonitor />
        </div>

        <!-- 功能测试面板 -->
        <div v-show="activeTab === 'function-test'" class="tab-content">
          <FunctionTestPanel />
        </div>

        <!-- 日志查看面板 -->
        <div v-show="activeTab === 'log-viewer'" class="tab-content">
          <LogViewer />
        </div>

        <!-- 配置管理面板 -->
        <div v-show="activeTab === 'config-manager'" class="tab-content">
          <ConfigManager />
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { Monitor, View, Tools, Document, Setting } from '@element-plus/icons-vue'

// 导入子组件 - 逐步恢复
import SystemStatusPanel from '../components/debug/SystemStatusPanel.vue'
// import GameStateMonitor from '../components/debug/GameStateMonitor.vue'
// import FunctionTestPanel from '../components/debug/FunctionTestPanel.vue'
// import LogViewer from '../components/debug/LogViewer.vue'
// import ConfigManager from '../components/debug/ConfigManager.vue'

// 临时占位组件用于其他未恢复的组件
const GameStateMonitor = {
  template: '<div class="placeholder">游戏状态监控面板 - 诊断模式</div>'
}
const FunctionTestPanel = {
  template: '<div class="placeholder">功能测试面板 - 诊断模式</div>'
}
const LogViewer = {
  template: '<div class="placeholder">日志查看器 - 诊断模式</div>'
}
const ConfigManager = {
  template: '<div class="placeholder">配置管理器 - 诊断模式</div>'
}

// 响应式数据
const activeTab = ref('system-status')

// 方法
const handleTabSelect = (key) => {
  activeTab.value = key
}

// 生命周期
onMounted(() => {
  console.log('调试控制台已加载')
})

onUnmounted(() => {
  console.log('调试控制台已卸载')
})
</script>

<style scoped>
.debug-console {
  height: 100%;
  background: #f5f5f5;
}

.console-container {
  height: 100%;
}

.console-sidebar {
  background: #fff;
  border-right: 1px solid #e6e6e6;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid #e6e6e6;
  text-align: center;
}

.sidebar-header h3 {
  margin: 0;
  color: #409eff;
  font-size: 18px;
  font-weight: 600;
}

.sidebar-menu {
  border-right: none;
}

.sidebar-menu .el-menu-item {
  height: 50px;
  line-height: 50px;
  font-size: 14px;
  color: #606266;
  transition: all 0.3s ease;
}

.sidebar-menu .el-menu-item:hover {
  background: #f0f8ff;
  color: #409eff;
}

.sidebar-menu .el-menu-item.is-active {
  background: #409eff;
  color: white;
}

.sidebar-menu .el-menu-item .el-icon {
  margin-right: 8px;
  font-size: 16px;
}

.console-main {
  padding: 20px;
  background: #f5f5f5;
}

.tab-content {
  height: 100%;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  font-size: 18px;
  color: #909399;
  background: #f9f9f9;
  border: 2px dashed #dcdfe6;
  border-radius: 8px;
}
</style>
