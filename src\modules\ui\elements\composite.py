"""
复合UI元素类实现
提供由多个基础UI元素组成的复合控件
"""

import time
from typing import Dict, List, Optional, Any, Callable
from abc import ABC, abstractmethod

from ....utils.logger import get_logger
from ..base.base_ui_element import BaseUIElement
from ..config.ui_element_config import UIElementConfig
from .buttons import Button
from .inputs import InputField
from .labels import Label


class CompositeElement(BaseUIElement):
    """复合UI元素基类"""

    def __init__(self, config: UIElementConfig, perception_module, action_controller):
        """
        初始化复合元素
        
        Args:
            config: 元素配置
            perception_module: 感知模块
            action_controller: 行动控制器
        """
        super().__init__(config, perception_module, action_controller)
        
        # 子元素管理
        self.child_elements: Dict[str, BaseUIElement] = {}
        self._element_order: List[str] = []  # 元素顺序
        
        # 复合元素状态
        self._interaction_sequence: List[Dict[str, Any]] = []
        self._validation_rules: List[Callable] = []

    def add_child_element(self, name: str, element: BaseUIElement, order: int = None):
        """
        添加子元素
        
        Args:
            name: 元素名称
            element: 元素实例
            order: 元素顺序（可选）
        """
        self.child_elements[name] = element
        
        if order is not None:
            # 插入到指定位置
            if order >= len(self._element_order):
                self._element_order.append(name)
            else:
                self._element_order.insert(order, name)
        else:
            self._element_order.append(name)
        
        self.logger.debug(f"添加子元素: {name}")

    def remove_child_element(self, name: str) -> bool:
        """
        移除子元素
        
        Args:
            name: 元素名称
            
        Returns:
            是否成功移除
        """
        if name in self.child_elements:
            del self.child_elements[name]
            if name in self._element_order:
                self._element_order.remove(name)
            self.logger.debug(f"移除子元素: {name}")
            return True
        return False

    def get_child_element(self, name: str) -> Optional[BaseUIElement]:
        """获取子元素"""
        return self.child_elements.get(name)

    def click(self) -> bool:
        """点击复合元素（默认点击第一个可点击的子元素）"""
        for element_name in self._element_order:
            element = self.child_elements.get(element_name)
            if element and hasattr(element, 'click'):
                try:
                    return element.click()
                except Exception as e:
                    self.logger.error(f"点击子元素 {element_name} 失败: {e}")
                    continue
        
        self.logger.warning("没有找到可点击的子元素")
        return False

    def is_visible(self) -> bool:
        """检查复合元素是否可见（所有子元素都可见）"""
        if not self.child_elements:
            return super().is_visible()
        
        for element in self.child_elements.values():
            if not element.is_visible():
                return False
        
        return True

    def is_partially_visible(self) -> bool:
        """检查是否部分可见（至少一个子元素可见）"""
        if not self.child_elements:
            return super().is_visible()
        
        for element in self.child_elements.values():
            if element.is_visible():
                return True
        
        return False

    def execute_sequence(self, sequence: List[Dict[str, Any]]) -> bool:
        """
        执行交互序列
        
        Args:
            sequence: 交互序列，每个元素包含 {'element': 'name', 'action': 'click/input', 'params': {...}}
            
        Returns:
            是否全部成功
        """
        self.logger.info(f"执行交互序列，共 {len(sequence)} 步")
        
        for i, step in enumerate(sequence):
            try:
                element_name = step.get('element')
                action = step.get('action')
                params = step.get('params', {})
                
                if not element_name or not action:
                    self.logger.error(f"序列步骤 {i+1} 配置无效")
                    return False
                
                element = self.get_child_element(element_name)
                if not element:
                    self.logger.error(f"序列步骤 {i+1}: 未找到元素 {element_name}")
                    return False
                
                # 执行操作
                success = self._execute_element_action(element, action, params)
                if not success:
                    self.logger.error(f"序列步骤 {i+1} 执行失败")
                    return False
                
                # 记录交互
                self._interaction_sequence.append({
                    'step': i + 1,
                    'element': element_name,
                    'action': action,
                    'params': params,
                    'timestamp': time.time(),
                    'success': True
                })
                
                # 步骤间延迟
                delay = step.get('delay', 0.1)
                if delay > 0:
                    time.sleep(delay)
                
            except Exception as e:
                self.logger.error(f"序列步骤 {i+1} 执行异常: {e}")
                return False
        
        self.logger.info("交互序列执行完成")
        return True

    def _execute_element_action(self, element: BaseUIElement, action: str, params: Dict[str, Any]) -> bool:
        """执行元素操作"""
        try:
            if action == 'click':
                return element.click()
            elif action == 'input' and hasattr(element, 'input_text'):
                text = params.get('text', '')
                return element.input_text(text)
            elif action == 'wait_visible':
                timeout = params.get('timeout', 5.0)
                start_time = time.time()
                while time.time() - start_time < timeout:
                    if element.is_visible():
                        return True
                    time.sleep(0.1)
                return False
            else:
                self.logger.error(f"不支持的操作: {action}")
                return False
                
        except Exception as e:
            self.logger.error(f"执行元素操作失败: {e}")
            return False

    def add_validation_rule(self, rule: Callable[[], bool]):
        """添加验证规则"""
        self._validation_rules.append(rule)

    def validate(self) -> bool:
        """执行所有验证规则"""
        for rule in self._validation_rules:
            try:
                if not rule():
                    return False
            except Exception as e:
                self.logger.error(f"验证规则执行失败: {e}")
                return False
        return True

    def get_interaction_history(self) -> List[Dict[str, Any]]:
        """获取交互历史"""
        return self._interaction_sequence.copy()

    def clear_interaction_history(self):
        """清理交互历史"""
        self._interaction_sequence.clear()


class FormElement(CompositeElement):
    """表单复合元素"""

    def __init__(self, config: UIElementConfig, perception_module, action_controller):
        super().__init__(config, perception_module, action_controller)
        
        # 表单特定属性
        self.form_data: Dict[str, Any] = {}
        self.required_fields: List[str] = []
        self.submit_button_name: Optional[str] = None

    def add_input_field(self, name: str, input_field: InputField, required: bool = False):
        """添加输入字段"""
        self.add_child_element(name, input_field)
        if required:
            self.required_fields.append(name)

    def add_submit_button(self, name: str, button: Button):
        """添加提交按钮"""
        self.add_child_element(name, button)
        self.submit_button_name = name

    def fill_form(self, data: Dict[str, str]) -> bool:
        """
        填写表单
        
        Args:
            data: 表单数据，键为字段名，值为输入内容
            
        Returns:
            是否成功填写
        """
        self.logger.info(f"填写表单，共 {len(data)} 个字段")
        
        for field_name, value in data.items():
            element = self.get_child_element(field_name)
            if not element:
                self.logger.warning(f"未找到表单字段: {field_name}")
                continue
            
            if hasattr(element, 'input_text'):
                if not element.input_text(str(value)):
                    self.logger.error(f"填写字段 {field_name} 失败")
                    return False
                self.form_data[field_name] = value
            else:
                self.logger.warning(f"字段 {field_name} 不支持文本输入")
        
        return True

    def submit_form(self) -> bool:
        """提交表单"""
        # 验证必填字段
        if not self._validate_required_fields():
            return False
        
        # 点击提交按钮
        if self.submit_button_name:
            submit_button = self.get_child_element(self.submit_button_name)
            if submit_button:
                return submit_button.click()
            else:
                self.logger.error("未找到提交按钮")
                return False
        else:
            self.logger.error("未设置提交按钮")
            return False

    def _validate_required_fields(self) -> bool:
        """验证必填字段"""
        for field_name in self.required_fields:
            if field_name not in self.form_data or not self.form_data[field_name]:
                self.logger.error(f"必填字段 {field_name} 未填写")
                return False
        return True

    def clear_form(self) -> bool:
        """清空表单"""
        success = True
        for element_name, element in self.child_elements.items():
            if hasattr(element, 'clear'):
                if not element.clear():
                    self.logger.warning(f"清空字段 {element_name} 失败")
                    success = False
        
        self.form_data.clear()
        return success


class DialogElement(CompositeElement):
    """对话框复合元素"""

    def __init__(self, config: UIElementConfig, perception_module, action_controller):
        super().__init__(config, perception_module, action_controller)
        
        # 对话框特定属性
        self.title_label_name: Optional[str] = None
        self.message_label_name: Optional[str] = None
        self.ok_button_name: Optional[str] = None
        self.cancel_button_name: Optional[str] = None

    def set_title_label(self, name: str):
        """设置标题标签"""
        self.title_label_name = name

    def set_message_label(self, name: str):
        """设置消息标签"""
        self.message_label_name = name

    def set_ok_button(self, name: str):
        """设置确定按钮"""
        self.ok_button_name = name

    def set_cancel_button(self, name: str):
        """设置取消按钮"""
        self.cancel_button_name = name

    def get_title(self) -> Optional[str]:
        """获取对话框标题"""
        if self.title_label_name:
            title_label = self.get_child_element(self.title_label_name)
            if title_label and hasattr(title_label, 'read_text'):
                return title_label.read_text()
        return None

    def get_message(self) -> Optional[str]:
        """获取对话框消息"""
        if self.message_label_name:
            message_label = self.get_child_element(self.message_label_name)
            if message_label and hasattr(message_label, 'read_text'):
                return message_label.read_text()
        return None

    def click_ok(self) -> bool:
        """点击确定按钮"""
        if self.ok_button_name:
            ok_button = self.get_child_element(self.ok_button_name)
            if ok_button:
                return ok_button.click()
        return False

    def click_cancel(self) -> bool:
        """点击取消按钮"""
        if self.cancel_button_name:
            cancel_button = self.get_child_element(self.cancel_button_name)
            if cancel_button:
                return cancel_button.click()
        return False

    def wait_for_dialog(self, timeout: float = 10.0) -> bool:
        """等待对话框出现"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if self.is_visible():
                return True
            time.sleep(0.1)
        
        return False

    def handle_dialog(self, action: str = "ok") -> bool:
        """
        处理对话框
        
        Args:
            action: 处理动作 ("ok", "cancel")
            
        Returns:
            是否成功处理
        """
        if not self.wait_for_dialog():
            self.logger.warning("对话框未出现")
            return False
        
        if action == "ok":
            return self.click_ok()
        elif action == "cancel":
            return self.click_cancel()
        else:
            self.logger.error(f"不支持的对话框操作: {action}")
            return False
