"""
后台输入模拟器
支持向后台窗口发送鼠标和键盘输入
"""

import time
import random
import ctypes
import ctypes.wintypes
from typing import Optional, Dict, Any, Tuple, List, Union
from enum import Enum
import win32gui
import win32con
import win32api

from ...utils.logger import get_logger
from ...core.data_structures import GakumasuBotException, ActionType
from .input_simulator import InputSimulator, InputSimulationError


class BackgroundInputMethod(Enum):
    """后台输入方式枚举"""
    AUTO = "auto"                   # 自动选择最佳方式
    WIN32_MESSAGE = "win32_message" # Windows消息方式
    WIN32_POSTMESSAGE = "win32_postmessage" # PostMessage方式
    WIN32_SENDMESSAGE = "win32_sendmessage" # SendMessage方式


class MouseButton(Enum):
    """鼠标按键枚举"""
    LEFT = "left"
    RIGHT = "right"
    MIDDLE = "middle"


class BackgroundInputSimulator(InputSimulator):
    """后台输入模拟器类"""
    
    def __init__(self, 
                 target_window_handle: Optional[int] = None,
                 enable_background_mode: bool = True,
                 background_input_method: BackgroundInputMethod = BackgroundInputMethod.AUTO):
        """
        初始化后台输入模拟器
        
        Args:
            target_window_handle: 目标窗口句柄
            enable_background_mode: 是否启用后台模式
            background_input_method: 后台输入方式
        """
        super().__init__()
        
        self.target_window_handle = target_window_handle
        self.enable_background_mode = enable_background_mode
        self.background_input_method = background_input_method
        
        # 后台输入统计
        self._input_stats = {
            "total_inputs": 0,
            "background_inputs": 0,
            "foreground_inputs": 0,
            "failed_inputs": 0
        }
        
        # Windows消息常量
        self._setup_win32_constants()
        
        self.logger.info(f"后台输入模拟器初始化完成，后台模式: {enable_background_mode}")
    
    def _setup_win32_constants(self):
        """设置Windows消息常量"""
        # 鼠标消息
        self.WM_MOUSEMOVE = 0x0200
        self.WM_LBUTTONDOWN = 0x0201
        self.WM_LBUTTONUP = 0x0202
        self.WM_RBUTTONDOWN = 0x0204
        self.WM_RBUTTONUP = 0x0205
        self.WM_MBUTTONDOWN = 0x0207
        self.WM_MBUTTONUP = 0x0208
        self.WM_MOUSEWHEEL = 0x020A
        
        # 键盘消息
        self.WM_KEYDOWN = 0x0100
        self.WM_KEYUP = 0x0101
        self.WM_CHAR = 0x0102
        self.WM_SYSKEYDOWN = 0x0104
        self.WM_SYSKEYUP = 0x0105
        
        # 鼠标按键标志
        self.MK_LBUTTON = 0x0001
        self.MK_RBUTTON = 0x0002
        self.MK_MBUTTON = 0x0010
    
    def set_target_window(self, window_handle: int):
        """设置目标窗口句柄"""
        self.target_window_handle = window_handle
        self.logger.debug(f"设置目标窗口句柄: {window_handle}")
    
    def is_background_input_needed(self) -> bool:
        """
        判断是否需要后台输入
        
        Returns:
            是否需要后台输入
        """
        if not self.enable_background_mode or not self.target_window_handle:
            return False
        
        try:
            # 检查目标窗口是否为前台窗口
            foreground_window = win32gui.GetForegroundWindow()
            return foreground_window != self.target_window_handle
        except Exception:
            return False
    
    def _make_lparam(self, x: int, y: int) -> int:
        """构造LPARAM参数"""
        return (y << 16) | (x & 0xFFFF)
    
    def _send_mouse_message(self, message: int, x: int, y: int, wparam: int = 0) -> bool:
        """
        发送鼠标消息到后台窗口

        Args:
            message: 消息类型
            x: X坐标（相对于窗口客户区）
            y: Y坐标（相对于窗口客户区）
            wparam: WPARAM参数

        Returns:
            是否发送成功
        """
        try:
            # 验证窗口句柄有效性
            if not self.target_window_handle or not win32gui.IsWindow(self.target_window_handle):
                self.logger.error(f"无效的窗口句柄: {self.target_window_handle}")
                return False

            lparam = self._make_lparam(x, y)

            # 优先使用PostMessage，因为它是异步的，不会阻塞
            if self.background_input_method == BackgroundInputMethod.WIN32_SENDMESSAGE:
                result = win32gui.SendMessage(self.target_window_handle, message, wparam, lparam)
                success = True  # SendMessage总是返回消息处理结果
            else:
                # 使用PostMessage（默认）
                result = win32gui.PostMessage(self.target_window_handle, message, wparam, lparam)
                success = result != 0

            if success:
                self.logger.debug(f"发送鼠标消息成功: 消息=0x{message:04X}, 坐标=({x},{y}), wparam={wparam}")
            else:
                self.logger.warning(f"发送鼠标消息失败: 消息=0x{message:04X}, 结果={result}")

            return success

        except Exception as e:
            self.logger.error(f"发送鼠标消息异常: {e}")
            return False
    
    def _send_key_message(self, message: int, key_code: int, lparam: int = 0) -> bool:
        """
        发送键盘消息到后台窗口
        
        Args:
            message: 消息类型
            key_code: 键码
            lparam: LPARAM参数
            
        Returns:
            是否发送成功
        """
        try:
            if self.background_input_method == BackgroundInputMethod.WIN32_POSTMESSAGE:
                result = win32gui.PostMessage(self.target_window_handle, message, key_code, lparam)
            else:
                result = win32gui.SendMessage(self.target_window_handle, message, key_code, lparam)
            
            return result != 0
            
        except Exception as e:
            self.logger.error(f"发送键盘消息失败: {e}")
            return False
    
    def background_click(self, x: int, y: int, button: MouseButton = MouseButton.LEFT) -> bool:
        """
        后台鼠标点击

        Args:
            x: X坐标（相对于窗口客户区）
            y: Y坐标（相对于窗口客户区）
            button: 鼠标按键

        Returns:
            是否点击成功
        """
        try:
            # 验证坐标范围
            if x < 0 or y < 0:
                self.logger.warning(f"坐标超出范围: ({x}, {y})")
                return False

            # 选择消息类型
            if button == MouseButton.LEFT:
                down_msg = self.WM_LBUTTONDOWN
                up_msg = self.WM_LBUTTONUP
                wparam = self.MK_LBUTTON
            elif button == MouseButton.RIGHT:
                down_msg = self.WM_RBUTTONDOWN
                up_msg = self.WM_RBUTTONUP
                wparam = self.MK_RBUTTON
            elif button == MouseButton.MIDDLE:
                down_msg = self.WM_MBUTTONDOWN
                up_msg = self.WM_MBUTTONUP
                wparam = self.MK_MBUTTON
            else:
                self.logger.error(f"不支持的鼠标按键: {button}")
                return False

            self.logger.debug(f"开始后台点击: ({x}, {y}), 按键: {button.value}")

            # 发送鼠标按下消息
            if not self._send_mouse_message(down_msg, x, y, wparam):
                self.logger.error("发送鼠标按下消息失败")
                return False

            # 模拟点击持续时间
            time.sleep(self.click_duration)

            # 发送鼠标释放消息
            if not self._send_mouse_message(up_msg, x, y, 0):
                self.logger.error("发送鼠标释放消息失败")
                return False

            self.logger.info(f"后台点击成功: ({x}, {y}), 按键: {button.value}")
            return True

        except Exception as e:
            self.logger.error(f"后台点击异常: {e}")
            return False
    
    def background_key_press(self, key_code: int, duration: Optional[float] = None) -> bool:
        """
        后台按键操作
        
        Args:
            key_code: 虚拟键码
            duration: 按键持续时间
            
        Returns:
            是否按键成功
        """
        try:
            if duration is None:
                duration = self.key_press_duration
            
            # 构造LPARAM参数
            lparam = 0x00000001  # 重复计数为1
            
            # 发送按键按下消息
            if not self._send_key_message(self.WM_KEYDOWN, key_code, lparam):
                return False
            
            # 模拟按键持续时间
            time.sleep(duration)
            
            # 构造释放消息的LPARAM（设置释放标志）
            lparam_up = lparam | 0xC0000000  # 设置释放标志
            
            # 发送按键释放消息
            if not self._send_key_message(self.WM_KEYUP, key_code, lparam_up):
                return False
            
            self.logger.debug(f"后台按键: {key_code}")
            return True
            
        except Exception as e:
            self.logger.error(f"后台按键失败: {e}")
            return False
    
    def background_scroll(self, x: int, y: int, direction: str = "up", clicks: int = 3) -> bool:
        """
        后台滚轮操作
        
        Args:
            x: X坐标
            y: Y坐标
            direction: 滚动方向 ("up" 或 "down")
            clicks: 滚动次数
            
        Returns:
            是否滚动成功
        """
        try:
            # 计算滚动增量
            delta = 120 if direction == "up" else -120
            wparam = (delta << 16)
            
            for _ in range(clicks):
                if not self._send_mouse_message(self.WM_MOUSEWHEEL, x, y, wparam):
                    return False
                time.sleep(0.1)  # 滚动间隔
            
            self.logger.debug(f"后台滚轮: ({x}, {y}), 方向: {direction}, 次数: {clicks}")
            return True
            
        except Exception as e:
            self.logger.error(f"后台滚轮失败: {e}")
            return False
    
    def click(self, x: int, y: int, button: str = "left") -> bool:
        """
        智能点击（支持前台和后台）

        Args:
            x: X坐标
            y: Y坐标
            button: 鼠标按键

        Returns:
            是否点击成功
        """
        self._input_stats["total_inputs"] += 1

        try:
            # 判断是否需要后台输入
            if self.is_background_input_needed():
                self.logger.debug("使用后台点击模式")
                mouse_button = MouseButton(button.lower())
                result = self.background_click(x, y, mouse_button)
                if result:
                    self._input_stats["background_inputs"] += 1
                    return True
                else:
                    self.logger.warning("后台点击失败")
                    self._input_stats["failed_inputs"] += 1
                    return False

            # 使用前台点击（仅当窗口在前台时）
            self.logger.debug("使用前台点击模式")
            result = super().click(x, y, button)
            if result:
                self._input_stats["foreground_inputs"] += 1

            return result

        except Exception as e:
            self._input_stats["failed_inputs"] += 1
            self.logger.error(f"智能点击失败: {e}")
            return False
    
    def key_press(self, key: Union[str, List[str]], duration: Optional[float] = None) -> bool:
        """
        智能按键（支持前台和后台）

        Args:
            key: 按键名称或按键组合列表
            duration: 按键持续时间

        Returns:
            是否按键成功
        """
        self._input_stats["total_inputs"] += 1

        try:
            # 判断是否需要后台输入
            if self.is_background_input_needed() and isinstance(key, str):
                self.logger.debug("使用后台按键模式")
                # 将按键名称转换为虚拟键码
                key_code = self._get_virtual_key_code(key)
                if key_code:
                    result = self.background_key_press(key_code, duration)
                    if result:
                        self._input_stats["background_inputs"] += 1
                        return True
                    else:
                        self.logger.warning("后台按键失败")
                        self._input_stats["failed_inputs"] += 1
                        return False

            # 使用前台按键（仅当窗口在前台时）
            self.logger.debug("使用前台按键模式")
            result = super().key_press(key, duration)
            if result:
                self._input_stats["foreground_inputs"] += 1

            return result

        except Exception as e:
            self._input_stats["failed_inputs"] += 1
            self.logger.error(f"智能按键失败: {e}")
            return False
    
    def _get_virtual_key_code(self, key_name: str) -> Optional[int]:
        """
        获取虚拟键码
        
        Args:
            key_name: 按键名称
            
        Returns:
            虚拟键码
        """
        key_map = {
            'space': 0x20,
            'enter': 0x0D,
            'escape': 0x1B,
            'tab': 0x09,
            'shift': 0x10,
            'ctrl': 0x11,
            'alt': 0x12,
            'left': 0x25,
            'up': 0x26,
            'right': 0x27,
            'down': 0x28,
            'f1': 0x70,
            'f2': 0x71,
            'f3': 0x72,
            'f4': 0x73,
            'f5': 0x74,
            'f6': 0x75,
            'f7': 0x76,
            'f8': 0x77,
            'f9': 0x78,
            'f10': 0x79,
            'f11': 0x7A,
            'f12': 0x7B,
        }
        
        key_lower = key_name.lower()
        
        # 检查特殊键
        if key_lower in key_map:
            return key_map[key_lower]
        
        # 检查字母和数字
        if len(key_name) == 1:
            char = key_name.upper()
            if 'A' <= char <= 'Z':
                return ord(char)
            elif '0' <= char <= '9':
                return ord(char)
        
        return None
    
    def get_input_stats(self) -> Dict[str, Any]:
        """获取输入统计信息"""
        return {
            **self._input_stats,
            "background_mode_enabled": self.enable_background_mode,
            "input_method": self.background_input_method.value,
            "target_window": self.target_window_handle
        }
