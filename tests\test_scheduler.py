"""
测试调度系统
"""

import pytest
import time
import tempfile
import shutil
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.modules.scheduler import (
    Scheduler, TaskManager, StateManager, ConfigManager,
    Task, TaskStatus, TaskPriority, GameStateSnapshot
)
from src.core.data_structures import (
    GameState, Action, ActionType, GameScene, UserStrategy
)


class TestTask:
    """测试Task类"""
    
    def test_task_creation(self):
        """测试Task创建"""
        def dummy_func():
            return "test"
        
        task = Task(
            name="测试任务",
            description="这是一个测试任务",
            priority=TaskPriority.HIGH,
            execute_func=dummy_func
        )
        
        assert task.name == "测试任务"
        assert task.description == "这是一个测试任务"
        assert task.priority == TaskPriority.HIGH
        assert task.status == TaskStatus.PENDING
        assert task.execute_func == dummy_func
        assert task.task_id is not None
    
    def test_task_ready_to_execute(self):
        """测试任务是否准备执行"""
        task = Task(name="测试任务")
        
        # 默认状态应该准备执行
        assert task.is_ready_to_execute() is True
        
        # 设置未来时间
        task.scheduled_at = datetime.now() + timedelta(hours=1)
        assert task.is_ready_to_execute() is False
        
        # 设置过去时间
        task.scheduled_at = datetime.now() - timedelta(hours=1)
        assert task.is_ready_to_execute() is True
        
        # 非PENDING状态
        task.status = TaskStatus.RUNNING
        assert task.is_ready_to_execute() is False
    
    def test_task_retry_logic(self):
        """测试任务重试逻辑"""
        task = Task(name="测试任务", max_retries=3)
        
        # 初始状态不能重试
        assert task.can_retry() is False
        
        # 失败状态可以重试
        task.status = TaskStatus.FAILED
        assert task.can_retry() is True
        
        # 重试次数用完
        task.retry_count = 3
        assert task.can_retry() is False
        
        # 重置重试
        task.retry_count = 1
        task.reset_for_retry()
        assert task.status == TaskStatus.PENDING
        assert task.retry_count == 2


class TestTaskManager:
    """测试TaskManager类"""
    
    def setup_method(self):
        """测试前准备"""
        self.task_manager = TaskManager()
    
    def test_task_manager_init(self):
        """测试TaskManager初始化"""
        assert len(self.task_manager.tasks) == 0
        assert len(self.task_manager.task_queue) == 0
        assert self.task_manager.total_tasks_created == 0
    
    def test_create_task(self):
        """测试创建任务"""
        def dummy_func():
            return "test"
        
        task_id = self.task_manager.create_task(
            name="测试任务",
            execute_func=dummy_func,
            description="测试描述",
            priority=TaskPriority.HIGH
        )
        
        assert task_id in self.task_manager.tasks
        assert len(self.task_manager.task_queue) == 1
        assert self.task_manager.total_tasks_created == 1
        
        task = self.task_manager.get_task(task_id)
        assert task.name == "测试任务"
        assert task.priority == TaskPriority.HIGH
    
    def test_task_priority_queue(self):
        """测试任务优先级队列"""
        # 创建不同优先级的任务
        low_id = self.task_manager.create_task("低优先级", lambda: None, priority=TaskPriority.LOW)
        high_id = self.task_manager.create_task("高优先级", lambda: None, priority=TaskPriority.HIGH)
        normal_id = self.task_manager.create_task("普通优先级", lambda: None, priority=TaskPriority.NORMAL)
        
        # 检查队列顺序（高优先级在前）
        assert self.task_manager.task_queue[0] == high_id
        assert self.task_manager.task_queue[1] == normal_id
        assert self.task_manager.task_queue[2] == low_id
    
    def test_get_next_task(self):
        """测试获取下一个任务"""
        # 空队列
        assert self.task_manager.get_next_task() is None
        
        # 创建任务
        task_id = self.task_manager.create_task("测试任务", lambda: None)
        next_task = self.task_manager.get_next_task()
        
        assert next_task is not None
        assert next_task.task_id == task_id
    
    def test_task_lifecycle(self):
        """测试任务生命周期"""
        task_id = self.task_manager.create_task("测试任务", lambda: None)
        
        # 开始任务
        assert self.task_manager.start_task(task_id) is True
        task = self.task_manager.get_task(task_id)
        assert task.status == TaskStatus.RUNNING
        assert task.started_at is not None
        
        # 完成任务
        assert self.task_manager.complete_task(task_id, "result") is True
        assert task.status == TaskStatus.COMPLETED
        assert task.completed_at is not None
        assert task.result == "result"
        assert self.task_manager.total_tasks_completed == 1
    
    def test_task_failure_and_retry(self):
        """测试任务失败和重试"""
        task_id = self.task_manager.create_task("测试任务", lambda: None, max_retries=2)
        
        # 开始任务
        self.task_manager.start_task(task_id)
        
        # 第一次失败（应该重试）
        assert self.task_manager.fail_task(task_id, "错误1") is True
        task = self.task_manager.get_task(task_id)
        assert task.status == TaskStatus.PENDING
        assert task.retry_count == 1
        
        # 再次开始和失败
        self.task_manager.start_task(task_id)
        assert self.task_manager.fail_task(task_id, "错误2") is True
        assert task.retry_count == 2
        
        # 第三次失败（不再重试）
        self.task_manager.start_task(task_id)
        assert self.task_manager.fail_task(task_id, "错误3") is False
        assert task.status == TaskStatus.FAILED
        assert self.task_manager.total_tasks_failed == 1
    
    def test_task_dependencies(self):
        """测试任务依赖"""
        # 创建依赖任务
        dep_id = self.task_manager.create_task("依赖任务", lambda: None)
        task_id = self.task_manager.create_task("主任务", lambda: None, dependencies=[dep_id])
        
        # 主任务不应该准备执行（依赖未完成）
        main_task = self.task_manager.get_task(task_id)
        assert not self.task_manager._are_dependencies_satisfied(main_task)
        
        # 完成依赖任务
        self.task_manager.start_task(dep_id)
        self.task_manager.complete_task(dep_id)
        
        # 现在主任务应该准备执行
        assert self.task_manager._are_dependencies_satisfied(main_task)
    
    def test_task_cancellation(self):
        """测试任务取消"""
        task_id = self.task_manager.create_task("测试任务", lambda: None)
        
        assert self.task_manager.cancel_task(task_id) is True
        task = self.task_manager.get_task(task_id)
        assert task.status == TaskStatus.CANCELLED
        assert task_id not in self.task_manager.task_queue
    
    def test_get_tasks_by_status(self):
        """测试按状态获取任务"""
        # 创建不同状态的任务
        pending_id = self.task_manager.create_task("待处理", lambda: None)
        running_id = self.task_manager.create_task("运行中", lambda: None)
        
        self.task_manager.start_task(running_id)
        
        pending_tasks = self.task_manager.get_tasks_by_status(TaskStatus.PENDING)
        running_tasks = self.task_manager.get_tasks_by_status(TaskStatus.RUNNING)
        
        assert len(pending_tasks) == 1
        assert len(running_tasks) == 1
        assert pending_tasks[0].task_id == pending_id
        assert running_tasks[0].task_id == running_id
    
    def test_cleanup_completed_tasks(self):
        """测试清理已完成任务"""
        # 创建多个任务并完成
        task_ids = []
        for i in range(5):
            task_id = self.task_manager.create_task(f"任务{i}", lambda: None)
            self.task_manager.start_task(task_id)
            self.task_manager.complete_task(task_id)
            task_ids.append(task_id)
        
        # 清理，只保留2个
        self.task_manager.cleanup_completed_tasks(keep_recent=2)
        
        # 检查剩余任务数量
        remaining_tasks = len([t for t in self.task_manager.tasks.values() 
                              if t.status == TaskStatus.COMPLETED])
        assert remaining_tasks == 2


class TestStateManager:
    """测试StateManager类"""
    
    def setup_method(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.state_manager = StateManager(data_dir=self.temp_dir)
    
    def teardown_method(self):
        """测试后清理"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_state_manager_init(self):
        """测试StateManager初始化"""
        assert self.state_manager.data_dir.exists()
        assert self.state_manager.current_state is None
        assert len(self.state_manager.state_history) == 0
    
    def test_save_and_load_current_state(self):
        """测试保存和加载当前状态"""
        game_state = GameState(
            current_scene=GameScene.PRODUCE_MAIN,
            stamina=80,
            vigor=70
        )
        
        # 保存状态
        assert self.state_manager.save_current_state(game_state) is True
        
        # 加载状态
        loaded_state = self.state_manager.load_current_state()
        assert loaded_state is not None
        assert loaded_state.current_scene == GameScene.PRODUCE_MAIN
        assert loaded_state.stamina == 80
        assert loaded_state.vigor == 70
    
    def test_create_and_restore_snapshot(self):
        """测试创建和恢复快照"""
        game_state = GameState(
            current_scene=GameScene.PRODUCE_BATTLE,
            stamina=60,
            vigor=80
        )
        
        # 创建快照
        snapshot_id = self.state_manager.create_snapshot(
            game_state, 
            snapshot_id="test_snapshot",
            metadata={"test": "data"}
        )
        
        assert snapshot_id == "test_snapshot"
        assert len(self.state_manager.state_history) == 1
        
        # 获取快照
        snapshot = self.state_manager.get_snapshot("test_snapshot")
        assert snapshot is not None
        assert snapshot.game_state.current_scene == GameScene.PRODUCE_BATTLE
        assert snapshot.metadata["test"] == "data"
        
        # 恢复快照
        restored_state = self.state_manager.restore_snapshot("test_snapshot")
        assert restored_state is not None
        assert restored_state.current_scene == GameScene.PRODUCE_BATTLE
    
    def test_list_and_delete_snapshots(self):
        """测试列出和删除快照"""
        # 创建多个快照
        for i in range(3):
            game_state = GameState(current_scene=GameScene.MAIN_MENU, stamina=50+i*10)
            self.state_manager.create_snapshot(game_state, f"snapshot_{i}")
        
        # 列出快照
        snapshots = self.state_manager.list_snapshots()
        assert len(snapshots) == 3
        
        # 删除快照
        assert self.state_manager.delete_snapshot("snapshot_1") is True
        assert len(self.state_manager.list_snapshots()) == 2
        
        # 删除不存在的快照
        assert self.state_manager.delete_snapshot("nonexistent") is False
    
    def test_cleanup_old_snapshots(self):
        """测试清理旧快照"""
        # 创建多个快照
        for i in range(10):
            game_state = GameState(current_scene=GameScene.MAIN_MENU)
            self.state_manager.create_snapshot(game_state)
        
        # 清理，只保留5个
        self.state_manager.cleanup_old_snapshots(keep_count=5)
        
        assert len(self.state_manager.state_history) == 5


class TestConfigManager:
    """测试ConfigManager类"""
    
    def setup_method(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_manager = ConfigManager(config_dir=self.temp_dir)
    
    def teardown_method(self):
        """测试后清理"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_config_manager_init(self):
        """测试ConfigManager初始化"""
        assert self.config_manager.config_dir.exists()
        assert self.config_manager.current_system_config is not None
        assert self.config_manager.current_user_strategy is not None
    
    def test_save_and_load_user_config(self):
        """测试保存和加载用户配置"""
        user_strategy = UserStrategy()
        user_strategy.produce_goal.target = "high_score"
        user_strategy.behavior.risk_aversion = 0.3
        
        # 保存配置
        assert self.config_manager.save_user_config(user_strategy) is True
        
        # 加载配置
        loaded_strategy = self.config_manager.load_user_config()
        assert loaded_strategy.produce_goal.target == "high_score"
        assert loaded_strategy.behavior.risk_aversion == 0.3
    
    def test_save_and_load_system_config(self):
        """测试保存和加载系统配置"""
        system_config = {
            "version": "1.0.0",
            "debug_mode": True,
            "modules": {
                "perception": {"enabled": True},
                "decision": {"enabled": True},
                "action": {"enabled": True},
                "scheduler": {"enabled": True}
            }
        }
        
        # 保存配置
        assert self.config_manager.save_system_config(system_config) is True
        
        # 加载配置
        loaded_config = self.config_manager.load_system_config()
        assert loaded_config["version"] == "1.0.0"
        assert loaded_config["debug_mode"] is True
    
    def test_get_and_set_config_value(self):
        """测试获取和设置配置值"""
        # 设置配置值
        assert self.config_manager.set_config_value("test.nested.value", 42) is True
        
        # 获取配置值
        value = self.config_manager.get_config_value("test.nested.value")
        assert value == 42
        
        # 获取不存在的值
        value = self.config_manager.get_config_value("nonexistent.key", "default")
        assert value == "default"
    
    def test_profile_management(self):
        """测试配置文件管理"""
        user_strategy = UserStrategy()
        user_strategy.produce_goal.target = "true_end"
        
        # 保存配置文件
        assert self.config_manager.save_user_config(user_strategy, "test_profile") is True
        
        # 列出配置文件
        profiles = self.config_manager.list_profiles()
        assert "test_profile" in profiles
        
        # 加载配置文件
        loaded_strategy = self.config_manager.load_user_config("test_profile")
        assert loaded_strategy.produce_goal.target == "true_end"
        
        # 删除配置文件
        assert self.config_manager.delete_profile("test_profile") is True
        profiles = self.config_manager.list_profiles()
        assert "test_profile" not in profiles


class TestScheduler:
    """测试Scheduler类"""
    
    def setup_method(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        
        # 使用临时目录创建调度器
        with patch('src.modules.scheduler.state_manager.StateManager') as mock_state_manager, \
             patch('src.modules.scheduler.config_manager.ConfigManager') as mock_config_manager:
            
            # 配置Mock对象
            mock_state_manager.return_value.load_current_state.return_value = None
            mock_config_manager.return_value.load_user_config.return_value = UserStrategy()
            mock_config_manager.return_value.load_system_config.return_value = {
                "modules": {
                    "perception": {"enabled": False},
                    "decision": {"enabled": False},
                    "action": {"enabled": False},
                    "scheduler": {"enabled": True}
                }
            }
            
            self.scheduler = Scheduler()
    
    def teardown_method(self):
        """测试后清理"""
        if hasattr(self, 'scheduler'):
            self.scheduler.stop()
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_scheduler_init(self):
        """测试Scheduler初始化"""
        assert self.scheduler.task_manager is not None
        assert self.scheduler.state_manager is not None
        assert self.scheduler.config_manager is not None
        assert self.scheduler.is_running is False
    
    def test_schedule_task(self):
        """测试调度任务"""
        def test_func():
            return "test_result"
        
        task_id = self.scheduler.schedule_task(
            name="测试任务",
            execute_func=test_func,
            description="测试描述",
            priority=TaskPriority.HIGH
        )
        
        assert task_id is not None
        task = self.scheduler.task_manager.get_task(task_id)
        assert task.name == "测试任务"
        assert task.priority == TaskPriority.HIGH
    
    def test_schedule_game_action(self):
        """测试调度游戏行动"""
        action = Action(
            action_type=ActionType.CLICK,
            target=(100, 200),
            description="测试点击"
        )
        
        task_id = self.scheduler.schedule_game_action(action, TaskPriority.HIGH)
        
        assert task_id is not None
        task = self.scheduler.task_manager.get_task(task_id)
        assert "执行行动" in task.name
        assert task.priority == TaskPriority.HIGH
    
    def test_scheduler_status(self):
        """测试调度器状态"""
        status = self.scheduler.get_scheduler_status()
        
        assert "is_running" in status
        assert "is_paused" in status
        assert "total_tasks_executed" in status
        assert "task_manager_stats" in status
        assert "state_manager_stats" in status
        assert "config_manager_stats" in status
    
    def test_scheduler_info(self):
        """测试调度器信息"""
        info = self.scheduler.get_scheduler_info()
        
        assert "status" in info
        assert "modules" in info
        assert "task_manager" in info
        assert "state_manager" in info
        assert "config_manager" in info


if __name__ == "__main__":
    pytest.main([__file__])
