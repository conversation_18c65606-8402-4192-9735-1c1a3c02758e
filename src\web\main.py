"""
Gakumasu-Bot Web API 主服务器
基于FastAPI的Web界面后端服务
"""

import os
import time
import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime
from pathlib import Path

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, Depends
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse, Response
from pydantic import BaseModel

from ..utils.logger import get_logger
from ..modules.scheduler.scheduler import Scheduler
from ..modules.scheduler.config_manager import ConfigManager
from ..modules.scheduler.state_manager import StateManager
from ..modules.scheduler.game_tasks import ProduceTask
from ..modules.screenshot_collector import ScreenshotCollector, ScreenshotMode, ScreenshotFormat
from ..modules.screenshot_collector import ScreenshotConfig as InternalScreenshotConfig
from ..core.data_structures import GameState, GakumasuBotException
from .models import *


# 数据模型定义
class SystemStatus(BaseModel):
    """系统状态响应模型"""
    status: str
    uptime: float
    scheduler_status: str
    active_tasks: int
    completed_tasks: int
    failed_tasks: int
    current_game_state: Optional[Dict[str, Any]] = None


class ControlRequest(BaseModel):
    """控制请求模型"""
    action: str
    parameters: Optional[Dict[str, Any]] = None


class TaskRequest(BaseModel):
    """任务请求模型"""
    task_type: str
    parameters: Optional[Dict[str, Any]] = None


class ConfigUpdateRequest(BaseModel):
    """配置更新请求模型"""
    section: str
    key: str
    value: Any


def convert_screenshot_record_data(record_dict: Dict[str, Any]) -> Dict[str, Any]:
    """
    转换截图记录数据格式，处理image_size字段类型转换和路径标准化

    Args:
        record_dict: 原始记录字典

    Returns:
        转换后的记录字典
    """
    converted = record_dict.copy()

    # 处理image_size字段
    image_size = converted.get('image_size')
    if isinstance(image_size, tuple) and len(image_size) == 2:
        # 从元组转换为ImageSize实例
        width, height = image_size
        converted['image_size'] = ImageSize(width=width, height=height)
    elif isinstance(image_size, dict):
        # 从字典转换为ImageSize实例
        converted['image_size'] = ImageSize(**image_size)
    elif not isinstance(image_size, ImageSize):
        # 如果不是预期的类型，设置默认值并记录警告
        print(f"[WARNING] 未知的image_size类型: {type(image_size)}, 值: {image_size}")
        converted['image_size'] = ImageSize(width=0, height=0)

    # 处理thumbnail_path字段，确保返回相对于静态文件服务的路径
    thumbnail_path = converted.get('thumbnail_path')
    if thumbnail_path:
        # 如果是完整路径，提取文件名部分
        if '/' in thumbnail_path or '\\' in thumbnail_path:
            # 提取文件名
            filename = thumbnail_path.split('/')[-1].split('\\')[-1]
            # 构建相对于静态文件服务的路径
            converted['thumbnail_path'] = f"thumbnails/{filename}"
            print(f"[DEBUG] 转换缩略图路径: {thumbnail_path} -> {converted['thumbnail_path']}")
        else:
            # 如果已经是文件名，添加thumbnails前缀
            converted['thumbnail_path'] = f"thumbnails/{thumbnail_path}"

    return converted


# Web API适配器类
class WebAPIAdapter:
    """Web API适配器，连接Web界面和核心系统"""
    
    def __init__(self):
        self.logger = get_logger("WebAPIAdapter")

        # 核心模块
        self.scheduler: Optional[Scheduler] = None
        self.config_manager: Optional[ConfigManager] = None
        self.state_manager: Optional[StateManager] = None
        self.screenshot_collector: Optional[ScreenshotCollector] = None

        # 状态跟踪
        self.start_time = datetime.now()
        self.websocket_connections: List[WebSocket] = []
        self.connection_stats = {
            "total_connections": 0,
            "active_connections": 0,
            "messages_sent": 0,
            "messages_received": 0,
            "errors": 0
        }

        self.logger.info("Web API适配器初始化完成")
    
    def initialize_core_modules(self):
        """初始化核心模块"""
        try:
            # 创建调度器（它会自动创建自己的config_manager和state_manager）
            self.scheduler = Scheduler()

            # 获取调度器内部的管理器实例
            self.config_manager = self.scheduler.config_manager
            self.state_manager = self.scheduler.state_manager

            # 初始化截图收集器
            self.screenshot_collector = ScreenshotCollector()

            self.logger.info("核心模块初始化完成")

        except Exception as e:
            self.logger.error(f"核心模块初始化失败: {e}")
            raise
    
    async def get_system_status(self) -> SystemStatus:
        """获取系统状态"""
        try:
            uptime = (datetime.now() - self.start_time).total_seconds()
            
            # 获取调度器状态
            scheduler_status = "未初始化"
            active_tasks = 0
            completed_tasks = 0
            failed_tasks = 0
            
            if self.scheduler:
                scheduler_status = "运行中" if self.scheduler.is_running else "已停止"
                # 使用task_manager的统计信息
                task_stats = self.scheduler.task_manager.get_manager_statistics()
                active_tasks = task_stats.get('status_counts', {}).get('running', 0)
                completed_tasks = task_stats.get('total_completed', 0)
                failed_tasks = task_stats.get('total_failed', 0)
            
            # 获取当前游戏状态
            current_game_state = None
            if self.state_manager:
                game_state = self.state_manager.load_current_state()
                if game_state:
                    current_game_state = game_state.to_dict()
            
            return SystemStatus(
                status="运行中",
                uptime=uptime,
                scheduler_status=scheduler_status,
                active_tasks=active_tasks,
                completed_tasks=completed_tasks,
                failed_tasks=failed_tasks,
                current_game_state=current_game_state
            )
            
        except Exception as e:
            self.logger.error(f"获取系统状态失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def control_system(self, action: str, parameters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """控制系统操作"""
        try:
            self.logger.info(f"执行系统控制: {action}")
            
            if action == "start":
                return await self._start_system()
            elif action == "stop":
                return await self._stop_system()
            elif action == "restart":
                return await self._restart_system()
            elif action == "pause":
                return await self._pause_system()
            elif action == "resume":
                return await self._resume_system()
            else:
                raise HTTPException(status_code=400, detail=f"未知的控制操作: {action}")
                
        except Exception as e:
            self.logger.error(f"系统控制失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def create_task(self, task_type: str, parameters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """创建新任务"""
        try:
            if not self.scheduler:
                raise HTTPException(status_code=503, detail="调度器未初始化")
            
            self.logger.info(f"创建任务: {task_type}")
            
            if task_type == "produce":
                # 创建育成任务
                task = ProduceTask(
                    user_strategy=parameters.get('strategy', {}) if parameters else {}
                )
                task_id = self.scheduler.add_task(task)
                
                return {
                    "task_id": task_id,
                    "task_type": task_type,
                    "status": "已创建",
                    "message": "育成任务已添加到队列"
                }
            else:
                raise HTTPException(status_code=400, detail=f"未知的任务类型: {task_type}")
                
        except Exception as e:
            self.logger.error(f"创建任务失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def get_task_list(self) -> List[Dict[str, Any]]:
        """获取任务列表"""
        try:
            if not self.scheduler:
                return []
            
            tasks = self.scheduler.get_all_tasks()
            return [
                {
                    "task_id": task.task_id,
                    "name": task.name,
                    "status": task.status.value,
                    "priority": task.priority.value,
                    "created_at": task.created_at.isoformat(),
                    "started_at": task.started_at.isoformat() if task.started_at else None,
                    "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                    "progress": getattr(task, 'current_phase', {}).value if hasattr(task, 'current_phase') else None
                }
                for task in tasks
            ]
            
        except Exception as e:
            self.logger.error(f"获取任务列表失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def get_config(self) -> Dict[str, Any]:
        """获取配置信息"""
        try:
            if not self.config_manager:
                return {}

            # 返回当前系统配置和用户策略
            config = {
                "system": self.config_manager.current_system_config,
                "user_strategy": self.config_manager.current_user_strategy.to_dict() if self.config_manager.current_user_strategy else None,
                "current_profile": self.config_manager.current_profile,
                "available_profiles": self.config_manager.list_profiles()
            }
            return config

        except Exception as e:
            self.logger.error(f"获取配置失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def update_config(self, section: str, key: str, value: Any) -> Dict[str, Any]:
        """更新配置"""
        try:
            if not self.config_manager:
                raise HTTPException(status_code=503, detail="配置管理器未初始化")

            # 使用set_config_value方法，它接受点分隔的键路径
            key_path = f"{section}.{key}" if section else key
            success = self.config_manager.set_config_value(key_path, value)

            if not success:
                raise Exception("配置更新失败")

            return {
                "section": section,
                "key": key,
                "value": value,
                "message": "配置更新成功"
            }

        except Exception as e:
            self.logger.error(f"更新配置失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def add_websocket_connection(self, websocket: WebSocket):
        """添加WebSocket连接"""
        self.websocket_connections.append(websocket)
        self.connection_stats["total_connections"] += 1
        self.connection_stats["active_connections"] = len(self.websocket_connections)
        self.logger.info(f"WebSocket连接已添加，当前连接数: {len(self.websocket_connections)}")

    async def remove_websocket_connection(self, websocket: WebSocket):
        """移除WebSocket连接"""
        if websocket in self.websocket_connections:
            self.websocket_connections.remove(websocket)
            self.connection_stats["active_connections"] = len(self.websocket_connections)
        self.logger.info(f"WebSocket连接已移除，当前连接数: {len(self.websocket_connections)}")
    
    async def broadcast_message(self, message: Dict[str, Any]):
        """广播消息到所有WebSocket连接"""
        if not self.websocket_connections:
            return

        disconnected = []
        sent_count = 0

        for websocket in self.websocket_connections:
            try:
                await websocket.send_json(message)
                sent_count += 1
                self.connection_stats["messages_sent"] += 1
            except Exception as e:
                self.logger.warning(f"WebSocket发送失败: {e}")
                self.connection_stats["errors"] += 1
                disconnected.append(websocket)

        # 清理断开的连接
        for websocket in disconnected:
            await self.remove_websocket_connection(websocket)

        if sent_count > 0:
            self.logger.debug(f"广播消息已发送到 {sent_count} 个连接")
    
    # 私有方法
    async def _start_system(self) -> Dict[str, Any]:
        """启动系统"""
        if not self.scheduler:
            self.initialize_core_modules()
        
        if not self.scheduler.is_running:
            await self.scheduler.start()
        
        await self.broadcast_message({
            "type": "system_status",
            "data": {"status": "started", "timestamp": datetime.now().isoformat()}
        })
        
        return {"status": "success", "message": "系统已启动"}
    
    async def _stop_system(self) -> Dict[str, Any]:
        """停止系统"""
        if self.scheduler and self.scheduler.is_running:
            await self.scheduler.stop()
        
        await self.broadcast_message({
            "type": "system_status",
            "data": {"status": "stopped", "timestamp": datetime.now().isoformat()}
        })
        
        return {"status": "success", "message": "系统已停止"}
    
    async def _restart_system(self) -> Dict[str, Any]:
        """重启系统"""
        await self._stop_system()
        await asyncio.sleep(1)  # 等待1秒
        return await self._start_system()
    
    async def _pause_system(self) -> Dict[str, Any]:
        """暂停系统"""
        if self.scheduler:
            self.scheduler.pause()
        
        return {"status": "success", "message": "系统已暂停"}
    
    async def _resume_system(self) -> Dict[str, Any]:
        """恢复系统"""
        if self.scheduler:
            self.scheduler.resume()

        return {"status": "success", "message": "系统已恢复"}

    # 截图相关方法
    async def capture_screenshot(self, config_dict: Dict[str, Any]) -> ScreenshotResult:
        """
        执行截图

        Args:
            config_dict: 截图配置字典

        Returns:
            截图结果
        """
        if not self.screenshot_collector:
            raise GakumasuBotException("截图收集器未初始化")

        # 生成任务ID用于跟踪
        task_id = f"screenshot_{int(time.time() * 1000)}"

        try:
            # 广播任务开始消息
            await self.broadcast_message({
                "type": "screenshot_task_started",
                "data": {
                    "task_id": task_id,
                    "config": config_dict,
                    "timestamp": datetime.now().isoformat()
                }
            })

            # 转换配置
            config = self._convert_screenshot_config(config_dict)

            # 执行截图
            result = await self.screenshot_collector.capture_single_shot(config)

            # 广播截图完成消息
            if result.success:
                await self.broadcast_message({
                    "type": "screenshot_task_completed",
                    "data": {
                        "task_id": task_id,
                        "result": result.to_dict(),
                        "timestamp": datetime.now().isoformat()
                    }
                })
            else:
                await self.broadcast_message({
                    "type": "screenshot_task_failed",
                    "data": {
                        "task_id": task_id,
                        "error": result.error_message,
                        "timestamp": datetime.now().isoformat()
                    }
                })

            return result

        except Exception as e:
            self.logger.error(f"截图失败: {e}")
            await self.broadcast_message({
                "type": "screenshot_task_failed",
                "data": {
                    "task_id": task_id,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                }
            })
            raise

    async def get_screenshot_preview(self) -> bytes:
        """
        获取截图预览

        Returns:
            JPEG格式的图像数据
        """
        if not self.screenshot_collector:
            raise GakumasuBotException("截图收集器未初始化")

        try:
            # 获取单帧预览
            async for frame in self.screenshot_collector.start_preview_stream():
                # 只返回第一帧
                self.screenshot_collector.stop_preview_stream()
                return frame

            raise GakumasuBotException("无法获取预览图像")

        except Exception as e:
            self.logger.error(f"获取预览失败: {e}")
            raise

    async def get_screenshot_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """
        获取截图历史记录

        Args:
            limit: 返回记录数量限制

        Returns:
            历史记录列表
        """
        if not self.screenshot_collector:
            raise GakumasuBotException("截图收集器未初始化")

        try:
            records = self.screenshot_collector.get_capture_history(limit)
            return [record.to_dict() for record in records]

        except Exception as e:
            self.logger.error(f"获取历史记录失败: {e}")
            raise

    async def delete_screenshot(self, screenshot_id: str) -> bool:
        """
        删除截图

        Args:
            screenshot_id: 截图ID

        Returns:
            是否删除成功
        """
        if not self.screenshot_collector:
            raise GakumasuBotException("截图收集器未初始化")

        try:
            success = self.screenshot_collector.delete_screenshot(screenshot_id)

            if success:
                # 广播删除消息
                await self.broadcast_message({
                    "type": "screenshot_deleted",
                    "data": {"id": screenshot_id}
                })

            return success

        except Exception as e:
            self.logger.error(f"删除截图失败: {e}")
            raise

    async def get_screenshot_stats(self) -> Dict[str, Any]:
        """
        获取截图统计信息

        Returns:
            统计信息字典
        """
        if not self.screenshot_collector:
            raise GakumasuBotException("截图收集器未初始化")

        try:
            return self.screenshot_collector.get_stats()

        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}")
            raise

    def _convert_screenshot_config(self, config_dict: Dict[str, Any]) -> InternalScreenshotConfig:
        """
        转换截图配置 - 清理版本

        Args:
            config_dict: 配置字典

        Returns:
            截图配置对象
        """
        print(f"[DEBUG] _convert_screenshot_config 接收到配置: {config_dict}")
        self.logger.info(f"_convert_screenshot_config 接收到配置: {config_dict}")

        # 转换模式 - 处理字符串和枚举对象两种情况
        mode_value = config_dict.get("mode", "window")
        print(f"[DEBUG] 原始模式值: {mode_value} (类型: {type(mode_value)})")
        self.logger.info(f"原始模式值: {mode_value} (类型: {type(mode_value)})")

        # 如果是枚举对象，获取其值
        if hasattr(mode_value, 'value'):
            mode_str = mode_value.value
            print(f"[DEBUG] 模式值来自枚举.value: {mode_str}")
            self.logger.info(f"模式值来自枚举.value: {mode_str}")
        elif hasattr(mode_value, 'name'):
            mode_str = mode_value.name.lower()
            print(f"[DEBUG] 模式值来自枚举.name: {mode_str}")
            self.logger.info(f"模式值来自枚举.name: {mode_str}")
        else:
            mode_str = str(mode_value).lower()
            print(f"[DEBUG] 模式值来自字符串: {mode_str}")
            self.logger.info(f"模式值来自字符串: {mode_str}")

        # 转换为内部枚举 - 关键修复点
        print(f"[DEBUG] 准备转换模式字符串 '{mode_str}' 为 ScreenshotMode 枚举")
        if mode_str == "fullscreen":
            mode = ScreenshotMode.FULLSCREEN
        elif mode_str == "window":
            mode = ScreenshotMode.WINDOW
        elif mode_str == "region":
            mode = ScreenshotMode.REGION
        else:
            print(f"[WARNING] 未知的截图模式: {mode_str}，使用默认窗口模式")
            self.logger.warning(f"未知的截图模式: {mode_str}，使用默认窗口模式")
            mode = ScreenshotMode.WINDOW

        print(f"[DEBUG] 模式转换完成: {mode_str} -> {mode} (类型: {type(mode)})")

        # 转换格式 - 处理字符串和枚举对象两种情况
        format_value = config_dict.get("format", "png")
        print(f"[DEBUG] 原始格式值: {format_value} (类型: {type(format_value)})")

        # 如果是枚举对象，获取其值
        if hasattr(format_value, 'value'):
            format_str = format_value.value
            print(f"[DEBUG] 格式值来自枚举.value: {format_str}")
        elif hasattr(format_value, 'name'):
            format_str = format_value.name.lower()
            print(f"[DEBUG] 格式值来自枚举.name: {format_str}")
        else:
            format_str = str(format_value).lower()
            print(f"[DEBUG] 格式值来自字符串: {format_str}")

        # 转换为内部枚举 - 关键修复点
        print(f"[DEBUG] 准备转换格式字符串 '{format_str}' 为 ScreenshotFormat 枚举")
        if format_str == "png":
            format_enum = ScreenshotFormat.PNG
        elif format_str == "jpeg":
            format_enum = ScreenshotFormat.JPEG
        elif format_str == "bmp":
            format_enum = ScreenshotFormat.BMP
        else:
            print(f"[WARNING] 未知的图片格式: {format_str}，使用默认PNG格式")
            format_enum = ScreenshotFormat.PNG

        print(f"[DEBUG] 格式转换完成: {format_str} -> {format_enum} (类型: {type(format_enum)})")

        # 转换区域
        region = None
        if config_dict.get("region"):
            region_data = config_dict["region"]
            region = {
                "x": region_data.get("x", 0),
                "y": region_data.get("y", 0),
                "width": region_data.get("width", 100),
                "height": region_data.get("height", 100)
            }

        # 创建最终配置对象 - 使用内部dataclass版本
        final_config = InternalScreenshotConfig(
            mode=mode,
            format=format_enum,
            quality=config_dict.get("quality", 90),
            region=region,
            save_to_disk=config_dict.get("save_to_disk", True),
            filename_prefix=config_dict.get("filename_prefix", "screenshot")
        )

        print(f"[DEBUG] 最终转换的配置对象: mode={final_config.mode} (类型: {type(final_config.mode)}), format={final_config.format}")
        self.logger.info(f"最终转换的配置对象: mode={final_config.mode} (类型: {type(final_config.mode)}), format={final_config.format}")
        return final_config

    def get_websocket_stats(self) -> Dict[str, Any]:
        """
        获取WebSocket连接统计信息

        Returns:
            连接统计信息字典
        """
        return {
            **self.connection_stats,
            "uptime_seconds": (datetime.now() - self.start_time).total_seconds()
        }

    async def _handle_preview_stream(self, websocket: WebSocket):
        """
        处理预览流

        Args:
            websocket: WebSocket连接
        """
        if not self.screenshot_collector:
            await websocket.send_json({
                "type": "screenshot_error",
                "data": {"error": "截图收集器未初始化"}
            })
            return

        try:
            frame_count = 0
            start_time = time.time()

            async for frame_data in self.screenshot_collector.start_preview_stream():
                try:
                    # 将图像数据编码为base64
                    import base64
                    encoded_data = base64.b64encode(frame_data).decode('utf-8')

                    frame_count += 1
                    current_time = time.time()

                    # 计算实际FPS
                    elapsed = current_time - start_time
                    actual_fps = frame_count / elapsed if elapsed > 0 else 0

                    await websocket.send_json({
                        "type": "screenshot_preview",
                        "data": {
                            "image_data": encoded_data,
                            "timestamp": datetime.now().isoformat(),
                            "frame_count": frame_count,
                            "fps": round(actual_fps, 2),
                            "data_size": len(encoded_data)
                        }
                    })

                except Exception as frame_error:
                    self.logger.warning(f"发送预览帧失败: {frame_error}")
                    # 继续处理下一帧，不中断整个流
                    continue

        except Exception as e:
            self.logger.error(f"预览流处理失败: {e}")
            await websocket.send_json({
                "type": "screenshot_error",
                "data": {"error": str(e)}
            })

    async def _handle_websocket_message(self, websocket: WebSocket, data: Dict[str, Any]):
        """
        处理WebSocket消息

        Args:
            websocket: WebSocket连接
            data: 消息数据
        """
        message_type = data.get("type")
        self.connection_stats["messages_received"] += 1

        try:
            if message_type == "ping":
                await websocket.send_json({
                    "type": "pong",
                    "timestamp": datetime.now().isoformat()
                })

            elif message_type == "get_status":
                status = await self.get_system_status()
                await websocket.send_json({
                    "type": "status_update",
                    "data": status.dict()
                })

            elif message_type == "start_preview":
                # 启动预览流
                await self._handle_preview_stream(websocket)

            elif message_type == "stop_preview":
                # 停止预览流
                if self.screenshot_collector:
                    self.screenshot_collector.stop_preview_stream()
                await websocket.send_json({
                    "type": "preview_stopped",
                    "data": {"message": "预览已停止"}
                })

            elif message_type == "get_screenshot_stats":
                # 获取截图统计
                stats = await self.get_screenshot_stats()
                await websocket.send_json({
                    "type": "screenshot_stats",
                    "data": stats
                })

            elif message_type == "get_screenshot_history":
                # 获取截图历史
                limit = data.get("limit", 50)
                history = await self.get_screenshot_history(limit)
                await websocket.send_json({
                    "type": "screenshot_history",
                    "data": {
                        "total": len(history),
                        "records": history
                    }
                })

            elif message_type == "get_websocket_stats":
                # 获取WebSocket统计信息
                stats = self.get_websocket_stats()
                await websocket.send_json({
                    "type": "websocket_stats",
                    "data": stats
                })

            else:
                await websocket.send_json({
                    "type": "error",
                    "data": {"error": f"未知消息类型: {message_type}"}
                })

        except Exception as e:
            self.logger.error(f"处理WebSocket消息失败: {e}")
            await websocket.send_json({
                "type": "error",
                "data": {"error": str(e)}
            })


# 全局API适配器实例
api_adapter = WebAPIAdapter()

# FastAPI应用实例
app = FastAPI(
    title="Gakumasu-Bot Web API",
    description="学园偶像大师自动化机器人Web控制界面",
    version="1.0.0"
)

# CORS中间件配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 静态文件服务
static_dir = Path(__file__).parent / "static"
if static_dir.exists():
    app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")

# 截图文件服务
screenshots_dir = Path("screenshots")
if screenshots_dir.exists():
    app.mount("/screenshots", StaticFiles(directory=str(screenshots_dir)), name="screenshots")


# API路由定义
@app.get("/")
async def root():
    """根路径，返回Web界面"""
    return {"message": "Gakumasu-Bot Web API", "version": "1.0.0"}


@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.get("/api/health")
async def api_health_check():
    """API健康检查端点"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}


@app.get("/api/v1/status", response_model=SystemStatus)
async def get_system_status():
    """获取系统状态"""
    return await api_adapter.get_system_status()


@app.post("/api/v1/control")
async def control_system(request: ControlRequest):
    """系统控制"""
    return await api_adapter.control_system(request.action, request.parameters)


@app.post("/api/v1/tasks")
async def create_task(request: TaskRequest):
    """创建新任务"""
    return await api_adapter.create_task(request.task_type, request.parameters)


@app.get("/api/v1/tasks")
async def get_task_list():
    """获取任务列表"""
    return await api_adapter.get_task_list()


@app.get("/api/v1/config")
async def get_config():
    """获取配置"""
    return await api_adapter.get_config()


@app.post("/api/v1/config")
async def update_config(request: ConfigUpdateRequest):
    """更新配置"""
    return await api_adapter.update_config(request.section, request.key, request.value)


# 截图相关API路由
@app.get("/api/v1/screenshot/preview")
async def get_screenshot_preview():
    """获取截图预览"""
    try:
        image_data = await api_adapter.get_screenshot_preview()
        return Response(content=image_data, media_type="image/jpeg")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/v1/screenshot/capture")
async def capture_screenshot(request: ScreenshotRequest):
    """执行截图 - 增强调试版本"""
    print("[CRITICAL] 截图API端点被调用！")
    try:
        # 调试原始输入
        print(f"[DEBUG] 原始请求: mode={request.config.mode} ({type(request.config.mode)}), format={request.config.format} ({type(request.config.format)})")

        # 强制转换枚举为字符串
        mode_str = request.config.mode.value if hasattr(request.config.mode, 'value') else str(request.config.mode)
        format_str = request.config.format.value if hasattr(request.config.format, 'value') else str(request.config.format)

        # 再次确保是字符串并转为小写
        mode_str = str(mode_str).lower()
        format_str = str(format_str).lower()

        # 构建配置字典
        config_dict = {
            "mode": mode_str,
            "format": format_str,
            "quality": request.config.quality,
            "region": request.config.region.dict() if request.config.region else None,
            "save_to_disk": request.config.save_to_disk,
            "filename_prefix": request.config.filename_prefix
        }

        # 详细调试输出 - 使用ASCII字符避免编码问题
        print(f"[DEBUG] 转换后的配置: mode={mode_str} ({type(mode_str)}), format={format_str} ({type(format_str)})")
        print(f"[DEBUG] 完整配置字典: {config_dict}")

        # 验证转换结果
        if not isinstance(mode_str, str) or not isinstance(format_str, str):
            error_msg = f"枚举转换失败！mode: {type(mode_str)}, format: {type(format_str)}"
            print(f"[ERROR] {error_msg}")
            raise HTTPException(status_code=500, detail=error_msg)
        
        # 执行截图
        print(f"[INFO] 开始执行截图，配置: {config_dict}")
        internal_result = await api_adapter.capture_screenshot(config_dict)
        print(f"[INFO] 截图执行完成，成功: {internal_result.success}, 错误: {internal_result.error_message}")

        # 构建响应
        response_data = {
            "id": internal_result.id,
            "filename": internal_result.filename,
            "filepath": internal_result.filepath,
            "config": {
                "mode": mode_str,
                "format": format_str,
                "quality": request.config.quality,
                "region": request.config.region.dict() if request.config.region else None,
                "save_to_disk": request.config.save_to_disk,
                "filename_prefix": request.config.filename_prefix
            },
            "timestamp": internal_result.timestamp.isoformat(),
            "file_size": internal_result.file_size,
            "image_size": {
                "width": internal_result.image_size[0],
                "height": internal_result.image_size[1]
            },
            "success": internal_result.success,
            "error_message": internal_result.error_message
        }
        
        return response_data
    except Exception as e:
        print(f"[ERROR] 截图API异常: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/v1/screenshot/history", response_model=ScreenshotHistoryResponse)
async def get_screenshot_history(limit: int = 100):
    """获取截图历史记录"""
    try:
        records = await api_adapter.get_screenshot_history(limit)

        # 转换数据格式，处理image_size字段
        converted_records = []
        for record in records:
            try:
                converted_record = convert_screenshot_record_data(record)
                converted_records.append(ScreenshotRecord(**converted_record))
            except Exception as convert_error:
                print(f"[ERROR] 转换截图记录失败: {convert_error}, 原始数据: {record}")
                # 跳过有问题的记录，继续处理其他记录
                continue

        return ScreenshotHistoryResponse(
            total=len(converted_records),
            records=converted_records
        )
    except Exception as e:
        print(f"[ERROR] 获取截图历史记录失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.delete("/api/v1/screenshot/{screenshot_id}")
async def delete_screenshot(screenshot_id: str):
    """删除截图"""
    try:
        success = await api_adapter.delete_screenshot(screenshot_id)
        if success:
            return {"success": True, "message": "截图已删除"}
        else:
            raise HTTPException(status_code=404, detail="截图不存在")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/v1/screenshot/stats", response_model=ScreenshotStatsResponse)
async def get_screenshot_stats():
    """获取截图统计信息"""
    try:
        stats = await api_adapter.get_screenshot_stats()
        return ScreenshotStatsResponse(**stats)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/v1/screenshot/config", response_model=ScreenshotToolConfig)
async def get_screenshot_config():
    """获取截图工具配置"""
    try:
        # 返回默认配置
        return ScreenshotToolConfig()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/v1/screenshot/config")
async def update_screenshot_config(config: ScreenshotToolConfig):
    """更新截图工具配置"""
    try:
        # 这里可以保存配置到文件或数据库
        # 目前只返回成功响应
        return ApiResponse(
            success=True,
            message="配置已更新",
            data=config.dict()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/v1/screenshot/batch-delete")
async def batch_delete_screenshots(request: BatchDeleteRequest):
    """批量删除截图"""
    try:
        success_count = 0
        failed_count = 0
        failed_ids = []
        errors = []

        for screenshot_id in request.ids:
            try:
                success = await api_adapter.delete_screenshot(screenshot_id)
                if success:
                    success_count += 1
                else:
                    failed_count += 1
                    failed_ids.append(screenshot_id)
                    errors.append(f"截图 {screenshot_id} 不存在")
            except Exception as e:
                failed_count += 1
                failed_ids.append(screenshot_id)
                errors.append(f"删除截图 {screenshot_id} 失败: {str(e)}")

        return BatchOperationResponse(
            total=len(request.ids),
            success_count=success_count,
            failed_count=failed_count,
            failed_ids=failed_ids,
            errors=errors
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/v1/screenshot/{screenshot_id}/download")
async def download_screenshot(screenshot_id: str):
    """下载截图文件"""
    try:
        # 从历史记录中查找文件
        history = await api_adapter.get_screenshot_history()

        target_record = None
        for record in history:
            if record["id"] == screenshot_id:
                target_record = record
                break

        if not target_record:
            raise HTTPException(status_code=404, detail="截图不存在")

        filepath = Path(target_record["filepath"])
        if not filepath.exists():
            raise HTTPException(status_code=404, detail="文件不存在")

        # 读取文件内容
        with open(filepath, "rb") as f:
            content = f.read()

        # 确定媒体类型
        if filepath.suffix.lower() == ".png":
            media_type = "image/png"
        elif filepath.suffix.lower() in [".jpg", ".jpeg"]:
            media_type = "image/jpeg"
        elif filepath.suffix.lower() == ".bmp":
            media_type = "image/bmp"
        else:
            media_type = "application/octet-stream"

        return Response(
            content=content,
            media_type=media_type,
            headers={"Content-Disposition": f"attachment; filename={target_record['filename']}"}
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/v1/websocket/stats")
async def get_websocket_stats():
    """获取WebSocket连接统计信息"""
    try:
        stats = api_adapter.get_websocket_stats()
        return ApiResponse(
            success=True,
            message="WebSocket统计信息获取成功",
            data=stats
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket端点"""
    await websocket.accept()
    await api_adapter.add_websocket_connection(websocket)

    # 发送连接成功消息
    await websocket.send_json({
        "type": "connection_established",
        "data": {
            "message": "WebSocket连接已建立",
            "timestamp": datetime.now().isoformat(),
            "server_version": "1.0.0"
        }
    })

    try:
        while True:
            # 接收客户端消息
            data = await websocket.receive_json()

            # 使用新的消息处理器
            await api_adapter._handle_websocket_message(websocket, data)

    except WebSocketDisconnect:
        await api_adapter.remove_websocket_connection(websocket)
    except Exception as e:
        logger = get_logger("WebSocket")
        logger.error(f"WebSocket连接错误: {e}")
        await api_adapter.remove_websocket_connection(websocket)


# 应用启动事件
@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化"""
    logger = get_logger("WebAPI")
    logger.info("Gakumasu-Bot Web API 启动中...")
    
    try:
        # 初始化API适配器
        api_adapter.initialize_core_modules()
        logger.info("Web API 启动完成")
        
    except Exception as e:
        logger.error(f"Web API 启动失败: {e}")
        raise


# 应用关闭事件
@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时的清理"""
    logger = get_logger("WebAPI")
    logger.info("Gakumasu-Bot Web API 关闭中...")
    
    try:
        # 停止调度器
        if api_adapter.scheduler and api_adapter.scheduler.is_running:
            await api_adapter.scheduler.stop()
        
        logger.info("Web API 关闭完成")
        
    except Exception as e:
        logger.error(f"Web API 关闭失败: {e}")


if __name__ == "__main__":
    import uvicorn
    
    # 开发模式运行
    uvicorn.run(
        "main:app",
        host="127.0.0.1",
        port=8000,
        reload=True,
        log_level="info"
    )
