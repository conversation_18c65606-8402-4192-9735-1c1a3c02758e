"""
剧本选择工厂
提供便捷的剧本和难度创建方法
"""

from typing import Dict, Any, Optional, List
from datetime import datetime

from ...core.data_structures import (
    ScenarioType, DifficultyLevel, ScenarioInfo, 
    DifficultyInfo, ScenarioSelection
)
from ...utils.logger import get_logger


class ScenarioFactory:
    """剧本工厂类"""
    
    def __init__(self):
        """初始化工厂"""
        self.logger = get_logger("ScenarioFactory")
    
    @staticmethod
    def create_scenario_info(
        scenario_type: ScenarioType,
        name: str,
        display_name: str = None,
        description: str = None,
        **kwargs
    ) -> ScenarioInfo:
        """
        创建剧本信息对象
        
        Args:
            scenario_type: 剧本类型
            name: 剧本名称
            display_name: 显示名称
            description: 描述
            **kwargs: 其他参数
            
        Returns:
            ScenarioInfo: 剧本信息对象
        """
        return ScenarioInfo(
            scenario_type=scenario_type,
            name=name,
            display_name=display_name or name,
            description=description or f"{display_name or name}剧本",
            special_attributes=kwargs.get('special_attributes', {}),
            unlock_conditions=kwargs.get('unlock_conditions', {}),
            rewards=kwargs.get('rewards', {}),
            is_available=kwargs.get('is_available', True),
            version=kwargs.get('version', '1.0.0')
        )
    
    @staticmethod
    def create_difficulty_info(
        difficulty_level: DifficultyLevel,
        name: str,
        display_name: str = None,
        description: str = None,
        **kwargs
    ) -> DifficultyInfo:
        """
        创建难度信息对象
        
        Args:
            difficulty_level: 难度等级
            name: 难度名称
            display_name: 显示名称
            description: 描述
            **kwargs: 其他参数
            
        Returns:
            DifficultyInfo: 难度信息对象
        """
        return DifficultyInfo(
            difficulty_level=difficulty_level,
            name=name,
            display_name=display_name or name,
            description=description or f"{display_name or name}难度",
            reward_multiplier=kwargs.get('reward_multiplier', 1.0),
            experience_multiplier=kwargs.get('experience_multiplier', 1.0),
            unlock_conditions=kwargs.get('unlock_conditions', {}),
            special_effects=kwargs.get('special_effects', {}),
            is_available=kwargs.get('is_available', True),
            order=kwargs.get('order', 0),
            color_theme=kwargs.get('color_theme', 'default')
        )
    
    @staticmethod
    def create_scenario_selection(
        scenario_info: ScenarioInfo,
        difficulty_info: DifficultyInfo,
        custom_settings: Dict[str, Any] = None
    ) -> ScenarioSelection:
        """
        创建剧本选择对象
        
        Args:
            scenario_info: 剧本信息
            difficulty_info: 难度信息
            custom_settings: 自定义设置
            
        Returns:
            ScenarioSelection: 剧本选择对象
        """
        return ScenarioSelection(
            scenario_info=scenario_info,
            difficulty_info=difficulty_info,
            custom_settings=custom_settings or {}
        )
    
    @classmethod
    def create_nia_scenario(cls, **kwargs) -> ScenarioInfo:
        """创建NIA剧本"""
        default_attributes = {
            'special_attributes': {
                'reward_multiplier': 1.0,
                'experience_multiplier': 1.0,
                'focus_stats': ['vocal', 'dance', 'visual', 'mental'],
                'special_events': ['nia_special_1', 'nia_special_2']
            },
            'unlock_conditions': {
                'min_level': 1,
                'required_achievements': []
            },
            'rewards': {
                'base_exp': 1000,
                'bonus_items': ['nia_badge', 'memory_piece']
            }
        }
        
        # 合并用户提供的参数
        merged_kwargs = {**default_attributes, **kwargs}
        
        return cls.create_scenario_info(
            scenario_type=ScenarioType.NIA,
            name='nia_scenario',
            display_name='NIA剧本',
            description='NIA主题的育成剧本，注重平衡发展',
            **merged_kwargs
        )
    
    @classmethod
    def create_hajime_scenario(cls, **kwargs) -> ScenarioInfo:
        """创建HAJIME剧本"""
        default_attributes = {
            'special_attributes': {
                'reward_multiplier': 1.1,
                'experience_multiplier': 1.05,
                'focus_stats': ['vocal', 'dance'],
                'special_events': ['hajime_special_1', 'hajime_special_2']
            },
            'unlock_conditions': {
                'min_level': 5,
                'required_achievements': ['complete_nia_scenario']
            },
            'rewards': {
                'base_exp': 1200,
                'bonus_items': ['hajime_badge', 'skill_book']
            }
        }
        
        # 合并用户提供的参数
        merged_kwargs = {**default_attributes, **kwargs}
        
        return cls.create_scenario_info(
            scenario_type=ScenarioType.HAJIME,
            name='hajime_scenario',
            display_name='HAJIME剧本',
            description='HAJIME主题的育成剧本，注重技能提升',
            **merged_kwargs
        )
    
    @classmethod
    def create_normal_difficulty(cls, **kwargs) -> DifficultyInfo:
        """创建普通难度"""
        default_attributes = {
            'reward_multiplier': 1.0,
            'experience_multiplier': 1.0,
            'unlock_conditions': {
                'min_level': 1
            },
            'special_effects': {
                'stamina_cost_reduction': 0.0,
                'failure_penalty_reduction': 0.1
            },
            'order': 1,
            'color_theme': 'green'
        }
        
        # 合并用户提供的参数
        merged_kwargs = {**default_attributes, **kwargs}
        
        return cls.create_difficulty_info(
            difficulty_level=DifficultyLevel.NORMAL,
            name='normal',
            display_name='普通',
            description='标准难度，适合新手玩家',
            **merged_kwargs
        )
    
    @classmethod
    def create_master_difficulty(cls, **kwargs) -> DifficultyInfo:
        """创建大师难度"""
        default_attributes = {
            'reward_multiplier': 1.5,
            'experience_multiplier': 1.3,
            'unlock_conditions': {
                'min_level': 10,
                'required_achievements': ['complete_normal_scenario']
            },
            'special_effects': {
                'stamina_cost_increase': 0.2,
                'failure_penalty_increase': 0.3,
                'bonus_event_chance': 0.15
            },
            'order': 2,
            'color_theme': 'red'
        }
        
        # 合并用户提供的参数
        merged_kwargs = {**default_attributes, **kwargs}
        
        return cls.create_difficulty_info(
            difficulty_level=DifficultyLevel.MASTER,
            name='master',
            display_name='大师',
            description='高难度，提供更多奖励和挑战',
            **merged_kwargs
        )
    
    @classmethod
    def create_quick_selection(
        cls,
        scenario_type: ScenarioType,
        difficulty_level: DifficultyLevel,
        custom_settings: Dict[str, Any] = None
    ) -> ScenarioSelection:
        """
        快速创建剧本选择
        
        Args:
            scenario_type: 剧本类型
            difficulty_level: 难度等级
            custom_settings: 自定义设置
            
        Returns:
            ScenarioSelection: 剧本选择对象
        """
        # 根据类型创建剧本信息
        if scenario_type == ScenarioType.NIA:
            scenario_info = cls.create_nia_scenario()
        elif scenario_type == ScenarioType.HAJIME:
            scenario_info = cls.create_hajime_scenario()
        else:
            raise ValueError(f"不支持的剧本类型: {scenario_type}")
        
        # 根据等级创建难度信息
        if difficulty_level == DifficultyLevel.NORMAL:
            difficulty_info = cls.create_normal_difficulty()
        elif difficulty_level == DifficultyLevel.MASTER:
            difficulty_info = cls.create_master_difficulty()
        else:
            raise ValueError(f"不支持的难度等级: {difficulty_level}")
        
        return cls.create_scenario_selection(
            scenario_info=scenario_info,
            difficulty_info=difficulty_info,
            custom_settings=custom_settings
        )
    
    @classmethod
    def get_default_scenarios(cls) -> List[ScenarioInfo]:
        """获取默认剧本列表"""
        return [
            cls.create_nia_scenario(),
            cls.create_hajime_scenario()
        ]
    
    @classmethod
    def get_default_difficulties(cls) -> List[DifficultyInfo]:
        """获取默认难度列表"""
        return [
            cls.create_normal_difficulty(),
            cls.create_master_difficulty()
        ]
    
    @classmethod
    def create_from_config(cls, config: Dict[str, Any]) -> ScenarioSelection:
        """
        从配置字典创建剧本选择
        
        Args:
            config: 配置字典，包含scenario和difficulty信息
            
        Returns:
            ScenarioSelection: 剧本选择对象
        """
        scenario_config = config.get('scenario', {})
        difficulty_config = config.get('difficulty', {})
        custom_settings = config.get('custom_settings', {})
        
        # 创建剧本信息
        scenario_info = ScenarioInfo.from_dict(scenario_config)
        
        # 创建难度信息
        difficulty_info = DifficultyInfo.from_dict(difficulty_config)
        
        return cls.create_scenario_selection(
            scenario_info=scenario_info,
            difficulty_info=difficulty_info,
            custom_settings=custom_settings
        )
