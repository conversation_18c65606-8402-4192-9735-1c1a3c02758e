#!/usr/bin/env python3
"""
后台输入验证测试脚本
专门验证后台输入模拟是否真正不影响前台鼠标光标
"""

import sys
import time
import win32gui
import win32api
from pathlib import Path
from typing import Tuple

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.modules.action.background_input_simulator import BackgroundInputSimulator, BackgroundInputMethod
from src.utils.logger import get_logger


class 后台输入验证器:
    """后台输入验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.logger = get_logger("后台输入验证器")
        
        # 查找gakumas窗口
        self.游戏窗口句柄 = self._find_game_window()
        if not self.游戏窗口句柄:
            raise Exception("未找到gakumas游戏窗口")
        
        # 初始化后台输入模拟器
        self.input_simulator = BackgroundInputSimulator(
            target_window_handle=self.游戏窗口句柄,
            enable_background_mode=True,
            background_input_method=BackgroundInputMethod.WIN32_POSTMESSAGE
        )
        
        self.logger.info(f"后台输入验证器初始化完成，目标窗口: {self.游戏窗口句柄}")
    
    def _find_game_window(self) -> int:
        """查找游戏窗口"""
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_title = win32gui.GetWindowText(hwnd)
                if "gakumas" in window_title.lower():
                    windows.append((hwnd, window_title))
            return True
        
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        
        if windows:
            return windows[0][0]
        return None
    
    def _get_mouse_position(self) -> Tuple[int, int]:
        """获取当前鼠标位置"""
        return win32gui.GetCursorPos()
    
    def 测试后台点击不影响鼠标光标(self):
        """测试后台点击是否影响鼠标光标位置"""
        print("\n" + "="*60)
        print("🔍 后台点击验证测试")
        print("="*60)
        
        # 获取初始鼠标位置
        初始位置 = self._get_mouse_position()
        print(f"📍 初始鼠标位置: {初始位置}")
        
        # 提示用户
        print("\n⚠️  请注意观察您的鼠标光标位置")
        print("⏰ 5秒后开始测试，请不要移动鼠标...")
        for i in range(5, 0, -1):
            print(f"   {i}秒...")
            time.sleep(1)
        
        print("\n🎯 开始执行后台点击测试...")
        
        # 执行多次后台点击
        测试点击次数 = 5
        点击成功次数 = 0
        
        for i in range(测试点击次数):
            # 在游戏窗口内的不同位置点击
            x = 200 + i * 50
            y = 300 + i * 30
            
            print(f"   第{i+1}次后台点击: ({x}, {y})")
            
            # 记录点击前的鼠标位置
            点击前位置 = self._get_mouse_position()
            
            # 执行后台点击
            成功 = self.input_simulator.background_click(x, y)
            
            # 记录点击后的鼠标位置
            点击后位置 = self._get_mouse_position()
            
            if 成功:
                点击成功次数 += 1
                print(f"   ✅ 点击成功")
            else:
                print(f"   ❌ 点击失败")
            
            # 检查鼠标位置是否改变
            if 点击前位置 != 点击后位置:
                print(f"   ⚠️  鼠标位置发生变化: {点击前位置} -> {点击后位置}")
            else:
                print(f"   ✅ 鼠标位置未改变: {点击前位置}")
            
            time.sleep(1)  # 间隔1秒
        
        # 获取最终鼠标位置
        最终位置 = self._get_mouse_position()
        
        print(f"\n📊 测试结果:")
        print(f"   总点击次数: {测试点击次数}")
        print(f"   成功次数: {点击成功次数}")
        print(f"   初始鼠标位置: {初始位置}")
        print(f"   最终鼠标位置: {最终位置}")
        
        # 判断测试结果
        位置未改变 = (初始位置 == 最终位置)
        
        if 位置未改变 and 点击成功次数 > 0:
            print(f"   🎉 测试通过：后台点击成功且未影响鼠标光标位置")
            return True
        elif not 位置未改变:
            print(f"   ❌ 测试失败：鼠标光标位置发生了改变")
            return False
        else:
            print(f"   ❌ 测试失败：所有后台点击都失败了")
            return False
    
    def 测试窗口消息发送(self):
        """测试Windows消息发送机制"""
        print("\n" + "="*60)
        print("🔧 Windows消息发送测试")
        print("="*60)
        
        # 测试窗口句柄有效性
        if win32gui.IsWindow(self.游戏窗口句柄):
            print(f"✅ 窗口句柄有效: {self.游戏窗口句柄}")
        else:
            print(f"❌ 窗口句柄无效: {self.游戏窗口句柄}")
            return False
        
        # 获取窗口信息
        try:
            窗口标题 = win32gui.GetWindowText(self.游戏窗口句柄)
            窗口矩形 = win32gui.GetWindowRect(self.游戏窗口句柄)
            客户区矩形 = win32gui.GetClientRect(self.游戏窗口句柄)
            
            print(f"📋 窗口信息:")
            print(f"   标题: {窗口标题}")
            print(f"   窗口矩形: {窗口矩形}")
            print(f"   客户区矩形: {客户区矩形}")
            
        except Exception as e:
            print(f"❌ 获取窗口信息失败: {e}")
            return False
        
        # 测试消息发送
        print(f"\n🔄 测试消息发送...")
        
        # 发送一个简单的鼠标移动消息
        try:
            result = win32gui.PostMessage(
                self.游戏窗口句柄, 
                0x0200,  # WM_MOUSEMOVE
                0,       # wparam
                (100 << 16) | 100  # lparam: y=100, x=100
            )
            
            if result:
                print(f"✅ PostMessage发送成功")
            else:
                print(f"❌ PostMessage发送失败")
            
        except Exception as e:
            print(f"❌ PostMessage发送异常: {e}")
            return False
        
        return True
    
    def 运行完整验证(self):
        """运行完整的验证测试"""
        print("🚀 开始后台输入验证测试")
        print("="*60)
        
        # 测试1: Windows消息发送机制
        消息测试结果 = self.测试窗口消息发送()
        
        # 测试2: 后台点击验证
        if 消息测试结果:
            点击测试结果 = self.测试后台点击不影响鼠标光标()
        else:
            print("\n⚠️  跳过后台点击测试（消息发送测试失败）")
            点击测试结果 = False
        
        # 输出最终结果
        print("\n" + "="*60)
        print("📋 验证测试总结")
        print("="*60)
        print(f"Windows消息发送: {'✅ 通过' if 消息测试结果 else '❌ 失败'}")
        print(f"后台点击验证: {'✅ 通过' if 点击测试结果 else '❌ 失败'}")
        
        if 消息测试结果 and 点击测试结果:
            print(f"\n🎉 所有测试通过！后台输入模拟工作正常")
        else:
            print(f"\n⚠️  存在问题，需要进一步调试")
        
        return 消息测试结果 and 点击测试结果


if __name__ == "__main__":
    try:
        验证器 = 后台输入验证器()
        验证器.运行完整验证()
    except Exception as e:
        print(f"❌ 验证测试失败: {e}")
        import traceback
        traceback.print_exc()
