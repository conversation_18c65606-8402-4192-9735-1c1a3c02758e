"""
主菜单场景实现
处理游戏主菜单的所有交互和导航功能
"""

import time
from typing import List, Dict, Any, Optional

from ....core.data_structures import GameScene
from ..config.scene_config import SceneConfig
from ..config.ui_element_config import ButtonConfig, LabelConfig
from .base_game_scene import BaseGameScene


class MainMenuScene(BaseGameScene):
    """主菜单场景类"""
    
    def __init__(self, config: SceneConfig, perception_module, action_controller):
        """
        初始化主菜单场景
        
        Args:
            config: 场景配置
            perception_module: 感知模块
            action_controller: 行动控制器
        """
        super().__init__(config, perception_module, action_controller)
        
        # 主菜单特定状态
        self._menu_items_loaded = False
        self._current_selection = None
        self._menu_animation_complete = False
        
        self.scene_logger.info("主菜单场景初始化完成")
    
    def _create_scene_ui_elements(self):
        """创建主菜单特定的UI元素"""
        try:
            # 主要功能按钮
            self._create_main_buttons()
            
            # 菜单标识和装饰元素
            self._create_menu_indicators()
            
            # 系统功能按钮
            self._create_system_buttons()
            
            # 信息显示元素
            self._create_info_elements()
            
            self.scene_logger.info(f"主菜单UI元素创建完成，共{len(self.ui_elements)}个元素")
            
        except Exception as e:
            self.scene_logger.error(f"主菜单UI元素创建失败: {e}")
            raise
    
    def _create_main_buttons(self):
        """创建主要功能按钮"""
        # 育成按钮
        produce_config = ButtonConfig(
            template_name="produce_button",
            confidence_threshold=0.8,
            timeout=5.0,
            retry_count=3,
            expected_scene_after_click="produce_setup",
            verify_click_result=True
        )
        self.ui_elements["produce_button"] = self.ui_factory.create_element_from_dict({
            "type": "enhanced_button",
            "template_name": "produce_button",
            "confidence_threshold": 0.8,
            "timeout": 5.0,
            "expected_scene_after_click": "produce_setup"
        })
        
        # 打工按钮
        part_time_config = ButtonConfig(
            template_name="part_time_job_button",
            confidence_threshold=0.8,
            timeout=5.0,
            retry_count=3,
            expected_scene_after_click="part_time_job"
        )
        self.ui_elements["part_time_job_button"] = self.ui_factory.create_element_from_dict({
            "type": "button",
            "template_name": "part_time_job_button",
            "confidence_threshold": 0.8,
            "timeout": 5.0
        })
        
        # 日常任务按钮
        daily_tasks_config = ButtonConfig(
            template_name="daily_tasks_button",
            confidence_threshold=0.8,
            timeout=5.0,
            retry_count=3,
            expected_scene_after_click="daily_tasks"
        )
        self.ui_elements["daily_tasks_button"] = self.ui_factory.create_element_from_dict({
            "type": "button",
            "template_name": "daily_tasks_button",
            "confidence_threshold": 0.8,
            "timeout": 5.0
        })
        
        # 商店按钮
        self.ui_elements["shop_button"] = self.ui_factory.create_button(
            "shop_button",
            confidence_threshold=0.8,
            timeout=5.0
        )
        
        # 卡池按钮
        self.ui_elements["gacha_button"] = self.ui_factory.create_button(
            "gacha_button",
            confidence_threshold=0.8,
            timeout=5.0
        )
    
    def _create_menu_indicators(self):
        """创建菜单标识元素"""
        # 主菜单标志
        self.ui_elements["main_menu_logo"] = self.ui_factory.create_label(
            "main_menu_logo",
            confidence_threshold=0.9,
            text_recognition_enabled=False
        )
        
        # 主菜单背景
        self.ui_elements["main_menu_bg"] = self.ui_factory.create_label(
            "main_menu_bg",
            confidence_threshold=0.7,
            text_recognition_enabled=False
        )
        
        # 版本信息
        self.ui_elements["version_info"] = self.ui_factory.create_label(
            "version_info",
            confidence_threshold=0.6,
            text_recognition_enabled=True,
            ocr_language="en"
        )
    
    def _create_system_buttons(self):
        """创建系统功能按钮"""
        # 设置按钮
        self.ui_elements["settings_button"] = self.ui_factory.create_button(
            "settings_button",
            confidence_threshold=0.8,
            timeout=3.0
        )
        
        # 公告按钮
        self.ui_elements["notice_button"] = self.ui_factory.create_button(
            "notice_button",
            confidence_threshold=0.8,
            timeout=3.0
        )
        
        # 好友按钮
        self.ui_elements["friends_button"] = self.ui_factory.create_button(
            "friends_button",
            confidence_threshold=0.8,
            timeout=3.0
        )
    
    def _create_info_elements(self):
        """创建信息显示元素"""
        # 用户名显示
        self.ui_elements["username_label"] = self.ui_factory.create_enhanced_label(
            "username_label",
            confidence_threshold=0.7,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
        
        # 等级显示
        self.ui_elements["level_label"] = self.ui_factory.create_label(
            "level_label",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="en"
        )
        
        # 货币显示
        self.ui_elements["currency_label"] = self.ui_factory.create_label(
            "currency_label",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="en"
        )
    
    def _get_critical_elements(self) -> List[str]:
        """获取主菜单的关键UI元素"""
        return [
            "main_menu_logo",
            "produce_button",
            "part_time_job_button",
            "daily_tasks_button"
        ]
    
    def _verify_scene_specific_state(self) -> bool:
        """验证主菜单特定状态"""
        try:
            # 验证主菜单标志可见
            logo_element = self.get_ui_element("main_menu_logo")
            if not logo_element or not logo_element.is_visible():
                self.scene_logger.debug("主菜单标志不可见")
                return False
            
            # 验证主要功能按钮可见
            main_buttons = ["produce_button", "part_time_job_button", "daily_tasks_button"]
            visible_buttons = 0
            
            for button_name in main_buttons:
                button = self.get_ui_element(button_name)
                if button and button.is_visible():
                    visible_buttons += 1
            
            if visible_buttons < 2:  # 至少2个主要按钮可见
                self.scene_logger.debug(f"主要功能按钮可见数量不足: {visible_buttons}/3")
                return False
            
            return True
            
        except Exception as e:
            self.scene_logger.error(f"主菜单状态验证异常: {e}")
            return False
    
    def start_produce(self) -> bool:
        """开始育成"""
        try:
            self.scene_logger.info("开始育成流程")
            
            # 点击育成按钮
            success = self.interact_with_element("produce_button", "click")
            if not success:
                self.scene_logger.error("点击育成按钮失败")
                return False
            
            # 等待场景切换
            time.sleep(1.0)
            
            # 验证是否成功进入育成准备场景
            # 这里可以添加更具体的验证逻辑
            
            return True
            
        except Exception as e:
            self.scene_logger.error(f"开始育成失败: {e}")
            return False
    
    def start_part_time_job(self) -> bool:
        """开始打工"""
        try:
            self.scene_logger.info("开始打工流程")
            
            success = self.interact_with_element("part_time_job_button", "click")
            if not success:
                self.scene_logger.error("点击打工按钮失败")
                return False
            
            time.sleep(1.0)
            return True
            
        except Exception as e:
            self.scene_logger.error(f"开始打工失败: {e}")
            return False
    
    def open_daily_tasks(self) -> bool:
        """打开日常任务"""
        try:
            self.scene_logger.info("打开日常任务")
            
            success = self.interact_with_element("daily_tasks_button", "click")
            if not success:
                self.scene_logger.error("点击日常任务按钮失败")
                return False
            
            time.sleep(1.0)
            return True
            
        except Exception as e:
            self.scene_logger.error(f"打开日常任务失败: {e}")
            return False
    
    def open_shop(self) -> bool:
        """打开商店"""
        try:
            self.scene_logger.info("打开商店")
            
            success = self.interact_with_element("shop_button", "click")
            if not success:
                self.scene_logger.error("点击商店按钮失败")
                return False
            
            time.sleep(1.0)
            return True
            
        except Exception as e:
            self.scene_logger.error(f"打开商店失败: {e}")
            return False
    
    def open_gacha(self) -> bool:
        """打开卡池"""
        try:
            self.scene_logger.info("打开卡池")
            
            success = self.interact_with_element("gacha_button", "click")
            if not success:
                self.scene_logger.error("点击卡池按钮失败")
                return False
            
            time.sleep(1.0)
            return True
            
        except Exception as e:
            self.scene_logger.error(f"打开卡池失败: {e}")
            return False
    
    def get_user_info(self) -> Dict[str, Any]:
        """获取用户信息"""
        try:
            user_info = {}
            
            # 读取用户名
            username_element = self.get_ui_element("username_label")
            if username_element:
                username = username_element.read_text()
                if username:
                    user_info["username"] = username.strip()
            
            # 读取等级
            level_element = self.get_ui_element("level_label")
            if level_element:
                level_text = level_element.read_text()
                if level_text:
                    # 提取数字
                    import re
                    level_match = re.search(r'\d+', level_text)
                    if level_match:
                        user_info["level"] = int(level_match.group())
            
            # 读取货币
            currency_element = self.get_ui_element("currency_label")
            if currency_element:
                currency_text = currency_element.read_text()
                if currency_text:
                    # 提取数字
                    import re
                    currency_match = re.search(r'[\d,]+', currency_text)
                    if currency_match:
                        currency_str = currency_match.group().replace(',', '')
                        user_info["currency"] = int(currency_str)
            
            self.scene_logger.debug(f"获取用户信息: {user_info}")
            return user_info
            
        except Exception as e:
            self.scene_logger.error(f"获取用户信息失败: {e}")
            return {}
    
    def _navigate_to_produce_setup(self, timeout: float) -> bool:
        """导航到育成准备场景"""
        try:
            self.scene_logger.info("导航到育成准备场景")
            
            # 点击育成按钮
            if not self.interact_with_element("produce_button", "click"):
                return False
            
            # 等待场景切换
            time.sleep(2.0)
            
            return True
            
        except Exception as e:
            self.scene_logger.error(f"导航到育成准备失败: {e}")
            return False
    
    def _navigate_to_part_time_job(self, timeout: float) -> bool:
        """导航到打工场景"""
        try:
            self.scene_logger.info("导航到打工场景")
            
            if not self.interact_with_element("part_time_job_button", "click"):
                return False
            
            time.sleep(2.0)
            return True
            
        except Exception as e:
            self.scene_logger.error(f"导航到打工场景失败: {e}")
            return False
    
    def _navigate_to_daily_tasks(self, timeout: float) -> bool:
        """导航到日常任务场景"""
        try:
            self.scene_logger.info("导航到日常任务场景")
            
            if not self.interact_with_element("daily_tasks_button", "click"):
                return False
            
            time.sleep(2.0)
            return True
            
        except Exception as e:
            self.scene_logger.error(f"导航到日常任务失败: {e}")
            return False
    
    def check_menu_animations(self) -> bool:
        """检查菜单动画是否完成"""
        try:
            # 这里可以添加检查菜单动画状态的逻辑
            # 例如检查特定的动画元素或等待固定时间
            
            if not self._menu_animation_complete:
                time.sleep(1.0)  # 等待动画完成
                self._menu_animation_complete = True
            
            return self._menu_animation_complete
            
        except Exception as e:
            self.scene_logger.error(f"检查菜单动画失败: {e}")
            return False
    
    def refresh_menu_state(self) -> bool:
        """刷新菜单状态"""
        try:
            self.scene_logger.info("刷新主菜单状态")
            
            # 重置状态
            self._menu_items_loaded = False
            self._current_selection = None
            self._menu_animation_complete = False
            
            # 重新验证场景
            if self.verify_scene_state():
                self._menu_items_loaded = True
                return True
            
            return False
            
        except Exception as e:
            self.scene_logger.error(f"刷新菜单状态失败: {e}")
            return False
