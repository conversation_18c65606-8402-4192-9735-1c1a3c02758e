"""
游戏任务类定义
包含各种游戏相关的自动化任务
"""

import time
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
from enum import Enum

from ...utils.logger import get_logger
from ...utils.config_loader import ConfigLoader
from ...core.data_structures import (
    GameState, GameScene, Action, ActionType,
    GakumasuBotException, GameUIElementNotFound
)
from .task_manager import Task, TaskStatus, TaskPriority


class NavigationStep(Enum):
    """导航步骤枚举"""
    SCENE_RECOGNITION = "scene_recognition"    # 场景识别
    PATH_PLANNING = "path_planning"           # 路径规划
    ACTION_EXECUTION = "action_execution"     # 操作执行
    RESULT_VERIFICATION = "result_verification"  # 结果验证
    COMPLETED = "completed"                   # 完成


class ProducePhase(Enum):
    """育成阶段枚举"""
    PREPARATION = "preparation"      # 准备阶段
    TEAM_SELECTION = "team_selection"  # 队伍选择
    MAIN_LOOP = "main_loop"         # 主循环
    MIDTERM_EXAM = "midterm_exam"   # 中期考试
    FINAL_EXAM = "final_exam"       # 最终考试
    SETTLEMENT = "settlement"       # 结算
    COMPLETED = "completed"         # 完成


class GameNavigationTasks:
    """
    游戏导航任务类
    负责高层次的游戏流程协调，实现跨模块的导航功能
    """

    def __init__(self,
                 perception_module=None,
                 decision_module=None,
                 action_controller=None,
                 config_loader: Optional[ConfigLoader] = None):
        """
        初始化游戏导航任务

        Args:
            perception_module: 感知模块实例
            decision_module: 决策模块实例
            action_controller: 行动控制器实例
            config_loader: 配置加载器实例
        """
        self.logger = get_logger("GameNavigationTasks")

        # 模块引用 - 依赖注入
        self.perception = perception_module
        self.decision = decision_module
        self.action = action_controller

        # 配置管理
        self.config_loader = config_loader or ConfigLoader()
        self._navigation_config = None

        # 导航状态跟踪
        self.current_step = NavigationStep.SCENE_RECOGNITION
        self.navigation_history = []
        self.step_start_times = {}
        self.step_durations = {}

    def navigate_to_produce_start(self, user_strategy: Optional[Dict[str, Any]] = None) -> bool:
        """
        从主菜单导航到育成开始界面的完整流程

        这是一个高层次的业务流程，协调感知、决策、行动三个模块：
        1. 感知模块：识别当前场景和UI状态
        2. 决策模块：根据用户策略决定导航路径
        3. 行动模块：执行具体的点击和导航操作

        Args:
            user_strategy: 用户策略配置，包含队伍配置等信息

        Returns:
            bool: 导航是否成功完成

        Raises:
            GakumasuBotException: 导航过程中发生错误时抛出
        """
        self.logger.info("开始执行从主菜单导航到育成开始的流程")

        try:
            # 加载导航配置
            self._load_navigation_config()

            # 步骤1：场景识别 - 确定当前所在位置
            if not self._execute_navigation_step(
                NavigationStep.SCENE_RECOGNITION,
                self._recognize_current_scene
            ):
                return False

            # 步骤2：路径规划 - 决定导航路径
            if not self._execute_navigation_step(
                NavigationStep.PATH_PLANNING,
                lambda: self._plan_navigation_path(user_strategy)
            ):
                return False

            # 步骤3：操作执行 - 执行导航操作序列
            if not self._execute_navigation_step(
                NavigationStep.ACTION_EXECUTION,
                self._execute_navigation_actions
            ):
                return False

            # 步骤4：结果验证 - 确认到达目标界面
            if not self._execute_navigation_step(
                NavigationStep.RESULT_VERIFICATION,
                self._verify_navigation_result
            ):
                return False

            # 完成导航
            self.current_step = NavigationStep.COMPLETED
            self.logger.info("导航到育成开始界面成功完成")

            # 记录导航统计信息
            self._log_navigation_statistics()

            return True

        except Exception as e:
            self.logger.error(f"导航到育成开始失败: {e}")
            # 尝试恢复到安全状态
            self._attempt_recovery()
            return False

    def _execute_navigation_step(self, step: NavigationStep, step_func) -> bool:
        """
        执行导航步骤的通用框架

        Args:
            step: 导航步骤类型
            step_func: 步骤执行函数

        Returns:
            bool: 步骤是否执行成功
        """
        self.current_step = step
        self.step_start_times[step] = datetime.now()

        self.logger.info(f"开始执行导航步骤: {step.value}")

        try:
            result = step_func()

            # 计算步骤耗时
            duration = datetime.now() - self.step_start_times[step]
            self.step_durations[step] = duration.total_seconds()

            # 记录步骤历史
            self.navigation_history.append({
                'step': step.value,
                'success': result,
                'duration': duration.total_seconds(),
                'timestamp': datetime.now()
            })

            if result:
                self.logger.info(f"导航步骤 {step.value} 完成，耗时: {duration.total_seconds():.1f}秒")
            else:
                self.logger.warning(f"导航步骤 {step.value} 失败，耗时: {duration.total_seconds():.1f}秒")

            return result

        except Exception as e:
            duration = datetime.now() - self.step_start_times[step]
            self.step_durations[step] = duration.total_seconds()

            self.logger.error(f"导航步骤 {step.value} 执行异常: {e}")

            # 记录失败历史
            self.navigation_history.append({
                'step': step.value,
                'success': False,
                'error': str(e),
                'duration': duration.total_seconds(),
                'timestamp': datetime.now()
            })

            return False

    def _load_navigation_config(self):
        """加载导航相关配置"""
        try:
            settings = self.config_loader.load_settings()
            self._navigation_config = settings.get('navigation', {})

            # 设置默认导航配置
            default_config = {
                'ui_elements': {
                    'main_menu': {
                        'produce_button': {'x': 500, 'y': 400, 'template': 'produce_button.png'},
                        'scene_indicators': ['main_menu_logo.png', 'main_menu_bg.png']
                    },
                    'produce_setup': {
                        'idol_selection_button': {'x': 300, 'y': 500, 'template': 'idol_select.png'},
                        'support_card_button': {'x': 600, 'y': 500, 'template': 'support_select.png'},
                        'start_produce_button': {'x': 960, 'y': 800, 'template': 'start_produce.png'},
                        'scene_indicators': ['produce_setup_title.png']
                    }
                },
                'timeouts': {
                    'scene_recognition': 10.0,
                    'action_execution': 5.0,
                    'scene_transition': 15.0
                },
                'retry_settings': {
                    'max_retries': 3,
                    'retry_delay': 2.0
                }
            }

            # 合并默认配置
            for key, value in default_config.items():
                if key not in self._navigation_config:
                    self._navigation_config[key] = value

            self.logger.debug("导航配置加载完成")

        except Exception as e:
            self.logger.warning(f"加载导航配置失败，使用默认配置: {e}")
            self._navigation_config = {}

    def _recognize_current_scene(self) -> bool:
        """
        识别当前场景

        使用感知模块识别当前所在的游戏场景

        Returns:
            bool: 场景识别是否成功
        """
        if not self.perception:
            self.logger.error("感知模块未初始化，无法识别场景")
            return False

        try:
            # 获取当前游戏状态
            game_state = self.perception.get_game_state()
            current_scene = game_state.current_scene

            self.logger.info(f"当前场景识别结果: {current_scene.value}")

            # 验证场景识别结果的有效性
            if current_scene == GameScene.UNKNOWN:
                self.logger.warning("场景识别结果为未知，可能需要重试")
                return False

            # 记录场景信息用于后续步骤
            self._current_scene = current_scene
            self._current_game_state = game_state

            return True

        except Exception as e:
            self.logger.error(f"场景识别失败: {e}")
            return False

    def _plan_navigation_path(self, user_strategy: Optional[Dict[str, Any]] = None) -> bool:
        """
        规划导航路径

        根据当前场景和用户策略，使用决策模块规划到达育成开始界面的路径

        Args:
            user_strategy: 用户策略配置

        Returns:
            bool: 路径规划是否成功
        """
        if not hasattr(self, '_current_scene'):
            self.logger.error("当前场景未识别，无法规划路径")
            return False

        try:
            current_scene = self._current_scene
            target_scene = GameScene.PRODUCE_SETUP

            self.logger.info(f"规划导航路径: {current_scene.value} -> {target_scene.value}")

            # 根据当前场景确定导航路径
            navigation_path = self._determine_navigation_path(current_scene, target_scene)

            if not navigation_path:
                self.logger.error("无法确定导航路径")
                return False

            # 如果有决策模块，可以让它优化路径
            if self.decision:
                try:
                    optimized_path = self.decision.optimize_navigation_path(
                        navigation_path,
                        self._current_game_state,
                        user_strategy
                    )
                    if optimized_path:
                        navigation_path = optimized_path
                        self.logger.debug("决策模块优化了导航路径")
                except AttributeError:
                    # 决策模块可能还没有这个方法，使用原始路径
                    self.logger.debug("决策模块暂不支持路径优化，使用默认路径")

            # 保存规划的路径
            self._navigation_path = navigation_path

            self.logger.info(f"导航路径规划完成，共 {len(navigation_path)} 个步骤")
            for i, step in enumerate(navigation_path, 1):
                self.logger.debug(f"步骤 {i}: {step.get('description', 'Unknown step')}")

            return True

        except Exception as e:
            self.logger.error(f"路径规划失败: {e}")
            return False

    def _determine_navigation_path(self, current_scene: GameScene, target_scene: GameScene) -> List[Dict[str, Any]]:
        """
        确定导航路径

        Args:
            current_scene: 当前场景
            target_scene: 目标场景

        Returns:
            导航步骤列表
        """
        navigation_path = []

        # 根据当前场景和目标场景确定路径
        if current_scene == GameScene.MAIN_MENU and target_scene == GameScene.PRODUCE_SETUP:
            # 从主菜单到育成准备界面
            navigation_path = [
                {
                    'action_type': 'click',
                    'target_element': 'produce_button',
                    'description': '点击育成按钮',
                    'expected_scene': GameScene.PRODUCE_SETUP,
                    'timeout': self._navigation_config.get('timeouts', {}).get('scene_transition', 15.0)
                }
            ]

        elif current_scene == target_scene:
            # 已经在目标场景
            self.logger.info("已经在目标场景，无需导航")
            return []

        elif current_scene == GameScene.PRODUCE_MAIN:
            # 从育成主界面返回到育成准备界面（可能需要退出当前育成）
            navigation_path = [
                {
                    'action_type': 'click',
                    'target_element': 'menu_button',
                    'description': '打开菜单',
                    'expected_scene': GameScene.MENU_OVERLAY,
                    'timeout': 5.0
                },
                {
                    'action_type': 'click',
                    'target_element': 'return_to_main_button',
                    'description': '返回主菜单',
                    'expected_scene': GameScene.MAIN_MENU,
                    'timeout': 10.0
                },
                {
                    'action_type': 'click',
                    'target_element': 'produce_button',
                    'description': '点击育成按钮',
                    'expected_scene': GameScene.PRODUCE_SETUP,
                    'timeout': 15.0
                }
            ]

        else:
            # 其他场景，先尝试返回主菜单
            navigation_path = [
                {
                    'action_type': 'click',
                    'target_element': 'back_button',
                    'description': '返回上一级',
                    'expected_scene': GameScene.MAIN_MENU,
                    'timeout': 10.0
                },
                {
                    'action_type': 'click',
                    'target_element': 'produce_button',
                    'description': '点击育成按钮',
                    'expected_scene': GameScene.PRODUCE_SETUP,
                    'timeout': 15.0
                }
            ]

        return navigation_path

    def _execute_navigation_actions(self) -> bool:
        """
        执行导航操作序列

        使用行动模块执行规划好的导航操作

        Returns:
            bool: 操作执行是否成功
        """
        if not hasattr(self, '_navigation_path'):
            self.logger.error("导航路径未规划，无法执行操作")
            return False

        if not self.action:
            self.logger.error("行动模块未初始化，无法执行操作")
            return False

        try:
            navigation_path = self._navigation_path

            if not navigation_path:
                self.logger.info("无需执行导航操作，已在目标位置")
                return True

            self.logger.info(f"开始执行 {len(navigation_path)} 个导航操作")

            for i, step in enumerate(navigation_path, 1):
                self.logger.info(f"执行导航操作 {i}/{len(navigation_path)}: {step.get('description', 'Unknown')}")

                # 执行单个导航步骤
                if not self._execute_single_navigation_action(step):
                    self.logger.error(f"导航操作 {i} 执行失败")
                    return False

                # 等待场景切换
                expected_scene = step.get('expected_scene')
                if expected_scene:
                    timeout = step.get('timeout', 10.0)
                    if not self._wait_for_scene_transition(expected_scene, timeout):
                        self.logger.error(f"等待场景切换到 {expected_scene.value} 超时")
                        return False

                self.logger.debug(f"导航操作 {i} 执行成功")

            self.logger.info("所有导航操作执行完成")
            return True

        except Exception as e:
            self.logger.error(f"执行导航操作失败: {e}")
            return False

    def _execute_single_navigation_action(self, step: Dict[str, Any]) -> bool:
        """
        执行单个导航操作

        Args:
            step: 导航步骤配置

        Returns:
            bool: 操作是否成功
        """
        try:
            action_type = step.get('action_type', 'click')
            target_element = step.get('target_element')

            if action_type == 'click':
                # 获取目标元素的坐标
                target_coords = self._get_element_coordinates(target_element)
                if not target_coords:
                    self.logger.error(f"无法获取元素坐标: {target_element}")
                    return False

                # 创建点击动作
                action = Action(
                    action_type=ActionType.CLICK,
                    target=target_coords,
                    description=step.get('description', f'点击 {target_element}')
                )

                # 执行动作并验证
                success = self.action.execute_and_verify(action)
                if not success:
                    self.logger.error(f"点击操作失败: {target_element}")
                    return False

                return True

            else:
                self.logger.error(f"不支持的操作类型: {action_type}")
                return False

        except Exception as e:
            self.logger.error(f"执行单个导航操作失败: {e}")
            return False

    def _get_element_coordinates(self, element_name: str) -> Optional[tuple]:
        """
        获取UI元素的坐标

        Args:
            element_name: 元素名称

        Returns:
            元素坐标 (x, y) 或 None
        """
        try:
            # 从配置中获取元素坐标
            ui_elements = self._navigation_config.get('ui_elements', {})

            # 查找元素配置
            element_config = None
            for scene_name, scene_elements in ui_elements.items():
                if element_name in scene_elements:
                    element_config = scene_elements[element_name]
                    break

            if not element_config:
                self.logger.warning(f"未找到元素配置: {element_name}")
                return None

            # 如果配置了模板图片，尝试使用模板匹配
            template_path = element_config.get('template')
            if template_path and self.perception:
                try:
                    # 使用感知模块进行模板匹配
                    match_result = self.perception.find_template(template_path)
                    if match_result and match_result.get('confidence', 0) > 0.8:
                        coords = match_result.get('center')
                        if coords:
                            self.logger.debug(f"通过模板匹配找到元素 {element_name}: {coords}")
                            return coords
                except AttributeError:
                    # 感知模块可能还没有模板匹配方法
                    pass

            # 使用配置中的固定坐标
            x = element_config.get('x')
            y = element_config.get('y')
            if x is not None and y is not None:
                coords = (x, y)
                self.logger.debug(f"使用配置坐标找到元素 {element_name}: {coords}")
                return coords

            self.logger.warning(f"无法确定元素坐标: {element_name}")
            return None

        except Exception as e:
            self.logger.error(f"获取元素坐标失败: {e}")
            return None

    def _verify_navigation_result(self) -> bool:
        """
        验证导航结果

        确认是否成功到达育成开始界面

        Returns:
            bool: 验证是否成功
        """
        if not self.perception:
            self.logger.error("感知模块未初始化，无法验证导航结果")
            return False

        try:
            # 等待界面稳定
            time.sleep(2.0)

            # 获取当前游戏状态
            game_state = self.perception.get_game_state()
            current_scene = game_state.current_scene

            # 检查是否到达目标场景
            target_scene = GameScene.PRODUCE_SETUP
            if current_scene == target_scene:
                self.logger.info(f"导航验证成功：已到达 {target_scene.value}")

                # 进一步验证界面元素
                if self._verify_produce_setup_elements():
                    self.logger.info("育成准备界面元素验证通过")
                    return True
                else:
                    self.logger.warning("育成准备界面元素验证失败，但场景识别正确")
                    return True  # 场景正确就认为成功

            else:
                self.logger.error(f"导航验证失败：期望 {target_scene.value}，实际 {current_scene.value}")
                return False

        except Exception as e:
            self.logger.error(f"验证导航结果失败: {e}")
            return False

    def _verify_produce_setup_elements(self) -> bool:
        """
        验证育成准备界面的关键元素

        Returns:
            bool: 验证是否成功
        """
        try:
            # 检查关键UI元素是否存在
            ui_elements = self._navigation_config.get('ui_elements', {}).get('produce_setup', {})
            scene_indicators = ui_elements.get('scene_indicators', [])

            if not scene_indicators:
                self.logger.debug("未配置场景指示器，跳过元素验证")
                return True

            # 检查场景指示器
            for indicator in scene_indicators:
                if self.perception and hasattr(self.perception, 'find_template'):
                    try:
                        result = self.perception.find_template(indicator)
                        if result and result.get('confidence', 0) > 0.7:
                            self.logger.debug(f"找到场景指示器: {indicator}")
                            return True
                    except:
                        continue

            self.logger.debug("未找到场景指示器，但不影响导航结果")
            return True

        except Exception as e:
            self.logger.warning(f"验证界面元素时出错: {e}")
            return True  # 验证失败不影响导航成功

    def _wait_for_scene_transition(self, target_scene: GameScene, timeout: float = 15.0) -> bool:
        """
        等待场景切换

        Args:
            target_scene: 目标场景
            timeout: 超时时间

        Returns:
            bool: 是否成功切换到目标场景
        """
        if not self.perception:
            return True  # 无法验证，假设成功

        start_time = time.time()

        while time.time() - start_time < timeout:
            try:
                game_state = self.perception.get_game_state()
                if game_state.current_scene == target_scene:
                    self.logger.debug(f"成功切换到场景: {target_scene.value}")
                    return True

                time.sleep(0.5)  # 等待0.5秒后重试

            except Exception as e:
                self.logger.warning(f"等待场景切换时出错: {e}")
                time.sleep(1.0)

        self.logger.warning(f"等待场景切换超时: {target_scene.value}")
        return False

    def _attempt_recovery(self):
        """
        尝试从导航失败中恢复

        当导航失败时，尝试返回到安全状态（主菜单）
        """
        try:
            self.logger.info("尝试从导航失败中恢复")

            if self.action:
                # 尝试按ESC键返回
                escape_action = Action(
                    action_type=ActionType.KEY_PRESS,
                    target="Escape",
                    description="按ESC键尝试返回"
                )
                self.action.execute_and_verify(escape_action)

                # 等待一段时间
                time.sleep(2.0)

            self.logger.info("恢复操作完成")

        except Exception as e:
            self.logger.error(f"恢复操作失败: {e}")

    def _log_navigation_statistics(self):
        """记录导航统计信息"""
        try:
            total_duration = sum(self.step_durations.values())

            self.logger.info("=== 导航统计信息 ===")
            self.logger.info(f"总耗时: {total_duration:.1f}秒")

            for step, duration in self.step_durations.items():
                self.logger.info(f"{step.value}: {duration:.1f}秒")

            self.logger.info(f"导航历史记录: {len(self.navigation_history)} 个步骤")

        except Exception as e:
            self.logger.warning(f"记录导航统计信息失败: {e}")


class ProduceTask(Task):
    """完整育成流程任务"""
    
    def __init__(self, 
                 perception_module=None,
                 decision_module=None,
                 action_controller=None,
                 user_strategy: Optional[Dict[str, Any]] = None,
                 **kwargs):
        """
        初始化育成任务
        
        Args:
            perception_module: 感知模块
            decision_module: 决策模块
            action_controller: 行动控制器
            user_strategy: 用户策略配置
            **kwargs: 其他任务参数
        """
        super().__init__(
            name="完整育成流程",
            description="执行一次完整的偶像育成流程",
            priority=TaskPriority.HIGH,
            timeout=3600,  # 1小时超时
            max_retries=1,  # 育成任务只重试一次
            **kwargs
        )
        
        self.logger = get_logger("ProduceTask")
        
        # 模块引用
        self.perception = perception_module
        self.decision = decision_module
        self.action = action_controller
        
        # 配置
        self.user_strategy = user_strategy or {}
        
        # 状态跟踪
        self.current_phase = ProducePhase.PREPARATION
        self.current_week = 0
        self.produce_result = None
        
        # 执行统计
        self.phase_start_times = {}
        self.phase_durations = {}
        self.weekly_actions = []
        self.exam_results = []
        
        # 设置执行函数
        self.execute_func = self._execute_produce_workflow
    
    def _execute_produce_workflow(self) -> Dict[str, Any]:
        """
        执行完整育成工作流程
        
        Returns:
            育成结果字典
        """
        self.logger.info("开始执行完整育成流程")
        
        try:
            # 阶段1：准备育成
            self._execute_phase(ProducePhase.PREPARATION, self._prepare_produce)
            
            # 阶段2：选择队伍
            self._execute_phase(ProducePhase.TEAM_SELECTION, self._select_team_composition)
            
            # 阶段3：育成主循环
            self._execute_phase(ProducePhase.MAIN_LOOP, self._execute_produce_main_loop)
            
            # 阶段4：中期考试
            self._execute_phase(ProducePhase.MIDTERM_EXAM, self._handle_midterm_exam)
            
            # 阶段5：最终考试
            self._execute_phase(ProducePhase.FINAL_EXAM, self._handle_final_exam)
            
            # 阶段6：结算
            self._execute_phase(ProducePhase.SETTLEMENT, self._complete_settlement)
            
            # 完成
            self.current_phase = ProducePhase.COMPLETED
            
            # 生成结果报告
            result = self._generate_produce_report()
            self.produce_result = result
            
            self.logger.info("育成流程执行完成")
            return result
            
        except Exception as e:
            self.logger.error(f"育成流程执行失败: {e}")
            raise GakumasuBotException(f"育成任务失败: {e}")
    
    def _execute_phase(self, phase: ProducePhase, phase_func):
        """
        执行育成阶段
        
        Args:
            phase: 阶段类型
            phase_func: 阶段执行函数
        """
        self.current_phase = phase
        self.phase_start_times[phase] = datetime.now()
        
        self.logger.info(f"开始执行阶段: {phase.value}")
        
        try:
            phase_func()
            duration = datetime.now() - self.phase_start_times[phase]
            self.phase_durations[phase] = duration.total_seconds()
            
            self.logger.info(f"阶段 {phase.value} 完成，耗时: {duration.total_seconds():.1f}秒")
            
        except Exception as e:
            self.logger.error(f"阶段 {phase.value} 执行失败: {e}")
            raise
    
    def _prepare_produce(self):
        """准备育成阶段"""
        self.logger.info("准备育成阶段")
        
        # 1. 检查游戏状态
        game_state = self._get_current_game_state()
        
        # 2. 导航到育成准备界面
        self._navigate_to_produce_setup()
        
        # 3. 检查资源（体力等）
        self._check_resources()
        
        self.logger.info("育成准备完成")
    
    def _select_team_composition(self):
        """选择队伍配置"""
        self.logger.info("选择队伍配置")
        
        # 1. 选择偶像
        self._select_produce_idol()
        
        # 2. 选择支援卡
        self._select_support_cards()
        
        # 3. 确认配置
        self._confirm_team_setup()
        
        self.logger.info("队伍配置完成")
    
    def _execute_produce_main_loop(self):
        """执行育成主循环"""
        self.logger.info("开始育成主循环")
        
        # 育成通常是12周
        for week in range(1, 13):
            self.current_week = week
            self.logger.info(f"执行第 {week} 周")
            
            try:
                # 执行每周行动
                action_result = self._execute_weekly_action(week)
                self.weekly_actions.append({
                    'week': week,
                    'action': action_result,
                    'timestamp': datetime.now()
                })
                
                # 处理每周事件
                self._handle_weekly_events()
                
                # 检查是否需要中期考试
                if week == 6:  # 通常第6周有中期考试
                    break
                
            except Exception as e:
                self.logger.error(f"第 {week} 周执行失败: {e}")
                # 可以选择继续或中断
                if self._should_continue_after_error(e):
                    continue
                else:
                    raise
        
        self.logger.info("育成主循环完成")
    
    def _execute_weekly_action(self, week: int) -> Dict[str, Any]:
        """
        执行每周行动
        
        Args:
            week: 周数
            
        Returns:
            行动结果
        """
        # 获取当前游戏状态
        game_state = self._get_current_game_state()
        
        # 使用决策模块选择行动
        if self.decision:
            action = self.decision.decide_weekly_action(game_state, week, self.user_strategy)
        else:
            # 默认行动选择逻辑
            action = self._default_weekly_action(game_state, week)
        
        # 执行行动
        if self.action:
            success = self.action.execute_and_verify(action)
            if not success:
                raise GakumasuBotException(f"第 {week} 周行动执行失败")
        
        return {
            'action_type': action.action_type.value if action else 'unknown',
            'description': action.description if action else 'default action',
            'success': success if 'success' in locals() else True
        }
    
    def _handle_weekly_events(self):
        """处理每周事件"""
        # 检查是否有随机事件
        game_state = self._get_current_game_state()
        
        # 如果检测到事件界面，使用决策模块处理
        if game_state.current_scene == GameScene.PRODUCE_EVENT:
            if self.decision:
                event_action = self.decision.handle_produce_event(game_state)
                if event_action and self.action:
                    self.action.execute_and_verify(event_action)
    
    def _handle_midterm_exam(self):
        """处理中期考试"""
        self.logger.info("处理中期考试")
        
        # 检查是否进入考试界面
        game_state = self._get_current_game_state()
        
        if game_state.current_scene == GameScene.PRODUCE_EXAM:
            exam_result = self._execute_exam_sequence()
            self.exam_results.append({
                'type': 'midterm',
                'result': exam_result,
                'timestamp': datetime.now()
            })
        
        self.logger.info("中期考试完成")
    
    def _handle_final_exam(self):
        """处理最终考试"""
        self.logger.info("处理最终考试")
        
        # 继续剩余周数的育成
        for week in range(7, 13):  # 从第7周到第12周
            self.current_week = week
            self.logger.info(f"执行第 {week} 周")
            
            action_result = self._execute_weekly_action(week)
            self.weekly_actions.append({
                'week': week,
                'action': action_result,
                'timestamp': datetime.now()
            })
            
            self._handle_weekly_events()
        
        # 执行最终考试
        game_state = self._get_current_game_state()
        if game_state.current_scene == GameScene.PRODUCE_EXAM:
            exam_result = self._execute_exam_sequence()
            self.exam_results.append({
                'type': 'final',
                'result': exam_result,
                'timestamp': datetime.now()
            })
        
        self.logger.info("最终考试完成")
    
    def _complete_settlement(self):
        """完成结算"""
        self.logger.info("处理育成结算")
        
        # 等待结算界面
        self._wait_for_scene(GameScene.PRODUCE_RESULT, timeout=30)
        
        # 获取育成结果
        game_state = self._get_current_game_state()
        
        # 点击确认按钮完成结算
        if self.action:
            confirm_action = Action(
                action_type=ActionType.CLICK,
                target=(960, 800),  # 确认按钮位置
                description="确认育成结果"
            )
            self.action.execute_and_verify(confirm_action)
        
        # 返回主菜单
        self._navigate_to_main_menu()
        
        self.logger.info("育成结算完成")
    
    def _get_current_game_state(self) -> GameState:
        """获取当前游戏状态"""
        if not self.perception:
            raise GakumasuBotException("感知模块未初始化")
        
        return self.perception.get_game_state()
    
    def _generate_produce_report(self) -> Dict[str, Any]:
        """生成育成报告"""
        total_duration = sum(self.phase_durations.values())
        
        return {
            'task_id': self.task_id,
            'start_time': self.started_at,
            'end_time': self.completed_at,
            'total_duration': total_duration,
            'phase_durations': self.phase_durations,
            'weekly_actions': self.weekly_actions,
            'exam_results': self.exam_results,
            'final_phase': self.current_phase.value,
            'success': self.current_phase == ProducePhase.COMPLETED
        }
    
    # 辅助方法实现
    def _navigate_to_produce_setup(self):
        """导航到育成准备界面"""
        self.logger.debug("导航到育成准备界面")

        # 使用新的导航框架
        navigation_tasks = GameNavigationTasks(
            perception_module=self.perception,
            decision_module=self.decision,
            action_controller=self.action
        )

        success = navigation_tasks.navigate_to_produce_start(self.user_strategy)
        if not success:
            raise GakumasuBotException("导航到育成准备界面失败")

    def _check_resources(self):
        """检查资源状态"""
        game_state = self._get_current_game_state()

        # 检查体力
        if game_state.stamina < 30:  # 假设育成需要30体力
            raise GakumasuBotException(f"体力不足: {game_state.stamina}/30")

        self.logger.info(f"资源检查通过 - 体力: {game_state.stamina}, 元气: {game_state.vigor}")

    def _select_produce_idol(self):
        """选择育成偶像"""
        self.logger.debug("选择育成偶像")

        # 从用户策略中获取偶像选择
        target_idol = self.user_strategy.get('team_composition', {}).get('produce_idol', '')

        if target_idol:
            self.logger.info(f"选择偶像: {target_idol}")
            # 这里需要实现具体的偶像选择逻辑
            # 可能需要滚动列表、识别偶像名称等
        else:
            self.logger.info("使用默认偶像选择")

        # 点击确认选择
        if self.action:
            confirm_action = Action(
                action_type=ActionType.CLICK,
                target=(800, 600),  # 确认按钮位置
                description="确认偶像选择"
            )
            self.action.execute_and_verify(confirm_action)

    def _select_support_cards(self):
        """选择支援卡"""
        self.logger.debug("选择支援卡")

        # 从用户策略中获取支援卡配置
        support_cards = self.user_strategy.get('team_composition', {}).get('support_cards', [])

        if support_cards:
            self.logger.info(f"目标支援卡: {support_cards}")
            # 这里需要实现具体的支援卡选择逻辑
        else:
            self.logger.info("使用默认支援卡配置")

        # 点击确认选择
        if self.action:
            confirm_action = Action(
                action_type=ActionType.CLICK,
                target=(800, 700),  # 确认按钮位置
                description="确认支援卡选择"
            )
            self.action.execute_and_verify(confirm_action)

    def _confirm_team_setup(self):
        """确认队伍配置"""
        self.logger.debug("确认队伍配置")

        # 点击开始育成按钮
        if self.action:
            start_action = Action(
                action_type=ActionType.CLICK,
                target=(960, 800),  # 开始育成按钮位置
                description="开始育成"
            )
            self.action.execute_and_verify(start_action)

        # 等待进入育成主界面
        self._wait_for_scene(GameScene.PRODUCE_MAIN, timeout=20)

    def _default_weekly_action(self, game_state: GameState, week: int) -> Optional[Action]:
        """
        默认每周行动选择逻辑

        Args:
            game_state: 当前游戏状态
            week: 周数

        Returns:
            选择的行动
        """
        # 简单的默认策略：前期练习，后期休息
        if week <= 8:
            # 选择练习
            return Action(
                action_type=ActionType.CLICK,
                target=(400, 300),  # 练习按钮位置
                description=f"第{week}周选择练习"
            )
        else:
            # 选择休息
            return Action(
                action_type=ActionType.CLICK,
                target=(600, 300),  # 休息按钮位置
                description=f"第{week}周选择休息"
            )

    def _should_continue_after_error(self, error: Exception) -> bool:
        """
        判断错误后是否继续执行

        Args:
            error: 发生的错误

        Returns:
            是否继续执行
        """
        # 对于一些非致命错误，可以选择继续
        if isinstance(error, GameUIElementNotFound):
            self.logger.warning(f"UI元素未找到，尝试继续: {error}")
            return True

        # 其他错误则中断执行
        return False

    def _execute_exam_sequence(self) -> Dict[str, Any]:
        """
        执行考试序列

        Returns:
            考试结果
        """
        self.logger.info("执行考试序列")

        exam_start_time = datetime.now()
        total_score = 0
        turn_count = 0

        try:
            # 考试通常是多回合的卡牌战斗
            while True:
                game_state = self._get_current_game_state()

                # 检查是否还在考试界面
                if game_state.current_scene != GameScene.PRODUCE_EXAM:
                    break

                turn_count += 1
                self.logger.debug(f"考试第 {turn_count} 回合")

                # 使用决策模块选择卡牌
                if self.decision:
                    card_action = self.decision.decide_exam_card(game_state)
                    if card_action and self.action:
                        self.action.execute_and_verify(card_action)

                # 等待回合结束
                time.sleep(2)

                # 防止无限循环
                if turn_count > 20:  # 最多20回合
                    self.logger.warning("考试回合数超过限制，强制结束")
                    break

            exam_duration = (datetime.now() - exam_start_time).total_seconds()

            return {
                'duration': exam_duration,
                'turns': turn_count,
                'score': total_score,
                'success': True
            }

        except Exception as e:
            self.logger.error(f"考试执行失败: {e}")
            return {
                'duration': (datetime.now() - exam_start_time).total_seconds(),
                'turns': turn_count,
                'score': 0,
                'success': False,
                'error': str(e)
            }

    def _wait_for_scene(self, target_scene: GameScene, timeout: float = 30):
        """
        等待指定场景出现

        Args:
            target_scene: 目标场景
            timeout: 超时时间（秒）
        """
        if not self.perception:
            return

        start_time = time.time()

        while time.time() - start_time < timeout:
            try:
                game_state = self.perception.get_game_state()
                if game_state.current_scene == target_scene:
                    self.logger.debug(f"成功等到场景: {target_scene.value}")
                    return

                time.sleep(1)  # 等待1秒后重试

            except Exception as e:
                self.logger.warning(f"等待场景时出错: {e}")
                time.sleep(1)

        raise GakumasuBotException(f"等待场景超时: {target_scene.value}")

    def _navigate_to_main_menu(self):
        """导航回主菜单"""
        self.logger.debug("导航回主菜单")

        # 点击返回主菜单按钮
        if self.action:
            menu_action = Action(
                action_type=ActionType.CLICK,
                target=(100, 100),  # 菜单按钮位置
                description="返回主菜单"
            )
            self.action.execute_and_verify(menu_action)

        # 等待主菜单出现
        self._wait_for_scene(GameScene.MAIN_MENU, timeout=15)
