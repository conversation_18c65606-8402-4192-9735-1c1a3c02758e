"""
战斗结果场景实现
处理战斗结束后的结果展示、奖励获取和经验值更新
"""

import time
from typing import List, Dict, Any, Optional, Tuple
from enum import Enum

from .....core.data_structures import GameScene
from ...config.scene_config import SceneConfig
from ...config.ui_element_config import ButtonConfig, LabelConfig
from ..base_game_scene import BaseGameScene
from ...utils.performance_monitor import measure_block
from ...utils.scene_optimizer import get_scene_optimizer


class BattleResult(Enum):
    """战斗结果枚举"""
    VICTORY = "victory"        # 胜利
    DEFEAT = "defeat"          # 失败
    DRAW = "draw"              # 平局
    TIMEOUT = "timeout"        # 超时


class RewardType(Enum):
    """奖励类型枚举"""
    EXPERIENCE = "experience"  # 经验值
    CURRENCY = "currency"      # 货币
    ITEM = "item"             # 道具
    SKILL = "skill"           # 技能
    ACHIEVEMENT = "achievement" # 成就


class BattleResultScene(BaseGameScene):
    """战斗结果场景类"""
    
    def __init__(self, config: SceneConfig, perception_module, action_controller):
        """
        初始化战斗结果场景
        
        Args:
            config: 场景配置
            perception_module: 感知模块
            action_controller: 行动控制器
        """
        super().__init__(config, perception_module, action_controller)
        
        # 战斗结果数据
        self._battle_result: Optional[BattleResult] = None
        self._victory_margin = 0.0  # 胜利优势
        self._battle_score = 0
        self._performance_rating = "C"
        
        # 奖励数据
        self._rewards: List[Dict[str, Any]] = []
        self._experience_gained = 0
        self._currency_gained = 0
        self._items_gained = []
        
        # 属性变化
        self._stat_changes = {
            "vocal": 0, "dance": 0, "visual": 0, "mental": 0
        }
        
        # 结果处理状态
        self._result_processed = False
        self._rewards_claimed = False
        
        # 性能监控
        self.optimizer = get_scene_optimizer()
        
        self.scene_logger.info("战斗结果场景初始化完成")
    
    def _create_scene_ui_elements(self):
        """创建战斗结果场景特定的UI元素"""
        try:
            with measure_block("battle_result_ui_creation"):
                # 场景标识元素
                self._create_result_indicators()
                
                # 结果展示元素
                self._create_result_displays()
                
                # 奖励展示元素
                self._create_reward_displays()
                
                # 统计信息元素
                self._create_statistics_displays()
                
                # 控制按钮
                self._create_control_buttons()
            
            self.scene_logger.info(f"战斗结果UI元素创建完成，共{len(self.ui_elements)}个元素")
            
        except Exception as e:
            self.scene_logger.error(f"战斗结果UI元素创建失败: {e}")
            raise
    
    def _create_result_indicators(self):
        """创建结果标识元素"""
        # 战斗结果标题
        self.ui_elements["battle_result_title"] = self.ui_factory.create_enhanced_label(
            "battle_result_title",
            confidence_threshold=0.9,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
        
        # 胜负结果显示
        self.ui_elements["victory_defeat_indicator"] = self.ui_factory.create_enhanced_label(
            "victory_defeat_indicator",
            confidence_threshold=0.9,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
        
        # 评级显示
        self.ui_elements["performance_rating"] = self.ui_factory.create_enhanced_label(
            "performance_rating",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="en"
        )
        
        # 得分显示
        self.ui_elements["battle_score_label"] = self.ui_factory.create_enhanced_label(
            "battle_score_label",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="en"
        )
    
    def _create_result_displays(self):
        """创建结果展示元素"""
        # 战斗总结
        self.ui_elements["battle_summary"] = self.ui_factory.create_enhanced_label(
            "battle_summary",
            confidence_threshold=0.7,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
        
        # 胜利优势显示
        self.ui_elements["victory_margin_display"] = self.ui_factory.create_label(
            "victory_margin_display",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="en"
        )
        
        # 战斗时长显示
        self.ui_elements["battle_duration_label"] = self.ui_factory.create_label(
            "battle_duration_label",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="en"
        )
        
        # 使用策略显示
        self.ui_elements["strategy_used_label"] = self.ui_factory.create_label(
            "strategy_used_label",
            confidence_threshold=0.7,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
    
    def _create_reward_displays(self):
        """创建奖励展示元素"""
        # 奖励标题
        self.ui_elements["rewards_title"] = self.ui_factory.create_label(
            "rewards_title",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
        
        # 经验值奖励
        self.ui_elements["experience_reward"] = self.ui_factory.create_enhanced_label(
            "experience_reward",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="en"
        )
        
        # 货币奖励
        self.ui_elements["currency_reward"] = self.ui_factory.create_enhanced_label(
            "currency_reward",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="en"
        )
        
        # 道具奖励区域
        self.ui_elements["item_rewards_area"] = self.ui_factory.create_label(
            "item_rewards_area",
            confidence_threshold=0.7,
            text_recognition_enabled=False
        )
        
        # 个别道具奖励（最多5个）
        for i in range(1, 6):
            self.ui_elements[f"item_reward_{i}"] = self.ui_factory.create_label(
                f"item_reward_{i}",
                confidence_threshold=0.7,
                text_recognition_enabled=True,
                ocr_language="ja"
            )
    
    def _create_statistics_displays(self):
        """创建统计信息元素"""
        # 属性变化显示
        for stat in ["vocal", "dance", "visual", "mental"]:
            self.ui_elements[f"{stat}_change"] = self.ui_factory.create_enhanced_label(
                f"{stat}_change",
                confidence_threshold=0.8,
                text_recognition_enabled=True,
                ocr_language="en"
            )
        
        # 等级变化显示
        self.ui_elements["level_change"] = self.ui_factory.create_enhanced_label(
            "level_change",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="en"
        )
        
        # 排名变化显示
        self.ui_elements["ranking_change"] = self.ui_factory.create_label(
            "ranking_change",
            confidence_threshold=0.7,
            text_recognition_enabled=True,
            ocr_language="en"
        )
    
    def _create_control_buttons(self):
        """创建控制按钮"""
        # 确认结果按钮
        self.ui_elements["confirm_result_button"] = self.ui_factory.create_enhanced_button(
            "confirm_result_button",
            confidence_threshold=0.9,
            timeout=5.0,
            verify_click_result=True
        )
        
        # 领取奖励按钮
        self.ui_elements["claim_rewards_button"] = self.ui_factory.create_enhanced_button(
            "claim_rewards_button",
            confidence_threshold=0.9,
            timeout=5.0,
            verify_click_result=True
        )
        
        # 继续按钮
        self.ui_elements["continue_button"] = self.ui_factory.create_enhanced_button(
            "continue_button",
            confidence_threshold=0.9,
            timeout=5.0,
            expected_scene_after_click="produce_main",
            verify_click_result=True
        )
        
        # 重新战斗按钮
        self.ui_elements["retry_battle_button"] = self.ui_factory.create_button(
            "retry_battle_button",
            confidence_threshold=0.8,
            timeout=5.0
        )
        
        # 查看详情按钮
        self.ui_elements["view_details_button"] = self.ui_factory.create_button(
            "view_details_button",
            confidence_threshold=0.8,
            timeout=3.0
        )
    
    def _get_critical_elements(self) -> List[str]:
        """获取战斗结果场景的关键UI元素"""
        return [
            "battle_result_title",
            "victory_defeat_indicator",
            "performance_rating",
            "confirm_result_button",
            "continue_button"
        ]
    
    def _verify_scene_specific_state(self) -> bool:
        """验证战斗结果场景特定状态"""
        try:
            # 验证结果标题可见
            title_element = self.get_ui_element("battle_result_title")
            if not title_element or not title_element.is_visible():
                self.scene_logger.debug("战斗结果标题不可见")
                return False
            
            # 验证胜负指示器可见
            result_element = self.get_ui_element("victory_defeat_indicator")
            if not result_element or not result_element.is_visible():
                self.scene_logger.debug("胜负指示器不可见")
                return False
            
            # 验证控制按钮可见
            confirm_button = self.get_ui_element("confirm_result_button")
            continue_button = self.get_ui_element("continue_button")
            
            if not ((confirm_button and confirm_button.is_visible()) or 
                   (continue_button and continue_button.is_visible())):
                self.scene_logger.debug("控制按钮不可见")
                return False
            
            return True
            
        except Exception as e:
            self.scene_logger.error(f"战斗结果场景状态验证异常: {e}")
            return False
    
    def process_battle_result(self) -> bool:
        """处理战斗结果"""
        try:
            self.scene_logger.info("开始处理战斗结果")
            
            with measure_block("battle_result_processing"):
                # 读取战斗结果
                if not self._read_battle_result():
                    return False
                
                # 读取奖励信息
                if not self._read_rewards():
                    return False
                
                # 读取属性变化
                if not self._read_stat_changes():
                    return False
                
                self._result_processed = True
                self.scene_logger.info("战斗结果处理完成")
                return True
            
        except Exception as e:
            self.scene_logger.error(f"处理战斗结果失败: {e}")
            return False
    
    def _read_battle_result(self) -> bool:
        """读取战斗结果"""
        try:
            # 读取胜负结果
            result_element = self.get_ui_element("victory_defeat_indicator")
            if result_element:
                result_text = result_element.read_text()
                if result_text:
                    result_text_lower = result_text.lower()
                    if "勝利" in result_text or "victory" in result_text_lower:
                        self._battle_result = BattleResult.VICTORY
                    elif "敗北" in result_text or "defeat" in result_text_lower:
                        self._battle_result = BattleResult.DEFEAT
                    elif "引き分け" in result_text or "draw" in result_text_lower:
                        self._battle_result = BattleResult.DRAW
            
            # 读取评级
            rating_element = self.get_ui_element("performance_rating")
            if rating_element:
                rating_text = rating_element.read_text()
                if rating_text:
                    import re
                    rating_match = re.search(r'[SABCD]', rating_text)
                    if rating_match:
                        self._performance_rating = rating_match.group()
            
            # 读取得分
            score_element = self.get_ui_element("battle_score_label")
            if score_element:
                score_text = score_element.read_text()
                if score_text:
                    import re
                    score_match = re.search(r'\d+', score_text)
                    if score_match:
                        self._battle_score = int(score_match.group())
            
            return True
            
        except Exception as e:
            self.scene_logger.error(f"读取战斗结果失败: {e}")
            return False
    
    def _read_rewards(self) -> bool:
        """读取奖励信息"""
        try:
            # 读取经验值奖励
            exp_element = self.get_ui_element("experience_reward")
            if exp_element:
                exp_text = exp_element.read_text()
                if exp_text:
                    import re
                    exp_match = re.search(r'\d+', exp_text)
                    if exp_match:
                        self._experience_gained = int(exp_match.group())
            
            # 读取货币奖励
            currency_element = self.get_ui_element("currency_reward")
            if currency_element:
                currency_text = currency_element.read_text()
                if currency_text:
                    import re
                    currency_match = re.search(r'[\d,]+', currency_text)
                    if currency_match:
                        currency_str = currency_match.group().replace(',', '')
                        self._currency_gained = int(currency_str)
            
            # 读取道具奖励
            self._items_gained.clear()
            for i in range(1, 6):
                item_element = self.get_ui_element(f"item_reward_{i}")
                if item_element and item_element.is_visible():
                    item_text = item_element.read_text()
                    if item_text:
                        self._items_gained.append(item_text.strip())
            
            return True
            
        except Exception as e:
            self.scene_logger.error(f"读取奖励信息失败: {e}")
            return False
    
    def _read_stat_changes(self) -> bool:
        """读取属性变化"""
        try:
            for stat in ["vocal", "dance", "visual", "mental"]:
                change_element = self.get_ui_element(f"{stat}_change")
                if change_element:
                    change_text = change_element.read_text()
                    if change_text:
                        import re
                        # 查找 +数字 或 -数字 格式
                        change_match = re.search(r'([+-]?\d+)', change_text)
                        if change_match:
                            self._stat_changes[stat] = int(change_match.group(1))
            
            return True
            
        except Exception as e:
            self.scene_logger.error(f"读取属性变化失败: {e}")
            return False
    
    def confirm_result(self) -> bool:
        """确认战斗结果"""
        try:
            self.scene_logger.info("确认战斗结果")
            
            # 记录UI元素使用
            self.optimizer.record_ui_element_usage(GameScene.BATTLE_RESULT, "confirm_result_button")
            
            if not self.interact_with_element("confirm_result_button", "click"):
                self.scene_logger.error("点击确认结果按钮失败")
                return False
            
            time.sleep(1.0)
            self.scene_logger.info("战斗结果确认成功")
            return True
            
        except Exception as e:
            self.scene_logger.error(f"确认战斗结果失败: {e}")
            return False
    
    def claim_rewards(self) -> bool:
        """领取奖励"""
        try:
            self.scene_logger.info("领取战斗奖励")
            
            # 记录UI元素使用
            self.optimizer.record_ui_element_usage(GameScene.BATTLE_RESULT, "claim_rewards_button")
            
            if not self.interact_with_element("claim_rewards_button", "click"):
                self.scene_logger.error("点击领取奖励按钮失败")
                return False
            
            # 等待奖励领取动画
            time.sleep(2.0)
            
            self._rewards_claimed = True
            self.scene_logger.info("战斗奖励领取成功")
            return True
            
        except Exception as e:
            self.scene_logger.error(f"领取战斗奖励失败: {e}")
            return False
    
    def continue_to_main(self) -> bool:
        """继续到主界面"""
        try:
            self.scene_logger.info("继续到主界面")
            
            if not self.interact_with_element("continue_button", "click"):
                self.scene_logger.error("点击继续按钮失败")
                return False
            
            time.sleep(2.0)
            self.scene_logger.info("成功返回主界面")
            return True
            
        except Exception as e:
            self.scene_logger.error(f"继续到主界面失败: {e}")
            return False
    
    def retry_battle(self) -> bool:
        """重新战斗"""
        try:
            self.scene_logger.info("重新开始战斗")
            
            if not self.interact_with_element("retry_battle_button", "click"):
                self.scene_logger.error("点击重新战斗按钮失败")
                return False
            
            time.sleep(2.0)
            self.scene_logger.info("重新战斗开始")
            return True
            
        except Exception as e:
            self.scene_logger.error(f"重新战斗失败: {e}")
            return False
    
    def get_battle_result_summary(self) -> Dict[str, Any]:
        """获取战斗结果摘要"""
        return {
            "battle_result": self._battle_result.value if self._battle_result else "unknown",
            "performance_rating": self._performance_rating,
            "battle_score": self._battle_score,
            "victory_margin": self._victory_margin,
            "experience_gained": self._experience_gained,
            "currency_gained": self._currency_gained,
            "items_gained": self._items_gained.copy(),
            "stat_changes": self._stat_changes.copy(),
            "result_processed": self._result_processed,
            "rewards_claimed": self._rewards_claimed,
            "scene_ready": self.is_scene_ready()
        }
    
    def is_victory(self) -> bool:
        """检查是否胜利"""
        return self._battle_result == BattleResult.VICTORY
    
    def is_defeat(self) -> bool:
        """检查是否失败"""
        return self._battle_result == BattleResult.DEFEAT
    
    def get_total_rewards_value(self) -> int:
        """获取奖励总价值"""
        return self._experience_gained + self._currency_gained + len(self._items_gained) * 100
