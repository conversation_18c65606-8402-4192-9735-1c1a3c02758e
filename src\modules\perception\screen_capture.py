"""
屏幕捕获模块
负责捕获游戏窗口的屏幕截图
"""

import time
import numpy as np
from typing import Optional, Tuple, Dict, Any
import mss
import cv2
from PIL import Image
import win32gui
import win32con

from ...utils.logger import get_logger
from ...core.data_structures import GakumasuBotException


class ScreenCaptureError(GakumasuBotException):
    """屏幕捕获错误"""
    pass


class GameWindowNotFound(GakumasuBotException):
    """游戏窗口未找到错误"""
    pass


class ScreenCapture:
    """屏幕捕获类"""
    
    def __init__(self, game_window_title: str = "gakumas"):
        """
        初始化屏幕捕获器
        
        Args:
            game_window_title: 游戏窗口标题
        """
        self.game_window_title = game_window_title
        self.logger = get_logger("ScreenCapture")
        self.mss_instance = mss.mss()
        
        # 窗口信息缓存
        self._window_handle = None
        self._window_rect = None
        self._last_window_check = 0
        self._window_check_interval = 5.0  # 5秒检查一次窗口
        
        self.logger.info(f"屏幕捕获器初始化完成，目标窗口: {game_window_title}")
    
    def find_game_window(self, force_refresh: bool = False) -> Optional[int]:
        """
        查找游戏窗口句柄
        
        Args:
            force_refresh: 是否强制刷新窗口信息
            
        Returns:
            窗口句柄，如果未找到返回None
        """
        current_time = time.time()
        
        # 如果不是强制刷新且缓存未过期，返回缓存的句柄
        if (not force_refresh and 
            self._window_handle and 
            current_time - self._last_window_check < self._window_check_interval):
            return self._window_handle
        
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_title = win32gui.GetWindowText(hwnd)
                if self.game_window_title in window_title:
                    windows.append((hwnd, window_title))
            return True
        
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        
        if windows:
            # 选择第一个匹配的窗口
            self._window_handle = windows[0][0]
            self._last_window_check = current_time
            self.logger.info(f"找到游戏窗口: {windows[0][1]} (句柄: {self._window_handle})")
            return self._window_handle
        else:
            self._window_handle = None
            self.logger.warning(f"未找到游戏窗口: {self.game_window_title}")
            return None
    
    def get_window_rect(self) -> Optional[Dict[str, int]]:
        """
        获取游戏窗口的位置和大小
        
        Returns:
            包含窗口位置信息的字典，格式: {"left": x, "top": y, "width": w, "height": h}
        """
        if not self._window_handle:
            self.find_game_window()
        
        if not self._window_handle:
            return None
        
        try:
            # 获取窗口矩形
            rect = win32gui.GetWindowRect(self._window_handle)
            left, top, right, bottom = rect
            
            # 获取客户区矩形（排除标题栏和边框）
            client_rect = win32gui.GetClientRect(self._window_handle)
            client_width = client_rect[2]
            client_height = client_rect[3]
            
            # 计算客户区在屏幕上的位置
            client_left, client_top = win32gui.ClientToScreen(self._window_handle, (0, 0))
            
            window_info = {
                "left": client_left,
                "top": client_top,
                "width": client_width,
                "height": client_height
            }
            
            self._window_rect = window_info
            return window_info
            
        except Exception as e:
            self.logger.error(f"获取窗口位置失败: {e}")
            return None
    
    def capture_screen(self, region: Optional[Dict[str, int]] = None) -> Optional[np.ndarray]:
        """
        捕获屏幕截图
        
        Args:
            region: 指定捕获区域，格式: {"left": x, "top": y, "width": w, "height": h}
                   如果为None，则捕获整个游戏窗口
        
        Returns:
            BGR格式的图像数组，如果捕获失败返回None
        """
        try:
            if region is None:
                # 捕获整个游戏窗口
                window_rect = self.get_window_rect()
                if not window_rect:
                    raise GameWindowNotFound("无法获取游戏窗口位置")
                region = window_rect
            
            # 使用mss捕获屏幕
            monitor = {
                "left": region["left"],
                "top": region["top"],
                "width": region["width"],
                "height": region["height"]
            }
            
            screenshot = self.mss_instance.grab(monitor)
            
            # 转换为numpy数组
            img_array = np.array(screenshot)
            
            # 转换颜色格式：BGRA -> BGR
            if img_array.shape[2] == 4:
                img_array = cv2.cvtColor(img_array, cv2.COLOR_BGRA2BGR)
            elif img_array.shape[2] == 3:
                img_array = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
            
            return img_array
            
        except Exception as e:
            self.logger.error(f"屏幕捕获失败: {e}")
            raise ScreenCaptureError(f"屏幕捕获失败: {e}")
    
    def capture_region(self, x: int, y: int, width: int, height: int) -> Optional[np.ndarray]:
        """
        捕获指定区域的截图
        
        Args:
            x: 区域左上角X坐标（相对于游戏窗口）
            y: 区域左上角Y坐标（相对于游戏窗口）
            width: 区域宽度
            height: 区域高度
            
        Returns:
            BGR格式的图像数组
        """
        window_rect = self.get_window_rect()
        if not window_rect:
            raise GameWindowNotFound("无法获取游戏窗口位置")
        
        # 计算绝对坐标
        abs_x = window_rect["left"] + x
        abs_y = window_rect["top"] + y
        
        region = {
            "left": abs_x,
            "top": abs_y,
            "width": width,
            "height": height
        }
        
        return self.capture_screen(region)
    
    def save_screenshot(self, image: np.ndarray, filename: str) -> bool:
        """
        保存截图到文件
        
        Args:
            image: 图像数组
            filename: 保存的文件名
            
        Returns:
            是否保存成功
        """
        try:
            cv2.imwrite(filename, image)
            self.logger.debug(f"截图已保存: {filename}")
            return True
        except Exception as e:
            self.logger.error(f"保存截图失败: {e}")
            return False
    
    def is_game_window_active(self) -> bool:
        """
        检查游戏窗口是否处于活动状态
        
        Returns:
            游戏窗口是否为前台窗口
        """
        if not self._window_handle:
            self.find_game_window()
        
        if not self._window_handle:
            return False
        
        try:
            foreground_window = win32gui.GetForegroundWindow()
            return foreground_window == self._window_handle
        except Exception as e:
            self.logger.error(f"检查窗口状态失败: {e}")
            return False
    
    def bring_game_window_to_front(self) -> bool:
        """
        将游戏窗口置于前台
        
        Returns:
            是否成功
        """
        if not self._window_handle:
            self.find_game_window()
        
        if not self._window_handle:
            return False
        
        try:
            # 恢复窗口（如果最小化）
            win32gui.ShowWindow(self._window_handle, win32con.SW_RESTORE)
            # 置于前台
            win32gui.SetForegroundWindow(self._window_handle)
            self.logger.info("游戏窗口已置于前台")
            return True
        except Exception as e:
            self.logger.error(f"置于前台失败: {e}")
            return False
    
    def get_window_info(self) -> Dict[str, Any]:
        """
        获取窗口详细信息
        
        Returns:
            窗口信息字典
        """
        info = {
            "window_handle": self._window_handle,
            "window_title": self.game_window_title,
            "window_rect": self._window_rect,
            "is_active": self.is_game_window_active(),
            "last_check_time": self._last_window_check
        }
        
        return info
    
    def __del__(self):
        """析构函数，清理资源"""
        if hasattr(self, 'mss_instance') and self.mss_instance:
            self.mss_instance.close()
