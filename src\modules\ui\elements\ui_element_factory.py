"""
UI元素工厂类
使用传统Class管理复杂创建逻辑
"""

from typing import Dict, Type, Optional, Any, Callable
from enum import Enum

from ....utils.logger import get_logger
from ..base.base_ui_element import BaseUIElement
from ..config.ui_element_config import (
    UIElementConfig, ButtonConfig, InputFieldConfig, LabelConfig
)
from .buttons import Button, EnhancedButton
from .inputs import InputField, EnhancedInputField
from .labels import Label, EnhancedLabel
from .composite import CompositeElement, FormElement, DialogElement


class UIElementType(Enum):
    """UI元素类型枚举"""
    BUTTON = "button"
    ENHANCED_BUTTON = "enhanced_button"
    INPUT_FIELD = "input_field"
    ENHANCED_INPUT_FIELD = "enhanced_input_field"
    LABEL = "label"
    ENHANCED_LABEL = "enhanced_label"
    COMPOSITE = "composite"
    FORM = "form"
    DIALOG = "dialog"


class UIElementFactory:
    """UI元素工厂 - 使用传统Class管理复杂创建逻辑"""

    def __init__(self, perception_module, action_controller):
        """
        初始化工厂
        
        Args:
            perception_module: 感知模块
            action_controller: 行动控制器
        """
        self.perception = perception_module
        self.action = action_controller
        self.logger = get_logger("UIElementFactory")
        
        # 元素类型注册表
        self.element_registry: Dict[UIElementType, Type[BaseUIElement]] = {
            UIElementType.BUTTON: Button,
            UIElementType.ENHANCED_BUTTON: EnhancedButton,
            UIElementType.INPUT_FIELD: InputField,
            UIElementType.ENHANCED_INPUT_FIELD: EnhancedInputField,
            UIElementType.LABEL: Label,
            UIElementType.ENHANCED_LABEL: EnhancedLabel,
            UIElementType.COMPOSITE: CompositeElement,
            UIElementType.FORM: FormElement,
            UIElementType.DIALOG: DialogElement
        }
        
        # 配置类型映射
        self.config_type_mapping: Dict[UIElementType, Type] = {
            UIElementType.BUTTON: ButtonConfig,
            UIElementType.ENHANCED_BUTTON: ButtonConfig,
            UIElementType.INPUT_FIELD: InputFieldConfig,
            UIElementType.ENHANCED_INPUT_FIELD: InputFieldConfig,
            UIElementType.LABEL: LabelConfig,
            UIElementType.ENHANCED_LABEL: LabelConfig,
            UIElementType.COMPOSITE: UIElementConfig,
            UIElementType.FORM: UIElementConfig,
            UIElementType.DIALOG: UIElementConfig
        }
        
        # 自定义创建器
        self.custom_creators: Dict[str, Callable] = {}

    def create_element(self, element_type: UIElementType, 
                      config: UIElementConfig) -> Optional[BaseUIElement]:
        """
        根据配置创建UI元素
        
        Args:
            element_type: 元素类型
            config: 元素配置
            
        Returns:
            UI元素实例，失败返回None
        """
        try:
            self.logger.debug(f"创建UI元素: {element_type.value}")
            
            # 验证配置类型
            if not self._validate_config_type(element_type, config):
                return None
            
            # 获取元素类
            element_class = self.element_registry.get(element_type)
            if not element_class:
                self.logger.error(f"不支持的UI元素类型: {element_type.value}")
                return None
            
            # 创建元素实例
            element = element_class(config, self.perception, self.action)
            
            self.logger.info(f"成功创建UI元素: {element_type.value} - {config.template_name}")
            return element
            
        except Exception as e:
            self.logger.error(f"创建UI元素失败: {element_type.value}, 错误: {e}")
            return None

    def create_element_from_dict(self, element_config: Dict[str, Any]) -> Optional[BaseUIElement]:
        """
        从字典配置创建UI元素
        
        Args:
            element_config: 元素配置字典，必须包含 'type' 字段
            
        Returns:
            UI元素实例，失败返回None
        """
        try:
            element_type_str = element_config.get('type')
            if not element_type_str:
                self.logger.error("元素配置缺少 'type' 字段")
                return None
            
            # 转换为枚举类型
            try:
                element_type = UIElementType(element_type_str)
            except ValueError:
                self.logger.error(f"无效的元素类型: {element_type_str}")
                return None
            
            # 创建配置对象
            config = self._create_config_from_dict(element_type, element_config)
            if not config:
                return None
            
            return self.create_element(element_type, config)
            
        except Exception as e:
            self.logger.error(f"从字典创建UI元素失败: {e}")
            return None

    def _validate_config_type(self, element_type: UIElementType, 
                             config: UIElementConfig) -> bool:
        """验证配置类型"""
        expected_config_type = self.config_type_mapping.get(element_type)
        if expected_config_type and not isinstance(config, expected_config_type):
            self.logger.warning(
                f"配置类型不匹配: 期望 {expected_config_type.__name__}, "
                f"实际 {type(config).__name__}"
            )
            # 不强制要求类型匹配，只是警告
        return True

    def _create_config_from_dict(self, element_type: UIElementType, 
                                config_dict: Dict[str, Any]) -> Optional[UIElementConfig]:
        """从字典创建配置对象"""
        try:
            config_class = self.config_type_mapping.get(element_type, UIElementConfig)
            
            # 过滤有效的配置参数
            valid_params = self._filter_valid_params(config_class, config_dict)
            
            # 创建配置实例
            config = config_class(**valid_params)
            return config
            
        except Exception as e:
            self.logger.error(f"创建配置对象失败: {e}")
            return None

    def _filter_valid_params(self, config_class: Type, params: Dict[str, Any]) -> Dict[str, Any]:
        """过滤有效的配置参数"""
        import inspect
        
        try:
            # 获取构造函数签名
            sig = inspect.signature(config_class.__init__)
            valid_param_names = set(sig.parameters.keys()) - {'self'}
            
            # 过滤参数
            filtered_params = {
                key: value for key, value in params.items() 
                if key in valid_param_names
            }
            
            return filtered_params
            
        except Exception as e:
            self.logger.error(f"过滤配置参数失败: {e}")
            return {}

    def register_element_type(self, element_type: UIElementType, 
                             element_class: Type[BaseUIElement],
                             config_class: Type[UIElementConfig] = None):
        """
        注册新的元素类型
        
        Args:
            element_type: 元素类型
            element_class: 元素类
            config_class: 配置类（可选）
        """
        self.element_registry[element_type] = element_class
        if config_class:
            self.config_type_mapping[element_type] = config_class
        
        self.logger.info(f"注册元素类型: {element_type.value} -> {element_class.__name__}")

    def register_custom_creator(self, element_name: str, creator: Callable):
        """
        注册自定义创建器
        
        Args:
            element_name: 元素名称
            creator: 创建器函数
        """
        self.custom_creators[element_name] = creator
        self.logger.info(f"注册自定义创建器: {element_name}")

    def create_custom_element(self, element_name: str, **kwargs) -> Optional[BaseUIElement]:
        """
        使用自定义创建器创建元素
        
        Args:
            element_name: 元素名称
            **kwargs: 创建参数
            
        Returns:
            UI元素实例，失败返回None
        """
        creator = self.custom_creators.get(element_name)
        if not creator:
            self.logger.error(f"未找到自定义创建器: {element_name}")
            return None
        
        try:
            element = creator(self.perception, self.action, **kwargs)
            self.logger.info(f"使用自定义创建器创建元素: {element_name}")
            return element
            
        except Exception as e:
            self.logger.error(f"自定义创建器执行失败: {element_name}, 错误: {e}")
            return None

    def create_button(self, template_name: str, **kwargs) -> Optional[Button]:
        """便捷方法：创建按钮"""
        config = ButtonConfig(template_name=template_name, **kwargs)
        return self.create_element(UIElementType.BUTTON, config)

    def create_enhanced_button(self, template_name: str, **kwargs) -> Optional[EnhancedButton]:
        """便捷方法：创建增强按钮"""
        config = ButtonConfig(template_name=template_name, **kwargs)
        return self.create_element(UIElementType.ENHANCED_BUTTON, config)

    def create_input_field(self, template_name: str, **kwargs) -> Optional[InputField]:
        """便捷方法：创建输入框"""
        config = InputFieldConfig(template_name=template_name, **kwargs)
        return self.create_element(UIElementType.INPUT_FIELD, config)

    def create_enhanced_input_field(self, template_name: str, **kwargs) -> Optional[EnhancedInputField]:
        """便捷方法：创建增强输入框"""
        config = InputFieldConfig(template_name=template_name, **kwargs)
        return self.create_element(UIElementType.ENHANCED_INPUT_FIELD, config)

    def create_label(self, template_name: str, **kwargs) -> Optional[Label]:
        """便捷方法：创建标签"""
        config = LabelConfig(template_name=template_name, **kwargs)
        return self.create_element(UIElementType.LABEL, config)

    def create_enhanced_label(self, template_name: str, **kwargs) -> Optional[EnhancedLabel]:
        """便捷方法：创建增强标签"""
        config = LabelConfig(template_name=template_name, **kwargs)
        return self.create_element(UIElementType.ENHANCED_LABEL, config)

    def create_form(self, template_name: str, **kwargs) -> Optional[FormElement]:
        """便捷方法：创建表单"""
        config = UIElementConfig(template_name=template_name, **kwargs)
        return self.create_element(UIElementType.FORM, config)

    def create_dialog(self, template_name: str, **kwargs) -> Optional[DialogElement]:
        """便捷方法：创建对话框"""
        config = UIElementConfig(template_name=template_name, **kwargs)
        return self.create_element(UIElementType.DIALOG, config)

    def get_supported_types(self) -> List[str]:
        """获取支持的元素类型列表"""
        return [element_type.value for element_type in self.element_registry.keys()]

    def get_factory_stats(self) -> Dict[str, Any]:
        """获取工厂统计信息"""
        return {
            'supported_types': len(self.element_registry),
            'custom_creators': len(self.custom_creators),
            'type_list': self.get_supported_types(),
            'custom_creator_list': list(self.custom_creators.keys())
        }
