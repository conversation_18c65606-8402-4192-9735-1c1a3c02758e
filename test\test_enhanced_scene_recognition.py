"""
增强的场景识别测试脚本
包含多尺度匹配和图像预处理功能
"""

import sys
import os
import time
import cv2
import numpy as np
from pathlib import Path
from datetime import datetime
from typing import Optional, Dict, Any, Tuple

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_enhanced_template_matching():
    """增强的模板匹配测试"""
    print("🧪 开始增强场景识别测试")
    print("=" * 60)
    
    test_results = {
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "template_file": "main_menu_full.png",
        "success": False,
        "details": {}
    }
    
    try:
        # 步骤1: 导入必要的模块
        print("📦 导入模块...")
        from src.modules.perception.template_matcher import TemplateMatcher
        from src.modules.perception.screen_capture import ScreenCapture
        
        # 步骤2: 初始化组件
        print("🔧 初始化组件...")
        screen_capture = ScreenCapture("gakumas")
        template_matcher = TemplateMatcher("assets/templates")
        
        # 步骤3: 捕获游戏截图
        print("📸 捕获游戏截图...")
        screenshot = screen_capture.capture_screen()
        if screenshot is None:
            raise Exception("截图捕获失败")
        
        print(f"   截图尺寸: {screenshot.shape}")
        
        # 步骤4: 加载模板
        print("🖼️ 加载模板...")
        template = template_matcher.load_template("main_menu_full")
        if template is None:
            raise Exception("模板加载失败")
        
        print(f"   模板尺寸: {template.shape}")
        
        # 步骤5: 执行多种匹配策略
        print("🔍 执行多种匹配策略...")
        
        strategies = [
            ("原始匹配", lambda s, t: match_template_basic(s, t)),
            ("多尺度匹配", lambda s, t: match_template_multiscale(s, t)),
            ("亮度调整匹配", lambda s, t: match_template_brightness_adjusted(s, t)),
            ("综合匹配", lambda s, t: match_template_comprehensive(s, t))
        ]
        
        best_result = None
        best_confidence = 0
        
        for strategy_name, strategy_func in strategies:
            print(f"\n   🎯 尝试策略: {strategy_name}")
            result = strategy_func(screenshot, template)
            
            if result and result["confidence"] > best_confidence:
                best_result = result
                best_confidence = result["confidence"]
                best_result["strategy"] = strategy_name
            
            if result:
                print(f"      ✅ 置信度: {result['confidence']:.3f}")
            else:
                print(f"      ❌ 未找到匹配")
        
        # 步骤6: 分析最佳结果
        if best_result and best_confidence > 0.3:  # 降低阈值用于测试
            print(f"\n✅ 找到最佳匹配！策略: {best_result['strategy']}")
            print(f"   置信度: {best_result['confidence']:.3f}")
            print(f"   位置: {best_result['position']}")
            
            test_results["success"] = True
            test_results["details"]["best_match"] = best_result
            
            # 保存标记的截图
            marked_screenshot = mark_match_on_screenshot(screenshot, best_result)
            marked_path = f"test/enhanced_marked_{int(time.time())}.png"
            cv2.imwrite(marked_path, marked_screenshot)
            test_results["details"]["marked_screenshot"] = marked_path
            
        else:
            print("\n❌ 所有策略都未找到满意的匹配")
            test_results["success"] = False
            test_results["details"]["best_confidence"] = best_confidence
            
            # 保存调试信息
            debug_info = create_debug_visualization(screenshot, template)
            test_results["details"]["debug_info"] = debug_info
        
        # 保存原始截图
        screenshot_path = f"test/enhanced_screenshot_{int(time.time())}.png"
        cv2.imwrite(screenshot_path, screenshot)
        test_results["details"]["screenshot"] = screenshot_path
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        test_results["success"] = False
        test_results["details"]["error"] = str(e)
        import traceback
        test_results["details"]["traceback"] = traceback.format_exc()
    
    # 输出测试结果摘要
    print("\n" + "=" * 60)
    print("📋 测试结果摘要")
    print("=" * 60)
    print(f"测试时间: {test_results['timestamp']}")
    print(f"模板文件: {test_results['template_file']}")
    print(f"测试结果: {'✅ 成功' if test_results['success'] else '❌ 失败'}")
    
    if test_results["success"]:
        match_info = test_results["details"]["best_match"]
        print(f"最佳策略: {match_info['strategy']}")
        print(f"匹配置信度: {match_info['confidence']:.3f}")
        print(f"匹配位置: {match_info['position']}")
    else:
        best_conf = test_results["details"].get("best_confidence", 0)
        print(f"最高置信度: {best_conf:.3f}")
        print("建议: 可能需要更新模板图片或调整游戏窗口大小")
    
    return test_results

def match_template_basic(screenshot, template):
    """基础模板匹配"""
    try:
        result = cv2.matchTemplate(screenshot, template, cv2.TM_CCOEFF_NORMED)
        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
        
        if max_val > 0.1:  # 很低的阈值
            return {
                "confidence": max_val,
                "position": max_loc,
                "size": template.shape[:2]
            }
    except Exception as e:
        print(f"      基础匹配错误: {e}")
    return None

def match_template_multiscale(screenshot, template):
    """多尺度模板匹配"""
    scales = [0.5, 0.6, 0.7, 0.8, 0.9, 1.0, 1.1, 1.2, 1.3, 1.4, 1.5]
    best_match = None
    best_confidence = 0
    
    for scale in scales:
        try:
            # 缩放模板
            new_width = int(template.shape[1] * scale)
            new_height = int(template.shape[0] * scale)
            
            if new_width > screenshot.shape[1] or new_height > screenshot.shape[0]:
                continue
                
            scaled_template = cv2.resize(template, (new_width, new_height))
            
            # 执行匹配
            result = cv2.matchTemplate(screenshot, scaled_template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            if max_val > best_confidence:
                best_confidence = max_val
                best_match = {
                    "confidence": max_val,
                    "position": max_loc,
                    "size": (new_height, new_width),
                    "scale": scale
                }
                
        except Exception as e:
            continue
    
    return best_match if best_confidence > 0.1 else None

def match_template_brightness_adjusted(screenshot, template):
    """亮度调整后的模板匹配"""
    try:
        # 计算亮度差异
        screenshot_mean = np.mean(screenshot)
        template_mean = np.mean(template)
        
        # 调整截图亮度以匹配模板
        brightness_factor = template_mean / screenshot_mean if screenshot_mean > 0 else 1
        adjusted_screenshot = np.clip(screenshot * brightness_factor, 0, 255).astype(np.uint8)
        
        # 执行匹配
        result = cv2.matchTemplate(adjusted_screenshot, template, cv2.TM_CCOEFF_NORMED)
        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
        
        if max_val > 0.1:
            return {
                "confidence": max_val,
                "position": max_loc,
                "size": template.shape[:2],
                "brightness_factor": brightness_factor
            }
    except Exception as e:
        print(f"      亮度调整匹配错误: {e}")
    return None

def match_template_comprehensive(screenshot, template):
    """综合匹配策略（多尺度 + 亮度调整）"""
    scales = [0.6, 0.7, 0.75, 0.8, 0.85, 0.9, 1.0]
    best_match = None
    best_confidence = 0
    
    # 计算亮度调整因子
    screenshot_mean = np.mean(screenshot)
    template_mean = np.mean(template)
    brightness_factor = template_mean / screenshot_mean if screenshot_mean > 0 else 1
    
    for scale in scales:
        try:
            # 缩放模板
            new_width = int(template.shape[1] * scale)
            new_height = int(template.shape[0] * scale)
            
            if new_width > screenshot.shape[1] or new_height > screenshot.shape[0]:
                continue
                
            scaled_template = cv2.resize(template, (new_width, new_height))
            
            # 调整截图亮度
            adjusted_screenshot = np.clip(screenshot * brightness_factor, 0, 255).astype(np.uint8)
            
            # 执行匹配
            result = cv2.matchTemplate(adjusted_screenshot, scaled_template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            if max_val > best_confidence:
                best_confidence = max_val
                best_match = {
                    "confidence": max_val,
                    "position": max_loc,
                    "size": (new_height, new_width),
                    "scale": scale,
                    "brightness_factor": brightness_factor
                }
                
        except Exception as e:
            continue
    
    return best_match if best_confidence > 0.1 else None

def mark_match_on_screenshot(screenshot, match_result):
    """在截图上标记匹配位置"""
    marked = screenshot.copy()
    x, y = match_result["position"]
    h, w = match_result["size"]
    
    # 绘制绿色矩形框
    cv2.rectangle(marked, (x, y), (x + w, y + h), (0, 255, 0), 3)
    
    # 添加置信度文本
    confidence_text = f"Confidence: {match_result['confidence']:.3f}"
    cv2.putText(marked, confidence_text, (x, y - 10), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
    
    return marked

def create_debug_visualization(screenshot, template):
    """创建调试可视化信息"""
    debug_info = {}
    
    try:
        # 尺寸信息
        debug_info["sizes"] = {
            "screenshot": screenshot.shape,
            "template": template.shape,
            "scale_needed": (screenshot.shape[1] / template.shape[1], 
                           screenshot.shape[0] / template.shape[0])
        }
        
        # 亮度信息
        debug_info["brightness"] = {
            "screenshot_mean": float(np.mean(screenshot)),
            "template_mean": float(np.mean(template)),
            "brightness_ratio": float(np.mean(template) / np.mean(screenshot))
        }
        
        # 保存调试图像
        debug_path = f"test/debug_comparison_{int(time.time())}.png"
        
        # 创建对比图像
        template_resized = cv2.resize(template, (screenshot.shape[1], screenshot.shape[0]))
        comparison = np.hstack([screenshot, template_resized])
        cv2.imwrite(debug_path, comparison)
        
        debug_info["comparison_image"] = debug_path
        
    except Exception as e:
        debug_info["error"] = str(e)
    
    return debug_info

def cleanup_test_files(test_results: Dict[str, Any]):
    """清理测试过程中生成的文件"""
    print("\n🧹 清理测试文件...")
    
    files_to_clean = []
    details = test_results.get("details", {})
    
    for key in ["screenshot", "marked_screenshot", "comparison_image"]:
        if key in details:
            files_to_clean.append(details[key])
    
    if "debug_info" in details and "comparison_image" in details["debug_info"]:
        files_to_clean.append(details["debug_info"]["comparison_image"])
    
    # 清理文件
    cleaned_count = 0
    for file_path in files_to_clean:
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                print(f"   ✅ 已删除: {file_path}")
                cleaned_count += 1
        except Exception as e:
            print(f"   ❌ 删除失败: {file_path} - {e}")
    
    print(f"   清理完成，共删除 {cleaned_count} 个文件")

if __name__ == "__main__":
    # 执行测试
    results = test_enhanced_template_matching()
    
    # 询问是否清理测试文件
    if "screenshot" in results.get("details", {}):
        print(f"\n测试文件保存位置:")
        details = results["details"]
        for key in ["screenshot", "marked_screenshot"]:
            if key in details:
                print(f"  {key}: {details[key]}")
        
        if "debug_info" in details and "comparison_image" in details["debug_info"]:
            print(f"  debug_comparison: {details['debug_info']['comparison_image']}")
        
        user_input = input("是否删除测试文件？(y/n): ").lower().strip()
        if user_input in ['y', 'yes', '是']:
            cleanup_test_files(results)
        else:
            print("测试文件已保留，可用于调试分析")
    
    print("\n🎯 增强场景识别测试完成！")
