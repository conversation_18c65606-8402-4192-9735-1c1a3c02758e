"""
UI元素抽象基类
使用传统Class实现复杂业务逻辑
"""

import time
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, Protocol, Callable
from collections import deque

from ....utils.logger import get_logger
from ..config.ui_element_config import UIElementConfig, Position, MatchResult


class UIElementInterface(Protocol):
    """UI元素接口协议"""
    
    def click(self) -> bool:
        """点击UI元素"""
        ...
    
    def is_visible(self) -> bool:
        """检查UI元素是否可见"""
        ...
    
    def get_position(self) -> Optional[Position]:
        """获取UI元素位置"""
        ...


class BaseUIElement(ABC):
    """UI元素抽象基类 - 使用传统Class"""

    def __init__(self, config: UIElementConfig, perception_module, action_controller):
        """
        初始化UI元素
        
        Args:
            config: UI元素配置
            perception_module: 感知模块
            action_controller: 行动控制器
        """
        self.config = config
        self.perception = perception_module
        self.action = action_controller
        self.logger = get_logger(f"UIElement.{self.__class__.__name__}")

        # 复杂状态管理
        self._cache: Dict[str, Any] = {}
        self._performance_history: deque = deque(maxlen=100)
        self._error_count = 0
        self._last_action_time = 0.0
        
        # 缓存相关
        self._visibility_cache: Dict[str, tuple] = {}
        self._position_cache: Dict[str, tuple] = {}

    @abstractmethod
    def click(self) -> bool:
        """抽象点击方法"""
        pass

    def is_visible(self) -> bool:
        """
        可见性检查 - 包含缓存逻辑
        
        Returns:
            是否可见
        """
        if not self.config.cache_enabled:
            return self._check_visibility_direct()
        
        return self._check_visibility_cached()

    def _check_visibility_cached(self) -> bool:
        """带缓存的可见性检查"""
        current_time = time.time()
        cache_key = f"visible_{self.config.template_name}"

        # 检查缓存
        if cache_key in self._visibility_cache:
            cached_time, cached_result = self._visibility_cache[cache_key]
            if current_time - cached_time < self.config.cache_ttl:
                return cached_result

        # 执行实际检查
        result = self._check_visibility_direct()
        self._visibility_cache[cache_key] = (current_time, result)

        # 清理过期缓存
        self._cleanup_cache()
        return result

    def _check_visibility_direct(self) -> bool:
        """直接可见性检查"""
        try:
            match_result = self.perception.find_ui_element(self.config.template_name)
            return (match_result is not None and 
                   match_result.confidence >= self.config.confidence_threshold)
        except Exception as e:
            self.logger.error(f"检查UI元素可见性失败: {e}")
            self._error_count += 1
            return False

    def get_position(self) -> Optional[Position]:
        """
        获取UI元素当前位置
        
        Returns:
            元素中心坐标，如果未找到返回None
        """
        if not self.config.cache_enabled:
            return self._get_position_direct()
        
        return self._get_position_cached()

    def _get_position_cached(self) -> Optional[Position]:
        """带缓存的位置获取"""
        current_time = time.time()
        cache_key = f"position_{self.config.template_name}"

        # 检查缓存
        if cache_key in self._position_cache:
            cached_time, cached_result = self._position_cache[cache_key]
            if current_time - cached_time < self.config.cache_ttl:
                return cached_result

        # 执行实际获取
        result = self._get_position_direct()
        self._position_cache[cache_key] = (current_time, result)

        # 清理过期缓存
        self._cleanup_cache()
        return result

    def _get_position_direct(self) -> Optional[Position]:
        """直接位置获取"""
        try:
            match_result = self.perception.find_ui_element(self.config.template_name)
            if match_result and match_result.confidence >= self.config.confidence_threshold:
                return match_result.center
            return self.config.position  # 返回预设位置作为备选
        except Exception as e:
            self.logger.error(f"获取UI元素位置失败: {e}")
            self._error_count += 1
            return self.config.position

    def _cleanup_cache(self):
        """清理过期缓存"""
        current_time = time.time()
        expired_keys = []

        # 清理可见性缓存
        for key, (cached_time, _) in self._visibility_cache.items():
            if current_time - cached_time > self.config.cache_ttl:
                expired_keys.append(key)

        for key in expired_keys:
            del self._visibility_cache[key]

        # 清理位置缓存
        expired_keys.clear()
        for key, (cached_time, _) in self._position_cache.items():
            if current_time - cached_time > self.config.cache_ttl:
                expired_keys.append(key)

        for key in expired_keys:
            del self._position_cache[key]

    def _record_performance(self, operation_name: str, start_time: float, 
                          success: bool, error_message: str = ""):
        """记录性能数据"""
        end_time = time.time()
        performance_data = {
            'operation': operation_name,
            'duration': end_time - start_time,
            'success': success,
            'timestamp': end_time,
            'error_message': error_message
        }
        self._performance_history.append(performance_data)

    def _pre_action_validation(self) -> bool:
        """执行前验证"""
        if not self.config.enabled:
            self.logger.warning(f"UI元素 {self.config.template_name} 已禁用")
            return False

        # 检查是否可见
        if not self.is_visible():
            self.logger.warning(f"UI元素 {self.config.template_name} 不可见")
            return False

        return True

    def _post_action_processing(self, success: bool):
        """执行后处理"""
        self._last_action_time = time.time()
        
        if success:
            self._error_count = 0  # 重置错误计数
        else:
            self._error_count += 1

        # 清理缓存（因为界面可能已经改变）
        self._cache.clear()
        self._visibility_cache.clear()
        self._position_cache.clear()

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        if not self._performance_history:
            return {}

        total_operations = len(self._performance_history)
        successful_operations = sum(1 for p in self._performance_history if p['success'])
        durations = [p['duration'] for p in self._performance_history]

        return {
            'total_operations': total_operations,
            'success_rate': successful_operations / total_operations,
            'average_duration': sum(durations) / len(durations),
            'min_duration': min(durations),
            'max_duration': max(durations),
            'error_count': self._error_count,
            'last_action_time': self._last_action_time
        }

    def reset_performance_stats(self):
        """重置性能统计"""
        self._performance_history.clear()
        self._error_count = 0
        self._last_action_time = 0.0

    def clear_cache(self):
        """清理所有缓存"""
        self._cache.clear()
        self._visibility_cache.clear()
        self._position_cache.clear()

    def set_enabled(self, enabled: bool):
        """设置启用状态"""
        # 注意：如果config是frozen dataclass，这里会失败
        # 在实际使用中，可能需要重新创建config对象
        try:
            self.config.enabled = enabled
            self.logger.debug(f"UI元素 {self.config.template_name} 状态设置为: {'启用' if enabled else '禁用'}")
        except AttributeError:
            self.logger.warning("无法修改frozen配置对象的enabled状态")

    def is_enabled(self) -> bool:
        """检查是否启用"""
        return self.config.enabled

    def __str__(self) -> str:
        """字符串表示"""
        return f"{self.__class__.__name__}(template='{self.config.template_name}')"

    def __repr__(self) -> str:
        """详细字符串表示"""
        return (f"{self.__class__.__name__}("
                f"template='{self.config.template_name}', "
                f"enabled={self.config.enabled}, "
                f"confidence={self.config.confidence_threshold})")
