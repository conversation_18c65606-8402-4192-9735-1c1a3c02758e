# 数据收集截图工具设计文档

## 1. 项目概述

### 1.1 设计目标
基于现有的MSS和Win32 API截图功能，开发一个专门用于数据收集的截图工具模块，提供批量截图、定时截图、区域截图等功能，并配备Web UI界面进行可视化管理。

### 1.2 核心功能
- **批量截图管理**: 支持批量截图任务的创建、执行、暂停和停止
- **定时截图**: 可配置的定时截图间隔和持续时间
- **区域截图**: 支持自定义截图区域选择
- **智能命名**: 自动生成带时间戳和元数据的文件名
- **分类存储**: 按日期、任务类型等维度自动分类存储
- **元数据记录**: 记录截图时间、窗口状态、截图参数等信息
- **Web UI管理**: 提供直观的Web界面进行截图任务管理

### 1.3 技术架构
- **后端**: 基于现有FastAPI框架扩展
- **前端**: Vue.js 3 + Element Plus，与现有UI风格保持一致
- **实时通信**: WebSocket实现状态更新和预览
- **存储**: 本地文件系统 + JSON元数据存储
- **截图引擎**: 复用现有ScreenCapture和EnhancedScreenCapture模块

## 2. 系统架构设计

### 2.1 模块结构
```
src/modules/tools/
├── screenshot_collector/
│   ├── __init__.py
│   ├── collector.py              # 核心截图收集器
│   ├── task_manager.py           # 截图任务管理器
│   ├── storage_manager.py        # 存储管理器
│   ├── metadata_manager.py       # 元数据管理器
│   └── config.py                 # 配置管理
├── web_ui/
│   ├── __init__.py
│   ├── api_routes.py             # API路由
│   ├── websocket_handler.py      # WebSocket处理
│   └── models.py                 # 数据模型
```

### 2.2 Web UI结构
```
src/web/static/screenshot_tool/
├── index.html                    # 截图工具主页面
├── css/
│   └── screenshot-tool.css       # 样式文件
├── js/
│   ├── screenshot-tool.js        # 主要逻辑
│   ├── api-client.js             # API客户端
│   └── websocket-client.js       # WebSocket客户端
└── components/
    ├── task-config.js            # 任务配置组件
    ├── preview-panel.js          # 预览面板组件
    ├── history-manager.js        # 历史记录管理组件
    └── performance-monitor.js    # 性能监控组件
```

### 2.3 数据流设计
```
用户操作 → Web UI → API接口 → 任务管理器 → 截图收集器 → 存储管理器
                ↓
            WebSocket ← 状态更新 ← 任务执行器
```

## 3. 核心模块设计

### 3.1 截图收集器 (ScreenshotCollector)

#### 3.1.1 类设计
```python
class ScreenshotCollector:
    """数据收集截图工具核心类"""
    
    def __init__(self, config: ScreenshotConfig):
        self.screen_capture = EnhancedScreenCapture()
        self.storage_manager = StorageManager()
        self.metadata_manager = MetadataManager()
        self.task_queue = Queue()
        self.is_running = False
        
    async def start_batch_capture(self, task: CaptureTask) -> str:
        """启动批量截图任务"""
        
    async def stop_capture(self, task_id: str) -> bool:
        """停止截图任务"""
        
    async def pause_capture(self, task_id: str) -> bool:
        """暂停截图任务"""
        
    def capture_single(self, config: CaptureConfig) -> CaptureResult:
        """单次截图"""
        
    def get_preview_image(self) -> bytes:
        """获取预览图像"""
```

#### 3.1.2 截图任务类型
```python
class CaptureTaskType(Enum):
    SINGLE = "single"           # 单次截图
    INTERVAL = "interval"       # 定时截图
    BATCH = "batch"            # 批量截图
    REGION = "region"          # 区域截图
    CONTINUOUS = "continuous"   # 连续截图
```

### 3.2 任务管理器 (TaskManager)

#### 3.2.1 任务调度
```python
class CaptureTaskManager:
    """截图任务管理器"""
    
    def __init__(self):
        self.active_tasks: Dict[str, CaptureTask] = {}
        self.task_history: List[TaskRecord] = []
        self.scheduler = AsyncIOScheduler()
        
    async def create_task(self, config: TaskConfig) -> str:
        """创建截图任务"""
        
    async def execute_task(self, task_id: str) -> None:
        """执行截图任务"""
        
    def get_task_status(self, task_id: str) -> TaskStatus:
        """获取任务状态"""
        
    def list_active_tasks(self) -> List[TaskInfo]:
        """获取活动任务列表"""
```

#### 3.2.2 任务配置
```python
@dataclass
class TaskConfig:
    task_type: CaptureTaskType
    interval: float = 1.0           # 截图间隔(秒)
    duration: Optional[float] = None # 持续时间(秒)
    region: Optional[Region] = None  # 截图区域
    save_path: str = "data/screenshots"
    filename_pattern: str = "{timestamp}_{task_id}"
    max_screenshots: Optional[int] = None
    quality: int = 95               # 图片质量
    format: str = "png"             # 图片格式
```

### 3.3 存储管理器 (StorageManager)

#### 3.3.1 文件组织
```python
class StorageManager:
    """截图存储管理器"""
    
    def __init__(self, base_path: str = "data/screenshots"):
        self.base_path = Path(base_path)
        self.create_directory_structure()
        
    def save_screenshot(self, image: np.ndarray, 
                       metadata: ScreenshotMetadata) -> str:
        """保存截图文件"""
        
    def generate_filename(self, metadata: ScreenshotMetadata) -> str:
        """生成文件名"""
        
    def organize_by_date(self, date: datetime) -> Path:
        """按日期组织目录"""
        
    def cleanup_old_files(self, days: int = 30) -> int:
        """清理旧文件"""
```

#### 3.3.2 目录结构
```
data/screenshots/
├── 2024/
│   ├── 01/
│   │   ├── 15/                   # 按日期分类
│   │   │   ├── task_001/         # 按任务分类
│   │   │   │   ├── screenshot_001.png
│   │   │   │   ├── screenshot_002.png
│   │   │   │   └── metadata.json
│   │   │   └── task_002/
│   │   └── 16/
│   └── 02/
├── temp/                         # 临时文件
└── exports/                      # 导出文件
```

## 4. Web UI界面设计

### 4.1 主界面布局
```html
<div class="screenshot-tool-container">
  <!-- 顶部导航 -->
  <header class="tool-header">
    <h2>数据收集截图工具</h2>
    <div class="status-indicators">
      <el-tag>连接状态</el-tag>
      <el-tag>任务状态</el-tag>
    </div>
  </header>
  
  <!-- 主要内容区域 -->
  <main class="tool-main">
    <!-- 左侧控制面板 -->
    <aside class="control-panel">
      <task-config-panel />
      <quick-actions />
    </aside>
    
    <!-- 中间预览区域 -->
    <section class="preview-area">
      <game-window-preview />
      <region-selector />
    </section>
    
    <!-- 右侧信息面板 -->
    <aside class="info-panel">
      <task-status-panel />
      <performance-monitor />
    </aside>
  </main>
  
  <!-- 底部历史记录 -->
  <footer class="history-panel">
    <screenshot-history />
  </footer>
</div>
```

### 4.2 核心组件设计

#### 4.2.1 任务配置面板
```javascript
const TaskConfigPanel = {
  template: `
    <el-card class="config-panel">
      <template #header>
        <span>截图任务配置</span>
      </template>
      
      <el-form :model="taskConfig" label-width="100px">
        <el-form-item label="任务类型">
          <el-select v-model="taskConfig.type">
            <el-option label="单次截图" value="single" />
            <el-option label="定时截图" value="interval" />
            <el-option label="批量截图" value="batch" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="截图间隔" v-if="taskConfig.type === 'interval'">
          <el-input-number v-model="taskConfig.interval" :min="0.1" :step="0.1" />
          <span class="unit">秒</span>
        </el-form-item>
        
        <el-form-item label="持续时间">
          <el-input-number v-model="taskConfig.duration" :min="1" />
          <span class="unit">秒</span>
        </el-form-item>
        
        <el-form-item label="保存路径">
          <el-input v-model="taskConfig.savePath" />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="startTask">开始截图</el-button>
          <el-button @click="stopTask">停止</el-button>
          <el-button @click="pauseTask">暂停</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  `,
  
  data() {
    return {
      taskConfig: {
        type: 'single',
        interval: 1.0,
        duration: 60,
        savePath: 'data/screenshots'
      }
    }
  },
  
  methods: {
    async startTask() {
      // 启动截图任务
    },
    
    async stopTask() {
      // 停止截图任务
    },
    
    async pauseTask() {
      // 暂停截图任务
    }
  }
}
```

#### 4.2.2 游戏窗口预览
```javascript
const GameWindowPreview = {
  template: `
    <el-card class="preview-card">
      <template #header>
        <span>游戏窗口预览</span>
        <el-button size="small" @click="refreshPreview">刷新</el-button>
      </template>
      
      <div class="preview-container">
        <canvas ref="previewCanvas" 
                @mousedown="startRegionSelect"
                @mousemove="updateRegionSelect"
                @mouseup="endRegionSelect">
        </canvas>
        
        <div v-if="selectedRegion" class="region-overlay">
          <div class="region-info">
            区域: {{selectedRegion.x}}, {{selectedRegion.y}} 
            尺寸: {{selectedRegion.width}} x {{selectedRegion.height}}
          </div>
        </div>
      </div>
    </el-card>
  `,
  
  data() {
    return {
      previewImage: null,
      selectedRegion: null,
      isSelecting: false
    }
  },
  
  methods: {
    async refreshPreview() {
      // 获取最新预览图像
    },
    
    startRegionSelect(event) {
      // 开始区域选择
    },
    
    updateRegionSelect(event) {
      // 更新区域选择
    },
    
    endRegionSelect(event) {
      // 结束区域选择
    }
  }
}
```

## 5. API接口设计

### 5.1 RESTful API

#### 5.1.1 任务管理接口
```python
# 创建截图任务
POST /api/v1/screenshot/tasks
{
  "task_type": "interval",
  "config": {
    "interval": 1.0,
    "duration": 60,
    "region": {"x": 0, "y": 0, "width": 800, "height": 600},
    "save_path": "data/screenshots",
    "quality": 95
  }
}

# 获取任务列表
GET /api/v1/screenshot/tasks

# 获取任务状态
GET /api/v1/screenshot/tasks/{task_id}

# 控制任务
POST /api/v1/screenshot/tasks/{task_id}/start
POST /api/v1/screenshot/tasks/{task_id}/stop
POST /api/v1/screenshot/tasks/{task_id}/pause

# 删除任务
DELETE /api/v1/screenshot/tasks/{task_id}
```

#### 5.1.2 截图管理接口
```python
# 获取预览图像
GET /api/v1/screenshot/preview

# 单次截图
POST /api/v1/screenshot/capture
{
  "region": {"x": 0, "y": 0, "width": 800, "height": 600},
  "save": true,
  "filename": "manual_capture.png"
}

# 获取截图历史
GET /api/v1/screenshot/history?page=1&size=20&date=2024-01-15

# 下载截图
GET /api/v1/screenshot/download/{filename}

# 删除截图
DELETE /api/v1/screenshot/{filename}
```

### 5.2 WebSocket事件

#### 5.2.1 客户端事件
```javascript
// 连接WebSocket
const ws = new WebSocket('ws://localhost:8000/ws/screenshot');

// 监听事件
ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  
  switch(data.type) {
    case 'task_status_update':
      updateTaskStatus(data.payload);
      break;
      
    case 'screenshot_captured':
      addToHistory(data.payload);
      break;
      
    case 'preview_update':
      updatePreview(data.payload);
      break;
      
    case 'error':
      showError(data.payload);
      break;
  }
};

// 发送事件
ws.send(JSON.stringify({
  type: 'subscribe_preview',
  payload: { interval: 2000 }
}));
```

#### 5.2.2 服务端事件
```python
class ScreenshotWebSocketHandler:
    async def handle_message(self, websocket, message):
        data = json.loads(message)
        
        if data['type'] == 'subscribe_preview':
            await self.start_preview_stream(websocket, data['payload'])
        elif data['type'] == 'subscribe_task_updates':
            await self.subscribe_task_updates(websocket)
        elif data['type'] == 'unsubscribe':
            await self.unsubscribe(websocket)
    
    async def broadcast_task_update(self, task_id: str, status: dict):
        message = {
            'type': 'task_status_update',
            'payload': {
                'task_id': task_id,
                'status': status,
                'timestamp': datetime.now().isoformat()
            }
        }
        await self.broadcast(message)
```

## 6. 配置管理

### 6.1 配置文件结构
```yaml
# config/screenshot_tool.yaml
screenshot_tool:
  # 基础设置
  enabled: true
  max_concurrent_tasks: 3
  default_save_path: "data/screenshots"
  
  # 截图设置
  capture:
    default_quality: 95
    default_format: "png"
    max_file_size_mb: 10
    enable_compression: true
  
  # 存储设置
  storage:
    auto_cleanup_days: 30
    max_storage_gb: 5
    backup_enabled: false
    
  # 性能设置
  performance:
    preview_update_interval: 2.0
    max_preview_fps: 10
    enable_gpu_acceleration: false
    
  # Web UI设置
  web_ui:
    enable_preview: true
    preview_quality: 70
    max_history_items: 100
```

### 6.2 运行时配置
```python
@dataclass
class ScreenshotToolConfig:
    enabled: bool = True
    max_concurrent_tasks: int = 3
    default_save_path: str = "data/screenshots"
    default_quality: int = 95
    default_format: str = "png"
    auto_cleanup_days: int = 30
    preview_update_interval: float = 2.0
    
    @classmethod
    def from_dict(cls, config_dict: dict) -> 'ScreenshotToolConfig':
        return cls(**config_dict)
    
    def to_dict(self) -> dict:
        return asdict(self)
```

## 7. 实施计划

### 7.1 开发阶段划分

#### 阶段1: 核心截图模块开发 (2-3天)
- 实现ScreenshotCollector核心类
- 集成现有ScreenCapture功能
- 实现基础的单次和批量截图功能
- 添加基础的存储管理

#### 阶段2: 任务管理系统 (2-3天)
- 实现CaptureTaskManager
- 添加任务调度和状态管理
- 实现定时截图和连续截图功能
- 添加任务持久化

#### 阶段3: Web API开发 (2-3天)
- 扩展FastAPI路由
- 实现RESTful API接口
- 添加WebSocket支持
- 集成现有ConfigManager和StateManager

#### 阶段4: 前端界面开发 (3-4天)
- 创建Vue.js组件
- 实现任务配置界面
- 添加预览和区域选择功能
- 实现历史记录管理

#### 阶段5: 集成测试和优化 (1-2天)
- 系统集成测试
- 性能优化
- 错误处理完善
- 文档完善

### 7.2 技术风险评估
- **风险1**: 截图性能影响主要功能
  - **缓解**: 独立线程执行，资源限制
- **风险2**: 大量截图文件存储管理
  - **缓解**: 自动清理机制，压缩存储
- **风险3**: Web UI实时性能
  - **缓解**: 限制预览帧率，优化传输

### 7.3 成功标准
- 能够稳定执行各种类型的截图任务
- Web UI响应流畅，实时性良好
- 不影响主要游戏自动化功能的性能
- 截图文件管理有序，元数据完整
- 用户界面直观易用，功能完备

---

**下一步**: 请确认此设计方案，我将开始第一阶段的开发实施。
