"""
数据表格组件
提供高级数据表格功能，包括排序、过滤、分页等
"""

import time
from typing import List, Dict, Any, Optional, Callable, Union
from enum import Enum
from dataclasses import dataclass, field

from ....utils.logger import get_logger
from ..elements.base_ui_element import BaseUIElement
from ..elements.button import Button
from ..elements.label import Label
from ..utils.performance_monitor import measure_block


class ColumnType(Enum):
    """列类型枚举"""
    TEXT = "text"                 # 文本
    NUMBER = "number"             # 数字
    DATE = "date"                 # 日期
    BOOLEAN = "boolean"           # 布尔值
    IMAGE = "image"               # 图片
    LINK = "link"                 # 链接
    BUTTON = "button"             # 按钮
    CUSTOM = "custom"             # 自定义


class SortDirection(Enum):
    """排序方向枚举"""
    ASC = "asc"                   # 升序
    DESC = "desc"                 # 降序


class FilterOperator(Enum):
    """过滤操作符枚举"""
    EQUALS = "equals"             # 等于
    NOT_EQUALS = "not_equals"     # 不等于
    CONTAINS = "contains"         # 包含
    NOT_CONTAINS = "not_contains" # 不包含
    STARTS_WITH = "starts_with"   # 开始于
    ENDS_WITH = "ends_with"       # 结束于
    GREATER_THAN = "gt"           # 大于
    LESS_THAN = "lt"              # 小于
    GREATER_EQUAL = "gte"         # 大于等于
    LESS_EQUAL = "lte"            # 小于等于
    IN = "in"                     # 在列表中
    NOT_IN = "not_in"             # 不在列表中


@dataclass
class TableColumn:
    """表格列定义"""
    id: str
    title: str
    field: str
    column_type: ColumnType = ColumnType.TEXT
    width: Optional[str] = None
    min_width: Optional[str] = None
    max_width: Optional[str] = None
    sortable: bool = True
    filterable: bool = True
    resizable: bool = True
    visible: bool = True
    frozen: bool = False  # 冻结列
    
    # 格式化
    formatter: Optional[Callable] = None
    cell_renderer: Optional[Callable] = None
    
    # 验证和编辑
    editable: bool = False
    validator: Optional[Callable] = None
    
    # 样式
    css_classes: List[str] = field(default_factory=list)
    header_css_classes: List[str] = field(default_factory=list)
    
    # 其他属性
    tooltip: str = ""
    help_text: str = ""


@dataclass
class TableRow:
    """表格行数据"""
    id: str
    data: Dict[str, Any]
    selected: bool = False
    expanded: bool = False
    css_classes: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class TableFilter:
    """表格过滤器"""
    column_id: str
    operator: FilterOperator
    value: Any
    case_sensitive: bool = False


@dataclass
class SortConfig:
    """排序配置"""
    column_id: str
    direction: SortDirection
    priority: int = 0  # 多列排序优先级


class DataTable(BaseUIElement):
    """数据表格组件"""
    
    def __init__(self, element_id: str, columns: List[TableColumn], **kwargs):
        super().__init__(element_id, **kwargs)
        self.logger = get_logger("DataTable")
        
        # 表格配置
        self.columns = columns
        self.column_map = {col.id: col for col in columns}
        
        # 数据
        self.rows: List[TableRow] = []
        self.filtered_rows: List[TableRow] = []
        self.displayed_rows: List[TableRow] = []
        
        # 状态
        self.selected_rows: List[str] = []
        self.expanded_rows: List[str] = []
        
        # 排序
        self.sort_configs: List[SortConfig] = []
        self.multi_sort = False
        
        # 过滤
        self.filters: List[TableFilter] = []
        self.global_filter = ""
        
        # 分页
        self.page_size = 20
        self.current_page = 1
        self.total_pages = 1
        self.total_rows = 0
        
        # 功能开关
        self.enable_selection = True
        self.enable_multi_selection = True
        self.enable_row_expansion = False
        self.enable_column_reorder = True
        self.enable_column_resize = True
        self.enable_virtual_scrolling = False
        
        # 事件回调
        self.on_row_select: Optional[Callable] = None
        self.on_row_double_click: Optional[Callable] = None
        self.on_cell_edit: Optional[Callable] = None
        self.on_sort_change: Optional[Callable] = None
        self.on_filter_change: Optional[Callable] = None
        self.on_page_change: Optional[Callable] = None
        
        # UI元素
        self.ui_elements: Dict[str, BaseUIElement] = {}
        
        self.logger.info(f"数据表格组件初始化: {element_id}")
    
    def set_data(self, data: List[Dict[str, Any]]):
        """设置表格数据"""
        try:
            with measure_block("table_data_setting"):
                self.rows.clear()
                
                for i, row_data in enumerate(data):
                    row = TableRow(
                        id=str(row_data.get('id', i)),
                        data=row_data
                    )
                    self.rows.append(row)
                
                self.total_rows = len(self.rows)
                self._apply_filters()
                self._apply_sorting()
                self._update_pagination()
                
                self.logger.info(f"设置表格数据，共{len(data)}行")
                
        except Exception as e:
            self.logger.error(f"设置表格数据失败: {e}")
    
    def add_row(self, row_data: Dict[str, Any], row_id: str = None) -> str:
        """添加行"""
        try:
            if row_id is None:
                row_id = str(len(self.rows))
            
            row = TableRow(id=row_id, data=row_data)
            self.rows.append(row)
            
            self.total_rows = len(self.rows)
            self._apply_filters()
            self._apply_sorting()
            self._update_pagination()
            
            return row_id
            
        except Exception as e:
            self.logger.error(f"添加行失败: {e}")
            return ""
    
    def update_row(self, row_id: str, row_data: Dict[str, Any]) -> bool:
        """更新行"""
        try:
            for row in self.rows:
                if row.id == row_id:
                    row.data.update(row_data)
                    self._apply_filters()
                    self._apply_sorting()
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"更新行失败: {e}")
            return False
    
    def remove_row(self, row_id: str) -> bool:
        """删除行"""
        try:
            for i, row in enumerate(self.rows):
                if row.id == row_id:
                    self.rows.pop(i)
                    
                    # 清理选择状态
                    if row_id in self.selected_rows:
                        self.selected_rows.remove(row_id)
                    if row_id in self.expanded_rows:
                        self.expanded_rows.remove(row_id)
                    
                    self.total_rows = len(self.rows)
                    self._apply_filters()
                    self._apply_sorting()
                    self._update_pagination()
                    
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"删除行失败: {e}")
            return False
    
    def add_column(self, column: TableColumn, index: int = None):
        """添加列"""
        if index is None:
            self.columns.append(column)
        else:
            self.columns.insert(index, column)
        
        self.column_map[column.id] = column
        self.logger.debug(f"添加列: {column.id}")
    
    def remove_column(self, column_id: str) -> bool:
        """删除列"""
        if column_id in self.column_map:
            column = self.column_map[column_id]
            self.columns.remove(column)
            del self.column_map[column_id]
            
            # 清理相关的排序和过滤
            self.sort_configs = [sc for sc in self.sort_configs if sc.column_id != column_id]
            self.filters = [f for f in self.filters if f.column_id != column_id]
            
            self.logger.debug(f"删除列: {column_id}")
            return True
        
        return False
    
    def set_column_visibility(self, column_id: str, visible: bool):
        """设置列可见性"""
        if column_id in self.column_map:
            self.column_map[column_id].visible = visible
    
    def sort_by_column(self, column_id: str, direction: SortDirection = SortDirection.ASC):
        """按列排序"""
        if column_id not in self.column_map:
            return
        
        if not self.multi_sort:
            self.sort_configs.clear()
        
        # 查找现有排序配置
        existing_config = None
        for config in self.sort_configs:
            if config.column_id == column_id:
                existing_config = config
                break
        
        if existing_config:
            existing_config.direction = direction
        else:
            config = SortConfig(
                column_id=column_id,
                direction=direction,
                priority=len(self.sort_configs)
            )
            self.sort_configs.append(config)
        
        self._apply_sorting()
        
        if self.on_sort_change:
            self.on_sort_change(self.sort_configs)
    
    def clear_sorting(self):
        """清除排序"""
        self.sort_configs.clear()
        self._apply_sorting()
    
    def add_filter(self, table_filter: TableFilter):
        """添加过滤器"""
        # 移除同列的现有过滤器
        self.filters = [f for f in self.filters if f.column_id != table_filter.column_id]
        self.filters.append(table_filter)
        
        self._apply_filters()
        
        if self.on_filter_change:
            self.on_filter_change(self.filters)
    
    def remove_filter(self, column_id: str):
        """移除过滤器"""
        self.filters = [f for f in self.filters if f.column_id != column_id]
        self._apply_filters()
    
    def clear_filters(self):
        """清除所有过滤器"""
        self.filters.clear()
        self.global_filter = ""
        self._apply_filters()
    
    def set_global_filter(self, filter_text: str):
        """设置全局过滤"""
        self.global_filter = filter_text
        self._apply_filters()
    
    def _apply_filters(self):
        """应用过滤器"""
        try:
            with measure_block("table_filtering"):
                self.filtered_rows = []
                
                for row in self.rows:
                    if self._row_matches_filters(row):
                        self.filtered_rows.append(row)
                
                self._update_pagination()
                
        except Exception as e:
            self.logger.error(f"应用过滤器失败: {e}")
    
    def _row_matches_filters(self, row: TableRow) -> bool:
        """检查行是否匹配过滤器"""
        # 检查列过滤器
        for table_filter in self.filters:
            if not self._row_matches_filter(row, table_filter):
                return False
        
        # 检查全局过滤器
        if self.global_filter:
            global_match = False
            filter_text = self.global_filter.lower()
            
            for column in self.columns:
                if not column.filterable:
                    continue
                
                cell_value = str(row.data.get(column.field, "")).lower()
                if filter_text in cell_value:
                    global_match = True
                    break
            
            if not global_match:
                return False
        
        return True
    
    def _row_matches_filter(self, row: TableRow, table_filter: TableFilter) -> bool:
        """检查行是否匹配单个过滤器"""
        cell_value = row.data.get(table_filter.column_id)
        filter_value = table_filter.value
        
        if cell_value is None:
            return table_filter.operator in [FilterOperator.NOT_EQUALS, FilterOperator.NOT_CONTAINS]
        
        # 字符串比较
        if isinstance(cell_value, str) and isinstance(filter_value, str):
            if not table_filter.case_sensitive:
                cell_value = cell_value.lower()
                filter_value = filter_value.lower()
        
        # 应用操作符
        if table_filter.operator == FilterOperator.EQUALS:
            return cell_value == filter_value
        elif table_filter.operator == FilterOperator.NOT_EQUALS:
            return cell_value != filter_value
        elif table_filter.operator == FilterOperator.CONTAINS:
            return str(filter_value) in str(cell_value)
        elif table_filter.operator == FilterOperator.NOT_CONTAINS:
            return str(filter_value) not in str(cell_value)
        elif table_filter.operator == FilterOperator.STARTS_WITH:
            return str(cell_value).startswith(str(filter_value))
        elif table_filter.operator == FilterOperator.ENDS_WITH:
            return str(cell_value).endswith(str(filter_value))
        elif table_filter.operator == FilterOperator.GREATER_THAN:
            try:
                return float(cell_value) > float(filter_value)
            except (ValueError, TypeError):
                return False
        elif table_filter.operator == FilterOperator.LESS_THAN:
            try:
                return float(cell_value) < float(filter_value)
            except (ValueError, TypeError):
                return False
        elif table_filter.operator == FilterOperator.GREATER_EQUAL:
            try:
                return float(cell_value) >= float(filter_value)
            except (ValueError, TypeError):
                return False
        elif table_filter.operator == FilterOperator.LESS_EQUAL:
            try:
                return float(cell_value) <= float(filter_value)
            except (ValueError, TypeError):
                return False
        elif table_filter.operator == FilterOperator.IN:
            return cell_value in filter_value if isinstance(filter_value, (list, tuple)) else False
        elif table_filter.operator == FilterOperator.NOT_IN:
            return cell_value not in filter_value if isinstance(filter_value, (list, tuple)) else True
        
        return False
    
    def _apply_sorting(self):
        """应用排序"""
        try:
            with measure_block("table_sorting"):
                if not self.sort_configs:
                    return
                
                # 按优先级排序配置
                sorted_configs = sorted(self.sort_configs, key=lambda x: x.priority)
                
                def sort_key(row: TableRow):
                    key_values = []
                    for config in sorted_configs:
                        value = row.data.get(config.column_id)
                        
                        # 处理None值
                        if value is None:
                            value = ""
                        
                        # 数字排序
                        if isinstance(value, (int, float)):
                            sort_value = value
                        else:
                            sort_value = str(value).lower()
                        
                        # 应用排序方向
                        if config.direction == SortDirection.DESC:
                            if isinstance(sort_value, str):
                                sort_value = sort_value  # 字符串降序通过reverse处理
                            else:
                                sort_value = -sort_value
                        
                        key_values.append(sort_value)
                    
                    return key_values
                
                # 执行排序
                reverse = any(config.direction == SortDirection.DESC for config in sorted_configs)
                self.filtered_rows.sort(key=sort_key, reverse=reverse)
                
                self._update_pagination()
                
        except Exception as e:
            self.logger.error(f"应用排序失败: {e}")
    
    def _update_pagination(self):
        """更新分页"""
        try:
            total_filtered = len(self.filtered_rows)
            self.total_pages = max(1, (total_filtered + self.page_size - 1) // self.page_size)
            
            # 调整当前页
            if self.current_page > self.total_pages:
                self.current_page = self.total_pages
            
            # 计算显示的行
            start_index = (self.current_page - 1) * self.page_size
            end_index = start_index + self.page_size
            self.displayed_rows = self.filtered_rows[start_index:end_index]
            
        except Exception as e:
            self.logger.error(f"更新分页失败: {e}")
    
    def set_page(self, page: int):
        """设置当前页"""
        if 1 <= page <= self.total_pages:
            self.current_page = page
            self._update_pagination()
            
            if self.on_page_change:
                self.on_page_change(page, self.total_pages)
    
    def set_page_size(self, size: int):
        """设置页面大小"""
        if size > 0:
            self.page_size = size
            self.current_page = 1
            self._update_pagination()
    
    def select_row(self, row_id: str, selected: bool = True):
        """选择/取消选择行"""
        if not self.enable_selection:
            return
        
        if selected:
            if not self.enable_multi_selection:
                self.selected_rows.clear()
            
            if row_id not in self.selected_rows:
                self.selected_rows.append(row_id)
        else:
            if row_id in self.selected_rows:
                self.selected_rows.remove(row_id)
        
        # 更新行状态
        for row in self.rows:
            if row.id == row_id:
                row.selected = selected
                break
        
        if self.on_row_select:
            self.on_row_select(row_id, selected, self.selected_rows)
    
    def select_all_rows(self, selected: bool = True):
        """全选/取消全选"""
        if not self.enable_selection or not self.enable_multi_selection:
            return
        
        if selected:
            self.selected_rows = [row.id for row in self.displayed_rows]
        else:
            self.selected_rows.clear()
        
        # 更新行状态
        for row in self.displayed_rows:
            row.selected = selected
    
    def expand_row(self, row_id: str, expanded: bool = True):
        """展开/折叠行"""
        if not self.enable_row_expansion:
            return
        
        if expanded:
            if row_id not in self.expanded_rows:
                self.expanded_rows.append(row_id)
        else:
            if row_id in self.expanded_rows:
                self.expanded_rows.remove(row_id)
        
        # 更新行状态
        for row in self.rows:
            if row.id == row_id:
                row.expanded = expanded
                break
    
    def get_selected_rows(self) -> List[TableRow]:
        """获取选中的行"""
        return [row for row in self.rows if row.id in self.selected_rows]
    
    def get_row_by_id(self, row_id: str) -> Optional[TableRow]:
        """根据ID获取行"""
        for row in self.rows:
            if row.id == row_id:
                return row
        return None
    
    def export_data(self, format_type: str = "json") -> Any:
        """导出数据"""
        try:
            if format_type == "json":
                return [row.data for row in self.filtered_rows]
            elif format_type == "csv":
                # 简化的CSV导出
                import csv
                import io
                
                output = io.StringIO()
                writer = csv.writer(output)
                
                # 写入表头
                headers = [col.title for col in self.columns if col.visible]
                writer.writerow(headers)
                
                # 写入数据
                for row in self.filtered_rows:
                    row_data = []
                    for col in self.columns:
                        if col.visible:
                            value = row.data.get(col.field, "")
                            row_data.append(str(value))
                    writer.writerow(row_data)
                
                return output.getvalue()
            
            return None
            
        except Exception as e:
            self.logger.error(f"导出数据失败: {e}")
            return None
    
    def get_table_stats(self) -> Dict[str, Any]:
        """获取表格统计信息"""
        return {
            "total_rows": self.total_rows,
            "filtered_rows": len(self.filtered_rows),
            "displayed_rows": len(self.displayed_rows),
            "selected_rows": len(self.selected_rows),
            "expanded_rows": len(self.expanded_rows),
            "total_columns": len(self.columns),
            "visible_columns": len([col for col in self.columns if col.visible]),
            "current_page": self.current_page,
            "total_pages": self.total_pages,
            "page_size": self.page_size,
            "active_filters": len(self.filters),
            "active_sorts": len(self.sort_configs)
        }
