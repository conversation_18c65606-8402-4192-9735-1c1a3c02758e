"""
调试枚举转换问题
模拟前端请求来测试API端点
"""

import requests
import json
import time


def test_api_with_debug():
    """测试API并查看调试日志"""
    print("🧪 测试API端点并查看调试日志...")
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(3)
    
    # 检查服务器是否运行
    try:
        health_response = requests.get("http://localhost:8000/health", timeout=5)
        if health_response.status_code != 200:
            print("❌ 服务器未运行，请先启动 gui.py")
            return False
        print("✅ 服务器运行正常")
    except:
        print("❌ 无法连接到服务器，请先启动 gui.py")
        return False
    
    # 模拟前端请求
    test_data = {
        "config": {
            "mode": "window",      # 字符串，不是枚举
            "format": "png",       # 字符串，不是枚举
            "quality": 90,
            "save_to_disk": True,
            "filename_prefix": "debug_test"
        }
    }
    
    print(f"📡 发送请求数据: {json.dumps(test_data, indent=2)}")
    
    try:
        response = requests.post(
            "http://localhost:8000/api/v1/screenshot/capture",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API调用成功!")
            print(f"📋 响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get("success"):
                print(f"🎉 截图执行成功: {result.get('filename')}")
                return True
            else:
                print(f"⚠️ 截图执行失败: {result.get('error_message')}")
                return False
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"📋 错误响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False


def test_with_enum_objects():
    """测试直接传递枚举对象（模拟可能的问题场景）"""
    print("\n🧪 测试传递枚举对象...")
    
    # 这个测试模拟如果前端意外传递了枚举对象会发生什么
    test_data = {
        "config": {
            "mode": "ScreenshotModeEnum.WINDOW",  # 模拟枚举字符串表示
            "format": "ScreenshotFormatEnum.PNG", # 模拟枚举字符串表示
            "quality": 90,
            "save_to_disk": True,
            "filename_prefix": "enum_object_test"
        }
    }
    
    print(f"📡 发送枚举对象测试数据: {json.dumps(test_data, indent=2)}")
    
    try:
        response = requests.post(
            "http://localhost:8000/api/v1/screenshot/capture",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 枚举对象测试API调用成功!")
            print(f"📋 响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ 枚举对象测试失败: {response.status_code}")
            print(f"📋 错误响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 枚举对象测试异常: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始枚举转换调试测试")
    print("=" * 60)
    print("📝 注意：请确保 gui.py 正在运行，并查看后端日志输出")
    print("=" * 60)
    
    # 执行测试
    tests = [
        ("正常字符串请求", test_api_with_debug),
        ("枚举对象请求", test_with_enum_objects)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 调试测试结果:")
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print("\n📝 请检查后端日志，查找以下关键信息:")
    print("   - 'API接收到的配置' - 查看接收到的数据类型")
    print("   - '转换后的配置' - 查看转换结果")
    print("   - '_convert_screenshot_config 接收到配置' - 查看内部转换")
    print("   - '最终转换的配置对象' - 查看最终配置")
    print("   - '截图捕获失败' - 查看是否仍有枚举类型错误")
    
    return True


if __name__ == "__main__":
    main()
