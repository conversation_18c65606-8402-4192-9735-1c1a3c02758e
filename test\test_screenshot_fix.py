"""
截图工具Pydantic验证错误修复测试
测试image_size字段的类型转换功能
"""

import sys
import os
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.web.models import ImageSize, ScreenshotRecord
from src.web.main import convert_screenshot_record_data


def test_imagesize_from_tuple():
    """测试从元组创建ImageSize"""
    print("🧪 测试ImageSize.from_tuple()...")
    
    try:
        # 测试正常情况
        size = ImageSize.from_tuple((1920, 1080))
        assert size.width == 1920
        assert size.height == 1080
        print("✅ 正常元组转换成功")
        
        # 测试异常情况 - 长度不对
        try:
            ImageSize.from_tuple((1920,))
            assert False, "应该抛出ValueError"
        except ValueError:
            print("✅ 长度不对的元组正确抛出异常")
        
        # 测试异常情况 - 类型不对
        try:
            ImageSize.from_tuple("invalid")
            assert False, "应该抛出ValueError"
        except ValueError:
            print("✅ 无效类型正确抛出异常")
            
        return True
        
    except Exception as e:
        print(f"❌ ImageSize.from_tuple()测试失败: {e}")
        return False


def test_imagesize_direct_creation():
    """测试直接创建ImageSize"""
    print("🧪 测试ImageSize直接创建...")
    
    try:
        # 测试正常创建
        size = ImageSize(width=1280, height=720)
        assert size.width == 1280
        assert size.height == 720
        print("✅ 直接创建ImageSize成功")
        
        # 测试负数值
        try:
            ImageSize(width=-100, height=720)
            assert False, "应该抛出验证错误"
        except Exception:
            print("✅ 负数值正确抛出验证错误")
            
        return True
        
    except Exception as e:
        print(f"❌ ImageSize直接创建测试失败: {e}")
        return False


def test_screenshot_record_conversion():
    """测试截图记录数据转换"""
    print("🧪 测试截图记录数据转换...")
    
    try:
        # 模拟原始数据（包含元组格式的image_size）
        raw_data = {
            "id": "test-123",
            "filename": "test.png",
            "filepath": "/path/to/test.png",
            "thumbnail_path": "/path/to/thumb.png",
            "timestamp": datetime.now(),
            "file_size": 1024000,
            "image_size": (1920, 1080),  # 元组格式
            "config": {"mode": "window", "format": "png"}
        }
        
        # 转换数据
        converted = convert_screenshot_record_data(raw_data)
        
        # 验证转换结果
        assert isinstance(converted['image_size'], ImageSize)
        assert converted['image_size'].width == 1920
        assert converted['image_size'].height == 1080
        print("✅ 元组格式image_size转换成功")
        
        # 测试创建ScreenshotRecord实例
        record = ScreenshotRecord(**converted)
        assert record.image_size.width == 1920
        assert record.image_size.height == 1080
        print("✅ ScreenshotRecord实例创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 截图记录转换测试失败: {e}")
        return False


def test_screenshot_record_with_dict_imagesize():
    """测试字典格式的image_size"""
    print("🧪 测试字典格式image_size转换...")
    
    try:
        raw_data = {
            "id": "test-456",
            "filename": "test2.png",
            "filepath": "/path/to/test2.png",
            "thumbnail_path": None,
            "timestamp": datetime.now(),
            "file_size": 512000,
            "image_size": {"width": 1280, "height": 720},  # 字典格式
            "config": {"mode": "region", "format": "jpg"}
        }
        
        converted = convert_screenshot_record_data(raw_data)
        record = ScreenshotRecord(**converted)
        
        assert record.image_size.width == 1280
        assert record.image_size.height == 720
        print("✅ 字典格式image_size转换成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 字典格式image_size测试失败: {e}")
        return False


def test_screenshot_record_with_invalid_imagesize():
    """测试无效的image_size处理"""
    print("🧪 测试无效image_size处理...")
    
    try:
        raw_data = {
            "id": "test-789",
            "filename": "test3.png",
            "filepath": "/path/to/test3.png",
            "thumbnail_path": None,
            "timestamp": datetime.now(),
            "file_size": 256000,
            "image_size": "invalid_data",  # 无效格式
            "config": {"mode": "window", "format": "png"}
        }
        
        converted = convert_screenshot_record_data(raw_data)
        record = ScreenshotRecord(**converted)
        
        # 应该设置为默认值
        assert record.image_size.width == 0
        assert record.image_size.height == 0
        print("✅ 无效image_size正确处理为默认值")
        
        return True
        
    except Exception as e:
        print(f"❌ 无效image_size处理测试失败: {e}")
        return False


def run_all_tests():
    """运行所有测试"""
    print("🚀 开始运行截图修复测试...")
    print("=" * 50)
    
    tests = [
        test_imagesize_from_tuple,
        test_imagesize_direct_creation,
        test_screenshot_record_conversion,
        test_screenshot_record_with_dict_imagesize,
        test_screenshot_record_with_invalid_imagesize
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ 测试 {test.__name__} 异常: {e}")
            failed += 1
        print("-" * 30)
    
    print("=" * 50)
    print(f"📊 测试结果: 通过 {passed}, 失败 {failed}")
    
    if failed == 0:
        print("🎉 所有测试通过！修复成功！")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
