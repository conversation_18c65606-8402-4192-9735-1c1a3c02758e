"""
感知模块集成测试
测试感知模块的实际功能，包括模板匹配、场景识别等
"""

import sys
import cv2
import numpy as np
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.modules.perception import PerceptionModule, TemplateMatcher, SceneRecognizer
from src.core.data_structures import GameScene
from src.utils.logger import setup_logger


def create_test_scene_image(scene_type: str) -> np.ndarray:
    """
    创建测试场景图像
    
    Args:
        scene_type: 场景类型 ('main_menu', 'produce_main', 'battle')
        
    Returns:
        测试图像
    """
    # 创建基础画布
    img = np.zeros((600, 800, 3), dtype=np.uint8)
    
    if scene_type == 'main_menu':
        # 模拟主菜单
        cv2.putText(img, 'MAIN MENU', (300, 100), cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 255), 3)
        cv2.rectangle(img, (300, 200), (500, 280), (0, 255, 0), 2)
        cv2.putText(img, 'PRODUCE', (325, 245), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
    elif scene_type == 'produce_main':
        # 模拟育成主界面
        cv2.putText(img, 'WEEK 1', (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)
        cv2.rectangle(img, (50, 80), (250, 110), (0, 255, 255), 2)
        cv2.rectangle(img, (55, 85), (200, 105), (0, 255, 255), -1)
        cv2.putText(img, 'STAMINA', (110, 98), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 0), 1)
        
    elif scene_type == 'battle':
        # 模拟战斗界面
        cv2.putText(img, 'SCORE: 5000', (300, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 0), 2)
        # 模拟手牌区域
        for i in range(5):
            x = 100 + i * 120
            cv2.rectangle(img, (x, 400), (x + 100, 550), (255, 255, 255), 2)
            cv2.putText(img, f'Card{i+1}', (x + 10, 480), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    return img


def test_template_matching():
    """测试模板匹配功能"""
    print("\n=== 测试模板匹配功能 ===")
    
    matcher = TemplateMatcher("assets/templates")
    
    # 创建包含模板的测试图像
    test_image = create_test_scene_image('main_menu')
    
    # 测试匹配主菜单logo
    result = matcher.match_template(test_image, "main_menu_logo", confidence_threshold=0.3)
    if result:
        print(f"✓ 成功匹配主菜单logo: 位置({result.x}, {result.y}), 置信度: {result.confidence:.3f}")
    else:
        print("✗ 未能匹配主菜单logo")
    
    # 测试匹配育成按钮
    result = matcher.match_template(test_image, "produce_button", confidence_threshold=0.3)
    if result:
        print(f"✓ 成功匹配育成按钮: 位置({result.x}, {result.y}), 置信度: {result.confidence:.3f}")
    else:
        print("✗ 未能匹配育成按钮")
    
    # 测试不存在的模板
    result = matcher.match_template(test_image, "nonexistent_template")
    if result is None:
        print("✓ 正确处理不存在的模板")
    else:
        print("✗ 不存在的模板返回了结果")


def test_scene_recognition():
    """测试场景识别功能"""
    print("\n=== 测试场景识别功能 ===")
    
    recognizer = SceneRecognizer("assets/templates")
    
    # 测试主菜单场景
    main_menu_image = create_test_scene_image('main_menu')
    scene = recognizer.recognize_scene(main_menu_image)
    print(f"主菜单场景识别结果: {scene.value}")
    
    # 测试育成主界面场景
    produce_image = create_test_scene_image('produce_main')
    scene = recognizer.recognize_scene(produce_image)
    print(f"育成主界面场景识别结果: {scene.value}")
    
    # 测试战斗界面场景
    battle_image = create_test_scene_image('battle')
    scene = recognizer.recognize_scene(battle_image)
    print(f"战斗界面场景识别结果: {scene.value}")
    
    # 测试空白图像
    blank_image = np.zeros((400, 400, 3), dtype=np.uint8)
    scene = recognizer.recognize_scene(blank_image)
    print(f"空白图像场景识别结果: {scene.value}")


def test_perception_module():
    """测试感知模块整体功能"""
    print("\n=== 测试感知模块整体功能 ===")
    
    try:
        # 注意：这里不会实际连接到游戏窗口，只是测试模块初始化
        perception = PerceptionModule(
            game_window_title="Test Game",
            templates_dir="assets/templates",
            language="ja"
        )
        
        print("✓ 感知模块初始化成功")
        
        # 测试使用提供的图像进行场景识别
        test_image = create_test_scene_image('main_menu')
        scene = perception.get_current_scene(test_image)
        print(f"✓ 场景识别功能正常: {scene.value}")
        
        # 测试模块信息获取
        info = perception.get_module_info()
        print(f"✓ 模块信息获取成功: 语言={info['language']}, 调试模式={info['debug_mode']}")
        
    except Exception as e:
        print(f"✗ 感知模块测试失败: {e}")


def test_template_files():
    """测试模板文件是否存在"""
    print("\n=== 测试模板文件 ===")
    
    templates_dir = Path("assets/templates")
    expected_templates = [
        "main_menu_logo.png",
        "produce_button.png", 
        "week_indicator.png",
        "stamina_bar.png"
    ]
    
    for template in expected_templates:
        template_path = templates_dir / template
        if template_path.exists():
            print(f"✓ 模板文件存在: {template}")
        else:
            print(f"✗ 模板文件缺失: {template}")


def test_image_processing():
    """测试图像处理功能"""
    print("\n=== 测试图像处理功能 ===")
    
    # 创建测试图像
    test_image = create_test_scene_image('main_menu')
    
    # 测试图像基本属性
    height, width, channels = test_image.shape
    print(f"✓ 测试图像尺寸: {width}x{height}, 通道数: {channels}")
    
    # 测试图像保存
    test_output_path = Path("logs/test_scene_main_menu.png")
    test_output_path.parent.mkdir(exist_ok=True)
    
    try:
        cv2.imwrite(str(test_output_path), test_image)
        if test_output_path.exists():
            print(f"✓ 测试图像保存成功: {test_output_path}")
        else:
            print("✗ 测试图像保存失败")
    except Exception as e:
        print(f"✗ 图像保存异常: {e}")


def main():
    """主测试函数"""
    print("开始感知模块集成测试...")
    
    # 设置日志
    setup_logger(log_level="INFO")
    
    # 运行各项测试
    test_template_files()
    test_image_processing()
    test_template_matching()
    test_scene_recognition()
    test_perception_module()
    
    print("\n=== 感知模块集成测试完成 ===")


if __name__ == "__main__":
    main()
