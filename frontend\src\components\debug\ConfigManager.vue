<template>
  <div class="config-manager">
    <div class="panel-header">
      <h2>配置管理</h2>
      <div class="header-actions">
        <el-button 
          type="primary" 
          :icon="Refresh"
          @click="loadConfig"
          :loading="loading.config"
          size="small"
        >
          重新加载
        </el-button>
        <el-button 
          type="success" 
          :icon="Check"
          @click="saveAllConfig"
          :loading="loading.save"
          size="small"
        >
          保存配置
        </el-button>
      </div>
    </div>

    <el-row :gutter="20">
      <!-- 左侧：配置分类 -->
      <el-col :span="6">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>配置分类</span>
            </div>
          </template>
          
          <el-menu
            :default-active="activeSection"
            class="config-menu"
            @select="handleSectionSelect"
          >
            <el-menu-item 
              v-for="section in configSections" 
              :key="section.key"
              :index="section.key"
            >
              <el-icon><component :is="section.icon" /></el-icon>
              <span>{{ section.name }}</span>
              <el-badge 
                v-if="getModifiedCount(section.key) > 0"
                :value="getModifiedCount(section.key)"
                class="config-badge"
              />
            </el-menu-item>
          </el-menu>
        </el-card>
      </el-col>

      <!-- 右侧：配置编辑 -->
      <el-col :span="18">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>{{ getCurrentSectionName() }}</span>
              <div class="section-actions">
                <el-button 
                  type="warning" 
                  :icon="RefreshLeft"
                  @click="resetSection"
                  size="small"
                >
                  重置
                </el-button>
                <el-button 
                  type="success" 
                  :icon="Check"
                  @click="saveSection"
                  size="small"
                >
                  保存
                </el-button>
              </div>
            </div>
          </template>

          <div class="config-content">
            <div v-if="!currentSectionConfig" class="no-config">
              <el-empty description="请选择配置分类" :image-size="80" />
            </div>

            <div v-else class="config-form">
              <!-- 系统配置 -->
              <div v-if="activeSection === 'system'" class="config-section">
                <el-form :model="currentSectionConfig" label-width="150px">
                  <el-form-item label="日志级别">
                    <el-select v-model="currentSectionConfig.log_level">
                      <el-option label="DEBUG" value="DEBUG" />
                      <el-option label="INFO" value="INFO" />
                      <el-option label="WARNING" value="WARNING" />
                      <el-option label="ERROR" value="ERROR" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="自动保存间隔(秒)">
                    <el-input-number 
                      v-model="currentSectionConfig.auto_save_interval"
                      :min="10"
                      :max="3600"
                    />
                  </el-form-item>
                  <el-form-item label="启用调试模式">
                    <el-switch v-model="currentSectionConfig.debug_mode" />
                  </el-form-item>
                </el-form>
              </div>

              <!-- 游戏配置 -->
              <div v-if="activeSection === 'game'" class="config-section">
                <el-form :model="currentSectionConfig" label-width="150px">
                  <el-form-item label="游戏窗口标题">
                    <el-input v-model="currentSectionConfig.window_title" />
                  </el-form-item>
                  <el-form-item label="操作延迟(毫秒)">
                    <el-input-number 
                      v-model="currentSectionConfig.action_delay"
                      :min="50"
                      :max="5000"
                    />
                  </el-form-item>
                  <el-form-item label="截图间隔(毫秒)">
                    <el-input-number 
                      v-model="currentSectionConfig.screenshot_interval"
                      :min="100"
                      :max="10000"
                    />
                  </el-form-item>
                  <el-form-item label="启用自动重试">
                    <el-switch v-model="currentSectionConfig.auto_retry" />
                  </el-form-item>
                </el-form>
              </div>

              <!-- AI配置 -->
              <div v-if="activeSection === 'ai'" class="config-section">
                <el-form :model="currentSectionConfig" label-width="150px">
                  <el-form-item label="MCTS迭代次数">
                    <el-input-number 
                      v-model="currentSectionConfig.mcts_iterations"
                      :min="100"
                      :max="10000"
                    />
                  </el-form-item>
                  <el-form-item label="MCTS超时(秒)">
                    <el-input-number 
                      v-model="currentSectionConfig.mcts_timeout"
                      :min="1"
                      :max="60"
                    />
                  </el-form-item>
                  <el-form-item label="启发式权重">
                    <el-slider 
                      v-model="currentSectionConfig.heuristic_weight"
                      :min="0"
                      :max="1"
                      :step="0.1"
                      show-input
                    />
                  </el-form-item>
                  <el-form-item label="探索系数">
                    <el-slider 
                      v-model="currentSectionConfig.exploration_factor"
                      :min="0"
                      :max="2"
                      :step="0.1"
                      show-input
                    />
                  </el-form-item>
                </el-form>
              </div>

              <!-- UI配置 -->
              <div v-if="activeSection === 'ui'" class="config-section">
                <el-form :model="currentSectionConfig" label-width="150px">
                  <el-form-item label="主题">
                    <el-select v-model="currentSectionConfig.theme">
                      <el-option label="浅色主题" value="light" />
                      <el-option label="深色主题" value="dark" />
                      <el-option label="自动" value="auto" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="语言">
                    <el-select v-model="currentSectionConfig.language">
                      <el-option label="中文" value="zh-CN" />
                      <el-option label="English" value="en-US" />
                      <el-option label="日本語" value="ja-JP" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="显示动画">
                    <el-switch v-model="currentSectionConfig.animations" />
                  </el-form-item>
                  <el-form-item label="自动刷新间隔(秒)">
                    <el-input-number 
                      v-model="currentSectionConfig.refresh_interval"
                      :min="1"
                      :max="60"
                    />
                  </el-form-item>
                </el-form>
              </div>

              <!-- JSON编辑器 -->
              <div v-if="activeSection === 'advanced'" class="config-section">
                <div class="json-editor">
                  <el-alert
                    title="高级配置"
                    description="直接编辑JSON配置，请谨慎操作"
                    type="warning"
                    :closable="false"
                    show-icon
                  />
                  <el-input
                    v-model="jsonConfig"
                    type="textarea"
                    :rows="20"
                    placeholder="JSON配置内容"
                    class="json-textarea"
                  />
                  <div class="json-actions">
                    <el-button @click="formatJson">格式化</el-button>
                    <el-button @click="validateJson">验证</el-button>
                    <el-button type="primary" @click="applyJsonConfig">应用</el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 配置预设对话框 -->
    <el-dialog
      v-model="showPresetDialog"
      title="配置预设"
      width="50%"
    >
      <div class="preset-content">
        <el-row :gutter="20">
          <el-col :span="12" v-for="preset in configPresets" :key="preset.key">
            <el-card class="preset-card" @click="applyPreset(preset)">
              <div class="preset-info">
                <h4>{{ preset.name }}</h4>
                <p>{{ preset.description }}</p>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { 
  Refresh, Check, RefreshLeft, Setting, 
  Monitor, BrainIcon, View, Tools
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useDebugConsole } from '../../composables/useDebugConsole.js'

// 使用调试控制台功能
const { debugState, loading, getConfig, updateConfig } = useDebugConsole()

// 响应式数据
const activeSection = ref('system')
const showPresetDialog = ref(false)
const jsonConfig = ref('')
const modifiedSections = ref(new Set())

// 配置分类
const configSections = ref([
  { key: 'system', name: '系统配置', icon: 'Setting' },
  { key: 'game', name: '游戏配置', icon: 'Monitor' },
  { key: 'ai', name: 'AI配置', icon: 'BrainIcon' },
  { key: 'ui', name: 'UI配置', icon: 'View' },
  { key: 'advanced', name: '高级配置', icon: 'Tools' }
])

// 配置预设
const configPresets = ref([
  {
    key: 'performance',
    name: '性能优先',
    description: '优化性能，降低资源消耗',
    config: {
      system: { log_level: 'WARNING', debug_mode: false },
      game: { action_delay: 100, screenshot_interval: 500 },
      ai: { mcts_iterations: 500, mcts_timeout: 5 }
    }
  },
  {
    key: 'accuracy',
    name: '精度优先',
    description: '提高决策精度，增加计算时间',
    config: {
      system: { log_level: 'INFO', debug_mode: true },
      game: { action_delay: 200, screenshot_interval: 200 },
      ai: { mcts_iterations: 2000, mcts_timeout: 15 }
    }
  }
])

// 默认配置
const defaultConfig = ref({
  system: {
    log_level: 'INFO',
    auto_save_interval: 300,
    debug_mode: false
  },
  game: {
    window_title: '学园偶像大师',
    action_delay: 150,
    screenshot_interval: 300,
    auto_retry: true
  },
  ai: {
    mcts_iterations: 1000,
    mcts_timeout: 10,
    heuristic_weight: 0.7,
    exploration_factor: 1.4
  },
  ui: {
    theme: 'light',
    language: 'zh-CN',
    animations: true,
    refresh_interval: 5
  }
})

// 计算属性
const currentSectionConfig = computed(() => {
  if (!activeSection.value) return null
  return debugState.config[activeSection.value] || defaultConfig.value[activeSection.value]
})

// 监听配置变化
watch(
  () => currentSectionConfig.value,
  () => {
    if (activeSection.value) {
      modifiedSections.value.add(activeSection.value)
    }
  },
  { deep: true }
)

// 方法
const handleSectionSelect = (key) => {
  activeSection.value = key
  if (key === 'advanced') {
    jsonConfig.value = JSON.stringify(debugState.config, null, 2)
  }
}

const getCurrentSectionName = () => {
  const section = configSections.value.find(s => s.key === activeSection.value)
  return section ? section.name : '未知配置'
}

const getModifiedCount = (sectionKey) => {
  return modifiedSections.value.has(sectionKey) ? 1 : 0
}

const loadConfig = async () => {
  try {
    await getConfig()
    modifiedSections.value.clear()
    ElMessage.success('配置加载成功')
  } catch (error) {
    ElMessage.error('配置加载失败')
  }
}

const saveSection = async () => {
  if (!activeSection.value || !currentSectionConfig.value) return
  
  try {
    for (const [key, value] of Object.entries(currentSectionConfig.value)) {
      await updateConfig(activeSection.value, key, value)
    }
    
    modifiedSections.value.delete(activeSection.value)
    ElMessage.success(`${getCurrentSectionName()}保存成功`)
  } catch (error) {
    ElMessage.error('配置保存失败')
  }
}

const saveAllConfig = async () => {
  try {
    for (const sectionKey of modifiedSections.value) {
      const sectionConfig = debugState.config[sectionKey]
      if (sectionConfig) {
        for (const [key, value] of Object.entries(sectionConfig)) {
          await updateConfig(sectionKey, key, value)
        }
      }
    }
    
    modifiedSections.value.clear()
    ElMessage.success('所有配置保存成功')
  } catch (error) {
    ElMessage.error('配置保存失败')
  }
}

const resetSection = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要重置${getCurrentSectionName()}吗？`,
      '确认重置',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    if (activeSection.value && defaultConfig.value[activeSection.value]) {
      debugState.config[activeSection.value] = { ...defaultConfig.value[activeSection.value] }
      modifiedSections.value.add(activeSection.value)
      ElMessage.success(`${getCurrentSectionName()}已重置`)
    }
  } catch {
    // 用户取消
  }
}

const formatJson = () => {
  try {
    const parsed = JSON.parse(jsonConfig.value)
    jsonConfig.value = JSON.stringify(parsed, null, 2)
    ElMessage.success('JSON格式化成功')
  } catch (error) {
    ElMessage.error('JSON格式错误')
  }
}

const validateJson = () => {
  try {
    JSON.parse(jsonConfig.value)
    ElMessage.success('JSON格式正确')
  } catch (error) {
    ElMessage.error('JSON格式错误: ' + error.message)
  }
}

const applyJsonConfig = () => {
  try {
    const parsed = JSON.parse(jsonConfig.value)
    debugState.config = parsed
    modifiedSections.value = new Set(Object.keys(parsed))
    ElMessage.success('JSON配置已应用')
  } catch (error) {
    ElMessage.error('JSON格式错误，无法应用')
  }
}

const applyPreset = (preset) => {
  try {
    Object.assign(debugState.config, preset.config)
    modifiedSections.value = new Set(Object.keys(preset.config))
    showPresetDialog.value = false
    ElMessage.success(`预设"${preset.name}"已应用`)
  } catch (error) {
    ElMessage.error('预设应用失败')
  }
}

// 生命周期
onMounted(async () => {
  // 初始化配置
  if (Object.keys(debugState.config).length === 0) {
    debugState.config = { ...defaultConfig.value }
  }
  
  // 尝试加载远程配置
  try {
    await getConfig()
  } catch (error) {
    console.warn('无法加载远程配置，使用默认配置')
  }
})
</script>

<style scoped>
.config-manager {
  height: 100%;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e6e6e6;
}

.panel-header h2 {
  margin: 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-actions {
  display: flex;
  gap: 8px;
}

.config-menu {
  border-right: none;
}

.config-menu .el-menu-item {
  position: relative;
}

.config-badge {
  position: absolute;
  top: 8px;
  right: 8px;
}

.config-content {
  min-height: 500px;
}

.no-config {
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.config-form {
  padding: 20px 0;
}

.config-section {
  max-height: 500px;
  overflow-y: auto;
}

.json-editor {
  padding: 20px 0;
}

.json-textarea {
  margin: 15px 0;
  font-family: 'Courier New', monospace;
}

.json-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.preset-content {
  padding: 20px 0;
}

.preset-card {
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 15px;
}

.preset-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.preset-info h4 {
  margin: 0 0 8px 0;
  color: #409eff;
  font-size: 16px;
}

.preset-info p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}
</style>
