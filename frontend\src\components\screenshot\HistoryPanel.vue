<template>
  <div class="history-panel">
    <el-card header="截图历史" class="history-card">
      <template #header>
        <div class="history-header">
          <span>截图历史</span>
          <div class="history-controls">
            <el-button-group size="small">
              <el-button @click="$emit('refresh')" :loading="loading">
                <el-icon><Refresh /></el-icon>
              </el-button>
              <el-button 
                @click="toggleBatchMode" 
                :type="batchMode ? 'primary' : 'default'"
              >
                <el-icon><Operation /></el-icon>
                {{ batchMode ? '取消' : '批量' }}
              </el-button>
              <el-button @click="clearAll" type="danger">
                <el-icon><Delete /></el-icon>
                清空
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>

      <!-- 搜索和筛选 -->
      <div class="search-section">
        <el-input 
          v-model="searchText" 
          placeholder="搜索截图..."
          clearable
          size="small"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <el-select 
          v-model="filterFormat" 
          placeholder="格式筛选" 
          clearable
          size="small"
          style="width: 100px; margin-top: 8px;"
        >
          <el-option label="PNG" value="png" />
          <el-option label="JPEG" value="jpeg" />
          <el-option label="BMP" value="bmp" />
        </el-select>
      </div>

      <!-- 批量操作栏 -->
      <div v-if="batchMode" class="batch-actions">
        <el-checkbox 
          v-model="selectAll" 
          @change="handleSelectAll"
          :indeterminate="isIndeterminate"
        >
          全选 ({{ selectedItems.length }})
        </el-checkbox>
        <el-button 
          type="danger" 
          size="small"
          :disabled="selectedItems.length === 0"
          @click="handleBatchDelete"
        >
          删除选中
        </el-button>
      </div>

      <!-- 历史记录列表 -->
      <div class="history-list" v-loading="loading">
        <div 
          v-for="item in filteredHistory" 
          :key="item.id"
          class="history-item"
          :class="{ 'selected': selectedItems.includes(item.id) }"
          @click="handleItemClick(item)"
        >
          <!-- 批量选择复选框 -->
          <el-checkbox 
            v-if="batchMode"
            :model-value="selectedItems.includes(item.id)"
            @change="handleItemSelect(item.id, $event)"
            @click.stop
            class="item-checkbox"
          />
          
          <!-- 缩略图 -->
          <div class="item-thumbnail">
            <img 
              :src="getThumbnailUrl(item)" 
              :alt="item.filename"
              @error="handleImageError"
            />
            <div class="thumbnail-overlay">
              <el-icon><ZoomIn /></el-icon>
            </div>
          </div>
          
          <!-- 文件信息 -->
          <div class="item-info">
            <div class="item-name" :title="item.filename">
              {{ item.filename }}
            </div>
            <div class="item-meta">
              <span class="meta-size">{{ formatFileSize(item.file_size) }}</span>
              <span class="meta-time">{{ formatTime(item.timestamp) }}</span>
              <span class="meta-dimensions">{{ item.image_size.width }}×{{ item.image_size.height }}</span>
            </div>
            <div class="item-config">
              <el-tag size="small" type="info">{{ item.config.mode }}</el-tag>
              <el-tag size="small" type="success">{{ item.config.format }}</el-tag>
            </div>
          </div>
          
          <!-- 操作按钮 -->
          <div class="item-actions" v-if="!batchMode">
            <el-button-group size="small">
              <el-button @click.stop="handlePreview(item)">
                <el-icon><View /></el-icon>
              </el-button>
              <el-button @click.stop="$emit('download', item.id)">
                <el-icon><Download /></el-icon>
              </el-button>
              <el-button @click.stop="$emit('delete', item.id)" type="danger">
                <el-icon><Delete /></el-icon>
              </el-button>
            </el-button-group>
          </div>
        </div>
        
        <!-- 空状态 -->
        <div v-if="filteredHistory.length === 0 && !loading" class="empty-state">
          <el-empty description="暂无截图记录" />
        </div>
      </div>
    </el-card>

    <!-- 预览对话框 -->
    <el-dialog
      v-model="showPreview"
      :title="previewItem?.filename"
      width="80%"
      center
    >
      <div class="preview-dialog">
        <img 
          :src="getFullImageUrl(previewItem)" 
          :alt="previewItem?.filename"
          class="preview-image"
        />
        <div class="preview-info">
          <div class="info-row">
            <span class="info-label">文件大小:</span>
            <span>{{ formatFileSize(previewItem?.file_size || 0) }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">图像尺寸:</span>
            <span>{{ previewItem?.image_size.width }}×{{ previewItem?.image_size.height }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">创建时间:</span>
            <span>{{ formatTime(previewItem?.timestamp) }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">截图模式:</span>
            <span>{{ previewItem?.config.mode }}</span>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { 
  Refresh, 
  Operation, 
  Delete, 
  Search, 
  ZoomIn, 
  View, 
  Download 
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  history: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits([
  'delete',
  'download',
  'refresh',
  'batch-delete'
])

// 响应式数据
const searchText = ref('')
const filterFormat = ref('')
const batchMode = ref(false)
const selectedItems = ref([])
const showPreview = ref(false)
const previewItem = ref(null)

// 计算属性
const filteredHistory = computed(() => {
  let filtered = props.history

  // 搜索过滤
  if (searchText.value) {
    const search = searchText.value.toLowerCase()
    filtered = filtered.filter(item => 
      item.filename.toLowerCase().includes(search)
    )
  }

  // 格式过滤
  if (filterFormat.value) {
    filtered = filtered.filter(item => 
      item.config.format === filterFormat.value
    )
  }

  // 按时间倒序排列
  return filtered.sort((a, b) => 
    new Date(b.timestamp) - new Date(a.timestamp)
  )
})

const selectAll = computed({
  get() {
    return filteredHistory.value.length > 0 && 
           selectedItems.value.length === filteredHistory.value.length
  },
  set(value) {
    if (value) {
      selectedItems.value = filteredHistory.value.map(item => item.id)
    } else {
      selectedItems.value = []
    }
  }
})

const isIndeterminate = computed(() => {
  return selectedItems.value.length > 0 && 
         selectedItems.value.length < filteredHistory.value.length
})

// 监听历史记录变化，清理无效选择
watch(() => props.history, () => {
  const validIds = props.history.map(item => item.id)
  selectedItems.value = selectedItems.value.filter(id => validIds.includes(id))
})

// 方法
function toggleBatchMode() {
  batchMode.value = !batchMode.value
  if (!batchMode.value) {
    selectedItems.value = []
  }
}

function handleSelectAll(value) {
  selectAll.value = value
}

function handleItemSelect(itemId, selected) {
  if (selected) {
    if (!selectedItems.value.includes(itemId)) {
      selectedItems.value.push(itemId)
    }
  } else {
    const index = selectedItems.value.indexOf(itemId)
    if (index > -1) {
      selectedItems.value.splice(index, 1)
    }
  }
}

function handleItemClick(item) {
  if (batchMode.value) {
    const isSelected = selectedItems.value.includes(item.id)
    handleItemSelect(item.id, !isSelected)
  } else {
    handlePreview(item)
  }
}

function handlePreview(item) {
  previewItem.value = item
  showPreview.value = true
}

async function handleBatchDelete() {
  if (selectedItems.value.length === 0) return

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedItems.value.length} 张截图吗？`,
      '批量删除',
      { type: 'warning' }
    )

    emit('batch-delete', [...selectedItems.value])
    selectedItems.value = []

  } catch (error) {
    // 用户取消
  }
}

async function clearAll() {
  if (props.history.length === 0) return

  try {
    await ElMessageBox.confirm(
      `确定要清空所有 ${props.history.length} 张截图吗？此操作不可恢复！`,
      '清空历史',
      { type: 'warning' }
    )

    const allIds = props.history.map(item => item.id)
    emit('batch-delete', allIds)

  } catch (error) {
    // 用户取消
  }
}

function getThumbnailUrl(item) {
  if (item.thumbnail_path) {
    // 更健壮的路径处理
    let filename = item.thumbnail_path

    // 处理完整路径，提取文件名
    if (filename.includes('/') || filename.includes('\\')) {
      filename = filename.split(/[/\\]/).pop()
    }

    // 确保文件名有效
    if (filename && filename.trim()) {
      const url = `/screenshots/thumbnails/${filename}`
      console.debug('缩略图URL:', url, '原始路径:', item.thumbnail_path)
      return url
    }
  }

  // 回退到原图
  console.debug('缩略图不可用，使用原图:', item.filename)
  return getFullImageUrl(item)
}

function getFullImageUrl(item) {
  if (!item || !item.filename) {
    console.warn('无效的图片项:', item)
    return ''
  }

  // 确保文件名有效
  let filename = item.filename
  if (filename.includes('/') || filename.includes('\\')) {
    filename = filename.split(/[/\\]/).pop()
  }

  const url = `/screenshots/${filename}`
  console.debug('原图URL:', url, '原始文件名:', item.filename)
  return url
}

function handleImageError(event) {
  const originalSrc = event.target.src
  console.warn('图片加载失败:', originalSrc)

  // 如果是缩略图加载失败，尝试加载原图
  if (originalSrc.includes('/thumbnails/')) {
    const filename = originalSrc.split('/').pop()
    const fallbackUrl = `/screenshots/${filename.replace('_thumb', '')}`
    console.debug('尝试回退到原图:', fallbackUrl)

    // 避免无限循环
    if (event.target.dataset.fallbackAttempted !== 'true') {
      event.target.dataset.fallbackAttempted = 'true'
      event.target.src = fallbackUrl
      return
    }
  }

  // 设置默认占位图
  event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yMCAyMEg0NFY0NEgyMFYyMFoiIHN0cm9rZT0iI0NDQ0NDQyIgc3Ryb2tlLXdpZHRoPSIyIi8+CjxwYXRoIGQ9Ik0yOCAzMkwzMiAyOEwzNiAzMkw0MCAyOEw0NCAzMlY0MEgyOFYzMloiIGZpbGw9IiNDQ0NDQ0MiLz4KPC9zdmc+'

  // 标记为加载失败
  event.target.classList.add('image-load-error')
}

function formatFileSize(bytes) {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

function formatTime(timestamp) {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date
  
  if (diff < 60000) { // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) { // 1天内
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString()
  }
}
</script>

<style scoped>
.history-panel {
  height: 100%;
}

.history-card {
  height: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.history-card :deep(.el-card__body) {
  height: calc(100% - 60px);
  padding: 16px;
  display: flex;
  flex-direction: column;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.search-section {
  margin-bottom: 16px;
}

.batch-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f0f8ff;
  border: 1px solid #409eff;
  border-radius: 6px;
  margin-bottom: 16px;
}

.history-list {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.history-item:hover {
  border-color: #409eff;
  background: #f0f8ff;
}

.history-item.selected {
  border-color: #409eff;
  background: #e6f3ff;
}

.item-checkbox {
  flex-shrink: 0;
}

.item-thumbnail {
  position: relative;
  width: 60px;
  height: 45px;
  flex-shrink: 0;
  border-radius: 6px;
  overflow: hidden;
  background: #f5f5f5;
}

.item-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease;
}

.item-thumbnail img.image-load-error {
  opacity: 0.6;
  filter: grayscale(100%);
}

.thumbnail-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.item-thumbnail:hover .thumbnail-overlay {
  opacity: 1;
}

.thumbnail-overlay .el-icon {
  color: white;
  font-size: 18px;
}

.item-info {
  flex: 1;
  min-width: 0;
}

.item-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-meta {
  display: flex;
  gap: 8px;
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.item-config {
  display: flex;
  gap: 4px;
}

.item-actions {
  flex-shrink: 0;
}

.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-dialog {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.preview-image {
  max-width: 100%;
  max-height: 60vh;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.preview-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  width: 100%;
  max-width: 400px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-label {
  font-weight: 600;
  color: #606266;
}
</style>
