"""
基础功能测试脚本
验证截图工具的核心功能是否正常
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试模块导入"""
    print("🧪 测试模块导入...")
    
    try:
        # 测试核心模块导入
        from src.modules.screenshot_collector import ScreenshotCollector, ScreenshotConfig
        from src.web.main import api_adapter
        from src.web.models import ScreenshotResult, ScreenshotRecord
        
        print("✅ 所有模块导入成功")
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 导入时发生错误: {e}")
        return False


def test_screenshot_collector_creation():
    """测试截图收集器创建"""
    print("\n🧪 测试截图收集器创建...")
    
    try:
        from src.modules.screenshot_collector import ScreenshotCollector
        
        collector = ScreenshotCollector()
        
        # 验证基本属性
        assert hasattr(collector, 'screen_capture'), "缺少screen_capture属性"
        assert hasattr(collector, 'storage_manager'), "缺少storage_manager属性"
        assert hasattr(collector, 'history'), "缺少history属性"
        
        print("✅ 截图收集器创建成功")
        return True
        
    except Exception as e:
        print(f"❌ 截图收集器创建失败: {e}")
        return False


def test_config_creation():
    """测试配置创建"""
    print("\n🧪 测试配置创建...")
    
    try:
        from src.modules.screenshot_collector import ScreenshotConfig, ScreenshotMode, ScreenshotFormat
        
        # 测试默认配置
        config1 = ScreenshotConfig()
        assert config1.mode == ScreenshotMode.WINDOW, "默认模式错误"
        assert config1.format == ScreenshotFormat.PNG, "默认格式错误"
        
        # 测试自定义配置
        config2 = ScreenshotConfig(
            mode=ScreenshotMode.FULLSCREEN,
            format=ScreenshotFormat.JPEG,
            quality=80
        )
        assert config2.mode == ScreenshotMode.FULLSCREEN, "自定义模式错误"
        assert config2.quality == 80, "自定义质量错误"
        
        print("✅ 配置创建测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置创建测试失败: {e}")
        return False


def test_api_adapter_initialization():
    """测试API适配器初始化"""
    print("\n🧪 测试API适配器初始化...")
    
    try:
        from src.web.main import api_adapter
        
        # 检查基本属性
        assert hasattr(api_adapter, 'initialize_core_modules'), "缺少初始化方法"
        assert hasattr(api_adapter, 'capture_screenshot'), "缺少截图方法"
        assert hasattr(api_adapter, 'get_screenshot_history'), "缺少历史记录方法"
        
        print("✅ API适配器结构正常")
        return True
        
    except Exception as e:
        print(f"❌ API适配器测试失败: {e}")
        return False


def test_data_models():
    """测试数据模型"""
    print("\n🧪 测试数据模型...")
    
    try:
        from src.web.models import (
            ScreenshotConfig as WebScreenshotConfig,
            ScreenshotResult as WebScreenshotResult,
            ScreenshotModeEnum,
            ScreenshotFormatEnum
        )
        from datetime import datetime
        
        # 测试Web配置模型
        web_config = WebScreenshotConfig(
            mode=ScreenshotModeEnum.WINDOW,
            format=ScreenshotFormatEnum.PNG,
            quality=90
        )
        assert web_config.mode == ScreenshotModeEnum.WINDOW, "Web配置模式错误"
        
        # 测试序列化
        config_dict = web_config.dict()
        assert 'mode' in config_dict, "序列化缺少mode字段"
        assert 'format' in config_dict, "序列化缺少format字段"
        
        print("✅ 数据模型测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 数据模型测试失败: {e}")
        return False


def test_file_structure():
    """测试文件结构"""
    print("\n🧪 测试文件结构...")
    
    try:
        # 检查关键文件是否存在
        key_files = [
            "src/modules/screenshot_collector.py",
            "src/web/main.py",
            "src/web/models.py",
            "frontend/src/views/ScreenshotTool.vue",
            "frontend/src/components/screenshot/ControlPanel.vue",
            "frontend/src/components/screenshot/PreviewArea.vue",
            "frontend/src/components/screenshot/HistoryPanel.vue",
            "frontend/src/composables/useScreenshotApi.js",
            "frontend/src/composables/useWebSocket.js"
        ]
        
        missing_files = []
        for file_path in key_files:
            full_path = project_root / file_path
            if not full_path.exists():
                missing_files.append(file_path)
        
        if missing_files:
            print(f"❌ 缺少文件: {missing_files}")
            return False
        
        print("✅ 所有关键文件存在")
        return True
        
    except Exception as e:
        print(f"❌ 文件结构检查失败: {e}")
        return False


def test_directory_structure():
    """测试目录结构"""
    print("\n🧪 测试目录结构...")
    
    try:
        # 检查关键目录
        key_dirs = [
            "src/modules",
            "src/web",
            "frontend/src/views",
            "frontend/src/components/screenshot",
            "frontend/src/composables",
            "test"
        ]
        
        missing_dirs = []
        for dir_path in key_dirs:
            full_path = project_root / dir_path
            if not full_path.exists() or not full_path.is_dir():
                missing_dirs.append(dir_path)
        
        if missing_dirs:
            print(f"❌ 缺少目录: {missing_dirs}")
            return False
        
        print("✅ 目录结构正常")
        return True
        
    except Exception as e:
        print(f"❌ 目录结构检查失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始基础功能测试")
    print("=" * 50)
    
    # 测试项目列表
    tests = [
        ("模块导入", test_imports),
        ("截图收集器创建", test_screenshot_collector_creation),
        ("配置创建", test_config_creation),
        ("API适配器", test_api_adapter_initialization),
        ("数据模型", test_data_models),
        ("文件结构", test_file_structure),
        ("目录结构", test_directory_structure)
    ]
    
    # 执行测试
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📊 基础功能测试结果:")
    
    passed_count = 0
    total_count = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed_count += 1
    
    print(f"\n总体结果: {passed_count}/{total_count} 通过 ({passed_count/total_count*100:.1f}%)")
    
    if passed_count == total_count:
        print("🎉 所有基础功能测试通过！")
        return True
    else:
        print("⚠️ 部分基础功能测试失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
