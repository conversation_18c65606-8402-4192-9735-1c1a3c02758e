"""
测试核心数据结构
"""

import pytest
from datetime import datetime
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.core.data_structures import (
    Card, CardEffect, GameState, Action, UserStrategy,
    ProduceGoal, TeamComposition, BehaviorConfig,
    ActionType, CardType, CardRarity, GameScene
)


class TestCardEffect:
    """测试CardEffect类"""
    
    def test_card_effect_creation(self):
        """测试CardEffect创建"""
        effect = CardEffect(
            type="add_score",
            value=100,
            duration=2,
            condition="vocal_focused"
        )
        
        assert effect.type == "add_score"
        assert effect.value == 100
        assert effect.duration == 2
        assert effect.condition == "vocal_focused"
    
    def test_card_effect_optional_fields(self):
        """测试CardEffect可选字段"""
        effect = CardEffect(type="add_vigor", value=10)
        
        assert effect.type == "add_vigor"
        assert effect.value == 10
        assert effect.duration is None
        assert effect.condition is None


class TestCard:
    """测试Card类"""
    
    def test_card_creation(self):
        """测试Card创建"""
        card = Card(
            card_id="test_001",
            name_jp="テストカード",
            name_cn="测试卡牌",
            card_type=CardType.VOCAL,
            rarity=CardRarity.SR,
            cost=2
        )
        
        assert card.card_id == "test_001"
        assert card.name_jp == "テストカード"
        assert card.name_cn == "测试卡牌"
        assert card.card_type == CardType.VOCAL
        assert card.rarity == CardRarity.SR
        assert card.cost == 2
        assert len(card.effects) == 0
    
    def test_card_with_effects(self):
        """测试带效果的Card"""
        effects = [
            {"type": "add_score", "value": 100},
            {"type": "add_vocal", "value": 5}
        ]
        
        card = Card(
            card_id="test_002",
            name_jp="効果カード",
            effects=effects
        )
        
        assert len(card.effects) == 2
        assert isinstance(card.effects[0], CardEffect)
        assert card.effects[0].type == "add_score"
        assert card.effects[0].value == 100


class TestGameState:
    """测试GameState类"""
    
    def test_game_state_creation(self):
        """测试GameState创建"""
        state = GameState(
            current_scene=GameScene.MAIN_MENU,
            current_language='ja',
            stamina=100,
            vigor=80
        )
        
        assert state.current_scene == GameScene.MAIN_MENU
        assert state.current_language == 'ja'
        assert state.stamina == 100
        assert state.vigor == 80
        assert isinstance(state.timestamp, datetime)
    
    def test_game_state_validity(self):
        """测试GameState有效性检查"""
        # 有效状态
        valid_state = GameState(
            current_scene=GameScene.PRODUCE_MAIN,
            stamina=50,
            vigor=30
        )
        assert valid_state.is_valid() is True
        
        # 无效状态 - 未知场景
        invalid_state = GameState(
            current_scene=GameScene.UNKNOWN,
            stamina=50,
            vigor=30
        )
        assert invalid_state.is_valid() is False
        
        # 无效状态 - 负数资源
        invalid_state2 = GameState(
            current_scene=GameScene.PRODUCE_MAIN,
            stamina=-10,
            vigor=30
        )
        assert invalid_state2.is_valid() is False


class TestAction:
    """测试Action类"""
    
    def test_action_creation(self):
        """测试Action创建"""
        action = Action(
            action_type=ActionType.CLICK,
            target=(100, 200),
            description="点击按钮"
        )
        
        assert action.action_type == ActionType.CLICK
        assert action.target == (100, 200)
        assert action.description == "点击按钮"
        assert action.delay_before == 0.1
        assert action.delay_after == 0.1
    
    def test_action_auto_description(self):
        """测试Action自动描述"""
        action = Action(
            action_type=ActionType.KEYPRESS,
            target="space"
        )
        
        assert "keypress" in action.description
        assert "space" in action.description


class TestUserStrategy:
    """测试UserStrategy类"""
    
    def test_user_strategy_creation(self):
        """测试UserStrategy创建"""
        strategy = UserStrategy()
        
        assert isinstance(strategy.produce_goal, ProduceGoal)
        assert isinstance(strategy.team_composition, TeamComposition)
        assert isinstance(strategy.behavior, BehaviorConfig)
    
    def test_user_strategy_from_dict(self):
        """测试从字典创建UserStrategy"""
        data = {
            "produce_goal": {
                "target": "high_score",
                "target_score": 15000
            },
            "team_composition": {
                "produce_idol": "花海咲季",
                "support_cards": ["【SSR】まだ見ぬ景色"]
            },
            "behavior": {
                "risk_aversion": 0.3,
                "enable_mcts": True
            }
        }
        
        strategy = UserStrategy.from_dict(data)
        
        assert strategy.produce_goal.target == "high_score"
        assert strategy.produce_goal.target_score == 15000
        assert strategy.team_composition.produce_idol == "花海咲季"
        assert strategy.behavior.risk_aversion == 0.3
        assert strategy.behavior.enable_mcts is True
    
    def test_user_strategy_to_dict(self):
        """测试UserStrategy转换为字典"""
        strategy = UserStrategy()
        strategy.produce_goal.target = "true_end"
        strategy.team_composition.produce_idol = "テストアイドル"
        
        data = strategy.to_dict()
        
        assert data["produce_goal"]["target"] == "true_end"
        assert data["team_composition"]["produce_idol"] == "テストアイドル"
        assert isinstance(data, dict)


if __name__ == "__main__":
    pytest.main([__file__])
