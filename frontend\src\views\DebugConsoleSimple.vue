<template>
  <div class="debug-console-simple">
    <el-container class="console-container">
      <!-- 侧边栏 -->
      <el-aside width="250px" class="console-sidebar">
        <div class="sidebar-header">
          <h3>调试控制台</h3>
        </div>
        <el-menu
          :default-active="activeTab"
          class="sidebar-menu"
          @select="handleTabSelect"
        >
          <el-menu-item index="system-status">
            <el-icon><Monitor /></el-icon>
            <span>系统状态</span>
          </el-menu-item>
          <el-menu-item index="api-test">
            <el-icon><Tools /></el-icon>
            <span>API测试</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 主内容区域 -->
      <el-main class="console-main">
        <!-- 系统状态面板 -->
        <div v-show="activeTab === 'system-status'" class="tab-content">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>系统状态</span>
                <el-button type="primary" @click="getSystemStatus" :loading="loading">
                  刷新状态
                </el-button>
              </div>
            </template>
            
            <div v-if="systemStatus">
              <el-descriptions :column="2">
                <el-descriptions-item label="系统状态">
                  {{ systemStatus.status || '未知' }}
                </el-descriptions-item>
                <el-descriptions-item label="运行时间">
                  {{ systemStatus.uptime || 0 }}秒
                </el-descriptions-item>
                <el-descriptions-item label="调度器状态">
                  {{ systemStatus.scheduler_status || '未知' }}
                </el-descriptions-item>
                <el-descriptions-item label="活跃任务">
                  {{ systemStatus.active_tasks || 0 }}
                </el-descriptions-item>
              </el-descriptions>
            </div>
            <div v-else>
              <el-empty description="暂无系统状态信息" />
            </div>
          </el-card>
        </div>

        <!-- API测试面板 -->
        <div v-show="activeTab === 'api-test'" class="tab-content">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>API测试</span>
              </div>
            </template>
            
            <div class="api-test-content">
              <el-space direction="vertical" style="width: 100%">
                <el-button type="primary" @click="testHealthCheck">健康检查</el-button>
                <el-button type="success" @click="testSystemStatus">系统状态</el-button>
                <el-button type="warning" @click="testConfig">配置获取</el-button>
                
                <div v-if="testResults.length > 0" class="test-results">
                  <h4>测试结果：</h4>
                  <div v-for="result in testResults" :key="result.id" class="result-item">
                    {{ result.message }}
                  </div>
                </div>
              </el-space>
            </div>
          </el-card>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Monitor, Tools } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const activeTab = ref('system-status')
const loading = ref(false)
const systemStatus = ref(null)
const testResults = ref([])

// API基础URL
const API_BASE_URL = 'http://localhost:8000/api/v1'

// 方法
const handleTabSelect = (key) => {
  activeTab.value = key
}

const getSystemStatus = async () => {
  loading.value = true
  try {
    const response = await fetch(`${API_BASE_URL}/status`)
    if (response.ok) {
      systemStatus.value = await response.json()
      ElMessage.success('系统状态获取成功')
    } else {
      ElMessage.error('系统状态获取失败')
    }
  } catch (error) {
    ElMessage.error('API连接失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const testHealthCheck = async () => {
  try {
    const response = await fetch('http://localhost:8000/health')
    if (response.ok) {
      const data = await response.json()
      testResults.value.unshift({
        id: Date.now(),
        message: `✅ 健康检查成功: ${data.status}`
      })
    } else {
      testResults.value.unshift({
        id: Date.now(),
        message: `❌ 健康检查失败: HTTP ${response.status}`
      })
    }
  } catch (error) {
    testResults.value.unshift({
      id: Date.now(),
      message: `❌ 健康检查错误: ${error.message}`
    })
  }
}

const testSystemStatus = async () => {
  try {
    const response = await fetch(`${API_BASE_URL}/status`)
    if (response.ok) {
      const data = await response.json()
      testResults.value.unshift({
        id: Date.now(),
        message: `✅ 系统状态获取成功: ${data.status}`
      })
    } else {
      testResults.value.unshift({
        id: Date.now(),
        message: `❌ 系统状态获取失败: HTTP ${response.status}`
      })
    }
  } catch (error) {
    testResults.value.unshift({
      id: Date.now(),
      message: `❌ 系统状态错误: ${error.message}`
    })
  }
}

const testConfig = async () => {
  try {
    const response = await fetch(`${API_BASE_URL}/config`)
    if (response.ok) {
      const data = await response.json()
      testResults.value.unshift({
        id: Date.now(),
        message: `✅ 配置获取成功，包含 ${Object.keys(data).length} 个配置项`
      })
    } else {
      testResults.value.unshift({
        id: Date.now(),
        message: `❌ 配置获取失败: HTTP ${response.status}`
      })
    }
  } catch (error) {
    testResults.value.unshift({
      id: Date.now(),
      message: `❌ 配置获取错误: ${error.message}`
    })
  }
}
</script>

<style scoped>
.debug-console-simple {
  height: 100%;
  background: #f5f5f5;
}

.console-container {
  height: 100%;
}

.console-sidebar {
  background: #fff;
  border-right: 1px solid #e6e6e6;
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid #e6e6e6;
  text-align: center;
}

.sidebar-header h3 {
  margin: 0;
  color: #409eff;
  font-size: 18px;
  font-weight: 600;
}

.sidebar-menu {
  border-right: none;
}

.console-main {
  padding: 20px;
  background: #f5f5f5;
}

.tab-content {
  height: 100%;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.api-test-content {
  padding: 20px;
}

.test-results {
  margin-top: 20px;
  padding: 15px;
  background: #f5f5f5;
  border-radius: 4px;
}

.result-item {
  margin: 5px 0;
  padding: 5px 0;
  border-bottom: 1px solid #eee;
}
</style>
