# 自动化游戏流程功能设计文档

**版本：** 1.0  
**日期：** 2025年8月1日  
**作者：** Gakumasu-Bot开发团队  

## 一、功能概述

### 1.1 功能目标
实现从游戏"主菜单"自动导航到"育成界面"的完整流程，为后续的自动化育成任务提供基础支撑。

### 1.2 功能范围
- 主菜单场景识别和验证
- 育成按钮的智能定位和点击
- 育成准备界面的导航和确认
- 场景切换的验证和异常处理
- 网络延迟和界面加载的容错机制

### 1.3 技术架构分析
基于现有项目架构，该功能将集成以下核心模块：
- **感知模块 (PerceptionModule)**: 场景识别和UI元素检测
- **行动模块 (ActionController)**: UI交互和操作执行
- **决策模块 (DecisionModule)**: 导航路径规划和异常处理
- **调度模块 (Scheduler)**: 任务协调和状态管理

## 二、现有架构分析

### 2.1 现有功能评估
通过代码分析发现项目已具备以下基础能力：

**场景识别能力**：
- 主菜单场景识别规则已定义 (`GameScene.MAIN_MENU`)
- 育成准备界面识别规则已定义 (`GameScene.PRODUCE_SETUP`)
- 育成主界面识别规则已定义 (`GameScene.PRODUCE_MAIN`)

**UI操作能力**：
- `click_ui_element()` 方法支持模板匹配点击
- `wait_for_scene_and_click()` 方法支持场景等待和点击
- 操作验证和重试机制已实现

**现有导航实现**：
```python
def _navigate_to_produce_setup(self):
    """导航到育成准备界面"""
    # 等待主菜单
    self._wait_for_scene(GameScene.MAIN_MENU, timeout=10)
    
    # 点击育成按钮 (硬编码坐标)
    produce_button_action = Action(
        action_type=ActionType.CLICK,
        target=(500, 400),  # 需要优化为动态识别
        description="点击育成按钮"
    )
    self.action.execute_and_verify(produce_button_action)
    
    # 等待育成准备界面
    self._wait_for_scene(GameScene.PRODUCE_SETUP, timeout=15)
```

### 2.2 现有问题识别
1. **硬编码坐标问题**: 当前使用固定坐标 `(500, 400)` 点击育成按钮，缺乏适应性
2. **模板资源不完整**: 缺少育成准备界面相关的UI模板
3. **异常处理不够完善**: 缺少网络延迟、界面加载失败的处理机制
4. **验证机制需要增强**: 需要更精确的场景切换验证

## 三、详细设计方案

### 3.1 核心流程设计

#### 3.1.1 主流程架构
```mermaid
graph TD
    A[开始导航] --> B[检查当前场景]
    B --> C{是否在主菜单?}
    C -->|是| D[识别育成按钮]
    C -->|否| E[导航到主菜单]
    E --> D
    D --> F{育成按钮可见?}
    F -->|是| G[点击育成按钮]
    F -->|否| H[等待界面加载]
    H --> I{超时检查}
    I -->|未超时| D
    I -->|超时| J[异常处理]
    G --> K[验证场景切换]
    K --> L{到达育成准备界面?}
    L -->|是| M[导航成功]
    L -->|否| N[重试机制]
    N --> O{重试次数检查}
    O -->|未超限| D
    O -->|超限| J
    J --> P[返回失败]
    M --> Q[返回成功]
```

#### 3.1.2 关键步骤详解

**步骤1: 场景状态检查**
```python
def check_current_scene(self) -> GameScene:
    """检查当前游戏场景"""
    try:
        game_state = self.perception.get_game_state()
        current_scene = game_state.current_scene
        
        self.logger.info(f"当前场景: {current_scene.value}")
        return current_scene
        
    except Exception as e:
        self.logger.error(f"场景检查失败: {e}")
        return GameScene.UNKNOWN
```

**步骤2: 智能UI元素识别**
```python
def locate_produce_button(self) -> Optional[MatchResult]:
    """智能定位育成按钮"""
    try:
        # 使用模板匹配定位按钮
        match_result = self.perception.find_ui_element("produce_button")
        
        if match_result and match_result.confidence >= 0.8:
            self.logger.info(f"育成按钮定位成功: 置信度 {match_result.confidence:.3f}")
            return match_result
        else:
            self.logger.warning("育成按钮定位失败或置信度过低")
            return None
            
    except Exception as e:
        self.logger.error(f"育成按钮定位异常: {e}")
        return None
```

**步骤3: 场景切换验证**
```python
def verify_scene_transition(self, target_scene: GameScene, timeout: float = 15.0) -> bool:
    """验证场景切换是否成功"""
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        try:
            current_scene = self.check_current_scene()
            
            if current_scene == target_scene:
                self.logger.info(f"场景切换成功: {target_scene.value}")
                return True
            
            # 检查是否出现加载界面或过渡动画
            if current_scene == GameScene.LOADING:
                self.logger.debug("检测到加载界面，继续等待...")
            
            time.sleep(1.0)
            
        except Exception as e:
            self.logger.warning(f"场景验证异常: {e}")
            time.sleep(1.0)
    
    self.logger.error(f"场景切换超时: 目标 {target_scene.value}")
    return False
```

### 3.2 异常处理机制

#### 3.2.1 网络延迟处理
```python
class NetworkDelayHandler:
    """网络延迟处理器"""
    
    def __init__(self, max_wait_time: float = 30.0):
        self.max_wait_time = max_wait_time
        self.retry_intervals = [1.0, 2.0, 3.0, 5.0]  # 递增等待时间
    
    def wait_for_ui_stable(self, check_func: Callable, description: str) -> bool:
        """等待UI稳定"""
        for i, interval in enumerate(self.retry_intervals):
            try:
                if check_func():
                    self.logger.info(f"{description} - 检查成功")
                    return True
                
                self.logger.debug(f"{description} - 第{i+1}次检查失败，等待{interval}秒")
                time.sleep(interval)
                
            except Exception as e:
                self.logger.warning(f"{description} - 检查异常: {e}")
                time.sleep(interval)
        
        self.logger.error(f"{description} - 最终检查失败")
        return False
```

#### 3.2.2 界面加载失败处理
```python
class LoadingFailureHandler:
    """界面加载失败处理器"""
    
    def handle_loading_failure(self, failure_type: str) -> bool:
        """处理加载失败"""
        self.logger.warning(f"检测到加载失败: {failure_type}")
        
        recovery_strategies = {
            "button_not_found": self._refresh_and_retry,
            "scene_not_changed": self._force_refresh,
            "timeout_exceeded": self._restart_navigation,
            "unknown_error": self._safe_fallback
        }
        
        strategy = recovery_strategies.get(failure_type, self._safe_fallback)
        return strategy()
    
    def _refresh_and_retry(self) -> bool:
        """刷新并重试"""
        self.logger.info("执行界面刷新重试")
        # 按F5刷新或其他刷新操作
        self.action.execute_key_press("F5")
        time.sleep(3.0)
        return True
    
    def _force_refresh(self) -> bool:
        """强制刷新"""
        self.logger.info("执行强制刷新")
        # 可以尝试点击其他区域再回到目标
        return True
    
    def _restart_navigation(self) -> bool:
        """重启导航流程"""
        self.logger.info("重启导航流程")
        # 返回主菜单重新开始
        return self._navigate_to_main_menu()
    
    def _safe_fallback(self) -> bool:
        """安全回退"""
        self.logger.error("执行安全回退")
        return False
```

### 3.3 性能优化设计

#### 3.3.1 智能等待策略
```python
class SmartWaitStrategy:
    """智能等待策略"""
    
    def __init__(self):
        self.base_wait_time = 1.0
        self.max_wait_time = 15.0
        self.confidence_threshold = 0.8
    
    def adaptive_wait(self, check_func: Callable, context: str) -> bool:
        """自适应等待"""
        wait_time = self.base_wait_time
        total_wait = 0.0
        
        while total_wait < self.max_wait_time:
            if check_func():
                return True
            
            # 根据上下文调整等待时间
            if "loading" in context.lower():
                wait_time = min(wait_time * 1.2, 3.0)  # 加载时等待更久
            elif "button" in context.lower():
                wait_time = min(wait_time * 1.1, 2.0)  # 按钮查找适中等待
            
            time.sleep(wait_time)
            total_wait += wait_time
        
        return False
```

#### 3.3.2 模板匹配优化
```python
class OptimizedTemplateMatching:
    """优化的模板匹配"""
    
    def __init__(self):
        self.template_cache = {}
        self.roi_cache = {}  # 感兴趣区域缓存
    
    def match_with_roi(self, image: np.ndarray, template_name: str) -> Optional[MatchResult]:
        """使用ROI优化的模板匹配"""
        # 根据场景确定搜索区域
        roi = self._get_search_roi(template_name)
        
        if roi:
            # 在ROI内搜索，提高效率
            roi_image = image[roi[1]:roi[3], roi[0]:roi[2]]
            result = self._match_template(roi_image, template_name)
            
            if result:
                # 调整坐标到全图坐标系
                result.x += roi[0]
                result.y += roi[1]
                return result
        
        # 回退到全图搜索
        return self._match_template(image, template_name)
    
    def _get_search_roi(self, template_name: str) -> Optional[tuple]:
        """获取搜索ROI"""
        roi_map = {
            "produce_button": (300, 200, 700, 600),  # 主菜单中央区域
            "main_menu_logo": (100, 50, 500, 200),   # 左上角区域
            "start_produce_button": (400, 500, 800, 700)  # 底部区域
        }
        return roi_map.get(template_name)
```

## 四、实现计划

### 4.1 开发阶段划分

**阶段1: 基础功能完善 (1-2天)**
- 完善现有的 `_navigate_to_produce_setup()` 方法
- 替换硬编码坐标为动态UI识别
- 添加基础的异常处理机制

**阶段2: 智能化增强 (2-3天)**
- 实现智能等待策略
- 添加网络延迟处理机制
- 优化模板匹配性能

**阶段3: 异常处理完善 (1-2天)**
- 实现完整的异常处理和恢复机制
- 添加多种失败场景的处理策略
- 完善日志记录和错误报告

**阶段4: 测试和优化 (1-2天)**
- 编写单元测试和集成测试
- 性能测试和优化
- 文档完善和代码审查

### 4.2 技术风险评估

**高风险项**:
- 游戏UI更新导致模板失效
- 网络不稳定影响界面加载
- 不同分辨率下的适配问题

**中风险项**:
- 场景识别准确率不足
- 异常恢复机制的可靠性
- 性能优化效果

**低风险项**:
- 基础功能实现
- 日志记录和监控
- 代码集成和部署

### 4.3 成功标准

**功能性标准**:
- 主菜单到育成界面导航成功率 ≥ 95%
- 平均导航时间 ≤ 10秒
- 异常情况自动恢复成功率 ≥ 80%

**非功能性标准**:
- 代码覆盖率 ≥ 85%
- 内存使用增长 ≤ 10MB
- CPU占用增长 ≤ 5%

## 五、后续扩展

### 5.1 功能扩展方向
- 支持更多游戏界面的自动导航
- 实现智能路径规划和优化
- 添加用户自定义导航流程

### 5.2 技术优化方向
- 引入机器学习提升识别准确率
- 实现分布式模板匹配
- 添加实时性能监控

## 六、具体实现代码设计

### 6.1 核心类设计

#### 6.1.1 导航结果枚举
```python
from enum import Enum

class NavigationResult(Enum):
    """导航结果枚举"""
    SUCCESS = "success"                    # 导航成功
    FAILED_MAIN_MENU = "failed_main_menu"  # 无法到达主菜单
    FAILED_BUTTON_CLICK = "failed_button_click"  # 按钮点击失败
    FAILED_SCENE_TRANSITION = "failed_scene_transition"  # 场景切换失败
    FAILED_EXCEPTION = "failed_exception"  # 异常导致失败
```

#### 6.1.2 自动化导航控制器
```python
class AutoNavigationController:
    """自动化导航控制器"""

    def __init__(self, perception_module, action_controller, logger):
        self.perception = perception_module
        self.action = action_controller
        self.logger = logger
        self.network_handler = NetworkDelayHandler()
        self.failure_handler = LoadingFailureHandler()
        self.wait_strategy = SmartWaitStrategy()

        # 导航配置
        self.max_retries = 3
        self.default_timeout = 15.0
        self.confidence_threshold = 0.8

    def navigate_to_produce_interface(self) -> NavigationResult:
        """导航到育成界面的主入口方法"""
        self.logger.info("开始导航到育成界面")

        try:
            # 步骤1: 确保在主菜单
            if not self._ensure_main_menu():
                return NavigationResult.FAILED_MAIN_MENU

            # 步骤2: 定位并点击育成按钮
            if not self._click_produce_button():
                return NavigationResult.FAILED_BUTTON_CLICK

            # 步骤3: 验证到达育成准备界面
            if not self._verify_produce_setup():
                return NavigationResult.FAILED_SCENE_TRANSITION

            self.logger.info("导航到育成界面成功")
            return NavigationResult.SUCCESS

        except Exception as e:
            self.logger.error(f"导航过程发生异常: {e}")
            return NavigationResult.FAILED_EXCEPTION
```

#### 6.1.2 场景验证器
```python
class SceneValidator:
    """场景验证器"""

    def __init__(self, perception_module, logger):
        self.perception = perception_module
        self.logger = logger
        self.validation_cache = {}

    def validate_scene_with_retry(self, target_scene: GameScene,
                                 max_attempts: int = 3) -> bool:
        """带重试的场景验证"""
        for attempt in range(max_attempts):
            try:
                if self._validate_scene_once(target_scene):
                    return True

                self.logger.debug(f"场景验证失败，第{attempt + 1}次尝试")
                time.sleep(1.0 * (attempt + 1))  # 递增等待时间

            except Exception as e:
                self.logger.warning(f"场景验证异常 (尝试 {attempt + 1}): {e}")

        return False

    def _validate_scene_once(self, target_scene: GameScene) -> bool:
        """单次场景验证"""
        game_state = self.perception.get_game_state()
        current_scene = game_state.current_scene

        # 基础场景匹配
        if current_scene == target_scene:
            return True

        # 特殊情况处理
        if target_scene == GameScene.PRODUCE_SETUP:
            return self._validate_produce_setup_details(game_state)

        return False

    def _validate_produce_setup_details(self, game_state: GameState) -> bool:
        """详细验证育成准备界面"""
        required_elements = ["idol_selection", "support_card_selection"]
        ui_elements = game_state.ui_elements

        found_elements = sum(1 for elem in required_elements
                           if elem in ui_elements)

        return found_elements >= 1  # 至少找到一个关键元素
```

### 6.2 错误处理和恢复机制

#### 6.2.1 分层异常处理
```python
# 使用现有的异常类，保持架构一致性
from ...core.data_structures import (
    GakumasuBotException, GameUIElementNotFound,
    GameCrashException, OCRUnreliableException
)

class NavigationException(GakumasuBotException):
    """导航异常基类，继承自现有的基础异常类"""
    pass

class SceneNotFoundError(NavigationException):
    """场景未找到异常"""
    pass

class NavigationTimeoutError(NavigationException):
    """导航超时异常"""
    pass

class NavigationRecoveryManager:
    """导航恢复管理器"""

    def __init__(self, navigation_controller):
        self.nav_controller = navigation_controller
        self.recovery_strategies = {
            SceneNotFoundError: self._recover_scene_not_found,
            GameUIElementNotFound: self._recover_ui_not_found,
            NavigationTimeoutError: self._recover_timeout,
        }

    def handle_exception(self, exception: Exception) -> bool:
        """处理异常并尝试恢复"""
        exception_type = type(exception)

        if exception_type in self.recovery_strategies:
            recovery_func = self.recovery_strategies[exception_type]
            return recovery_func(exception)

        # 未知异常，执行通用恢复
        return self._generic_recovery(exception)

    def _recover_scene_not_found(self, error: SceneNotFoundError) -> bool:
        """恢复场景未找到错误"""
        self.nav_controller.logger.info("尝试恢复场景未找到错误")

        # 尝试刷新界面
        self.nav_controller.action.execute_key_press("F5")
        time.sleep(3.0)

        # 重新检查场景
        return self.nav_controller._ensure_main_menu()

    def _recover_ui_not_found(self, error: GameUIElementNotFound) -> bool:
        """恢复UI元素未找到错误"""
        self.nav_controller.logger.info("尝试恢复UI元素未找到错误")

        # 等待更长时间让界面完全加载
        time.sleep(5.0)

        # 尝试滚动或移动鼠标激活界面
        self.nav_controller.action.execute_mouse_move((960, 540))
        time.sleep(1.0)

        return True

    def _recover_timeout(self, error: NavigationTimeoutError) -> bool:
        """恢复超时错误"""
        self.nav_controller.logger.info("尝试恢复超时错误")

        # 检查网络连接状态
        if self._check_network_connectivity():
            # 网络正常，可能是服务器响应慢
            return True
        else:
            # 网络异常，等待恢复
            time.sleep(10.0)
            return self._check_network_connectivity()

    def _check_network_connectivity(self) -> bool:
        """检查网络连接状态"""
        # 简单的网络检查实现
        try:
            import requests
            response = requests.get("https://www.google.com", timeout=5)
            return response.status_code == 200
        except:
            return False
```

### 6.3 性能监控和优化

#### 6.3.1 性能监控器
```python
class NavigationPerformanceMonitor:
    """导航性能监控器"""

    def __init__(self):
        self.metrics = {
            'total_navigations': 0,
            'successful_navigations': 0,
            'average_time': 0.0,
            'error_counts': defaultdict(int),
            'performance_history': []
        }

    def record_navigation(self, result: NavigationResult,
                         duration: float, error: str = None):
        """记录导航性能数据"""
        self.metrics['total_navigations'] += 1

        if result == NavigationResult.SUCCESS:
            self.metrics['successful_navigations'] += 1

        if error:
            self.metrics['error_counts'][error] += 1

        # 更新平均时间
        total_time = (self.metrics['average_time'] *
                     (self.metrics['total_navigations'] - 1) + duration)
        self.metrics['average_time'] = total_time / self.metrics['total_navigations']

        # 记录历史数据
        self.metrics['performance_history'].append({
            'timestamp': datetime.now(),
            'result': result,
            'duration': duration,
            'error': error
        })

        # 保持历史记录在合理范围内
        if len(self.metrics['performance_history']) > 1000:
            self.metrics['performance_history'] = \
                self.metrics['performance_history'][-500:]

    def get_success_rate(self) -> float:
        """获取成功率"""
        if self.metrics['total_navigations'] == 0:
            return 0.0

        return (self.metrics['successful_navigations'] /
                self.metrics['total_navigations'])

    def get_performance_report(self) -> dict:
        """获取性能报告"""
        return {
            'success_rate': self.get_success_rate(),
            'average_time': self.metrics['average_time'],
            'total_attempts': self.metrics['total_navigations'],
            'common_errors': dict(self.metrics['error_counts']),
            'recent_performance': self.metrics['performance_history'][-10:]
        }
```

## 七、测试策略

### 7.1 单元测试设计
```python
class TestAutoNavigationController(unittest.TestCase):
    """自动化导航控制器测试"""

    def setUp(self):
        self.mock_perception = Mock()
        self.mock_action = Mock()
        self.mock_logger = Mock()

        self.nav_controller = AutoNavigationController(
            self.mock_perception, self.mock_action, self.mock_logger
        )

    def test_successful_navigation(self):
        """测试成功导航流程"""
        # 模拟主菜单场景
        self.mock_perception.get_game_state.return_value = GameState(
            current_scene=GameScene.MAIN_MENU
        )

        # 模拟找到育成按钮
        self.mock_perception.find_ui_element.return_value = MatchResult(
            x=500, y=400, width=120, height=60, confidence=0.9
        )

        # 模拟点击成功
        self.mock_action.execute_action.return_value = True

        # 模拟场景切换成功
        self.mock_perception.get_game_state.side_effect = [
            GameState(current_scene=GameScene.MAIN_MENU),
            GameState(current_scene=GameScene.PRODUCE_SETUP)
        ]

        # 执行导航
        result = self.nav_controller.navigate_to_produce_interface()

        # 验证结果
        self.assertEqual(result, NavigationResult.SUCCESS)
        self.mock_action.execute_action.assert_called()

    def test_button_not_found_handling(self):
        """测试按钮未找到的处理"""
        # 模拟主菜单场景
        self.mock_perception.get_game_state.return_value = GameState(
            current_scene=GameScene.MAIN_MENU
        )

        # 模拟未找到育成按钮
        self.mock_perception.find_ui_element.return_value = None

        # 执行导航
        result = self.nav_controller.navigate_to_produce_interface()

        # 验证结果
        self.assertEqual(result, NavigationResult.FAILED_BUTTON_CLICK)
```

### 7.2 集成测试设计
```python
class TestNavigationIntegration(unittest.TestCase):
    """导航集成测试"""

    def test_end_to_end_navigation(self):
        """端到端导航测试"""
        # 使用真实的感知和行动模块进行集成测试
        perception = PerceptionModule()
        action = ActionController()

        nav_controller = AutoNavigationController(perception, action, logger)

        # 执行完整导航流程
        result = nav_controller.navigate_to_produce_interface()

        # 验证最终状态
        final_state = perception.get_game_state()
        self.assertEqual(final_state.current_scene, GameScene.PRODUCE_SETUP)
```

## 八、实施方案总结

### 8.1 核心改进点

**1. 动态UI识别替代硬编码坐标**
- 当前问题：使用固定坐标 `(500, 400)` 点击育成按钮
- 解决方案：使用模板匹配动态定位UI元素
- 预期效果：提高适应性，支持不同分辨率和UI布局

**2. 增强异常处理机制**
- 当前问题：异常处理不够完善，缺少恢复策略
- 解决方案：实现分层异常处理和自动恢复机制
- 预期效果：提高系统稳定性和容错能力

**3. 智能等待和验证策略**
- 当前问题：固定等待时间，无法适应网络延迟
- 解决方案：实现自适应等待和多重验证机制
- 预期效果：提高导航成功率和响应速度

### 8.2 技术实现要点

**模块集成方式**：
- 基于现有的 `ProduceTask` 类进行扩展
- 复用现有的感知、行动、决策模块
- 保持与现有异常处理体系的兼容性

**关键技术难点**：
- UI元素的准确识别和定位
- 网络延迟和界面加载的处理
- 异常情况的自动恢复

**性能优化策略**：
- ROI区域优化模板匹配
- 智能缓存减少重复计算
- 自适应等待时间调整

### 8.3 风险控制措施

**技术风险**：
- 游戏UI更新：建立模板更新机制
- 网络不稳定：实现多重重试策略
- 分辨率适配：使用相对坐标和缩放

**实施风险**：
- 代码集成：分阶段实施，逐步验证
- 测试覆盖：建立完整的测试用例
- 性能影响：持续监控资源使用

### 8.4 验收标准

**功能性指标**：
- 导航成功率 ≥ 95%
- 平均导航时间 ≤ 10秒
- 异常恢复成功率 ≥ 80%

**技术性指标**：
- 代码覆盖率 ≥ 85%
- 内存使用增长 ≤ 10MB
- CPU占用增长 ≤ 5%

**用户体验指标**：
- 界面响应时间 ≤ 2秒
- 错误信息清晰度 ≥ 90%
- 日志记录完整性 100%

---

**文档状态**: 待审核
**下一步**: 等待用户确认设计方案后开始实施

**预计实施时间**: 5-7个工作日
**主要交付物**:
- 优化的导航控制器类
- 完善的异常处理机制
- 完整的测试用例
- 性能监控工具
- 技术文档更新
