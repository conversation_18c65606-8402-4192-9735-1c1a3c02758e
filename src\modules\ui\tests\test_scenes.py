"""
场景测试
测试场景管理和导航功能
"""

import unittest
import time
from unittest.mock import Mock, patch

from .test_utils import SceneTestCase, create_test_scene_config
from .test_data import TEST_SCENE_CONFIGS, MOCK_GAME_STATES
from ....core.data_structures import GameScene
from ..managers.scene_factory import SceneFactory, EnhancedSceneFactory
from ..managers.scene_manager import SceneManager, EnhancedSceneManager, NavigationResult
from ..managers.config_loader import UIConfigLoader


class MockScene:
    """模拟场景类"""
    
    def __init__(self, scene_type: GameScene, config=None):
        self.config = config or create_test_scene_config(scene_type)
        self.ui_elements = {}
        self._is_current = False
        self._navigation_success = True
    
    def is_current_scene(self) -> bool:
        return self._is_current
    
    def wait_for_scene(self, timeout: float = 10.0) -> bool:
        return self._is_current
    
    def navigate_to_scene(self, target_scene: GameScene, timeout: float = 30.0) -> bool:
        return self._navigation_success
    
    def get_ui_element(self, name: str):
        return self.ui_elements.get(name)
    
    def set_current(self, is_current: bool):
        self._is_current = is_current
    
    def set_navigation_success(self, success: bool):
        self._navigation_success = success


class TestSceneFactory(SceneTestCase):
    """场景工厂测试类"""
    
    def setUp(self):
        super().setUp()
        self.config_loader = Mock(spec=UIConfigLoader)
        self.scene_factory = SceneFactory(self.perception, self.action, self.config_loader)
    
    def test_factory_creation(self):
        """测试工厂创建"""
        self.assertIsNotNone(self.scene_factory)
        self.assertEqual(self.scene_factory.perception, self.perception)
        self.assertEqual(self.scene_factory.action, self.action)
    
    def test_register_scene_class(self):
        """测试注册场景类"""
        # 注册模拟场景类
        self.scene_factory.register_scene_class(GameScene.MAIN_MENU, MockScene)
        
        # 检查是否支持该场景
        self.assertTrue(self.scene_factory.is_scene_supported(GameScene.MAIN_MENU))
        
        # 获取支持的场景列表
        supported_scenes = self.scene_factory.get_supported_scenes()
        self.assertIn(GameScene.MAIN_MENU, supported_scenes)
    
    def test_create_scene_with_config(self):
        """测试使用配置创建场景"""
        # 注册模拟场景类
        self.scene_factory.register_scene_class(GameScene.MAIN_MENU, MockScene)
        
        # 创建场景配置
        config = create_test_scene_config(GameScene.MAIN_MENU)
        
        # 创建场景
        scene = self.scene_factory.create_scene(GameScene.MAIN_MENU, config)
        
        self.assertIsNotNone(scene)
        self.assertIsInstance(scene, MockScene)
        self.assertEqual(scene.config.scene_type, GameScene.MAIN_MENU)
    
    def test_create_scene_without_config(self):
        """测试不使用配置创建场景"""
        # 注册模拟场景类
        self.scene_factory.register_scene_class(GameScene.MAIN_MENU, MockScene)
        
        # 模拟配置加载器返回配置
        mock_config = create_test_scene_config(GameScene.MAIN_MENU)
        self.config_loader.load_scene_config.return_value = mock_config
        
        # 创建场景
        scene = self.scene_factory.create_scene(GameScene.MAIN_MENU)
        
        self.assertIsNotNone(scene)
        self.config_loader.load_scene_config.assert_called_once_with(GameScene.MAIN_MENU)
    
    def test_register_custom_creator(self):
        """测试注册自定义创建器"""
        def custom_creator(scene_type, config, perception, action):
            return MockScene(scene_type, config)
        
        self.scene_factory.register_custom_creator(GameScene.MAIN_MENU, custom_creator)
        
        # 创建场景
        scene = self.scene_factory.create_scene(GameScene.MAIN_MENU)
        
        self.assertIsNotNone(scene)
        self.assertIsInstance(scene, MockScene)
    
    def test_creation_stats(self):
        """测试创建统计"""
        # 注册模拟场景类
        self.scene_factory.register_scene_class(GameScene.MAIN_MENU, MockScene)
        
        # 创建几个场景
        for _ in range(3):
            self.scene_factory.create_scene(GameScene.MAIN_MENU)
        
        stats = self.scene_factory.get_creation_stats()
        
        self.assertEqual(stats['total_created'], 3)
        self.assertIn(GameScene.MAIN_MENU.value, stats['scene_stats'])
        self.assertEqual(stats['scene_stats'][GameScene.MAIN_MENU.value]['total_created'], 3)


class TestEnhancedSceneFactory(SceneTestCase):
    """增强场景工厂测试类"""
    
    def setUp(self):
        super().setUp()
        self.config_loader = Mock(spec=UIConfigLoader)
        self.scene_factory = EnhancedSceneFactory(self.perception, self.action, self.config_loader)
    
    def test_scene_caching(self):
        """测试场景缓存"""
        # 注册模拟场景类
        self.scene_factory.register_scene_class(GameScene.MAIN_MENU, MockScene)
        
        # 第一次创建
        scene1 = self.scene_factory.create_scene(GameScene.MAIN_MENU, use_cache=True)
        
        # 第二次创建（应该从缓存获取）
        scene2 = self.scene_factory.create_scene(GameScene.MAIN_MENU, use_cache=True)
        
        self.assertIs(scene1, scene2)  # 应该是同一个对象
    
    def test_preload_scenes(self):
        """测试预加载场景"""
        # 注册模拟场景类
        self.scene_factory.register_scene_class(GameScene.MAIN_MENU, MockScene)
        self.scene_factory.register_scene_class(GameScene.PRODUCE_SETUP, MockScene)
        
        # 预加载场景
        scenes_to_preload = [GameScene.MAIN_MENU, GameScene.PRODUCE_SETUP]
        self.scene_factory.preload_scenes(scenes_to_preload)
        
        # 检查缓存统计
        cache_stats = self.scene_factory.get_cache_stats()
        self.assertEqual(cache_stats['cached_scenes'], 2)
        self.assertEqual(cache_stats['preloaded_scenes'], 2)
    
    def test_cache_control(self):
        """测试缓存控制"""
        # 注册模拟场景类
        self.scene_factory.register_scene_class(GameScene.MAIN_MENU, MockScene)
        
        # 启用缓存创建场景
        scene1 = self.scene_factory.create_scene(GameScene.MAIN_MENU, use_cache=True)
        
        # 禁用缓存
        self.scene_factory.set_cache_enabled(False)
        
        # 再次创建（应该是新对象）
        scene2 = self.scene_factory.create_scene(GameScene.MAIN_MENU, use_cache=True)
        
        self.assertIsNot(scene1, scene2)  # 应该是不同对象


class TestSceneManager(SceneTestCase):
    """场景管理器测试类"""
    
    def setUp(self):
        super().setUp()
        self.config_loader = Mock(spec=UIConfigLoader)
        self.scene_factory = SceneFactory(self.perception, self.action, self.config_loader)
        self.scene_manager = SceneManager(self.scene_factory)
        
        # 注册模拟场景类
        self.scene_factory.register_scene_class(GameScene.MAIN_MENU, MockScene)
        self.scene_factory.register_scene_class(GameScene.PRODUCE_SETUP, MockScene)
        self.scene_factory.register_scene_class(GameScene.PRODUCE_MAIN, MockScene)
    
    def test_get_scene(self):
        """测试获取场景"""
        scene = self.scene_manager.get_scene(GameScene.MAIN_MENU)
        
        self.assertIsNotNone(scene)
        self.assertIsInstance(scene, MockScene)
        self.assertEqual(scene.config.scene_type, GameScene.MAIN_MENU)
    
    def test_get_current_scene(self):
        """测试获取当前场景"""
        # 创建并设置当前场景
        main_menu_scene = self.scene_manager.get_scene(GameScene.MAIN_MENU)
        main_menu_scene.set_current(True)
        
        current_scene = self.scene_manager.get_current_scene()
        
        self.assertIsNotNone(current_scene)
        self.assertEqual(current_scene.config.scene_type, GameScene.MAIN_MENU)
    
    def test_navigate_to_scene_success(self):
        """测试成功导航到场景"""
        # 设置当前场景
        main_menu_scene = self.scene_manager.get_scene(GameScene.MAIN_MENU)
        main_menu_scene.set_current(True)
        main_menu_scene.set_navigation_success(True)
        
        # 设置目标场景
        produce_setup_scene = self.scene_manager.get_scene(GameScene.PRODUCE_SETUP)
        produce_setup_scene.set_current(False)
        
        # 执行导航
        result = self.scene_manager.navigate_to_scene(GameScene.PRODUCE_SETUP)
        
        self.assertEqual(result, NavigationResult.SUCCESS)
    
    def test_navigate_to_scene_already_at_target(self):
        """测试导航到已在的场景"""
        # 设置当前场景为目标场景
        main_menu_scene = self.scene_manager.get_scene(GameScene.MAIN_MENU)
        main_menu_scene.set_current(True)
        
        # 导航到同一场景
        result = self.scene_manager.navigate_to_scene(GameScene.MAIN_MENU)
        
        self.assertEqual(result, NavigationResult.ALREADY_AT_TARGET)
    
    def test_wait_for_scene(self):
        """测试等待场景"""
        # 设置场景为当前
        main_menu_scene = self.scene_manager.get_scene(GameScene.MAIN_MENU)
        main_menu_scene.set_current(True)
        
        # 等待场景
        result = self.scene_manager.wait_for_scene(GameScene.MAIN_MENU, timeout=1.0)
        
        self.assertTrue(result)
    
    def test_navigation_history(self):
        """测试导航历史"""
        # 执行几次导航
        self.scene_manager.navigate_to_scene(GameScene.PRODUCE_SETUP)
        self.scene_manager.navigate_to_scene(GameScene.PRODUCE_MAIN)
        
        # 获取导航历史
        history = self.scene_manager.get_navigation_history(limit=5)
        
        self.assertEqual(len(history), 2)
        self.assertEqual(history[0]['to_scene'], GameScene.PRODUCE_SETUP)
        self.assertEqual(history[1]['to_scene'], GameScene.PRODUCE_MAIN)
    
    def test_navigation_stats(self):
        """测试导航统计"""
        # 执行多次导航到同一场景
        for _ in range(3):
            self.scene_manager.navigate_to_scene(GameScene.PRODUCE_SETUP)
        
        # 获取统计
        stats = self.scene_manager.get_navigation_stats(GameScene.PRODUCE_SETUP)
        
        self.assertEqual(stats['total_navigations'], 3)
        self.assertGreater(stats['success_rate'], 0)
        self.assertGreater(stats['average_duration'], 0)
    
    def test_scene_check_interval(self):
        """测试场景检查间隔"""
        # 设置检查间隔
        self.scene_manager.set_scene_check_interval(2.0)
        
        stats = self.scene_manager.get_manager_stats()
        self.assertEqual(stats['scene_check_interval'], 2.0)
    
    def test_cache_management(self):
        """测试缓存管理"""
        # 获取场景（会被缓存）
        scene1 = self.scene_manager.get_scene(GameScene.MAIN_MENU)
        
        # 清理缓存
        self.scene_manager.clear_cache()
        
        # 再次获取（应该是新对象）
        scene2 = self.scene_manager.get_scene(GameScene.MAIN_MENU)
        
        self.assertIsNot(scene1, scene2)


class TestEnhancedSceneManager(SceneTestCase):
    """增强场景管理器测试类"""
    
    def setUp(self):
        super().setUp()
        self.config_loader = Mock(spec=UIConfigLoader)
        self.scene_factory = EnhancedSceneFactory(self.perception, self.action, self.config_loader)
        self.scene_manager = EnhancedSceneManager(self.scene_factory)
        
        # 注册模拟场景类
        self.scene_factory.register_scene_class(GameScene.MAIN_MENU, MockScene)
        self.scene_factory.register_scene_class(GameScene.PRODUCE_SETUP, MockScene)
        self.scene_factory.register_scene_class(GameScene.PRODUCE_MAIN, MockScene)
    
    def test_scene_change_callback(self):
        """测试场景变化回调"""
        callback_calls = []
        
        def scene_change_callback(old_scene, new_scene):
            callback_calls.append((old_scene, new_scene))
        
        self.scene_manager.add_scene_change_callback(scene_change_callback)
        
        # 模拟场景变化
        main_menu_scene = self.scene_manager.get_scene(GameScene.MAIN_MENU)
        main_menu_scene.set_current(True)
        
        # 强制更新当前场景
        self.scene_manager._update_current_scene()
        
        # 检查回调是否被调用
        self.assertGreater(len(callback_calls), 0)
    
    def test_smart_navigation(self):
        """测试智能导航"""
        # 设置当前场景
        main_menu_scene = self.scene_manager.get_scene(GameScene.MAIN_MENU)
        main_menu_scene.set_current(True)
        
        # 执行智能导航
        result = self.scene_manager.smart_navigate(GameScene.PRODUCE_SETUP)
        
        self.assertEqual(result, NavigationResult.SUCCESS)
    
    def test_path_learning(self):
        """测试路径学习"""
        # 启用路径学习
        self.scene_manager.set_path_learning(True)
        
        # 执行成功的导航
        main_menu_scene = self.scene_manager.get_scene(GameScene.MAIN_MENU)
        main_menu_scene.set_current(True)
        
        result = self.scene_manager.smart_navigate(GameScene.PRODUCE_SETUP)
        
        # 检查是否学习到路径
        path = self.scene_manager.get_optimal_path(GameScene.MAIN_MENU, GameScene.PRODUCE_SETUP)
        self.assertIsNotNone(path)
    
    def test_navigation_optimization(self):
        """测试导航优化"""
        # 禁用导航优化
        self.scene_manager.set_navigation_optimization(False)
        
        stats = self.scene_manager.get_enhanced_stats()
        self.assertFalse(stats['navigation_optimization'])
        
        # 启用导航优化
        self.scene_manager.set_navigation_optimization(True)
        
        stats = self.scene_manager.get_enhanced_stats()
        self.assertTrue(stats['navigation_optimization'])
    
    def test_learned_paths_management(self):
        """测试学习路径管理"""
        # 手动添加一些学习路径
        self.scene_manager._optimal_paths[(GameScene.MAIN_MENU, GameScene.PRODUCE_SETUP)] = [GameScene.PRODUCE_SETUP]
        self.scene_manager._optimal_paths[(GameScene.PRODUCE_SETUP, GameScene.PRODUCE_MAIN)] = [GameScene.PRODUCE_MAIN]
        
        # 获取统计
        stats = self.scene_manager.get_enhanced_stats()
        self.assertEqual(stats['learned_paths'], 2)
        
        # 清理学习路径
        self.scene_manager.clear_learned_paths()
        
        stats = self.scene_manager.get_enhanced_stats()
        self.assertEqual(stats['learned_paths'], 0)


if __name__ == '__main__':
    unittest.main()
