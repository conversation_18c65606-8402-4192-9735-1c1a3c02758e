"""
UI模块初始化文件
提供面向对象的场景和UI元素管理功能
"""

from .base_ui_element import BaseUIElement, Button, InputField, Label
from .game_buttons import (
    ProduceButton, PartTimeJobButton, DailyTasksButton,
    IdolSelectionButton, SupportCardButton, StartProduceButton,
    LessonButton, RestButton, OutingButton,
    ConfirmButton, CancelButton, MenuButton, BackButton
)
from .game_scenes import BaseScene, MainMenuScene, ProduceSetupScene, ProduceMainScene
from .scene_manager import SceneFactory, SceneManager, LegacyCompatibilityAdapter

__all__ = [
    # 基础UI元素类
    'BaseUIElement',
    'Button',
    'InputField', 
    'Label',
    
    # 游戏特定按钮类
    'ProduceButton',
    'PartTimeJobButton',
    'DailyTasksButton',
    'IdolSelectionButton',
    'SupportCardButton',
    'StartProduceButton',
    'LessonButton',
    'RestButton',
    'OutingButton',
    'ConfirmButton',
    'CancelButton',
    'MenuButton',
    'BackButton',
    
    # 场景类
    'BaseScene',
    'MainMenuScene',
    'ProduceSetupScene',
    'ProduceMainScene',
    
    # 管理器和工厂类
    'SceneFactory',
    'SceneManager',
    'LegacyCompatibilityAdapter'
]

# 版本信息
__version__ = '1.0.0'
__author__ = 'Gakumasu-Bot Team'
__description__ = '面向对象的游戏场景和UI元素管理模块'
