"""
任务管理器
负责游戏任务的创建、管理、调度和执行
"""

import uuid
from typing import List, Dict, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
from threading import Lock

from ...utils.logger import get_logger
from ...core.data_structures import GakumasuBotException


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"         # 等待执行
    RUNNING = "running"         # 正在执行
    COMPLETED = "completed"     # 已完成
    FAILED = "failed"          # 执行失败
    CANCELLED = "cancelled"     # 已取消
    PAUSED = "paused"          # 已暂停


class TaskPriority(Enum):
    """任务优先级枚举"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4
    CRITICAL = 5


@dataclass
class Task:
    """任务数据结构"""
    task_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    priority: TaskPriority = TaskPriority.NORMAL
    status: TaskStatus = TaskStatus.PENDING
    
    # 执行相关
    execute_func: Optional[Callable] = None
    execute_args: tuple = field(default_factory=tuple)
    execute_kwargs: Dict[str, Any] = field(default_factory=dict)
    
    # 时间相关
    created_at: datetime = field(default_factory=datetime.now)
    scheduled_at: Optional[datetime] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    timeout: Optional[float] = None  # 超时时间（秒）
    
    # 依赖关系
    dependencies: List[str] = field(default_factory=list)  # 依赖的任务ID
    dependents: List[str] = field(default_factory=list)    # 依赖此任务的任务ID
    
    # 重试机制
    max_retries: int = 3
    retry_count: int = 0
    retry_delay: float = 1.0  # 重试延迟（秒）
    
    # 结果和错误
    result: Any = None
    error: Optional[str] = None
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        if not self.name:
            self.name = f"Task_{self.task_id[:8]}"
    
    def is_ready_to_execute(self) -> bool:
        """检查任务是否准备好执行"""
        if self.status != TaskStatus.PENDING:
            return False
        
        # 检查调度时间
        if self.scheduled_at and datetime.now() < self.scheduled_at:
            return False
        
        return True
    
    def can_retry(self) -> bool:
        """检查是否可以重试"""
        return (self.status == TaskStatus.FAILED and 
                self.retry_count < self.max_retries)
    
    def reset_for_retry(self):
        """重置任务状态以便重试"""
        self.status = TaskStatus.PENDING
        self.started_at = None
        self.completed_at = None
        self.error = None
        self.retry_count += 1


class TaskManagerError(GakumasuBotException):
    """任务管理器错误"""
    pass


class TaskManager:
    """任务管理器类"""
    
    def __init__(self):
        """初始化任务管理器"""
        self.logger = get_logger("TaskManager")
        
        # 任务存储
        self.tasks: Dict[str, Task] = {}
        self.task_queue: List[str] = []  # 按优先级排序的任务队列
        
        # 线程安全
        self._lock = Lock()
        
        # 统计信息
        self.total_tasks_created = 0
        self.total_tasks_completed = 0
        self.total_tasks_failed = 0
        
        self.logger.info("任务管理器初始化完成")
    
    def create_task(self, name: str, execute_func: Callable,
                   description: str = "", priority: TaskPriority = TaskPriority.NORMAL,
                   scheduled_at: Optional[datetime] = None,
                   timeout: Optional[float] = None,
                   dependencies: Optional[List[str]] = None,
                   max_retries: int = 3,
                   execute_args: tuple = (),
                   execute_kwargs: Optional[Dict[str, Any]] = None,
                   metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        创建新任务
        
        Args:
            name: 任务名称
            execute_func: 执行函数
            description: 任务描述
            priority: 任务优先级
            scheduled_at: 调度时间
            timeout: 超时时间
            dependencies: 依赖任务列表
            max_retries: 最大重试次数
            execute_args: 执行函数参数
            execute_kwargs: 执行函数关键字参数
            metadata: 元数据
            
        Returns:
            任务ID
        """
        with self._lock:
            task = Task(
                name=name,
                description=description,
                priority=priority,
                execute_func=execute_func,
                execute_args=execute_args,
                execute_kwargs=execute_kwargs or {},
                scheduled_at=scheduled_at,
                timeout=timeout,
                dependencies=dependencies or [],
                max_retries=max_retries,
                metadata=metadata or {}
            )
            
            self.tasks[task.task_id] = task
            self._add_to_queue(task.task_id)
            
            # 更新依赖关系
            for dep_id in task.dependencies:
                if dep_id in self.tasks:
                    self.tasks[dep_id].dependents.append(task.task_id)
            
            self.total_tasks_created += 1
            
            self.logger.info(f"创建任务: {task.name} ({task.task_id})")
            return task.task_id
    
    def get_task(self, task_id: str) -> Optional[Task]:
        """获取任务"""
        return self.tasks.get(task_id)
    
    def get_next_task(self) -> Optional[Task]:
        """获取下一个待执行的任务"""
        with self._lock:
            for task_id in self.task_queue:
                task = self.tasks.get(task_id)
                if task and task.is_ready_to_execute():
                    # 检查依赖是否满足
                    if self._are_dependencies_satisfied(task):
                        return task
            return None
    
    def start_task(self, task_id: str) -> bool:
        """开始执行任务"""
        with self._lock:
            task = self.tasks.get(task_id)
            if not task:
                return False
            
            if task.status != TaskStatus.PENDING:
                return False
            
            task.status = TaskStatus.RUNNING
            task.started_at = datetime.now()
            
            self.logger.info(f"开始执行任务: {task.name}")
            return True
    
    def complete_task(self, task_id: str, result: Any = None) -> bool:
        """完成任务"""
        with self._lock:
            task = self.tasks.get(task_id)
            if not task:
                return False
            
            task.status = TaskStatus.COMPLETED
            task.completed_at = datetime.now()
            task.result = result
            
            # 从队列中移除
            if task_id in self.task_queue:
                self.task_queue.remove(task_id)
            
            self.total_tasks_completed += 1
            
            # 检查并启用依赖此任务的任务
            self._check_dependent_tasks(task_id)
            
            self.logger.info(f"任务完成: {task.name}")
            return True
    
    def fail_task(self, task_id: str, error: str) -> bool:
        """任务执行失败"""
        with self._lock:
            task = self.tasks.get(task_id)
            if not task:
                return False
            
            task.error = error
            
            # 检查是否可以重试
            if task.can_retry():
                task.reset_for_retry()
                # 添加重试延迟
                if task.retry_delay > 0:
                    task.scheduled_at = datetime.now() + timedelta(seconds=task.retry_delay)
                
                self.logger.warning(f"任务失败，准备重试 ({task.retry_count}/{task.max_retries}): {task.name}")
                return True
            else:
                task.status = TaskStatus.FAILED
                task.completed_at = datetime.now()
                
                # 从队列中移除
                if task_id in self.task_queue:
                    self.task_queue.remove(task_id)
                
                self.total_tasks_failed += 1
                
                self.logger.error(f"任务最终失败: {task.name}, 错误: {error}")
                return False
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        with self._lock:
            task = self.tasks.get(task_id)
            if not task:
                return False
            
            if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED]:
                return False
            
            task.status = TaskStatus.CANCELLED
            task.completed_at = datetime.now()
            
            # 从队列中移除
            if task_id in self.task_queue:
                self.task_queue.remove(task_id)
            
            self.logger.info(f"任务已取消: {task.name}")
            return True
    
    def pause_task(self, task_id: str) -> bool:
        """暂停任务"""
        with self._lock:
            task = self.tasks.get(task_id)
            if not task:
                return False
            
            if task.status == TaskStatus.PENDING:
                task.status = TaskStatus.PAUSED
                self.logger.info(f"任务已暂停: {task.name}")
                return True
            
            return False
    
    def resume_task(self, task_id: str) -> bool:
        """恢复任务"""
        with self._lock:
            task = self.tasks.get(task_id)
            if not task:
                return False
            
            if task.status == TaskStatus.PAUSED:
                task.status = TaskStatus.PENDING
                self.logger.info(f"任务已恢复: {task.name}")
                return True
            
            return False
    
    def get_tasks_by_status(self, status: TaskStatus) -> List[Task]:
        """根据状态获取任务列表"""
        return [task for task in self.tasks.values() if task.status == status]
    
    def get_pending_tasks(self) -> List[Task]:
        """获取待执行任务列表"""
        return self.get_tasks_by_status(TaskStatus.PENDING)
    
    def get_running_tasks(self) -> List[Task]:
        """获取正在执行的任务列表"""
        return self.get_tasks_by_status(TaskStatus.RUNNING)
    
    def cleanup_completed_tasks(self, keep_recent: int = 100):
        """清理已完成的任务"""
        with self._lock:
            completed_tasks = [
                task for task in self.tasks.values() 
                if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]
            ]
            
            # 按完成时间排序，保留最近的任务
            completed_tasks.sort(key=lambda t: t.completed_at or datetime.min, reverse=True)
            
            tasks_to_remove = completed_tasks[keep_recent:]
            
            for task in tasks_to_remove:
                del self.tasks[task.task_id]
            
            if tasks_to_remove:
                self.logger.info(f"清理了 {len(tasks_to_remove)} 个已完成的任务")
    
    def _add_to_queue(self, task_id: str):
        """将任务添加到队列中（按优先级排序）"""
        task = self.tasks[task_id]
        
        # 找到合适的插入位置
        insert_index = 0
        for i, existing_task_id in enumerate(self.task_queue):
            existing_task = self.tasks[existing_task_id]
            if task.priority.value > existing_task.priority.value:
                insert_index = i
                break
            insert_index = i + 1
        
        self.task_queue.insert(insert_index, task_id)
    
    def _are_dependencies_satisfied(self, task: Task) -> bool:
        """检查任务依赖是否满足"""
        for dep_id in task.dependencies:
            dep_task = self.tasks.get(dep_id)
            if not dep_task or dep_task.status != TaskStatus.COMPLETED:
                return False
        return True
    
    def _check_dependent_tasks(self, completed_task_id: str):
        """检查并启用依赖已完成任务的任务"""
        completed_task = self.tasks.get(completed_task_id)
        if not completed_task:
            return
        
        for dependent_id in completed_task.dependents:
            dependent_task = self.tasks.get(dependent_id)
            if (dependent_task and 
                dependent_task.status == TaskStatus.PENDING and
                self._are_dependencies_satisfied(dependent_task)):
                
                self.logger.debug(f"任务 {dependent_task.name} 的依赖已满足")
    
    def get_manager_statistics(self) -> Dict[str, Any]:
        """获取管理器统计信息"""
        with self._lock:
            status_counts = {}
            for status in TaskStatus:
                status_counts[status.value] = len(self.get_tasks_by_status(status))
            
            return {
                "total_tasks": len(self.tasks),
                "total_created": self.total_tasks_created,
                "total_completed": self.total_tasks_completed,
                "total_failed": self.total_tasks_failed,
                "status_counts": status_counts,
                "queue_length": len(self.task_queue)
            }
    
    def get_manager_info(self) -> Dict[str, Any]:
        """获取管理器信息"""
        return {
            "task_count": len(self.tasks),
            "queue_length": len(self.task_queue),
            "statistics": self.get_manager_statistics()
        }

    def clear_all_tasks(self):
        """清空所有任务"""
        with self._lock:
            self.tasks.clear()
            self.task_queue.clear()
            self.logger.info("已清空所有任务")

    def export_tasks(self) -> List[Dict[str, Any]]:
        """导出任务数据"""
        with self._lock:
            exported_tasks = []
            for task in self.tasks.values():
                task_data = {
                    "task_id": task.task_id,
                    "name": task.name,
                    "description": task.description,
                    "priority": task.priority.value,
                    "status": task.status.value,
                    "created_at": task.created_at.isoformat(),
                    "scheduled_at": task.scheduled_at.isoformat() if task.scheduled_at else None,
                    "started_at": task.started_at.isoformat() if task.started_at else None,
                    "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                    "timeout": task.timeout,
                    "dependencies": task.dependencies,
                    "dependents": task.dependents,
                    "max_retries": task.max_retries,
                    "retry_count": task.retry_count,
                    "retry_delay": task.retry_delay,
                    "error": task.error,
                    "metadata": task.metadata
                }
                exported_tasks.append(task_data)

            return exported_tasks
