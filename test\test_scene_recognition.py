"""
场景识别功能测试脚本
测试场景识别器对主菜单界面的识别准确性
"""

import sys
import os
import time
import cv2
import numpy as np
from pathlib import Path
from datetime import datetime
from typing import Optional, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_scene_recognition():
    """主要的场景识别测试函数"""
    print("🧪 开始场景识别功能测试")
    print("=" * 60)
    
    test_results = {
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "template_file": "main_menu_full.png",
        "expected_scene": "主菜单界面",
        "success": False,
        "details": {}
    }
    
    try:
        # 步骤1: 导入必要的模块
        print("📦 导入模块...")
        from src.modules.perception.scene_recognizer import SceneRecognizer
        from src.modules.perception.screen_capture import ScreenCapture
        from src.core.data_structures import GameScene
        
        # 步骤2: 初始化组件
        print("🔧 初始化组件...")
        screen_capture = ScreenCapture("gakumas")
        scene_recognizer = SceneRecognizer("assets/templates")
        
        # 步骤3: 检查游戏窗口
        print("🎮 检查游戏窗口...")
        game_window = screen_capture.find_game_window()
        if not game_window:
            raise Exception("未找到游戏窗口，请确保游戏正在运行")
        
        window_info = screen_capture.get_window_info()
        print(f"   游戏窗口信息: {window_info}")
        
        # 步骤4: 捕获游戏截图
        print("📸 捕获游戏截图...")
        screenshot = screen_capture.capture_screen()
        if screenshot is None:
            raise Exception("截图捕获失败")
        
        print(f"   截图尺寸: {screenshot.shape}")
        test_results["details"]["screenshot_size"] = screenshot.shape
        
        # 步骤5: 保存测试截图（用于调试）
        test_screenshot_path = f"test/test_screenshot_{int(time.time())}.png"
        cv2.imwrite(test_screenshot_path, screenshot)
        print(f"   测试截图已保存: {test_screenshot_path}")
        test_results["details"]["test_screenshot"] = test_screenshot_path
        
        # 步骤6: 执行场景识别
        print("🔍 执行场景识别...")
        recognized_scene = scene_recognizer.recognize_scene(screenshot)
        
        print(f"   识别结果: {recognized_scene}")
        print(f"   场景值: {recognized_scene.value}")
        
        test_results["details"]["recognized_scene"] = recognized_scene.value
        test_results["details"]["recognized_scene_enum"] = str(recognized_scene)
        
        # 步骤7: 获取详细的场景特征信息
        print("📊 获取场景特征信息...")
        scene_features = scene_recognizer.get_scene_features(screenshot, GameScene.MAIN_MENU)
        print(f"   主菜单特征: {scene_features}")
        test_results["details"]["scene_features"] = scene_features
        
        # 步骤8: 验证识别结果
        print("✅ 验证识别结果...")
        if recognized_scene == GameScene.MAIN_MENU:
            print("   ✅ 场景识别成功！正确识别为主菜单界面")
            test_results["success"] = True
            test_results["details"]["result_message"] = "场景识别成功，正确识别为主菜单界面"
        else:
            print(f"   ❌ 场景识别失败！预期: {GameScene.MAIN_MENU.value}, 实际: {recognized_scene.value}")
            test_results["success"] = False
            test_results["details"]["result_message"] = f"场景识别失败，预期主菜单但识别为{recognized_scene.value}"
            
            # 获取所有场景的得分信息用于调试
            print("🔍 获取详细调试信息...")
            debug_info = get_debug_scene_scores(scene_recognizer, screenshot)
            test_results["details"]["debug_info"] = debug_info
        
        # 步骤9: 测试模板匹配详情
        print("🎯 测试模板匹配详情...")
        template_results = test_template_matching(scene_recognizer, screenshot)
        test_results["details"]["template_matching"] = template_results
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        test_results["success"] = False
        test_results["details"]["error"] = str(e)
        import traceback
        test_results["details"]["traceback"] = traceback.format_exc()
    
    # 输出测试结果摘要
    print("\n" + "=" * 60)
    print("📋 测试结果摘要")
    print("=" * 60)
    print(f"测试时间: {test_results['timestamp']}")
    print(f"模板文件: {test_results['template_file']}")
    print(f"预期场景: {test_results['expected_scene']}")
    print(f"测试结果: {'✅ 成功' if test_results['success'] else '❌ 失败'}")
    
    if not test_results["success"]:
        print(f"失败原因: {test_results['details'].get('result_message', '未知错误')}")
    
    return test_results

def get_debug_scene_scores(scene_recognizer, screenshot) -> Dict[str, Any]:
    """获取所有场景的调试得分信息"""
    debug_info = {}
    
    try:
        # 手动计算每个场景的得分
        for scene, rules in scene_recognizer.scene_rules.items():
            score = scene_recognizer._calculate_scene_score(screenshot, scene, rules)
            debug_info[scene.value] = {
                "score": score,
                "rules": rules,
                "threshold": scene_recognizer.confidence_threshold
            }
            print(f"   {scene.value}: {score:.3f}")
    except Exception as e:
        debug_info["error"] = str(e)
    
    return debug_info

def test_template_matching(scene_recognizer, screenshot) -> Dict[str, Any]:
    """测试具体的模板匹配结果"""
    template_results = {}
    
    try:
        # 测试主菜单相关的模板
        main_menu_templates = ["main_menu_full", "main_menu_logo", "produce_button", "part_time_job_button", "daily_tasks_button"]
        
        for template_name in main_menu_templates:
            print(f"   测试模板: {template_name}")
            match_result = scene_recognizer.template_matcher.match_template(
                screenshot, template_name, scene_recognizer.confidence_threshold
            )
            
            if match_result:
                template_results[template_name] = {
                    "found": True,
                    "confidence": match_result.confidence,
                    "position": (match_result.x, match_result.y),
                    "size": (match_result.width, match_result.height)
                }
                print(f"     ✅ 找到匹配 - 置信度: {match_result.confidence:.3f}")
            else:
                template_results[template_name] = {
                    "found": False,
                    "confidence": 0.0
                }
                print(f"     ❌ 未找到匹配")
    
    except Exception as e:
        template_results["error"] = str(e)
    
    return template_results

def cleanup_test_files(test_results: Dict[str, Any]):
    """清理测试过程中生成的文件"""
    print("\n🧹 清理测试文件...")
    
    files_to_clean = []
    
    # 添加测试截图文件
    if "test_screenshot" in test_results.get("details", {}):
        files_to_clean.append(test_results["details"]["test_screenshot"])
    
    # 清理文件
    cleaned_count = 0
    for file_path in files_to_clean:
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                print(f"   ✅ 已删除: {file_path}")
                cleaned_count += 1
            else:
                print(f"   ⚠️  文件不存在: {file_path}")
        except Exception as e:
            print(f"   ❌ 删除失败: {file_path} - {e}")
    
    print(f"   清理完成，共删除 {cleaned_count} 个文件")

if __name__ == "__main__":
    # 执行测试
    results = test_scene_recognition()
    
    # 询问是否清理测试文件
    if "test_screenshot" in results.get("details", {}):
        print(f"\n测试截图保存在: {results['details']['test_screenshot']}")
        user_input = input("是否删除测试截图文件？(y/n): ").lower().strip()
        if user_input in ['y', 'yes', '是']:
            cleanup_test_files(results)
        else:
            print("测试文件已保留，可用于调试分析")
    
    print("\n🎯 场景识别测试完成！")
