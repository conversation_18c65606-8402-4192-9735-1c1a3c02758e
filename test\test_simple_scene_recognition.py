"""
简化的场景识别测试脚本
仅使用现有的 main_menu_full.png 模板进行测试
"""

import sys
import os
import time
import cv2
import numpy as np
from pathlib import Path
from datetime import datetime
from typing import Optional, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_simple_template_matching():
    """简化的模板匹配测试"""
    print("🧪 开始简化场景识别测试")
    print("=" * 60)
    
    test_results = {
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "template_file": "main_menu_full.png",
        "success": False,
        "details": {}
    }
    
    try:
        # 步骤1: 导入必要的模块
        print("📦 导入模块...")
        from src.modules.perception.template_matcher import TemplateMatcher
        from src.modules.perception.screen_capture import ScreenCapture
        
        # 步骤2: 初始化组件
        print("🔧 初始化组件...")
        screen_capture = ScreenCapture("gakumas")
        template_matcher = TemplateMatcher("assets/templates")
        
        # 步骤3: 检查游戏窗口
        print("🎮 检查游戏窗口...")
        game_window = screen_capture.find_game_window()
        if not game_window:
            raise Exception("未找到游戏窗口，请确保游戏正在运行")
        
        window_info = screen_capture.get_window_info()
        print(f"   游戏窗口信息: {window_info}")
        
        # 步骤4: 捕获游戏截图
        print("📸 捕获游戏截图...")
        screenshot = screen_capture.capture_screen()
        if screenshot is None:
            raise Exception("截图捕获失败")
        
        print(f"   截图尺寸: {screenshot.shape}")
        test_results["details"]["screenshot_size"] = screenshot.shape
        
        # 步骤5: 保存测试截图
        test_screenshot_path = f"test/simple_test_screenshot_{int(time.time())}.png"
        cv2.imwrite(test_screenshot_path, screenshot)
        print(f"   测试截图已保存: {test_screenshot_path}")
        test_results["details"]["test_screenshot"] = test_screenshot_path
        
        # 步骤6: 检查模板文件是否存在
        print("📁 检查模板文件...")
        template_path = Path("assets/templates/main_menu_full.png")
        if not template_path.exists():
            raise Exception(f"模板文件不存在: {template_path}")
        
        print(f"   模板文件存在: {template_path}")
        
        # 步骤7: 加载模板
        print("🖼️ 加载模板...")
        template = template_matcher.load_template("main_menu_full")
        if template is None:
            raise Exception("模板加载失败")
        
        print(f"   模板尺寸: {template.shape}")
        test_results["details"]["template_size"] = template.shape
        
        # 步骤8: 执行模板匹配
        print("🔍 执行模板匹配...")
        
        # 尝试不同的置信度阈值
        confidence_thresholds = [0.9, 0.8, 0.7, 0.6, 0.5]
        best_match = None
        
        for threshold in confidence_thresholds:
            print(f"   尝试置信度阈值: {threshold}")
            match_result = template_matcher.match_template(
                screenshot, "main_menu_full", threshold
            )
            
            if match_result:
                print(f"   ✅ 找到匹配！置信度: {match_result.confidence:.3f}")
                best_match = match_result
                break
            else:
                print(f"   ❌ 未找到匹配（阈值: {threshold}）")
        
        # 步骤9: 分析结果
        if best_match:
            print("✅ 模板匹配成功！")
            test_results["success"] = True
            test_results["details"]["match_result"] = {
                "confidence": best_match.confidence,
                "position": (best_match.x, best_match.y),
                "size": (best_match.width, best_match.height),
                "template_name": best_match.template_name
            }
            
            # 在截图上标记匹配位置
            marked_screenshot = screenshot.copy()
            cv2.rectangle(marked_screenshot, 
                         (best_match.x, best_match.y),
                         (best_match.x + best_match.width, best_match.y + best_match.height),
                         (0, 255, 0), 2)
            
            marked_screenshot_path = f"test/marked_screenshot_{int(time.time())}.png"
            cv2.imwrite(marked_screenshot_path, marked_screenshot)
            print(f"   标记截图已保存: {marked_screenshot_path}")
            test_results["details"]["marked_screenshot"] = marked_screenshot_path
            
        else:
            print("❌ 模板匹配失败！")
            test_results["success"] = False
            test_results["details"]["result_message"] = "所有置信度阈值下都未找到匹配"
            
            # 执行详细分析
            print("🔍 执行详细分析...")
            detailed_analysis = analyze_template_matching_failure(
                screenshot, template, template_matcher
            )
            test_results["details"]["detailed_analysis"] = detailed_analysis
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        test_results["success"] = False
        test_results["details"]["error"] = str(e)
        import traceback
        test_results["details"]["traceback"] = traceback.format_exc()
    
    # 输出测试结果摘要
    print("\n" + "=" * 60)
    print("📋 测试结果摘要")
    print("=" * 60)
    print(f"测试时间: {test_results['timestamp']}")
    print(f"模板文件: {test_results['template_file']}")
    print(f"测试结果: {'✅ 成功' if test_results['success'] else '❌ 失败'}")
    
    if test_results["success"]:
        match_info = test_results["details"]["match_result"]
        print(f"匹配置信度: {match_info['confidence']:.3f}")
        print(f"匹配位置: {match_info['position']}")
        print(f"匹配尺寸: {match_info['size']}")
    else:
        print(f"失败原因: {test_results['details'].get('result_message', '未知错误')}")
    
    return test_results

def analyze_template_matching_failure(screenshot, template, template_matcher):
    """分析模板匹配失败的原因"""
    analysis = {}
    
    try:
        # 执行原始OpenCV匹配
        result = cv2.matchTemplate(screenshot, template, cv2.TM_CCOEFF_NORMED)
        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
        
        analysis["raw_opencv_result"] = {
            "max_confidence": max_val,
            "min_confidence": min_val,
            "max_location": max_loc,
            "min_location": min_loc
        }
        
        print(f"   原始OpenCV结果 - 最大置信度: {max_val:.3f}")
        print(f"   原始OpenCV结果 - 最大置信度位置: {max_loc}")
        
        # 检查图像尺寸比较
        screenshot_size = screenshot.shape[:2]
        template_size = template.shape[:2]
        
        analysis["size_comparison"] = {
            "screenshot_size": screenshot_size,
            "template_size": template_size,
            "size_ratio": (screenshot_size[0] / template_size[0], 
                          screenshot_size[1] / template_size[1])
        }
        
        print(f"   截图尺寸: {screenshot_size}")
        print(f"   模板尺寸: {template_size}")
        print(f"   尺寸比例: {analysis['size_comparison']['size_ratio']}")
        
        # 检查图像统计信息
        screenshot_stats = {
            "mean": np.mean(screenshot),
            "std": np.std(screenshot),
            "min": np.min(screenshot),
            "max": np.max(screenshot)
        }
        
        template_stats = {
            "mean": np.mean(template),
            "std": np.std(template),
            "min": np.min(template),
            "max": np.max(template)
        }
        
        analysis["image_stats"] = {
            "screenshot": screenshot_stats,
            "template": template_stats
        }
        
        print(f"   截图统计 - 均值: {screenshot_stats['mean']:.2f}, 标准差: {screenshot_stats['std']:.2f}")
        print(f"   模板统计 - 均值: {template_stats['mean']:.2f}, 标准差: {template_stats['std']:.2f}")
        
    except Exception as e:
        analysis["error"] = str(e)
        print(f"   分析过程中发生错误: {e}")
    
    return analysis

def cleanup_test_files(test_results: Dict[str, Any]):
    """清理测试过程中生成的文件"""
    print("\n🧹 清理测试文件...")
    
    files_to_clean = []
    
    # 添加测试截图文件
    if "test_screenshot" in test_results.get("details", {}):
        files_to_clean.append(test_results["details"]["test_screenshot"])
    
    if "marked_screenshot" in test_results.get("details", {}):
        files_to_clean.append(test_results["details"]["marked_screenshot"])
    
    # 清理文件
    cleaned_count = 0
    for file_path in files_to_clean:
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                print(f"   ✅ 已删除: {file_path}")
                cleaned_count += 1
            else:
                print(f"   ⚠️  文件不存在: {file_path}")
        except Exception as e:
            print(f"   ❌ 删除失败: {file_path} - {e}")
    
    print(f"   清理完成，共删除 {cleaned_count} 个文件")

if __name__ == "__main__":
    # 执行测试
    results = test_simple_template_matching()
    
    # 询问是否清理测试文件
    if "test_screenshot" in results.get("details", {}):
        print(f"\n测试文件保存位置:")
        for key in ["test_screenshot", "marked_screenshot"]:
            if key in results["details"]:
                print(f"  {key}: {results['details'][key]}")
        
        user_input = input("是否删除测试文件？(y/n): ").lower().strip()
        if user_input in ['y', 'yes', '是']:
            cleanup_test_files(results)
        else:
            print("测试文件已保留，可用于调试分析")
    
    print("\n🎯 简化场景识别测试完成！")
