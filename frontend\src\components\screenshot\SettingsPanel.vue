<template>
  <div class="settings-panel">
    <el-form 
      :model="localConfig" 
      label-width="120px"
      size="default"
    >
      <!-- 游戏窗口设置 -->
      <el-divider content-position="left">
        <el-icon><Monitor /></el-icon>
        游戏窗口设置
      </el-divider>
      
      <el-form-item label="窗口标题">
        <el-input 
          v-model="localConfig.game_window.window_title"
          placeholder="gakumas"
        />
        <div class="form-hint">
          用于识别游戏窗口的标题，支持部分匹配
        </div>
      </el-form-item>
      
      <el-form-item label="后台模式">
        <el-switch 
          v-model="localConfig.game_window.enable_background_mode"
          active-text="启用"
          inactive-text="禁用"
        />
        <div class="form-hint">
          启用后可以在游戏窗口被遮挡时进行截图
        </div>
      </el-form-item>
      
      <el-form-item label="捕获方法">
        <el-select 
          v-model="localConfig.game_window.capture_method"
          style="width: 100%"
        >
          <el-option label="自动选择" value="auto" />
          <el-option label="GDI" value="gdi" />
          <el-option label="DirectX" value="directx" />
          <el-option label="Windows Graphics Capture" value="wgc" />
        </el-select>
        <div class="form-hint">
          不同的捕获方法适用于不同的游戏和系统环境
        </div>
      </el-form-item>

      <!-- 存储设置 -->
      <el-divider content-position="left">
        <el-icon><FolderOpened /></el-icon>
        存储设置
      </el-divider>
      
      <el-form-item label="存储目录">
        <el-input 
          v-model="localConfig.storage.base_directory"
          placeholder="screenshots"
        >
          <template #append>
            <el-button @click="selectDirectory">
              <el-icon><Folder /></el-icon>
              选择
            </el-button>
          </template>
        </el-input>
        <div class="form-hint">
          截图文件的存储目录，相对于程序根目录
        </div>
      </el-form-item>
      
      <el-form-item label="历史记录数">
        <el-input-number 
          v-model="localConfig.storage.max_history_size"
          :min="100"
          :max="10000"
          :step="100"
          style="width: 100%"
        />
        <div class="form-hint">
          最多保留的截图历史记录数量，超出后自动删除最旧的记录
        </div>
      </el-form-item>
      
      <el-form-item label="自动清理">
        <el-switch 
          v-model="localConfig.storage.auto_cleanup"
          active-text="启用"
          inactive-text="禁用"
        />
        <div class="form-hint">
          启用后会自动删除过期的截图文件
        </div>
      </el-form-item>
      
      <el-form-item 
        label="清理天数" 
        v-if="localConfig.storage.auto_cleanup"
      >
        <el-input-number 
          v-model="localConfig.storage.cleanup_days"
          :min="1"
          :max="365"
          style="width: 100%"
        />
        <div class="form-hint">
          超过指定天数的截图文件将被自动删除
        </div>
      </el-form-item>

      <!-- 预览设置 -->
      <el-divider content-position="left">
        <el-icon><VideoPlay /></el-icon>
        预览设置
      </el-divider>
      
      <el-form-item label="预览帧率">
        <el-slider 
          v-model="localConfig.preview_fps"
          :min="1"
          :max="10"
          :step="1"
          show-input
          :show-input-controls="false"
        />
        <div class="form-hint">
          实时预览的刷新频率，数值越高越流畅但消耗更多资源
        </div>
      </el-form-item>

      <!-- 默认设置 -->
      <el-divider content-position="left">
        <el-icon><Setting /></el-icon>
        默认设置
      </el-divider>
      
      <el-form-item label="默认质量">
        <el-slider 
          v-model="localConfig.default_quality"
          :min="10"
          :max="100"
          :step="5"
          show-input
          :show-input-controls="false"
        />
        <div class="form-hint">
          新建截图任务时的默认图片质量
        </div>
      </el-form-item>
      
      <el-form-item label="默认格式">
        <el-select 
          v-model="localConfig.default_format"
          style="width: 100%"
        >
          <el-option label="PNG (无损)" value="png" />
          <el-option label="JPEG (有损)" value="jpeg" />
          <el-option label="BMP (无压缩)" value="bmp" />
        </el-select>
        <div class="form-hint">
          新建截图任务时的默认图片格式
        </div>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <div class="settings-actions">
      <el-button @click="resetToDefaults">
        <el-icon><RefreshLeft /></el-icon>
        恢复默认
      </el-button>
      <el-button @click="exportSettings">
        <el-icon><Download /></el-icon>
        导出配置
      </el-button>
      <el-button @click="importSettings">
        <el-icon><Upload /></el-icon>
        导入配置
      </el-button>
      <div class="actions-right">
        <el-button @click="$emit('cancel')">
          取消
        </el-button>
        <el-button type="primary" @click="saveSettings">
          <el-icon><Check /></el-icon>
          保存设置
        </el-button>
      </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input 
      ref="fileInput" 
      type="file" 
      accept=".json"
      style="display: none"
      @change="handleFileImport"
    />
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Monitor, 
  FolderOpened, 
  Folder, 
  VideoPlay, 
  Setting,
  RefreshLeft,
  Download,
  Upload,
  Check
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  config: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['save', 'cancel'])

// 响应式数据
const fileInput = ref(null)
const localConfig = reactive({
  game_window: {
    window_title: 'gakumas',
    enable_background_mode: true,
    capture_method: 'auto'
  },
  storage: {
    base_directory: 'screenshots',
    max_history_size: 1000,
    auto_cleanup: true,
    cleanup_days: 30
  },
  preview_fps: 2,
  default_quality: 90,
  default_format: 'png'
})

// 默认配置
const defaultConfig = {
  game_window: {
    window_title: 'gakumas',
    enable_background_mode: true,
    capture_method: 'auto'
  },
  storage: {
    base_directory: 'screenshots',
    max_history_size: 1000,
    auto_cleanup: true,
    cleanup_days: 30
  },
  preview_fps: 2,
  default_quality: 90,
  default_format: 'png'
}

// 监听props变化
watch(() => props.config, (newConfig) => {
  Object.assign(localConfig, newConfig)
}, { immediate: true, deep: true })

// 方法
function selectDirectory() {
  // 这里可以集成文件选择器
  ElMessage.info('文件选择器功能需要后端支持')
}

function resetToDefaults() {
  ElMessageBox.confirm('确定要恢复所有设置到默认值吗？', '恢复默认', {
    type: 'warning'
  }).then(() => {
    Object.assign(localConfig, JSON.parse(JSON.stringify(defaultConfig)))
    ElMessage.success('已恢复默认设置')
  }).catch(() => {
    // 用户取消
  })
}

function exportSettings() {
  try {
    const configJson = JSON.stringify(localConfig, null, 2)
    const blob = new Blob([configJson], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    
    const a = document.createElement('a')
    a.href = url
    a.download = `screenshot-tool-config-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    ElMessage.success('配置已导出')
  } catch (error) {
    ElMessage.error('导出配置失败: ' + error.message)
  }
}

function importSettings() {
  fileInput.value?.click()
}

function handleFileImport(event) {
  const file = event.target.files[0]
  if (!file) return
  
  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const importedConfig = JSON.parse(e.target.result)
      
      // 验证配置结构
      if (validateConfig(importedConfig)) {
        Object.assign(localConfig, importedConfig)
        ElMessage.success('配置导入成功')
      } else {
        ElMessage.error('配置文件格式不正确')
      }
    } catch (error) {
      ElMessage.error('配置文件解析失败: ' + error.message)
    }
  }
  
  reader.readAsText(file)
  
  // 清空文件输入
  event.target.value = ''
}

function validateConfig(config) {
  // 简单的配置验证
  return config && 
         config.game_window && 
         config.storage && 
         typeof config.preview_fps === 'number' &&
         typeof config.default_quality === 'number' &&
         typeof config.default_format === 'string'
}

function saveSettings() {
  try {
    // 验证配置
    if (localConfig.storage.max_history_size < 100) {
      ElMessage.error('历史记录数量不能少于100')
      return
    }
    
    if (localConfig.preview_fps < 1 || localConfig.preview_fps > 10) {
      ElMessage.error('预览帧率必须在1-10之间')
      return
    }
    
    if (localConfig.default_quality < 10 || localConfig.default_quality > 100) {
      ElMessage.error('默认质量必须在10-100之间')
      return
    }
    
    // 发送保存事件
    emit('save', JSON.parse(JSON.stringify(localConfig)))
    
  } catch (error) {
    ElMessage.error('保存设置失败: ' + error.message)
  }
}
</script>

<style scoped>
.settings-panel {
  max-height: 70vh;
  overflow-y: auto;
}

.form-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.el-divider {
  margin: 24px 0 16px 0;
}

.el-divider :deep(.el-divider__text) {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #409eff;
}

.settings-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  padding-top: 24px;
  border-top: 1px solid #e9ecef;
  margin-top: 24px;
}

.actions-right {
  margin-left: auto;
  display: flex;
  gap: 12px;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-input-number {
  width: 100%;
}

.el-slider {
  margin: 12px 0;
}

/* 滚动条样式 */
.settings-panel::-webkit-scrollbar {
  width: 6px;
}

.settings-panel::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.settings-panel::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.settings-panel::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
