"""
场景识别器
负责识别当前游戏场景
"""

import cv2
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path

from ...utils.logger import get_logger
from ...core.data_structures import GameScene
from .template_matcher import TemplateMatcher, MatchResult


class SceneRecognizer:
    """场景识别器类"""
    
    def __init__(self, templates_dir: str = "assets/templates"):
        """
        初始化场景识别器
        
        Args:
            templates_dir: 模板图片目录
        """
        self.logger = get_logger("SceneRecognizer")
        self.template_matcher = TemplateMatcher(templates_dir)
        
        # 场景识别规则配置
        self.scene_rules = self._init_scene_rules()
        
        # 识别参数
        self.confidence_threshold = 0.7
        self.min_matches_required = 1  # 最少需要匹配的特征数量
        
        self.logger.info("场景识别器初始化完成")
    
    def _init_scene_rules(self) -> Dict[GameScene, Dict[str, Any]]:
        """
        初始化场景识别规则
        
        Returns:
            场景识别规则字典
        """
        return {
            GameScene.MAIN_MENU: {
                "required_templates": ["main_menu_full", "main_menu_logo"],
                "optional_templates": ["produce_button", "part_time_job_button", "daily_tasks_button"],
                "min_required": 1,
                "description": "主菜单界面"
            },
            GameScene.PRODUCE_SETUP: {
                "required_templates": ["produce_setup_title", "idol_selection"],
                "optional_templates": ["support_card_selection", "start_produce_button"],
                "min_required": 1,
                "description": "育成准备界面"
            },
            GameScene.PRODUCE_MAIN: {
                "required_templates": ["week_indicator", "stamina_bar"],
                "optional_templates": ["lesson_buttons", "rest_button", "outing_button"],
                "min_required": 1,
                "description": "育成主界面"
            },
            GameScene.PRODUCE_BATTLE: {
                "required_templates": ["battle_ui", "card_hand"],
                "optional_templates": ["score_display", "turn_indicator"],
                "min_required": 1,
                "description": "育成战斗界面"
            },
            GameScene.PRODUCE_EXAM: {
                "required_templates": ["exam_title", "exam_ui"],
                "optional_templates": ["exam_score", "exam_cards"],
                "min_required": 1,
                "description": "考试界面"
            },
            GameScene.PRODUCE_RESULT: {
                "required_templates": ["result_title", "final_score"],
                "optional_templates": ["idol_stats", "result_buttons"],
                "min_required": 1,
                "description": "育成结果界面"
            },
            GameScene.PART_TIME_JOB: {
                "required_templates": ["part_time_job_title"],
                "optional_templates": ["job_selection", "job_timer"],
                "min_required": 1,
                "description": "打工界面"
            },
            GameScene.DAILY_TASKS: {
                "required_templates": ["daily_tasks_title"],
                "optional_templates": ["task_list", "reward_buttons"],
                "min_required": 1,
                "description": "日常任务界面"
            }
        }
    
    def recognize_scene(self, image: np.ndarray) -> GameScene:
        """
        识别当前游戏场景
        
        Args:
            image: 游戏截图
            
        Returns:
            识别出的游戏场景
        """
        scene_scores = {}
        
        # 对每个场景进行评分
        for scene, rules in self.scene_rules.items():
            score = self._calculate_scene_score(image, scene, rules)
            scene_scores[scene] = score
            
            self.logger.debug(f"场景 {scene.value} 得分: {score:.3f}")
        
        # 选择得分最高的场景
        best_scene = max(scene_scores, key=scene_scores.get)
        best_score = scene_scores[best_scene]
        
        # 如果最高得分太低，返回未知场景
        if best_score < self.confidence_threshold:
            self.logger.warning(f"场景识别置信度过低: {best_score:.3f}")
            return GameScene.UNKNOWN
        
        self.logger.info(f"识别场景: {best_scene.value} (置信度: {best_score:.3f})")
        return best_scene
    
    def _calculate_scene_score(self, image: np.ndarray, scene: GameScene, rules: Dict[str, Any]) -> float:
        """
        计算场景匹配得分
        
        Args:
            image: 游戏截图
            scene: 场景类型
            rules: 场景识别规则
            
        Returns:
            场景匹配得分 (0.0-1.0)
        """
        required_templates = rules.get("required_templates", [])
        optional_templates = rules.get("optional_templates", [])
        min_required = rules.get("min_required", 1)
        
        all_templates = required_templates + optional_templates
        
        if not all_templates:
            return 0.0
        
        # 匹配所有模板
        matches = self.template_matcher.match_multiple_templates(
            image, all_templates, self.confidence_threshold
        )
        
        if not matches:
            return 0.0
        
        # 计算必需模板的匹配情况
        required_matches = [m for m in matches if m.template_name in required_templates]
        optional_matches = [m for m in matches if m.template_name in optional_templates]
        
        # 检查是否满足最小匹配要求
        if len(required_matches) < min_required:
            return 0.0
        
        # 计算综合得分
        total_confidence = 0.0
        total_weight = 0.0
        
        # 必需模板权重更高
        for match in required_matches:
            total_confidence += match.confidence * 2.0  # 必需模板权重为2
            total_weight += 2.0
        
        # 可选模板权重较低
        for match in optional_matches:
            total_confidence += match.confidence * 1.0  # 可选模板权重为1
            total_weight += 1.0
        
        if total_weight == 0:
            return 0.0
        
        # 计算平均置信度
        average_confidence = total_confidence / total_weight
        
        # 根据匹配数量给予奖励
        match_bonus = min(len(matches) / len(all_templates), 1.0) * 0.1
        
        final_score = min(average_confidence + match_bonus, 1.0)
        
        return final_score
    
    def get_scene_features(self, image: np.ndarray, scene: GameScene) -> Dict[str, Any]:
        """
        获取指定场景的特征信息
        
        Args:
            image: 游戏截图
            scene: 场景类型
            
        Returns:
            场景特征信息字典
        """
        if scene not in self.scene_rules:
            return {}
        
        rules = self.scene_rules[scene]
        all_templates = rules.get("required_templates", []) + rules.get("optional_templates", [])
        
        # 匹配所有相关模板
        matches = self.template_matcher.match_multiple_templates(
            image, all_templates, self.confidence_threshold
        )
        
        features = {
            "scene": scene.value,
            "description": rules.get("description", ""),
            "matches": [
                {
                    "template": match.template_name,
                    "position": (match.x, match.y),
                    "center": (match.center_x, match.center_y),
                    "size": (match.width, match.height),
                    "confidence": match.confidence
                }
                for match in matches
            ],
            "match_count": len(matches),
            "total_templates": len(all_templates)
        }
        
        return features
    
    def detect_ui_elements(self, image: np.ndarray, element_templates: List[str]) -> Dict[str, MatchResult]:
        """
        检测指定的UI元素
        
        Args:
            image: 游戏截图
            element_templates: UI元素模板名称列表
            
        Returns:
            UI元素匹配结果字典
        """
        ui_elements = {}
        
        for template_name in element_templates:
            match_result = self.template_matcher.match_template(
                image, template_name, self.confidence_threshold
            )
            
            if match_result:
                ui_elements[template_name] = match_result
                self.logger.debug(f"检测到UI元素: {template_name}")
        
        return ui_elements
    
    def is_scene_stable(self, images: List[np.ndarray], target_scene: GameScene, 
                       stability_threshold: float = 0.8) -> bool:
        """
        检查场景是否稳定（连续多帧都识别为同一场景）
        
        Args:
            images: 连续的游戏截图列表
            target_scene: 目标场景
            stability_threshold: 稳定性阈值
            
        Returns:
            场景是否稳定
        """
        if not images:
            return False
        
        correct_count = 0
        
        for image in images:
            recognized_scene = self.recognize_scene(image)
            if recognized_scene == target_scene:
                correct_count += 1
        
        stability_ratio = correct_count / len(images)
        
        return stability_ratio >= stability_threshold
    
    def get_recognition_info(self) -> Dict[str, Any]:
        """
        获取识别器信息
        
        Returns:
            识别器信息字典
        """
        return {
            "supported_scenes": [scene.value for scene in self.scene_rules.keys()],
            "confidence_threshold": self.confidence_threshold,
            "min_matches_required": self.min_matches_required,
            "template_matcher_info": self.template_matcher.get_template_info()
        }
