<template>
  <div id="app">
    <el-container class="app-container">
      <el-header class="app-header">
        <div class="header-left">
          <h1>Gakumasu-Bot 控制面板</h1>
        </div>
        <div class="header-nav">
          <el-menu
            :default-active="$route.path"
            mode="horizontal"
            router
            class="nav-menu"
          >
            <el-menu-item index="/">
              <el-icon><House /></el-icon>
              <span>首页</span>
            </el-menu-item>
            <el-menu-item index="/screenshot-tool">
              <el-icon><Camera /></el-icon>
              <span>截图工具</span>
            </el-menu-item>
            <el-menu-item index="/debug-working">
              <el-icon><Tools /></el-icon>
              <span>调试控制台</span>
            </el-menu-item>
          </el-menu>
        </div>
      </el-header>
      <el-main class="app-main">
        <router-view />
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { House, Camera, Tools } from '@element-plus/icons-vue'
// 主应用组件
</script>

<style scoped>
.app-container {
  height: 100vh;
}

.app-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fff;
  border-bottom: 1px solid #e6e6e6;
  padding: 0 20px;
}

.header-left h1 {
  margin: 0;
  color: #409eff;
  font-size: 24px;
}

.header-nav {
  flex: 1;
  display: flex;
  justify-content: center;
  max-width: 600px;
}

.nav-menu {
  border-bottom: none;
  background: transparent;
}

.nav-menu .el-menu-item {
  font-size: 16px;
  font-weight: 500;
  margin: 0 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.nav-menu .el-menu-item:hover {
  background: #f0f8ff;
  color: #409eff;
}

.nav-menu .el-menu-item.is-active {
  background: #409eff;
  color: white;
}

.nav-menu .el-menu-item .el-icon {
  margin-right: 6px;
}

.app-main {
  background: #f5f5f5;
  padding: 20px;
}
</style>
