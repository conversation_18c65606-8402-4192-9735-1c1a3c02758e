"""
场景管理器
提供场景的管理、导航和状态跟踪功能
"""

import time
from typing import Dict, Optional, List, Any, Callable
from collections import defaultdict, deque
from enum import Enum

from ....utils.logger import get_logger
from ....core.data_structures import GameScene
from ..base.base_scene import BaseScene
from ..config.scene_config import SceneConfig
from .scene_factory import SceneFactory, EnhancedSceneFactory


class NavigationResult(Enum):
    """导航结果枚举"""
    SUCCESS = "success"
    TIMEOUT = "timeout"
    FAILED = "failed"
    ALREADY_AT_TARGET = "already_at_target"
    INVALID_TARGET = "invalid_target"


class SceneManager:
    """场景管理器 - 使用传统Class管理复杂场景逻辑"""
    
    def __init__(self, scene_factory: SceneFactory):
        """
        初始化场景管理器
        
        Args:
            scene_factory: 场景工厂实例
        """
        self.scene_factory = scene_factory
        self.logger = get_logger("SceneManager")
        
        # 场景缓存
        self._scene_cache: Dict[GameScene, BaseScene] = {}
        self._cache_enabled = True
        
        # 导航历史和统计
        self._navigation_history: deque = deque(maxlen=100)
        self._navigation_stats = defaultdict(list)
        
        # 当前场景跟踪
        self._current_scene: Optional[BaseScene] = None
        self._last_scene_check_time = 0.0
        self._scene_check_interval = 1.0  # 场景检查间隔（秒）
        
        # 导航路径缓存
        self._navigation_paths: Dict[tuple, List[GameScene]] = {}
        
        # 错误恢复
        self._error_recovery_enabled = True
        self._max_recovery_attempts = 3
    
    def get_scene(self, scene_type: GameScene, use_cache: bool = True) -> Optional[BaseScene]:
        """
        获取场景实例
        
        Args:
            scene_type: 场景类型
            use_cache: 是否使用缓存
            
        Returns:
            场景实例
        """
        if use_cache and self._cache_enabled and scene_type in self._scene_cache:
            return self._scene_cache[scene_type]
        
        scene = self.scene_factory.create_scene(scene_type)
        if scene and use_cache and self._cache_enabled:
            self._scene_cache[scene_type] = scene
        
        return scene
    
    def get_current_scene(self) -> Optional[BaseScene]:
        """
        获取当前场景实例
        
        Returns:
            当前场景实例，如果无法识别返回None
        """
        current_time = time.time()
        
        # 检查是否需要更新当前场景
        if (current_time - self._last_scene_check_time > self._scene_check_interval or 
            self._current_scene is None):
            
            self._update_current_scene()
            self._last_scene_check_time = current_time
        
        return self._current_scene
    
    def _update_current_scene(self):
        """更新当前场景"""
        try:
            # 尝试识别当前场景
            for scene_type in GameScene:
                scene = self.get_scene(scene_type)
                if scene and scene.is_current_scene():
                    if self._current_scene != scene:
                        self.logger.info(f"场景切换: {self._current_scene.config.scene_name if self._current_scene else 'None'} -> {scene.config.scene_name}")
                        self._current_scene = scene
                    return
            
            # 如果没有识别到任何场景
            if self._current_scene is not None:
                self.logger.warning("无法识别当前场景")
                self._current_scene = None
                
        except Exception as e:
            self.logger.error(f"更新当前场景失败: {e}")
    
    def navigate_to_scene(self, target_scene: GameScene, timeout: float = 30.0) -> NavigationResult:
        """
        导航到指定场景
        
        Args:
            target_scene: 目标场景
            timeout: 导航超时时间
            
        Returns:
            导航结果
        """
        start_time = time.time()
        
        try:
            self.logger.info(f"开始导航到场景: {target_scene.value}")
            
            # 检查目标场景是否有效
            if not self.scene_factory.is_scene_supported(target_scene):
                self.logger.error(f"不支持的目标场景: {target_scene.value}")
                return NavigationResult.INVALID_TARGET
            
            # 获取当前场景
            current_scene = self.get_current_scene()
            if current_scene and current_scene.config.scene_type == target_scene:
                self.logger.info(f"已在目标场景: {target_scene.value}")
                return NavigationResult.ALREADY_AT_TARGET
            
            # 执行导航
            result = self._execute_navigation(current_scene, target_scene, timeout)
            
            # 记录导航历史
            navigation_record = {
                'from_scene': current_scene.config.scene_type if current_scene else None,
                'to_scene': target_scene,
                'result': result,
                'start_time': start_time,
                'end_time': time.time(),
                'duration': time.time() - start_time
            }
            
            self._navigation_history.append(navigation_record)
            self._navigation_stats[target_scene].append(navigation_record)
            
            # 保持统计数据在合理范围内
            if len(self._navigation_stats[target_scene]) > 50:
                self._navigation_stats[target_scene] = self._navigation_stats[target_scene][-50:]
            
            return result
            
        except Exception as e:
            self.logger.error(f"导航过程异常: {e}")
            return NavigationResult.FAILED
    
    def _execute_navigation(self, current_scene: Optional[BaseScene], 
                           target_scene: GameScene, timeout: float) -> NavigationResult:
        """执行导航逻辑"""
        try:
            # 如果有当前场景，使用场景的导航方法
            if current_scene:
                success = current_scene.navigate_to_scene(target_scene, timeout)
                if success:
                    # 验证导航结果
                    if self._verify_navigation_result(target_scene):
                        return NavigationResult.SUCCESS
                    else:
                        self.logger.warning("导航后验证失败")
                        return NavigationResult.FAILED
                else:
                    return NavigationResult.FAILED
            else:
                # 没有当前场景，尝试直接导航
                return self._direct_navigation(target_scene, timeout)
                
        except Exception as e:
            self.logger.error(f"执行导航失败: {e}")
            return NavigationResult.FAILED
    
    def _direct_navigation(self, target_scene: GameScene, timeout: float) -> NavigationResult:
        """直接导航（当无法识别当前场景时）"""
        try:
            self.logger.info(f"尝试直接导航到: {target_scene.value}")
            
            # 获取目标场景
            target_scene_obj = self.get_scene(target_scene)
            if not target_scene_obj:
                return NavigationResult.FAILED
            
            # 等待目标场景出现
            if target_scene_obj.wait_for_scene(timeout):
                return NavigationResult.SUCCESS
            else:
                return NavigationResult.TIMEOUT
                
        except Exception as e:
            self.logger.error(f"直接导航失败: {e}")
            return NavigationResult.FAILED
    
    def _verify_navigation_result(self, target_scene: GameScene, timeout: float = 5.0) -> bool:
        """验证导航结果"""
        try:
            start_time = time.time()
            
            while time.time() - start_time < timeout:
                # 强制更新当前场景
                self._update_current_scene()
                
                if (self._current_scene and 
                    self._current_scene.config.scene_type == target_scene):
                    return True
                
                time.sleep(0.2)
            
            return False
            
        except Exception as e:
            self.logger.error(f"验证导航结果失败: {e}")
            return False
    
    def wait_for_scene(self, scene_type: GameScene, timeout: float = 10.0) -> bool:
        """
        等待指定场景出现
        
        Args:
            scene_type: 场景类型
            timeout: 等待超时时间
            
        Returns:
            是否成功等待到场景
        """
        scene = self.get_scene(scene_type)
        if scene:
            return scene.wait_for_scene(timeout)
        return False
    
    def is_scene_available(self, scene_type: GameScene) -> bool:
        """检查场景是否可用"""
        scene = self.get_scene(scene_type)
        return scene is not None and scene.is_current_scene()
    
    def get_navigation_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取导航历史
        
        Args:
            limit: 返回记录数量限制
            
        Returns:
            导航历史记录列表
        """
        history = list(self._navigation_history)
        return history[-limit:] if limit > 0 else history
    
    def get_navigation_stats(self, scene_type: GameScene = None) -> Dict[str, Any]:
        """
        获取导航统计
        
        Args:
            scene_type: 指定场景类型，None表示获取所有统计
            
        Returns:
            导航统计信息
        """
        if scene_type:
            records = self._navigation_stats.get(scene_type, [])
            if not records:
                return {}
            
            durations = [r['duration'] for r in records]
            success_count = sum(1 for r in records if r['result'] == NavigationResult.SUCCESS)
            
            return {
                'scene_type': scene_type.value,
                'total_navigations': len(records),
                'success_rate': success_count / len(records),
                'average_duration': sum(durations) / len(durations),
                'min_duration': min(durations),
                'max_duration': max(durations)
            }
        else:
            # 返回所有场景的统计
            all_stats = {}
            for scene, records in self._navigation_stats.items():
                if records:
                    durations = [r['duration'] for r in records]
                    success_count = sum(1 for r in records if r['result'] == NavigationResult.SUCCESS)
                    
                    all_stats[scene.value] = {
                        'total_navigations': len(records),
                        'success_rate': success_count / len(records),
                        'average_duration': sum(durations) / len(durations),
                        'min_duration': min(durations),
                        'max_duration': max(durations)
                    }
            
            return all_stats
    
    def clear_cache(self):
        """清理场景缓存"""
        self._scene_cache.clear()
        self._current_scene = None
        self.logger.info("场景管理器缓存已清理")
    
    def clear_navigation_history(self):
        """清理导航历史"""
        self._navigation_history.clear()
        self._navigation_stats.clear()
        self.logger.info("导航历史已清理")
    
    def set_cache_enabled(self, enabled: bool):
        """设置缓存启用状态"""
        self._cache_enabled = enabled
        if not enabled:
            self.clear_cache()
        self.logger.info(f"场景缓存{'启用' if enabled else '禁用'}")
    
    def set_scene_check_interval(self, interval: float):
        """设置场景检查间隔"""
        self._scene_check_interval = max(0.1, interval)
        self.logger.info(f"场景检查间隔设置为: {self._scene_check_interval}秒")
    
    def get_manager_stats(self) -> Dict[str, Any]:
        """获取管理器统计信息"""
        return {
            'cache_enabled': self._cache_enabled,
            'cached_scenes': len(self._scene_cache),
            'current_scene': self._current_scene.config.scene_name if self._current_scene else None,
            'navigation_history_count': len(self._navigation_history),
            'tracked_scenes': len(self._navigation_stats),
            'scene_check_interval': self._scene_check_interval,
            'last_scene_check_time': self._last_scene_check_time
        }


class EnhancedSceneManager(SceneManager):
    """增强场景管理器 - 提供更多高级功能"""

    def __init__(self, scene_factory: EnhancedSceneFactory):
        super().__init__(scene_factory)

        # 增强功能
        self._auto_recovery_enabled = True
        self._scene_change_callbacks: List[Callable] = []
        self._navigation_optimization = True

        # 智能导航路径
        self._optimal_paths: Dict[tuple, List[GameScene]] = {}
        self._path_learning_enabled = True

    def add_scene_change_callback(self, callback: Callable[[Optional[BaseScene], Optional[BaseScene]], None]):
        """添加场景变化回调"""
        self._scene_change_callbacks.append(callback)

    def _update_current_scene(self):
        """更新当前场景（增强版）"""
        old_scene = self._current_scene
        super()._update_current_scene()

        # 触发场景变化回调
        if old_scene != self._current_scene:
            for callback in self._scene_change_callbacks:
                try:
                    callback(old_scene, self._current_scene)
                except Exception as e:
                    self.logger.error(f"场景变化回调失败: {e}")

    def smart_navigate(self, target_scene: GameScene, timeout: float = 30.0) -> NavigationResult:
        """
        智能导航 - 使用学习到的最优路径

        Args:
            target_scene: 目标场景
            timeout: 导航超时时间

        Returns:
            导航结果
        """
        current_scene = self.get_current_scene()
        current_scene_type = current_scene.config.scene_type if current_scene else None

        # 检查是否有学习到的最优路径
        path_key = (current_scene_type, target_scene)
        if self._navigation_optimization and path_key in self._optimal_paths:
            return self._navigate_via_path(self._optimal_paths[path_key], timeout)
        else:
            # 使用标准导航
            result = self.navigate_to_scene(target_scene, timeout)

            # 学习成功的导航路径
            if result == NavigationResult.SUCCESS and self._path_learning_enabled:
                self._learn_navigation_path(current_scene_type, target_scene)

            return result

    def _navigate_via_path(self, path: List[GameScene], timeout: float) -> NavigationResult:
        """通过指定路径导航"""
        try:
            total_timeout = timeout
            start_time = time.time()

            for i, intermediate_scene in enumerate(path[:-1]):
                remaining_timeout = total_timeout - (time.time() - start_time)
                if remaining_timeout <= 0:
                    return NavigationResult.TIMEOUT

                step_timeout = min(remaining_timeout / (len(path) - i), 10.0)
                result = self.navigate_to_scene(intermediate_scene, step_timeout)

                if result != NavigationResult.SUCCESS:
                    self.logger.warning(f"路径导航在中间步骤失败: {intermediate_scene.value}")
                    return result

            # 导航到最终目标
            remaining_timeout = total_timeout - (time.time() - start_time)
            return self.navigate_to_scene(path[-1], remaining_timeout)

        except Exception as e:
            self.logger.error(f"路径导航失败: {e}")
            return NavigationResult.FAILED

    def _learn_navigation_path(self, from_scene: Optional[GameScene], to_scene: GameScene):
        """学习导航路径"""
        if from_scene is None:
            return

        path_key = (from_scene, to_scene)

        # 简单的路径学习：记录直接路径
        if path_key not in self._optimal_paths:
            self._optimal_paths[path_key] = [to_scene]
            self.logger.debug(f"学习到新的导航路径: {from_scene.value} -> {to_scene.value}")

    def get_optimal_path(self, from_scene: GameScene, to_scene: GameScene) -> Optional[List[GameScene]]:
        """获取最优导航路径"""
        path_key = (from_scene, to_scene)
        return self._optimal_paths.get(path_key)

    def set_navigation_optimization(self, enabled: bool):
        """设置导航优化启用状态"""
        self._navigation_optimization = enabled
        self.logger.info(f"导航优化{'启用' if enabled else '禁用'}")

    def set_path_learning(self, enabled: bool):
        """设置路径学习启用状态"""
        self._path_learning_enabled = enabled
        self.logger.info(f"路径学习{'启用' if enabled else '禁用'}")

    def clear_learned_paths(self):
        """清理学习到的路径"""
        self._optimal_paths.clear()
        self.logger.info("学习到的导航路径已清理")

    def get_enhanced_stats(self) -> Dict[str, Any]:
        """获取增强统计信息"""
        base_stats = self.get_manager_stats()
        enhanced_stats = {
            'auto_recovery_enabled': self._auto_recovery_enabled,
            'scene_change_callbacks': len(self._scene_change_callbacks),
            'navigation_optimization': self._navigation_optimization,
            'path_learning_enabled': self._path_learning_enabled,
            'learned_paths': len(self._optimal_paths),
            'optimal_paths': {
                f"{k[0].value if k[0] else 'None'} -> {k[1].value}": v
                for k, v in self._optimal_paths.items()
            }
        }

        return {**base_stats, **enhanced_stats}
