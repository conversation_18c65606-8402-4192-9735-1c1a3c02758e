#!/usr/bin/env python3
"""
Gakumasu-Bot 快速配置设置工具
帮助用户快速生成适合的配置文件
"""

import os
import sys
import yaml
import shutil
from pathlib import Path
from typing import Dict, Any

def detect_dmm_player_path() -> str:
    """自动检测DMM Player路径"""
    possible_paths = [
        os.path.expanduser("~/AppData/Local/DMM Game Player/DMMGamePlayer.exe"),
        "C:/Users/<USER>/AppData/Local/DMM Game Player/DMMGamePlayer.exe",
        "C:/Program Files/DMM Game Player/DMMGamePlayer.exe",
        "C:/Program Files (x86)/DMM Game Player/DMMGamePlayer.exe",
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    
    return ""

def get_system_specs() -> Dict[str, Any]:
    """获取系统规格信息"""
    import psutil
    
    # 获取CPU核心数
    cpu_count = psutil.cpu_count()
    
    # 获取内存大小（GB）
    memory_gb = psutil.virtual_memory().total / (1024**3)
    
    # 检查是否有NVIDIA GPU
    has_nvidia_gpu = False
    try:
        import GPUtil
        gpus = GPUtil.getGPUs()
        has_nvidia_gpu = len(gpus) > 0 and any("NVIDIA" in gpu.name for gpu in gpus)
    except ImportError:
        # 如果没有GPUtil，尝试其他方法检测
        pass
    
    return {
        'cpu_count': cpu_count,
        'memory_gb': memory_gb,
        'has_nvidia_gpu': has_nvidia_gpu
    }

def generate_optimized_config(system_specs: Dict[str, Any], user_preferences: Dict[str, Any]) -> Dict[str, Any]:
    """根据系统规格和用户偏好生成优化配置"""
    
    # 基础配置
    config = {
        'system': {
            'log_level': 'INFO',
            'language': user_preferences.get('language', 'ja'),
            'screen_resolution': user_preferences.get('resolution', [1920, 1080]),
            'game_window_title': 'gakumas'
        },
        'dmm': {
            'dmm_player_path': user_preferences.get('dmm_path', ''),
            'launch_timeout': 60,
            'auto_launch': True
        },
        'performance': {},
        'template_matching': {},
        'ocr': {
            'enable_ocr': True,
            'languages': ['ja', 'en'] if user_preferences.get('language') == 'ja' else ['ch_sim', 'en'],
            'confidence_threshold': 0.7
        },
        'ai': {},
        'error_handling': {
            'max_retries': 3,
            'retry_interval': 2.0,
            'enable_auto_recovery': True
        }
    }
    
    # 根据系统性能调整配置
    if system_specs['memory_gb'] >= 16 and system_specs['cpu_count'] >= 8:
        # 高性能配置
        config['performance'] = {
            'screenshot_interval': 0.3,
            'action_delay_min': 0.03,
            'action_delay_max': 0.08,
            'decision_timeout': 20.0,
            'enable_gpu_acceleration': system_specs['has_nvidia_gpu'],
            'max_worker_threads': min(system_specs['cpu_count'], 8)
        }
        config['template_matching'] = {
            'confidence_threshold': 0.85,
            'enable_multi_scale': True,
            'scale_range': [0.8, 1.2],
            'scale_step': 0.1
        }
        config['ai'] = {
            'enable_mcts': True,
            'mcts_iterations': 1500,
            'mcts_timeout': 12.0
        }
    elif system_specs['memory_gb'] >= 8 and system_specs['cpu_count'] >= 4:
        # 中等性能配置
        config['performance'] = {
            'screenshot_interval': 0.5,
            'action_delay_min': 0.05,
            'action_delay_max': 0.15,
            'decision_timeout': 30.0,
            'enable_gpu_acceleration': system_specs['has_nvidia_gpu'],
            'max_worker_threads': min(system_specs['cpu_count'], 4)
        }
        config['template_matching'] = {
            'confidence_threshold': 0.8,
            'enable_multi_scale': True,
            'scale_range': [0.9, 1.1],
            'scale_step': 0.1
        }
        config['ai'] = {
            'enable_mcts': True,
            'mcts_iterations': 1000,
            'mcts_timeout': 10.0
        }
    else:
        # 低性能配置
        config['performance'] = {
            'screenshot_interval': 0.8,
            'action_delay_min': 0.1,
            'action_delay_max': 0.25,
            'decision_timeout': 45.0,
            'enable_gpu_acceleration': False,
            'max_worker_threads': 2
        }
        config['template_matching'] = {
            'confidence_threshold': 0.75,
            'enable_multi_scale': False,
            'scale_range': [1.0, 1.0],
            'scale_step': 0.1
        }
        config['ai'] = {
            'enable_mcts': False,  # 禁用MCTS以节省资源
            'mcts_iterations': 500,
            'mcts_timeout': 5.0
        }
    
    # 根据用户偏好调整
    if user_preferences.get('priority') == 'performance':
        config['performance']['screenshot_interval'] *= 0.7
        config['ai']['mcts_iterations'] = int(config['ai']['mcts_iterations'] * 1.5)
    elif user_preferences.get('priority') == 'stability':
        config['performance']['screenshot_interval'] *= 1.5
        config['template_matching']['confidence_threshold'] *= 0.9
        config['error_handling']['max_retries'] = 5
    
    return config

def interactive_setup():
    """交互式配置设置"""
    print("🚀 Gakumasu-Bot 快速配置向导")
    print("=" * 50)
    
    user_preferences = {}
    
    # 1. 检测DMM Player路径
    print("\n📂 第一步: DMM Player 路径配置")
    detected_path = detect_dmm_player_path()
    if detected_path:
        print(f"✅ 自动检测到DMM Player路径: {detected_path}")
        use_detected = input("是否使用此路径？(Y/n): ").lower().strip()
        if use_detected != 'n':
            user_preferences['dmm_path'] = detected_path
        else:
            user_preferences['dmm_path'] = input("请输入DMM Player路径: ").strip()
    else:
        print("❌ 未能自动检测到DMM Player路径")
        user_preferences['dmm_path'] = input("请输入DMM Player路径: ").strip()
    
    # 2. 语言设置
    print("\n🌐 第二步: 语言设置")
    print("1. 日语 (ja) - 推荐")
    print("2. 中文 (cn)")
    lang_choice = input("请选择语言 (1/2): ").strip()
    user_preferences['language'] = 'ja' if lang_choice != '2' else 'cn'
    
    # 3. 分辨率设置
    print("\n🖥️ 第三步: 屏幕分辨率")
    print("1. 1920x1080 (推荐)")
    print("2. 1366x768")
    print("3. 2560x1440")
    print("4. 自定义")
    res_choice = input("请选择分辨率 (1/2/3/4): ").strip()
    
    resolution_map = {
        '1': [1920, 1080],
        '2': [1366, 768],
        '3': [2560, 1440]
    }
    
    if res_choice in resolution_map:
        user_preferences['resolution'] = resolution_map[res_choice]
    else:
        try:
            width = int(input("请输入屏幕宽度: "))
            height = int(input("请输入屏幕高度: "))
            user_preferences['resolution'] = [width, height]
        except ValueError:
            print("输入无效，使用默认分辨率 1920x1080")
            user_preferences['resolution'] = [1920, 1080]
    
    # 4. 性能优先级
    print("\n⚡ 第四步: 性能优先级")
    print("1. 性能优先 - 更快的响应速度，更高的资源占用")
    print("2. 平衡模式 - 平衡性能和稳定性 (推荐)")
    print("3. 稳定优先 - 更稳定的运行，较慢的响应速度")
    priority_choice = input("请选择优先级 (1/2/3): ").strip()
    
    priority_map = {
        '1': 'performance',
        '2': 'balanced',
        '3': 'stability'
    }
    user_preferences['priority'] = priority_map.get(priority_choice, 'balanced')
    
    return user_preferences

def save_config(config: Dict[str, Any], config_path: str):
    """保存配置到文件"""
    os.makedirs(os.path.dirname(config_path), exist_ok=True)
    
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
    
    print(f"✅ 配置已保存到: {config_path}")

def create_user_strategy_template(language: str):
    """创建用户策略模板"""
    strategy_config = {
        'produce_goal': {
            'target': 'high_score',
            'target_score': 15000,
            'priority_stats': ['vocal', 'dance']
        },
        'team_composition': {
            'produce_idol': '花海咲季' if language == 'ja' else '花海咲季',
            'support_cards': [
                '【SSR】まだ見ぬ景色',
                '【SR】あの日と同じように',
                '【R】新しい一歩'
            ]
        },
        'behavior': {
            'risk_aversion': 0.5,
            'stamina_management_style': 'balanced',
            'prioritize_events': True,
            'rest_threshold': 30
        }
    }
    
    strategy_path = "config/user_strategy.yaml"
    save_config(strategy_config, strategy_path)

def main():
    """主函数"""
    print("欢迎使用 Gakumasu-Bot 快速配置工具！")
    print("此工具将帮助您生成适合您系统的配置文件。\n")
    
    # 检查是否已有配置文件
    settings_path = "config/settings.yaml"
    if os.path.exists(settings_path):
        overwrite = input(f"配置文件 {settings_path} 已存在，是否覆盖？(y/N): ").lower().strip()
        if overwrite != 'y':
            print("配置设置已取消。")
            return
    
    # 获取系统规格
    print("🔍 正在检测系统规格...")
    try:
        system_specs = get_system_specs()
        print(f"✅ 检测完成: CPU核心数={system_specs['cpu_count']}, "
              f"内存={system_specs['memory_gb']:.1f}GB, "
              f"NVIDIA GPU={'是' if system_specs['has_nvidia_gpu'] else '否'}")
    except Exception as e:
        print(f"⚠️ 系统规格检测失败: {e}")
        system_specs = {'cpu_count': 4, 'memory_gb': 8, 'has_nvidia_gpu': False}
        print("使用默认系统规格进行配置")
    
    # 交互式设置
    user_preferences = interactive_setup()
    
    # 生成配置
    print("\n⚙️ 正在生成优化配置...")
    config = generate_optimized_config(system_specs, user_preferences)
    
    # 保存配置
    save_config(config, settings_path)
    
    # 创建用户策略模板
    print("\n📋 正在创建用户策略模板...")
    create_user_strategy_template(user_preferences['language'])
    
    # 创建必要的目录
    print("\n📁 正在创建必要的目录...")
    directories = ['logs', 'temp', 'backup', 'assets/templates']
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ 创建目录: {directory}")
    
    print("\n🎉 配置设置完成！")
    print("\n📋 下一步操作:")
    print("1. 检查并编辑 config/settings.yaml 文件")
    print("2. 根据需要修改 config/user_strategy.yaml 文件")
    print("3. 将游戏界面模板图片放入 assets/templates/ 目录")
    print("4. 运行 python main.py 启动程序")
    
    print(f"\n💡 提示: 您可以运行 'python tools/config_validator.py' 来验证配置")

if __name__ == "__main__":
    main()
