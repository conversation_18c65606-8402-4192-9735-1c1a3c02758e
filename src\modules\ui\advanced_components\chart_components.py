"""
图表组件
提供各种图表类型的展示功能，包括折线图、柱状图、饼图等
"""

import time
from typing import List, Dict, Any, Optional, Union, Tuple
from enum import Enum
from dataclasses import dataclass, field

from ....utils.logger import get_logger
from ..elements.base_ui_element import BaseUIElement
from ..utils.performance_monitor import measure_block


class ChartType(Enum):
    """图表类型枚举"""
    LINE = "line"                 # 折线图
    BAR = "bar"                   # 柱状图
    PIE = "pie"                   # 饼图
    DOUGHNUT = "doughnut"         # 环形图
    RADAR = "radar"               # 雷达图
    SCATTER = "scatter"           # 散点图
    AREA = "area"                 # 面积图
    BUBBLE = "bubble"             # 气泡图


class AnimationType(Enum):
    """动画类型枚举"""
    NONE = "none"                 # 无动画
    FADE_IN = "fade_in"           # 淡入
    SLIDE_IN = "slide_in"         # 滑入
    SCALE_IN = "scale_in"         # 缩放进入
    BOUNCE = "bounce"             # 弹跳
    ELASTIC = "elastic"           # 弹性


class LegendPosition(Enum):
    """图例位置枚举"""
    TOP = "top"
    BOTTOM = "bottom"
    LEFT = "left"
    RIGHT = "right"
    NONE = "none"


@dataclass
class ChartData:
    """图表数据"""
    labels: List[str] = field(default_factory=list)
    datasets: List[Dict[str, Any]] = field(default_factory=list)


@dataclass
class ChartSeries:
    """图表数据系列"""
    name: str
    data: List[Union[int, float]]
    color: Optional[str] = None
    background_color: Optional[str] = None
    border_color: Optional[str] = None
    border_width: int = 1
    fill: bool = False
    visible: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ChartConfig:
    """图表配置"""
    title: str = ""
    subtitle: str = ""
    width: Optional[int] = None
    height: Optional[int] = None
    responsive: bool = True
    maintain_aspect_ratio: bool = True
    
    # 动画
    animation_type: AnimationType = AnimationType.FADE_IN
    animation_duration: int = 1000
    animation_easing: str = "ease-in-out"
    
    # 图例
    legend_position: LegendPosition = LegendPosition.TOP
    show_legend: bool = True
    
    # 工具提示
    show_tooltips: bool = True
    tooltip_format: Optional[str] = None
    
    # 网格
    show_grid: bool = True
    grid_color: str = "#e0e0e0"
    
    # 坐标轴
    x_axis_title: str = ""
    y_axis_title: str = ""
    show_x_axis: bool = True
    show_y_axis: bool = True
    
    # 颜色主题
    color_scheme: List[str] = field(default_factory=lambda: [
        "#3498db", "#e74c3c", "#2ecc71", "#f39c12", "#9b59b6",
        "#1abc9c", "#34495e", "#e67e22", "#95a5a6", "#f1c40f"
    ])
    
    # 其他选项
    enable_zoom: bool = False
    enable_pan: bool = False
    enable_export: bool = True
    export_formats: List[str] = field(default_factory=lambda: ["png", "jpg", "svg", "pdf"])


class BaseChart(BaseUIElement):
    """图表基类"""
    
    def __init__(self, element_id: str, chart_type: ChartType, config: ChartConfig = None, **kwargs):
        super().__init__(element_id, **kwargs)
        self.logger = get_logger(f"Chart.{chart_type.value}")
        
        self.chart_type = chart_type
        self.config = config or ChartConfig()
        
        # 数据
        self.chart_data = ChartData()
        self.series: List[ChartSeries] = []
        
        # 状态
        self.is_rendered = False
        self.last_update_time = 0.0
        
        # 事件回调
        self.on_data_point_click: Optional[callable] = None
        self.on_legend_click: Optional[callable] = None
        self.on_zoom: Optional[callable] = None
        self.on_export: Optional[callable] = None
        
        self.logger.info(f"{chart_type.value}图表组件初始化: {element_id}")
    
    def set_data(self, data: ChartData):
        """设置图表数据"""
        try:
            with measure_block("chart_data_setting"):
                self.chart_data = data
                self.last_update_time = time.time()
                self._validate_data()
                
                self.logger.debug(f"设置图表数据，{len(data.labels)}个标签，{len(data.datasets)}个数据集")
                
        except Exception as e:
            self.logger.error(f"设置图表数据失败: {e}")
    
    def add_series(self, series: ChartSeries):
        """添加数据系列"""
        self.series.append(series)
        self._update_chart_data()
    
    def remove_series(self, series_name: str) -> bool:
        """移除数据系列"""
        for i, series in enumerate(self.series):
            if series.name == series_name:
                self.series.pop(i)
                self._update_chart_data()
                return True
        return False
    
    def update_series_data(self, series_name: str, data: List[Union[int, float]]) -> bool:
        """更新系列数据"""
        for series in self.series:
            if series.name == series_name:
                series.data = data
                self._update_chart_data()
                return True
        return False
    
    def _update_chart_data(self):
        """更新图表数据结构"""
        datasets = []
        for series in self.series:
            if series.visible:
                dataset = {
                    "label": series.name,
                    "data": series.data,
                    "borderWidth": series.border_width,
                    "fill": series.fill
                }
                
                if series.color:
                    dataset["backgroundColor"] = series.background_color or series.color
                    dataset["borderColor"] = series.border_color or series.color
                
                datasets.append(dataset)
        
        self.chart_data.datasets = datasets
        self.last_update_time = time.time()
    
    def _validate_data(self):
        """验证图表数据"""
        if not self.chart_data.labels:
            self.logger.warning("图表标签为空")
        
        if not self.chart_data.datasets:
            self.logger.warning("图表数据集为空")
        
        # 检查数据长度一致性
        for dataset in self.chart_data.datasets:
            data_length = len(dataset.get("data", []))
            labels_length = len(self.chart_data.labels)
            
            if data_length != labels_length:
                self.logger.warning(f"数据集'{dataset.get('label', 'unknown')}'长度({data_length})与标签长度({labels_length})不匹配")
    
    def set_config(self, config: ChartConfig):
        """设置图表配置"""
        self.config = config
        self.last_update_time = time.time()
    
    def update_config(self, **kwargs):
        """更新图表配置"""
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
        
        self.last_update_time = time.time()
    
    def export_chart(self, format_type: str = "png", filename: str = None) -> Optional[str]:
        """导出图表"""
        try:
            if format_type not in self.config.export_formats:
                self.logger.error(f"不支持的导出格式: {format_type}")
                return None
            
            if not filename:
                timestamp = int(time.time())
                filename = f"{self.element_id}_{timestamp}.{format_type}"
            
            # 这里应该调用实际的图表渲染引擎进行导出
            # 简化实现，返回文件名
            
            if self.on_export:
                self.on_export(format_type, filename)
            
            self.logger.info(f"导出图表: {filename}")
            return filename
            
        except Exception as e:
            self.logger.error(f"导出图表失败: {e}")
            return None
    
    def get_data_summary(self) -> Dict[str, Any]:
        """获取数据摘要"""
        summary = {
            "chart_type": self.chart_type.value,
            "labels_count": len(self.chart_data.labels),
            "datasets_count": len(self.chart_data.datasets),
            "series_count": len(self.series),
            "last_update": self.last_update_time,
            "is_rendered": self.is_rendered
        }
        
        # 计算数据统计
        all_values = []
        for dataset in self.chart_data.datasets:
            all_values.extend(dataset.get("data", []))
        
        if all_values:
            summary.update({
                "min_value": min(all_values),
                "max_value": max(all_values),
                "avg_value": sum(all_values) / len(all_values),
                "total_data_points": len(all_values)
            })
        
        return summary


class LineChart(BaseChart):
    """折线图组件"""
    
    def __init__(self, element_id: str, config: ChartConfig = None, **kwargs):
        super().__init__(element_id, ChartType.LINE, config, **kwargs)
        
        # 折线图特定配置
        self.show_points = True
        self.point_radius = 3
        self.line_tension = 0.1
        self.stepped = False
    
    def set_line_style(self, series_name: str, tension: float = 0.1, 
                      stepped: bool = False, point_radius: int = 3):
        """设置线条样式"""
        for series in self.series:
            if series.name == series_name:
                series.metadata.update({
                    "tension": tension,
                    "stepped": stepped,
                    "pointRadius": point_radius
                })
                break


class BarChart(BaseChart):
    """柱状图组件"""
    
    def __init__(self, element_id: str, config: ChartConfig = None, **kwargs):
        super().__init__(element_id, ChartType.BAR, config, **kwargs)
        
        # 柱状图特定配置
        self.bar_thickness = None
        self.max_bar_thickness = None
        self.category_percentage = 0.8
        self.bar_percentage = 0.9
        self.horizontal = False
    
    def set_horizontal(self, horizontal: bool = True):
        """设置为水平柱状图"""
        self.horizontal = horizontal
        if horizontal:
            self.chart_type = ChartType.BAR  # 可以扩展为HORIZONTAL_BAR
    
    def set_bar_style(self, thickness: int = None, max_thickness: int = None):
        """设置柱子样式"""
        self.bar_thickness = thickness
        self.max_bar_thickness = max_thickness


class PieChart(BaseChart):
    """饼图组件"""
    
    def __init__(self, element_id: str, config: ChartConfig = None, **kwargs):
        super().__init__(element_id, ChartType.PIE, config, **kwargs)
        
        # 饼图特定配置
        self.start_angle = 0
        self.show_percentages = True
        self.show_values = False
        self.inner_radius = 0  # 0为饼图，>0为环形图
    
    def set_as_doughnut(self, inner_radius: float = 0.5):
        """设置为环形图"""
        self.chart_type = ChartType.DOUGHNUT
        self.inner_radius = inner_radius
    
    def calculate_percentages(self) -> List[float]:
        """计算百分比"""
        if not self.chart_data.datasets:
            return []
        
        data = self.chart_data.datasets[0].get("data", [])
        total = sum(data)
        
        if total == 0:
            return [0] * len(data)
        
        return [(value / total) * 100 for value in data]


class RadarChart(BaseChart):
    """雷达图组件"""
    
    def __init__(self, element_id: str, config: ChartConfig = None, **kwargs):
        super().__init__(element_id, ChartType.RADAR, config, **kwargs)
        
        # 雷达图特定配置
        self.scale_min = 0
        self.scale_max = 100
        self.scale_steps = 5
        self.point_radius = 3
        self.point_hover_radius = 5
    
    def set_scale(self, min_value: float, max_value: float, steps: int = 5):
        """设置刻度范围"""
        self.scale_min = min_value
        self.scale_max = max_value
        self.scale_steps = steps


class ScatterChart(BaseChart):
    """散点图组件"""
    
    def __init__(self, element_id: str, config: ChartConfig = None, **kwargs):
        super().__init__(element_id, ChartType.SCATTER, config, **kwargs)
        
        # 散点图特定配置
        self.point_radius = 5
        self.point_hover_radius = 7
        self.show_line = False
    
    def add_scatter_data(self, series_name: str, points: List[Tuple[float, float]], 
                        color: str = None):
        """添加散点数据"""
        # 转换点数据格式
        scatter_data = [{"x": x, "y": y} for x, y in points]
        
        series = ChartSeries(
            name=series_name,
            data=scatter_data,
            color=color
        )
        
        self.add_series(series)
    
    def enable_trend_line(self, series_name: str, enabled: bool = True):
        """启用趋势线"""
        for series in self.series:
            if series.name == series_name:
                series.metadata["showLine"] = enabled
                break


class ChartFactory:
    """图表工厂类"""
    
    @staticmethod
    def create_chart(chart_type: ChartType, element_id: str, 
                    config: ChartConfig = None, **kwargs) -> BaseChart:
        """创建图表实例"""
        if chart_type == ChartType.LINE:
            return LineChart(element_id, config, **kwargs)
        elif chart_type == ChartType.BAR:
            return BarChart(element_id, config, **kwargs)
        elif chart_type == ChartType.PIE or chart_type == ChartType.DOUGHNUT:
            return PieChart(element_id, config, **kwargs)
        elif chart_type == ChartType.RADAR:
            return RadarChart(element_id, config, **kwargs)
        elif chart_type == ChartType.SCATTER:
            return ScatterChart(element_id, config, **kwargs)
        else:
            return BaseChart(element_id, chart_type, config, **kwargs)
    
    @staticmethod
    def create_line_chart(element_id: str, labels: List[str], 
                         datasets: List[Dict[str, Any]], **kwargs) -> LineChart:
        """快速创建折线图"""
        chart = LineChart(element_id, **kwargs)
        chart.set_data(ChartData(labels=labels, datasets=datasets))
        return chart
    
    @staticmethod
    def create_bar_chart(element_id: str, labels: List[str], 
                        data: List[Union[int, float]], 
                        series_name: str = "数据", **kwargs) -> BarChart:
        """快速创建柱状图"""
        chart = BarChart(element_id, **kwargs)
        series = ChartSeries(name=series_name, data=data)
        chart.add_series(series)
        chart.chart_data.labels = labels
        chart._update_chart_data()
        return chart
    
    @staticmethod
    def create_pie_chart(element_id: str, labels: List[str], 
                        data: List[Union[int, float]], **kwargs) -> PieChart:
        """快速创建饼图"""
        chart = PieChart(element_id, **kwargs)
        series = ChartSeries(name="数据", data=data)
        chart.add_series(series)
        chart.chart_data.labels = labels
        chart._update_chart_data()
        return chart


class ChartManager:
    """图表管理器"""
    
    def __init__(self):
        self.charts: Dict[str, BaseChart] = {}
        self.logger = get_logger("ChartManager")
    
    def register_chart(self, chart: BaseChart):
        """注册图表"""
        self.charts[chart.element_id] = chart
        self.logger.debug(f"注册图表: {chart.element_id}")
    
    def unregister_chart(self, chart_id: str):
        """注销图表"""
        if chart_id in self.charts:
            del self.charts[chart_id]
            self.logger.debug(f"注销图表: {chart_id}")
    
    def get_chart(self, chart_id: str) -> Optional[BaseChart]:
        """获取图表"""
        return self.charts.get(chart_id)
    
    def update_all_charts(self):
        """更新所有图表"""
        for chart in self.charts.values():
            chart.last_update_time = time.time()
    
    def export_all_charts(self, format_type: str = "png") -> List[str]:
        """导出所有图表"""
        exported_files = []
        for chart in self.charts.values():
            filename = chart.export_chart(format_type)
            if filename:
                exported_files.append(filename)
        return exported_files
    
    def get_charts_summary(self) -> Dict[str, Any]:
        """获取图表摘要"""
        return {
            "total_charts": len(self.charts),
            "chart_types": list(set(chart.chart_type.value for chart in self.charts.values())),
            "charts": {chart_id: chart.get_data_summary() for chart_id, chart in self.charts.items()}
        }
