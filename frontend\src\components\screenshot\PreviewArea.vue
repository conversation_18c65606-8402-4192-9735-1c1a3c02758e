<template>
  <div class="preview-area">
    <el-card header="游戏窗口预览" class="preview-card">
      <template #header>
        <div class="preview-header">
          <span>游戏窗口预览</span>
          <div class="preview-controls">
            <el-tag 
              :type="previewActive ? 'success' : 'info'" 
              size="small"
            >
              {{ previewActive ? '实时预览' : '静态预览' }}
            </el-tag>
            <el-button-group size="small">
              <el-button @click="$emit('refresh-preview')" :loading="loading">
                <el-icon><Refresh /></el-icon>
              </el-button>
              <el-button @click="clearSelection">
                <el-icon><Close /></el-icon>
                清除选择
              </el-button>
              <el-button @click="fitToWindow">
                <el-icon><FullScreen /></el-icon>
                适应窗口
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>

      <div class="preview-container" v-loading="loading">
        <div class="preview-canvas-wrapper" ref="canvasWrapper">
          <canvas 
            ref="previewCanvas"
            class="preview-canvas"
            @mousedown="startRegionSelect"
            @mousemove="updateRegionSelect"
            @mouseup="endRegionSelect"
            @mouseleave="endRegionSelect"
          ></canvas>
          
          <!-- 区域选择覆盖层 -->
          <div 
            v-if="isSelecting || selectedRegion"
            class="region-overlay"
            :style="regionOverlayStyle"
          >
            <div class="region-info">
              {{ regionInfo }}
            </div>
            <!-- 调整手柄 -->
            <div 
              v-if="selectedRegion && !isSelecting"
              class="resize-handles"
            >
              <div class="handle handle-nw" @mousedown="startResize('nw', $event)"></div>
              <div class="handle handle-ne" @mousedown="startResize('ne', $event)"></div>
              <div class="handle handle-sw" @mousedown="startResize('sw', $event)"></div>
              <div class="handle handle-se" @mousedown="startResize('se', $event)"></div>
            </div>
          </div>
        </div>
        
        <!-- 预览信息 -->
        <div class="preview-info">
          <div class="info-left">
            <span v-if="lastUpdate">更新时间: {{ lastUpdate }}</span>
            <span v-if="fps > 0">FPS: {{ fps }}</span>
            <span v-if="imageSize">尺寸: {{ imageSize }}</span>
          </div>
          <div class="info-right">
            <span v-if="selectedRegion">
              选择区域: {{ selectedRegion.width }}×{{ selectedRegion.height }}
            </span>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted } from 'vue'
import { Refresh, Close, FullScreen } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  previewActive: {
    type: Boolean,
    default: false
  },
  previewImage: {
    type: String,
    default: null
  },
  selectedRegion: {
    type: Object,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits([
  'region-select',
  'refresh-preview'
])

// 响应式数据
const canvasWrapper = ref(null)
const previewCanvas = ref(null)
const isSelecting = ref(false)
const isResizing = ref(false)
const resizeType = ref('')
const selectionStart = ref(null)
const currentRegion = ref(null)
const lastUpdate = ref(null)
const fps = ref(0)
const imageSize = ref('')

// 计算属性
const regionOverlayStyle = computed(() => {
  const region = currentRegion.value || props.selectedRegion
  if (!region) return {}
  
  return {
    left: `${region.x}px`,
    top: `${region.y}px`,
    width: `${region.width}px`,
    height: `${region.height}px`
  }
})

const regionInfo = computed(() => {
  const region = currentRegion.value || props.selectedRegion
  if (!region) return ''
  
  return `${Math.round(region.x)}, ${Math.round(region.y)} - ${Math.round(region.width)}×${Math.round(region.height)}`
})

// 监听预览图像变化
watch(() => props.previewImage, (newImage) => {
  if (newImage) {
    loadImageToCanvas(newImage)
    lastUpdate.value = new Date().toLocaleTimeString()
  }
})

// 生命周期
onMounted(() => {
  if (props.previewImage) {
    loadImageToCanvas(props.previewImage)
  }
})

// 方法
async function loadImageToCanvas(imageSrc) {
  if (!previewCanvas.value) return
  
  const canvas = previewCanvas.value
  const ctx = canvas.getContext('2d')
  
  const img = new Image()
  img.onload = () => {
    // 设置画布尺寸
    const wrapper = canvasWrapper.value
    const maxWidth = wrapper.clientWidth
    const maxHeight = wrapper.clientHeight - 60 // 减去信息栏高度
    
    let { width, height } = calculateDisplaySize(img.width, img.height, maxWidth, maxHeight)
    
    canvas.width = width
    canvas.height = height
    canvas.style.width = `${width}px`
    canvas.style.height = `${height}px`
    
    // 绘制图像
    ctx.clearRect(0, 0, width, height)
    ctx.drawImage(img, 0, 0, width, height)
    
    imageSize.value = `${img.width}×${img.height}`
  }
  
  img.src = imageSrc
}

function calculateDisplaySize(imgWidth, imgHeight, maxWidth, maxHeight) {
  const aspectRatio = imgWidth / imgHeight
  
  let width = imgWidth
  let height = imgHeight
  
  if (width > maxWidth) {
    width = maxWidth
    height = width / aspectRatio
  }
  
  if (height > maxHeight) {
    height = maxHeight
    width = height * aspectRatio
  }
  
  return { width: Math.floor(width), height: Math.floor(height) }
}

function startRegionSelect(event) {
  if (isResizing.value) return
  
  const rect = previewCanvas.value.getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top
  
  isSelecting.value = true
  selectionStart.value = { x, y }
  currentRegion.value = { x, y, width: 0, height: 0 }
  
  event.preventDefault()
}

function updateRegionSelect(event) {
  if (!isSelecting.value && !isResizing.value) return
  
  const rect = previewCanvas.value.getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top
  
  if (isSelecting.value) {
    const startX = selectionStart.value.x
    const startY = selectionStart.value.y
    
    currentRegion.value = {
      x: Math.min(startX, x),
      y: Math.min(startY, y),
      width: Math.abs(x - startX),
      height: Math.abs(y - startY)
    }
  } else if (isResizing.value) {
    updateResize(x, y)
  }
  
  event.preventDefault()
}

function endRegionSelect() {
  if (isSelecting.value) {
    isSelecting.value = false
    
    if (currentRegion.value && currentRegion.value.width > 5 && currentRegion.value.height > 5) {
      emit('region-select', { ...currentRegion.value })
    } else {
      currentRegion.value = null
    }
  } else if (isResizing.value) {
    isResizing.value = false
    resizeType.value = ''
    
    if (currentRegion.value) {
      emit('region-select', { ...currentRegion.value })
    }
  }
}

function startResize(type, event) {
  isResizing.value = true
  resizeType.value = type
  currentRegion.value = { ...props.selectedRegion }
  
  event.stopPropagation()
  event.preventDefault()
}

function updateResize(x, y) {
  if (!currentRegion.value) return
  
  const region = currentRegion.value
  
  switch (resizeType.value) {
    case 'nw':
      region.width += region.x - x
      region.height += region.y - y
      region.x = x
      region.y = y
      break
    case 'ne':
      region.width = x - region.x
      region.height += region.y - y
      region.y = y
      break
    case 'sw':
      region.width += region.x - x
      region.height = y - region.y
      region.x = x
      break
    case 'se':
      region.width = x - region.x
      region.height = y - region.y
      break
  }
  
  // 确保尺寸为正数
  if (region.width < 0) {
    region.x += region.width
    region.width = -region.width
  }
  if (region.height < 0) {
    region.y += region.height
    region.height = -region.height
  }
}

function clearSelection() {
  currentRegion.value = null
  emit('region-select', null)
}

function fitToWindow() {
  if (!previewCanvas.value) return
  
  const canvas = previewCanvas.value
  const wrapper = canvasWrapper.value
  
  const maxWidth = wrapper.clientWidth
  const maxHeight = wrapper.clientHeight - 60
  
  canvas.style.width = `${maxWidth}px`
  canvas.style.height = `${maxHeight}px`
}
</script>

<style scoped>
.preview-area {
  height: 100%;
}

.preview-card {
  height: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.preview-card :deep(.el-card__body) {
  height: calc(100% - 60px);
  padding: 0;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.preview-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.preview-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.preview-canvas-wrapper {
  flex: 1;
  position: relative;
  overflow: hidden;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-canvas {
  border: 1px solid #ddd;
  cursor: crosshair;
  background: white;
}

.region-overlay {
  position: absolute;
  border: 2px dashed #409eff;
  background: rgba(64, 158, 255, 0.1);
  pointer-events: none;
}

.region-info {
  position: absolute;
  top: -25px;
  left: 0;
  background: #409eff;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
}

.resize-handles {
  pointer-events: auto;
}

.handle {
  position: absolute;
  width: 8px;
  height: 8px;
  background: #409eff;
  border: 1px solid white;
  border-radius: 50%;
}

.handle-nw { top: -4px; left: -4px; cursor: nw-resize; }
.handle-ne { top: -4px; right: -4px; cursor: ne-resize; }
.handle-sw { bottom: -4px; left: -4px; cursor: sw-resize; }
.handle-se { bottom: -4px; right: -4px; cursor: se-resize; }

.preview-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  font-size: 12px;
  color: #6c757d;
}

.info-left,
.info-right {
  display: flex;
  gap: 16px;
}
</style>
