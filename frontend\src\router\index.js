import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('../views/Home.vue')
  },
  {
    path: '/screenshot-tool',
    name: 'ScreenshotTool',
    component: () => import('../views/ScreenshotTool.vue'),
    meta: {
      title: '截图工具',
      icon: 'Camera',
      description: '游戏截图数据收集工具'
    }
  },
  {
    path: '/debug-console',
    name: 'DebugConsole',
    component: () => import('../views/DebugConsole.vue'),
    meta: {
      title: '调试控制台',
      icon: 'Tools',
      description: '系统调试和功能测试工具'
    }
  },
  {
    path: '/debug-test',
    name: 'DebugConsoleTest',
    component: () => import('../views/DebugConsoleTest.vue'),
    meta: {
      title: '调试测试',
      icon: 'Tools',
      description: '调试控制台测试页面'
    }
  },
  {
    path: '/debug-simple',
    name: 'DebugConsoleSimple',
    component: () => import('../views/DebugConsoleSimple.vue'),
    meta: {
      title: '调试控制台(简化版)',
      icon: 'Tools',
      description: '调试控制台简化版本'
    }
  },
  {
    path: '/debug-working',
    name: 'DebugConsoleWorking',
    component: () => import('../views/DebugConsoleWorking.vue'),
    meta: {
      title: '调试控制台(工作版)',
      icon: 'Tools',
      description: '调试控制台完全工作版本'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
