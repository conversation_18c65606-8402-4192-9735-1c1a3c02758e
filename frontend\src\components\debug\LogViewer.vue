<template>
  <div class="log-viewer">
    <div class="panel-header">
      <h2>日志查看器</h2>
      <div class="header-actions">
        <el-button 
          type="primary" 
          :icon="Refresh"
          @click="refreshLogs"
          size="small"
        >
          刷新日志
        </el-button>
        <el-button 
          type="danger" 
          :icon="Delete"
          @click="clearLogs"
          size="small"
        >
          清空日志
        </el-button>
      </div>
    </div>

    <!-- 日志控制面板 -->
    <div class="log-controls">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-select v-model="logFilter.level" placeholder="选择日志级别" clearable>
            <el-option label="全部" value="" />
            <el-option label="DEBUG" value="DEBUG" />
            <el-option label="INFO" value="INFO" />
            <el-option label="WARNING" value="WARNING" />
            <el-option label="ERROR" value="ERROR" />
            <el-option label="CRITICAL" value="CRITICAL" />
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-input
            v-model="logFilter.search"
            placeholder="搜索日志内容"
            :prefix-icon="Search"
            clearable
          />
        </el-col>
        <el-col :span="4">
          <el-switch
            v-model="autoScroll"
            active-text="自动滚动"
            inactive-text="手动滚动"
          />
        </el-col>
        <el-col :span="6">
          <div class="log-stats">
            <el-tag type="info" size="small">
              共{{ filteredLogs.length }}条日志
            </el-tag>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 日志显示区域 -->
    <div class="log-display">
      <el-card>
        <div 
          ref="logContainer"
          class="log-container"
          :class="{ 'auto-scroll': autoScroll }"
        >
          <div v-if="filteredLogs.length === 0" class="no-logs">
            <el-empty description="暂无日志数据" :image-size="80">
              <el-button type="primary" @click="generateSampleLogs">
                生成示例日志
              </el-button>
            </el-empty>
          </div>

          <div v-else class="log-list">
            <div 
              v-for="log in filteredLogs" 
              :key="log.id"
              class="log-entry"
              :class="getLogLevelClass(log.level)"
            >
              <div class="log-header">
                <div class="log-info">
                  <el-tag 
                    :type="getLogLevelType(log.level)" 
                    size="small"
                    class="log-level"
                  >
                    {{ log.level }}
                  </el-tag>
                  <span class="log-time">{{ formatTime(log.timestamp) }}</span>
                </div>
                <div class="log-actions">
                  <el-button 
                    type="text" 
                    size="small"
                    @click="copyLogEntry(log)"
                  >
                    复制
                  </el-button>
                </div>
              </div>
              
              <div class="log-content">
                <div class="log-message">{{ log.message }}</div>
                <div v-if="log.data" class="log-data">
                  <el-collapse>
                    <el-collapse-item title="详细数据" :name="log.id">
                      <pre class="log-data-content">{{ formatLogData(log.data) }}</pre>
                    </el-collapse-item>
                  </el-collapse>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 日志统计面板 -->
    <div class="log-statistics">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card>
            <div class="stat-item">
              <div class="stat-icon debug">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ getLogCountByLevel('DEBUG') }}</div>
                <div class="stat-label">DEBUG</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card>
            <div class="stat-item">
              <div class="stat-icon info">
                <el-icon><InfoFilled /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ getLogCountByLevel('INFO') }}</div>
                <div class="stat-label">INFO</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card>
            <div class="stat-item">
              <div class="stat-icon warning">
                <el-icon><WarningFilled /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ getLogCountByLevel('WARNING') }}</div>
                <div class="stat-label">WARNING</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="20" style="margin-top: 15px;">
        <el-col :span="12">
          <el-card>
            <div class="stat-item">
              <div class="stat-icon error">
                <el-icon><CircleCloseFilled /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ getLogCountByLevel('ERROR') }}</div>
                <div class="stat-label">ERROR</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <div class="stat-item">
              <div class="stat-icon critical">
                <el-icon><CircleCloseFilled /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ getLogCountByLevel('CRITICAL') }}</div>
                <div class="stat-label">CRITICAL</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted } from 'vue'
import { 
  Refresh, Delete, Search, Document, 
  InfoFilled, WarningFilled, CircleCloseFilled 
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useDebugConsole } from '../../composables/useDebugConsole.js'

// 使用调试控制台功能
const { debugState, addLog, clearErrors } = useDebugConsole()

// 响应式数据
const logContainer = ref(null)
const autoScroll = ref(true)
const logFilter = ref({
  level: '',
  search: ''
})

// 计算属性
const filteredLogs = computed(() => {
  let logs = debugState.logs || []
  
  // 按级别过滤
  if (logFilter.value.level) {
    logs = logs.filter(log => log.level === logFilter.value.level)
  }
  
  // 按内容搜索
  if (logFilter.value.search) {
    const searchTerm = logFilter.value.search.toLowerCase()
    logs = logs.filter(log => 
      log.message.toLowerCase().includes(searchTerm) ||
      (log.data && JSON.stringify(log.data).toLowerCase().includes(searchTerm))
    )
  }
  
  return logs
})

// 监听日志变化，自动滚动
watch(
  () => debugState.logs.length,
  async () => {
    if (autoScroll.value) {
      await nextTick()
      scrollToBottom()
    }
  }
)

// 方法
const refreshLogs = () => {
  // 模拟刷新日志
  addLog('INFO', '日志已刷新', { timestamp: new Date() })
  ElMessage.success('日志已刷新')
}

const clearLogs = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有日志吗？',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    debugState.logs = []
    ElMessage.success('日志已清空')
  } catch {
    // 用户取消
  }
}

const generateSampleLogs = () => {
  const sampleLogs = [
    { level: 'INFO', message: '系统启动完成', data: { module: 'system', status: 'ready' } },
    { level: 'DEBUG', message: '屏幕捕获模块初始化', data: { resolution: '1920x1080', fps: 30 } },
    { level: 'INFO', message: '游戏窗口检测成功', data: { window_title: '学园偶像大师', pid: 12345 } },
    { level: 'WARNING', message: '体力值较低', data: { stamina: 15, threshold: 20 } },
    { level: 'ERROR', message: '场景识别失败', data: { scene: 'unknown', confidence: 0.3 } },
    { level: 'INFO', message: 'MCTS决策完成', data: { iterations: 1000, best_action: 'select_card_1' } },
    { level: 'DEBUG', message: '操作执行成功', data: { action: 'click', coordinates: [640, 360] } },
    { level: 'CRITICAL', message: '系统异常，自动重启', data: { error_code: 500, restart_count: 1 } }
  ]
  
  sampleLogs.forEach(log => {
    addLog(log.level, log.message, log.data)
  })
  
  ElMessage.success('示例日志已生成')
}

const copyLogEntry = async (log) => {
  const logText = `[${log.level}] ${formatTime(log.timestamp)} - ${log.message}`
  
  try {
    await navigator.clipboard.writeText(logText)
    ElMessage.success('日志已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const scrollToBottom = () => {
  if (logContainer.value) {
    logContainer.value.scrollTop = logContainer.value.scrollHeight
  }
}

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleString()
}

const formatLogData = (data) => {
  return JSON.stringify(data, null, 2)
}

const getLogLevelClass = (level) => {
  return `log-level-${level.toLowerCase()}`
}

const getLogLevelType = (level) => {
  const types = {
    'DEBUG': '',
    'INFO': 'info',
    'WARNING': 'warning',
    'ERROR': 'danger',
    'CRITICAL': 'danger'
  }
  return types[level] || ''
}

const getLogCountByLevel = (level) => {
  return debugState.logs.filter(log => log.level === level).length
}

// 生命周期
onMounted(() => {
  // 初始化时生成一些示例日志
  if (debugState.logs.length === 0) {
    addLog('INFO', '日志查看器已启动', { component: 'LogViewer' })
  }
})
</script>

<style scoped>
.log-viewer {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e6e6e6;
}

.panel-header h2 {
  margin: 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.log-controls {
  margin-bottom: 20px;
}

.log-stats {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.log-display {
  flex: 1;
  margin-bottom: 20px;
}

.log-container {
  height: 400px;
  overflow-y: auto;
  padding: 10px;
}

.log-container.auto-scroll {
  scroll-behavior: smooth;
}

.no-logs {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.log-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.log-entry {
  border: 1px solid #e6e6e6;
  border-radius: 6px;
  padding: 12px;
  background: #fafafa;
  border-left: 4px solid #e6e6e6;
}

.log-entry.log-level-debug {
  border-left-color: #909399;
  background: #f8f9fa;
}

.log-entry.log-level-info {
  border-left-color: #409eff;
  background: #f0f8ff;
}

.log-entry.log-level-warning {
  border-left-color: #e6a23c;
  background: #fdf6ec;
}

.log-entry.log-level-error {
  border-left-color: #f56c6c;
  background: #fef0f0;
}

.log-entry.log-level-critical {
  border-left-color: #f56c6c;
  background: #fef0f0;
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.2);
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.log-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.log-level {
  font-size: 12px;
  font-weight: 600;
}

.log-time {
  font-size: 12px;
  color: #909399;
}

.log-actions {
  display: flex;
  gap: 5px;
}

.log-content {
  margin-top: 5px;
}

.log-message {
  font-size: 14px;
  color: #303133;
  line-height: 1.4;
}

.log-data {
  margin-top: 8px;
}

.log-data-content {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 10px;
  font-size: 12px;
  color: #495057;
  max-height: 200px;
  overflow-y: auto;
}

.log-statistics {
  margin-top: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 10px;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
}

.stat-icon.debug {
  background: linear-gradient(135deg, #909399, #b1b3b8);
}

.stat-icon.info {
  background: linear-gradient(135deg, #409eff, #66b1ff);
}

.stat-icon.warning {
  background: linear-gradient(135deg, #e6a23c, #ebb563);
}

.stat-icon.error {
  background: linear-gradient(135deg, #f56c6c, #f78989);
}

.stat-icon.critical {
  background: linear-gradient(135deg, #f56c6c, #f78989);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.stat-label {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}
</style>
