# Gakumasu-Bot 技术实现路线图

**版本：** 1.0  
**日期：** 2025年7月11日  
**目标：** 为阶段6系统集成提供详细的技术实现指导

## 一、核心技术实现方案

### 1.1 OCR集成实现方案

**技术选型：EasyOCR + 优化策略**

```python
# src/modules/perception/ocr_processor.py
import easyocr
import cv2
import numpy as np
from typing import List, Dict, Tuple, Optional

class OCRProcessor:
    """OCR文本识别处理器"""
    
    def __init__(self, languages=['ja', 'en'], gpu=True):
        """
        初始化OCR处理器
        
        Args:
            languages: 支持的语言列表，日语优先
            gpu: 是否使用GPU加速
        """
        self.reader = easyocr.Reader(languages, gpu=gpu)
        self.confidence_threshold = 0.6
        self.preprocessing_enabled = True
        
    def extract_text_from_region(self, image: np.ndarray, 
                                region: Tuple[int, int, int, int]) -> Dict[str, any]:
        """
        从指定区域提取文本
        
        Args:
            image: 输入图像
            region: 区域坐标 (x, y, width, height)
            
        Returns:
            包含文本和置信度的字典
        """
        # 提取ROI
        x, y, w, h = region
        roi = image[y:y+h, x:x+w]
        
        # 图像预处理
        if self.preprocessing_enabled:
            roi = self._preprocess_image(roi)
        
        # OCR识别
        results = self.reader.readtext(roi)
        
        # 处理结果
        return self._process_ocr_results(results)
    
    def _preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """图像预处理优化OCR效果"""
        # 转换为灰度图
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        
        # 高斯模糊去噪
        blurred = cv2.GaussianBlur(gray, (3, 3), 0)
        
        # 自适应阈值二值化
        binary = cv2.adaptiveThreshold(
            blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
            cv2.THRESH_BINARY, 11, 2
        )
        
        return binary
    
    def _process_ocr_results(self, results: List) -> Dict[str, any]:
        """处理OCR识别结果"""
        if not results:
            return {"text": "", "confidence": 0.0, "bbox": None}
        
        # 选择置信度最高的结果
        best_result = max(results, key=lambda x: x[2])
        bbox, text, confidence = best_result
        
        return {
            "text": text.strip(),
            "confidence": confidence,
            "bbox": bbox,
            "all_results": results
        }
```

**集成到感知模块：**

```python
# 在PerceptionModule中集成OCR
class PerceptionModule:
    def __init__(self, ...):
        # ... 现有初始化代码 ...
        self.ocr_processor = OCRProcessor(['ja', 'en'])
        
    def extract_game_text(self, screenshot: np.ndarray, 
                         text_regions: Dict[str, Tuple[int, int, int, int]]) -> Dict[str, str]:
        """
        提取游戏界面中的文本信息
        
        Args:
            screenshot: 游戏截图
            text_regions: 文本区域定义
            
        Returns:
            提取的文本信息
        """
        text_results = {}
        
        for region_name, region_coords in text_regions.items():
            try:
                result = self.ocr_processor.extract_text_from_region(
                    screenshot, region_coords
                )
                
                if result["confidence"] > 0.6:  # 置信度阈值
                    text_results[region_name] = result["text"]
                else:
                    text_results[region_name] = ""
                    
            except Exception as e:
                self.logger.warning(f"OCR提取失败 {region_name}: {e}")
                text_results[region_name] = ""
        
        return text_results
```

### 1.2 完整育成流程实现方案

**核心工作流程设计：**

```python
# src/modules/scheduler/produce_task.py
from typing import Dict, List, Optional
from ...core.data_structures import Task, TaskStatus, GameState, Action

class ProduceTask(Task):
    """完整育成任务"""
    
    def __init__(self, user_strategy, scheduler):
        super().__init__(
            name="完整育成流程",
            priority=TaskPriority.HIGH,
            estimated_duration=1800  # 30分钟
        )
        self.user_strategy = user_strategy
        self.scheduler = scheduler
        self.current_week = 0
        self.produce_state = "not_started"
        
    def execute(self) -> bool:
        """执行完整育成流程"""
        try:
            self.logger.info("开始执行完整育成流程")
            
            # 阶段1：育成准备
            if not self._prepare_produce():
                raise Exception("育成准备失败")
            
            # 阶段2：育成主循环
            if not self._execute_produce_main_loop():
                raise Exception("育成主循环失败")
            
            # 阶段3：最终考试
            if not self._handle_final_exam():
                raise Exception("最终考试处理失败")
            
            # 阶段4：结果结算
            if not self._complete_settlement():
                raise Exception("结果结算失败")
            
            self.logger.info("育成流程完成")
            return True
            
        except Exception as e:
            self.logger.error(f"育成流程失败: {e}")
            self._handle_produce_error(e)
            return False
    
    def _prepare_produce(self) -> bool:
        """育成准备阶段"""
        self.logger.info("开始育成准备")
        
        # 1. 导航到育成界面
        if not self._navigate_to_produce_menu():
            return False
        
        # 2. 选择偶像
        if not self._select_produce_idol():
            return False
        
        # 3. 选择支援卡
        if not self._select_support_cards():
            return False
        
        # 4. 确认开始
        if not self._confirm_produce_start():
            return False
        
        self.produce_state = "in_progress"
        return True
    
    def _execute_produce_main_loop(self) -> bool:
        """育成主循环（12周）"""
        self.logger.info("开始育成主循环")
        
        for week in range(1, 13):  # 12周育成
            self.current_week = week
            self.logger.info(f"执行第{week}周行动")
            
            # 获取当前游戏状态
            game_state = self.scheduler.perception_module.get_current_game_state()
            
            # 检查是否在正确场景
            if game_state.current_scene != GameScene.PRODUCE_MAIN:
                self.logger.warning(f"场景不匹配，当前: {game_state.current_scene}")
                if not self._recover_to_produce_main():
                    return False
            
            # 执行每周行动
            if not self._execute_weekly_action(game_state):
                return False
            
            # 处理可能的事件
            if not self._handle_weekly_events():
                return False
            
            # 检查是否有中期考试
            if week == 6:  # 中期考试通常在第6周
                if not self._handle_midterm_exam():
                    return False
        
        return True
    
    def _execute_weekly_action(self, game_state: GameState) -> bool:
        """执行每周行动选择"""
        try:
            # 使用决策模块选择行动
            decision = self.scheduler.decision_module.make_decision(game_state)
            
            if not decision:
                self.logger.error("决策模块未返回有效决策")
                return False
            
            # 执行行动
            success = self.scheduler.action_controller.execute_action(decision)
            
            if success:
                self.logger.info(f"第{self.current_week}周行动执行成功: {decision.description}")
            else:
                self.logger.warning(f"第{self.current_week}周行动执行失败")
            
            return success
            
        except Exception as e:
            self.logger.error(f"每周行动执行异常: {e}")
            return False
```

### 1.3 智能决策集成方案

**决策流程优化：**

```python
# 在DecisionModule中添加育成专用决策逻辑
class DecisionModule:
    def make_produce_decision(self, game_state: GameState, 
                            week: int, user_strategy: UserStrategy) -> Optional[Action]:
        """
        育成专用决策逻辑
        
        Args:
            game_state: 当前游戏状态
            week: 当前周数
            user_strategy: 用户策略
            
        Returns:
            决策行动
        """
        try:
            # 1. 分析当前状态
            state_analysis = self._analyze_produce_state(game_state, week)
            
            # 2. 根据周数和策略选择决策方法
            if week <= 4:  # 前期：快速决策
                decision = self._make_heuristic_decision(state_analysis, user_strategy)
            elif week <= 8:  # 中期：平衡决策
                decision = self._make_hybrid_decision(state_analysis, user_strategy)
            else:  # 后期：深度决策
                decision = self._make_mcts_decision(state_analysis, user_strategy)
            
            # 3. 验证决策有效性
            if self._validate_decision(decision, game_state):
                return decision
            else:
                # 回退到安全决策
                return self._make_safe_decision(game_state)
                
        except Exception as e:
            self.logger.error(f"育成决策失败: {e}")
            return self._make_safe_decision(game_state)
    
    def _analyze_produce_state(self, game_state: GameState, week: int) -> Dict:
        """分析育成状态"""
        analysis = {
            "week": week,
            "stamina_ratio": game_state.stamina / 120.0,  # 假设最大体力120
            "vigor_ratio": game_state.vigor / 100.0,      # 假设最大元气100
            "idol_stats": game_state.idol_stats,
            "available_actions": self._get_available_actions(game_state),
            "risk_level": self._assess_risk_level(game_state, week)
        }
        
        return analysis
```

### 1.4 Web UI实现方案

**基础架构：**

```python
# src/web/app.py
from flask import Flask, render_template, jsonify, request
from flask_socketio import SocketIO, emit
import json
from datetime import datetime

app = Flask(__name__)
app.config['SECRET_KEY'] = 'gakumasu-bot-secret'
socketio = SocketIO(app, cors_allowed_origins="*")

class WebInterface:
    def __init__(self, scheduler):
        self.scheduler = scheduler
        self.app = app
        self.socketio = socketio
        self._setup_routes()
    
    def _setup_routes(self):
        """设置路由"""
        
        @app.route('/')
        def dashboard():
            return render_template('dashboard.html')
        
        @app.route('/api/status')
        def get_status():
            """获取系统状态"""
            status = {
                "is_running": self.scheduler.is_running,
                "current_task": self.scheduler.get_current_task_info(),
                "task_queue": self.scheduler.get_task_queue_info(),
                "system_stats": self.scheduler.get_system_stats(),
                "timestamp": datetime.now().isoformat()
            }
            return jsonify(status)
        
        @app.route('/api/start', methods=['POST'])
        def start_scheduler():
            """启动调度器"""
            try:
                self.scheduler.start()
                return jsonify({"success": True, "message": "调度器已启动"})
            except Exception as e:
                return jsonify({"success": False, "error": str(e)})
        
        @app.route('/api/stop', methods=['POST'])
        def stop_scheduler():
            """停止调度器"""
            try:
                self.scheduler.stop()
                return jsonify({"success": True, "message": "调度器已停止"})
            except Exception as e:
                return jsonify({"success": False, "error": str(e)})
        
        @socketio.on('connect')
        def handle_connect():
            """客户端连接"""
            emit('connected', {'message': 'WebSocket连接成功'})
        
        @socketio.on('request_status')
        def handle_status_request():
            """处理状态请求"""
            status = self.scheduler.get_detailed_status()
            emit('status_update', status)
    
    def run(self, host='127.0.0.1', port=5000, debug=False):
        """运行Web服务器"""
        self.socketio.run(self.app, host=host, port=port, debug=debug)
```

**前端模板：**

```html
<!-- templates/dashboard.html -->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gakumasu-Bot 控制面板</title>
    <script src="https://cdn.socket.io/4.0.0/socket.io.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status-card { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .running { background-color: #d4edda; }
        .stopped { background-color: #f8d7da; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 3px; cursor: pointer; }
        .btn-start { background-color: #28a745; color: white; }
        .btn-stop { background-color: #dc3545; color: white; }
        #log { height: 300px; overflow-y: scroll; border: 1px solid #ddd; padding: 10px; }
    </style>
</head>
<body>
    <h1>Gakumasu-Bot 控制面板</h1>
    
    <div class="status-card" id="status-card">
        <h3>系统状态</h3>
        <p>状态: <span id="system-status">未知</span></p>
        <p>当前任务: <span id="current-task">无</span></p>
        <p>队列任务: <span id="queue-count">0</span></p>
    </div>
    
    <div>
        <button class="btn btn-start" onclick="startBot()">启动</button>
        <button class="btn btn-stop" onclick="stopBot()">停止</button>
        <button class="btn" onclick="refreshStatus()">刷新状态</button>
    </div>
    
    <div class="status-card">
        <h3>实时日志</h3>
        <div id="log"></div>
    </div>

    <script>
        const socket = io();
        
        socket.on('connect', function() {
            console.log('WebSocket连接成功');
            refreshStatus();
        });
        
        socket.on('status_update', function(data) {
            updateStatus(data);
        });
        
        function startBot() {
            fetch('/api/start', {method: 'POST'})
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('启动成功');
                        refreshStatus();
                    } else {
                        alert('启动失败: ' + data.error);
                    }
                });
        }
        
        function stopBot() {
            fetch('/api/stop', {method: 'POST'})
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('停止成功');
                        refreshStatus();
                    } else {
                        alert('停止失败: ' + data.error);
                    }
                });
        }
        
        function refreshStatus() {
            socket.emit('request_status');
        }
        
        function updateStatus(status) {
            document.getElementById('system-status').textContent = 
                status.is_running ? '运行中' : '已停止';
            document.getElementById('current-task').textContent = 
                status.current_task || '无';
            document.getElementById('queue-count').textContent = 
                status.task_queue_size || 0;
            
            const statusCard = document.getElementById('status-card');
            statusCard.className = 'status-card ' + 
                (status.is_running ? 'running' : 'stopped');
        }
        
        // 定期刷新状态
        setInterval(refreshStatus, 5000);
    </script>
</body>
</html>
```

## 二、关键技术难点解决方案

### 2.1 OCR准确率优化
1. **图像预处理**：灰度化、去噪、二值化
2. **区域精确定位**：基于模板匹配精确定位文本区域
3. **多语言支持**：日语优先，英语备选
4. **置信度过滤**：设置合理的置信度阈值

### 2.2 游戏状态同步
1. **状态缓存机制**：避免频繁的屏幕捕获
2. **状态变化检测**：基于关键UI元素的变化检测
3. **异步状态更新**：使用异步机制提高响应速度

### 2.3 错误恢复策略
1. **分层错误处理**：操作级、任务级、系统级
2. **状态回滚机制**：关键节点的状态保存和恢复
3. **安全模式**：遇到严重错误时的安全退出

### 2.4 性能优化方案
1. **并发处理**：利用Python 3.13的无GIL特性
2. **内存管理**：及时释放不需要的资源
3. **缓存策略**：合理使用缓存减少重复计算

---

**技术路线图将指导阶段6的具体实现工作，确保系统集成的顺利完成。**
