"""
总结场景实现
处理育成过程的全面回顾、统计分析和经验总结
"""

import time
from typing import List, Dict, Any, Optional, Tuple
from enum import Enum
from datetime import datetime, timedelta

from .....core.data_structures import GameScene
from ...config.scene_config import SceneConfig
from ...config.ui_element_config import ButtonConfig, LabelConfig
from ..base_game_scene import BaseGameScene
from ...utils.performance_monitor import measure_block
from ...utils.scene_optimizer import get_scene_optimizer


class SummaryCategory(Enum):
    """总结类别枚举"""
    OVERVIEW = "overview"          # 总览
    TIMELINE = "timeline"          # 时间线
    STATISTICS = "statistics"      # 统计数据
    ACHIEVEMENTS = "achievements"  # 成就回顾
    LESSONS = "lessons"           # 经验教训
    COMPARISON = "comparison"      # 对比分析


class TimelineEvent(Enum):
    """时间线事件类型枚举"""
    LESSON = "lesson"             # 课程
    REST = "rest"                 # 休息
    OUTING = "outing"            # 外出
    BATTLE = "battle"            # 战斗
    EXAM = "exam"                # 考试
    EVENT = "event"              # 特殊事件
    MILESTONE = "milestone"       # 里程碑


class InsightType(Enum):
    """洞察类型枚举"""
    STRENGTH = "strength"         # 优势
    WEAKNESS = "weakness"         # 劣势
    OPPORTUNITY = "opportunity"   # 机会
    THREAT = "threat"            # 威胁
    RECOMMENDATION = "recommendation"  # 建议


class SummaryScene(BaseGameScene):
    """总结场景类"""
    
    def __init__(self, config: SceneConfig, perception_module, action_controller):
        """
        初始化总结场景
        
        Args:
            config: 场景配置
            perception_module: 感知模块
            action_controller: 行动控制器
        """
        super().__init__(config, perception_module, action_controller)
        
        # 总结数据
        self._current_category = SummaryCategory.OVERVIEW
        self._summary_data: Dict[str, Any] = {}
        
        # 时间线数据
        self._timeline_events: List[Dict[str, Any]] = []
        self._key_moments: List[Dict[str, Any]] = []
        
        # 统计分析
        self._performance_metrics = {
            "efficiency": 0.0,
            "consistency": 0.0,
            "growth_rate": 0.0,
            "balance_score": 0.0
        }
        
        # 对比数据
        self._comparison_data = {
            "previous_runs": [],
            "average_performance": {},
            "best_performance": {},
            "improvement_areas": []
        }
        
        # 洞察和建议
        self._insights: List[Dict[str, Any]] = []
        self._recommendations: List[Dict[str, Any]] = []
        self._lessons_learned: List[str] = []
        
        # 可视化数据
        self._charts_data = {
            "growth_curve": [],
            "activity_distribution": {},
            "performance_radar": {}
        }
        
        # 性能监控
        self.optimizer = get_scene_optimizer()
        
        self.scene_logger.info("总结场景初始化完成")
    
    def _create_scene_ui_elements(self):
        """创建总结场景特定的UI元素"""
        try:
            with measure_block("summary_ui_creation"):
                # 场景标识元素
                self._create_summary_indicators()
                
                # 导航元素
                self._create_navigation_elements()
                
                # 总览显示元素
                self._create_overview_displays()
                
                # 时间线显示元素
                self._create_timeline_displays()
                
                # 统计显示元素
                self._create_statistics_displays()
                
                # 洞察显示元素
                self._create_insights_displays()
                
                # 控制按钮
                self._create_control_buttons()
            
            self.scene_logger.info(f"总结场景UI元素创建完成，共{len(self.ui_elements)}个元素")
            
        except Exception as e:
            self.scene_logger.error(f"总结场景UI元素创建失败: {e}")
            raise
    
    def _create_summary_indicators(self):
        """创建总结标识元素"""
        # 总结场景标题
        self.ui_elements["summary_title"] = self.ui_factory.create_enhanced_label(
            "summary_title",
            confidence_threshold=0.9,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
        
        # 当前类别显示
        self.ui_elements["current_category"] = self.ui_factory.create_enhanced_label(
            "current_category",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
        
        # 总结日期
        self.ui_elements["summary_date"] = self.ui_factory.create_label(
            "summary_date",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="en"
        )
        
        # 育成名称
        self.ui_elements["produce_name"] = self.ui_factory.create_enhanced_label(
            "produce_name",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
    
    def _create_navigation_elements(self):
        """创建导航元素"""
        # 类别导航按钮
        for category in SummaryCategory:
            button_name = f"{category.value}_tab_button"
            self.ui_elements[button_name] = self.ui_factory.create_enhanced_button(
                button_name,
                confidence_threshold=0.8,
                timeout=3.0,
                verify_click_result=True
            )
        
        # 上一页/下一页按钮
        self.ui_elements["previous_page_button"] = self.ui_factory.create_button(
            "previous_page_button",
            confidence_threshold=0.8,
            timeout=3.0
        )
        
        self.ui_elements["next_page_button"] = self.ui_factory.create_button(
            "next_page_button",
            confidence_threshold=0.8,
            timeout=3.0
        )
    
    def _create_overview_displays(self):
        """创建总览显示元素"""
        # 总体评价
        self.ui_elements["overall_evaluation"] = self.ui_factory.create_enhanced_label(
            "overall_evaluation",
            confidence_threshold=0.7,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
        
        # 关键指标
        key_metrics = ["final_score", "completion_time", "efficiency_rating", "growth_total"]
        for metric in key_metrics:
            self.ui_elements[metric] = self.ui_factory.create_enhanced_label(
                metric,
                confidence_threshold=0.8,
                text_recognition_enabled=True,
                ocr_language="en"
            )
        
        # 亮点总结
        self.ui_elements["highlights_summary"] = self.ui_factory.create_enhanced_label(
            "highlights_summary",
            confidence_threshold=0.7,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
        
        # 改进点总结
        self.ui_elements["improvements_summary"] = self.ui_factory.create_enhanced_label(
            "improvements_summary",
            confidence_threshold=0.7,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
    
    def _create_timeline_displays(self):
        """创建时间线显示元素"""
        # 时间线容器
        self.ui_elements["timeline_container"] = self.ui_factory.create_label(
            "timeline_container",
            confidence_threshold=0.7,
            text_recognition_enabled=False
        )
        
        # 时间线事件（显示最多10个关键事件）
        for i in range(1, 11):
            self.ui_elements[f"timeline_event_{i}"] = self.ui_factory.create_enhanced_label(
                f"timeline_event_{i}",
                confidence_threshold=0.7,
                text_recognition_enabled=True,
                ocr_language="ja"
            )
        
        # 关键里程碑
        for i in range(1, 6):
            self.ui_elements[f"milestone_{i}"] = self.ui_factory.create_enhanced_label(
                f"milestone_{i}",
                confidence_threshold=0.7,
                text_recognition_enabled=True,
                ocr_language="ja"
            )
        
        # 时间线导航
        self.ui_elements["timeline_scroll_left"] = self.ui_factory.create_button(
            "timeline_scroll_left",
            confidence_threshold=0.8,
            timeout=2.0
        )
        
        self.ui_elements["timeline_scroll_right"] = self.ui_factory.create_button(
            "timeline_scroll_right",
            confidence_threshold=0.8,
            timeout=2.0
        )
    
    def _create_statistics_displays(self):
        """创建统计显示元素"""
        # 活动统计图表
        self.ui_elements["activity_chart"] = self.ui_factory.create_label(
            "activity_chart",
            confidence_threshold=0.7,
            text_recognition_enabled=False
        )
        
        # 成长曲线图
        self.ui_elements["growth_chart"] = self.ui_factory.create_label(
            "growth_chart",
            confidence_threshold=0.7,
            text_recognition_enabled=False
        )
        
        # 性能雷达图
        self.ui_elements["performance_radar"] = self.ui_factory.create_label(
            "performance_radar",
            confidence_threshold=0.7,
            text_recognition_enabled=False
        )
        
        # 详细统计数据
        stat_categories = ["lessons", "battles", "exams", "events", "achievements"]
        for category in stat_categories:
            self.ui_elements[f"{category}_stats"] = self.ui_factory.create_enhanced_label(
                f"{category}_stats",
                confidence_threshold=0.8,
                text_recognition_enabled=True,
                ocr_language="en"
            )
        
        # 效率指标
        efficiency_metrics = ["time_efficiency", "resource_efficiency", "growth_efficiency"]
        for metric in efficiency_metrics:
            self.ui_elements[metric] = self.ui_factory.create_enhanced_label(
                metric,
                confidence_threshold=0.8,
                text_recognition_enabled=True,
                ocr_language="en"
            )
    
    def _create_insights_displays(self):
        """创建洞察显示元素"""
        # 洞察标题
        self.ui_elements["insights_title"] = self.ui_factory.create_label(
            "insights_title",
            confidence_threshold=0.8,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
        
        # 主要洞察（最多5个）
        for i in range(1, 6):
            self.ui_elements[f"insight_{i}"] = self.ui_factory.create_enhanced_label(
                f"insight_{i}",
                confidence_threshold=0.7,
                text_recognition_enabled=True,
                ocr_language="ja"
            )
        
        # 改进建议
        for i in range(1, 6):
            self.ui_elements[f"recommendation_{i}"] = self.ui_factory.create_enhanced_label(
                f"recommendation_{i}",
                confidence_threshold=0.7,
                text_recognition_enabled=True,
                ocr_language="ja"
            )
        
        # 经验教训
        self.ui_elements["lessons_learned"] = self.ui_factory.create_enhanced_label(
            "lessons_learned",
            confidence_threshold=0.7,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
        
        # 下次改进点
        self.ui_elements["next_improvements"] = self.ui_factory.create_enhanced_label(
            "next_improvements",
            confidence_threshold=0.7,
            text_recognition_enabled=True,
            ocr_language="ja"
        )
    
    def _create_control_buttons(self):
        """创建控制按钮"""
        # 导出报告按钮
        self.ui_elements["export_report_button"] = self.ui_factory.create_enhanced_button(
            "export_report_button",
            confidence_threshold=0.8,
            timeout=5.0,
            verify_click_result=True
        )
        
        # 保存总结按钮
        self.ui_elements["save_summary_button"] = self.ui_factory.create_enhanced_button(
            "save_summary_button",
            confidence_threshold=0.8,
            timeout=5.0,
            verify_click_result=True
        )
        
        # 分享总结按钮
        self.ui_elements["share_summary_button"] = self.ui_factory.create_button(
            "share_summary_button",
            confidence_threshold=0.8,
            timeout=3.0
        )
        
        # 打印报告按钮
        self.ui_elements["print_report_button"] = self.ui_factory.create_button(
            "print_report_button",
            confidence_threshold=0.8,
            timeout=3.0
        )
        
        # 完成总结按钮
        self.ui_elements["finish_summary_button"] = self.ui_factory.create_enhanced_button(
            "finish_summary_button",
            confidence_threshold=0.9,
            timeout=5.0,
            expected_scene_after_click="main_menu",
            verify_click_result=True
        )
        
        # 返回结果按钮
        self.ui_elements["back_to_result_button"] = self.ui_factory.create_button(
            "back_to_result_button",
            confidence_threshold=0.8,
            timeout=3.0
        )
    
    def _get_critical_elements(self) -> List[str]:
        """获取总结场景的关键UI元素"""
        return [
            "summary_title",
            "current_category",
            "overall_evaluation",
            "finish_summary_button",
            "export_report_button"
        ]
    
    def _verify_scene_specific_state(self) -> bool:
        """验证总结场景特定状态"""
        try:
            # 验证总结标题可见
            title_element = self.get_ui_element("summary_title")
            if not title_element or not title_element.is_visible():
                self.scene_logger.debug("总结标题不可见")
                return False
            
            # 验证当前类别显示可见
            category_element = self.get_ui_element("current_category")
            if not category_element or not category_element.is_visible():
                self.scene_logger.debug("当前类别显示不可见")
                return False
            
            # 验证至少有一个导航按钮可见
            nav_buttons = [f"{cat.value}_tab_button" for cat in SummaryCategory]
            visible_nav = 0
            
            for button_name in nav_buttons:
                button = self.get_ui_element(button_name)
                if button and button.is_visible():
                    visible_nav += 1
            
            if visible_nav < 3:
                self.scene_logger.debug(f"可见导航按钮数量不足: {visible_nav}/6")
                return False
            
            return True
            
        except Exception as e:
            self.scene_logger.error(f"总结场景状态验证异常: {e}")
            return False
    
    def switch_category(self, category: SummaryCategory) -> bool:
        """
        切换总结类别
        
        Args:
            category: 总结类别
            
        Returns:
            是否切换成功
        """
        try:
            self.scene_logger.info(f"切换总结类别: {category.value}")
            
            # 记录UI元素使用
            self.optimizer.record_ui_element_usage(GameScene.SUMMARY, f"{category.value}_tab_button")
            
            button_name = f"{category.value}_tab_button"
            
            if not self.interact_with_element(button_name, "click"):
                self.scene_logger.error(f"点击{category.value}标签按钮失败")
                return False
            
            # 等待内容加载
            time.sleep(1.0)
            
            self._current_category = category
            
            # 根据类别加载相应数据
            self._load_category_data(category)
            
            self.scene_logger.info(f"总结类别切换成功: {category.value}")
            return True
            
        except Exception as e:
            self.scene_logger.error(f"切换总结类别失败: {e}")
            return False
    
    def _load_category_data(self, category: SummaryCategory):
        """加载类别数据"""
        try:
            if category == SummaryCategory.OVERVIEW:
                self._load_overview_data()
            elif category == SummaryCategory.TIMELINE:
                self._load_timeline_data()
            elif category == SummaryCategory.STATISTICS:
                self._load_statistics_data()
            elif category == SummaryCategory.ACHIEVEMENTS:
                self._load_achievements_data()
            elif category == SummaryCategory.LESSONS:
                self._load_lessons_data()
            elif category == SummaryCategory.COMPARISON:
                self._load_comparison_data()
                
        except Exception as e:
            self.scene_logger.error(f"加载类别数据失败: {e}")
    
    def _load_overview_data(self):
        """加载总览数据"""
        try:
            # 读取总体评价
            eval_element = self.get_ui_element("overall_evaluation")
            if eval_element:
                eval_text = eval_element.read_text()
                if eval_text:
                    self._summary_data["overall_evaluation"] = eval_text.strip()
            
            # 读取关键指标
            key_metrics = ["final_score", "completion_time", "efficiency_rating", "growth_total"]
            for metric in key_metrics:
                metric_element = self.get_ui_element(metric)
                if metric_element:
                    metric_text = metric_element.read_text()
                    if metric_text:
                        self._summary_data[metric] = metric_text.strip()
            
        except Exception as e:
            self.scene_logger.error(f"加载总览数据失败: {e}")
    
    def _load_timeline_data(self):
        """加载时间线数据"""
        try:
            self._timeline_events.clear()
            self._key_moments.clear()
            
            # 读取时间线事件
            for i in range(1, 11):
                event_element = self.get_ui_element(f"timeline_event_{i}")
                if event_element and event_element.is_visible():
                    event_text = event_element.read_text()
                    if event_text:
                        self._timeline_events.append({
                            "index": i,
                            "description": event_text.strip(),
                            "timestamp": datetime.now() - timedelta(days=10-i)
                        })
            
            # 读取关键里程碑
            for i in range(1, 6):
                milestone_element = self.get_ui_element(f"milestone_{i}")
                if milestone_element and milestone_element.is_visible():
                    milestone_text = milestone_element.read_text()
                    if milestone_text:
                        self._key_moments.append({
                            "index": i,
                            "description": milestone_text.strip(),
                            "type": TimelineEvent.MILESTONE
                        })
            
        except Exception as e:
            self.scene_logger.error(f"加载时间线数据失败: {e}")
    
    def _load_statistics_data(self):
        """加载统计数据"""
        try:
            # 读取各类统计
            stat_categories = ["lessons", "battles", "exams", "events", "achievements"]
            for category in stat_categories:
                stat_element = self.get_ui_element(f"{category}_stats")
                if stat_element:
                    stat_text = stat_element.read_text()
                    if stat_text:
                        import re
                        stat_match = re.search(r'\d+', stat_text)
                        if stat_match:
                            self._summary_data[f"{category}_count"] = int(stat_match.group())
            
            # 读取效率指标
            efficiency_metrics = ["time_efficiency", "resource_efficiency", "growth_efficiency"]
            for metric in efficiency_metrics:
                metric_element = self.get_ui_element(metric)
                if metric_element:
                    metric_text = metric_element.read_text()
                    if metric_text:
                        import re
                        metric_match = re.search(r'(\d+\.?\d*)%', metric_text)
                        if metric_match:
                            self._performance_metrics[metric] = float(metric_match.group(1)) / 100.0
            
        except Exception as e:
            self.scene_logger.error(f"加载统计数据失败: {e}")
    
    def _load_achievements_data(self):
        """加载成就数据"""
        try:
            # 这里可以从之前的结果场景获取成就数据
            # 简化实现，直接从UI读取
            pass
            
        except Exception as e:
            self.scene_logger.error(f"加载成就数据失败: {e}")
    
    def _load_lessons_data(self):
        """加载经验教训数据"""
        try:
            self._insights.clear()
            self._recommendations.clear()
            
            # 读取主要洞察
            for i in range(1, 6):
                insight_element = self.get_ui_element(f"insight_{i}")
                if insight_element and insight_element.is_visible():
                    insight_text = insight_element.read_text()
                    if insight_text:
                        self._insights.append({
                            "type": InsightType.STRENGTH,  # 简化分类
                            "content": insight_text.strip(),
                            "priority": i
                        })
            
            # 读取改进建议
            for i in range(1, 6):
                rec_element = self.get_ui_element(f"recommendation_{i}")
                if rec_element and rec_element.is_visible():
                    rec_text = rec_element.read_text()
                    if rec_text:
                        self._recommendations.append({
                            "type": InsightType.RECOMMENDATION,
                            "content": rec_text.strip(),
                            "priority": i
                        })
            
        except Exception as e:
            self.scene_logger.error(f"加载经验教训数据失败: {e}")
    
    def _load_comparison_data(self):
        """加载对比数据"""
        try:
            # 简化实现，基于当前数据生成对比
            self._comparison_data = {
                "current_performance": self._performance_metrics.copy(),
                "improvement_potential": {
                    "efficiency": 0.1,
                    "consistency": 0.05,
                    "growth_rate": 0.15
                }
            }
            
        except Exception as e:
            self.scene_logger.error(f"加载对比数据失败: {e}")
    
    def export_report(self) -> bool:
        """导出报告"""
        try:
            self.scene_logger.info("导出总结报告")
            
            if not self.interact_with_element("export_report_button", "click"):
                self.scene_logger.error("点击导出报告按钮失败")
                return False
            
            # 等待导出处理
            time.sleep(3.0)
            
            self.scene_logger.info("总结报告导出成功")
            return True
            
        except Exception as e:
            self.scene_logger.error(f"导出报告失败: {e}")
            return False
    
    def save_summary(self) -> bool:
        """保存总结"""
        try:
            self.scene_logger.info("保存总结")
            
            if not self.interact_with_element("save_summary_button", "click"):
                self.scene_logger.error("点击保存总结按钮失败")
                return False
            
            time.sleep(2.0)
            self.scene_logger.info("总结保存成功")
            return True
            
        except Exception as e:
            self.scene_logger.error(f"保存总结失败: {e}")
            return False
    
    def finish_summary(self) -> bool:
        """完成总结"""
        try:
            self.scene_logger.info("完成总结")
            
            if not self.interact_with_element("finish_summary_button", "click"):
                self.scene_logger.error("点击完成总结按钮失败")
                return False
            
            time.sleep(2.0)
            self.scene_logger.info("总结完成，返回主菜单")
            return True
            
        except Exception as e:
            self.scene_logger.error(f"完成总结失败: {e}")
            return False
    
    def get_summary_data(self) -> Dict[str, Any]:
        """获取总结数据"""
        return {
            "current_category": self._current_category.value,
            "summary_data": self._summary_data.copy(),
            "timeline_events_count": len(self._timeline_events),
            "key_moments_count": len(self._key_moments),
            "performance_metrics": self._performance_metrics.copy(),
            "insights_count": len(self._insights),
            "recommendations_count": len(self._recommendations),
            "comparison_data": self._comparison_data.copy(),
            "scene_ready": self.is_scene_ready()
        }
    
    def get_key_insights(self, count: int = 3) -> List[Dict[str, Any]]:
        """获取关键洞察"""
        return sorted(self._insights, key=lambda x: x.get("priority", 999))[:count]
    
    def get_top_recommendations(self, count: int = 3) -> List[Dict[str, Any]]:
        """获取主要建议"""
        return sorted(self._recommendations, key=lambda x: x.get("priority", 999))[:count]
    
    def get_performance_score(self) -> float:
        """获取综合性能评分"""
        if not self._performance_metrics:
            return 0.0
        
        # 计算加权平均分
        weights = {
            "efficiency": 0.3,
            "consistency": 0.2,
            "growth_rate": 0.3,
            "balance_score": 0.2
        }
        
        total_score = 0.0
        total_weight = 0.0
        
        for metric, value in self._performance_metrics.items():
            if metric in weights:
                total_score += value * weights[metric]
                total_weight += weights[metric]
        
        return total_score / total_weight if total_weight > 0 else 0.0
