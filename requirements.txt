# Gakumasu-Bo<PERSON> Dependencies
# Python 3.13.3+ required

# Computer Vision and Image Processing
opencv-python>=4.9.0.80
mss>=9.0.1

# OCR and Text Recognition (with Japanese support)
easyocr>=1.7.1

# Deep Learning Backend
torch>=2.3.0
torchvision>=0.18.0

# Object Detection
ultralytics>=8.2.28

# Input Simulation
pydirectinput>=1.0.4
pywin32>=306

# Configuration and Data
pyyaml>=6.0.1

# Scheduling and Task Management
apscheduler>=3.10.4

# Testing
pytest>=8.2.2
pytest-cov>=5.0.0
pytest-mock>=3.12.0

# Logging and Utilities
colorlog>=6.8.2
tqdm>=4.66.4

# Data Processing
numpy>=1.26.4
pandas>=2.2.2

# Web UI and API
fastapi>=0.104.1
uvicorn[standard]>=0.24.0
websockets>=12.0
requests>=2.31.0

# Legacy Web UI (Optional)
flask>=3.0.3
flask-socketio>=5.3.6

# Development Tools
black>=24.4.2
flake8>=7.0.0
mypy>=1.10.0
