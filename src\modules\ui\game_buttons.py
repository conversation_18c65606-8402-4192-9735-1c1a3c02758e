"""
游戏特定按钮类实现
包含各种游戏场景中的特定按钮
"""

import time
from typing import Optional, List

from ...utils.logger import get_logger
from ...core.data_structures import GameScene
from .base_ui_element import Button


class ProduceButton(Button):
    """育成按钮类"""
    
    def __init__(self, perception_module, action_controller, **kwargs):
        super().__init__(
            template_name="produce_button",
            perception_module=perception_module,
            action_controller=action_controller,
            **kwargs
        )
    
    def click(self) -> bool:
        """
        点击育成按钮并验证结果
        
        Returns:
            是否成功导航到育成准备界面
        """
        self.logger.info("点击育成按钮")
        
        # 执行基础点击操作
        if not super().click():
            return False
        
        # 验证是否成功跳转到育成准备界面
        return self.verify_navigation_result()
    
    def verify_navigation_result(self) -> bool:
        """
        验证导航结果
        
        Returns:
            是否成功跳转到目标场景
        """
        try:
            # 等待场景切换
            time.sleep(2.0)
            
            # 检查当前场景
            game_state = self.perception.get_game_state()
            if game_state.current_scene == GameScene.PRODUCE_SETUP:
                self.logger.info("成功导航到育成准备界面")
                return True
            else:
                self.logger.warning(f"导航失败，当前场景: {game_state.current_scene.value}")
                return False
                
        except Exception as e:
            self.logger.error(f"验证导航结果失败: {e}")
            return False


class PartTimeJobButton(Button):
    """打工按钮类"""
    
    def __init__(self, perception_module, action_controller, **kwargs):
        super().__init__(
            template_name="part_time_job_button",
            perception_module=perception_module,
            action_controller=action_controller,
            **kwargs
        )
    
    def click(self) -> bool:
        """点击打工按钮"""
        self.logger.info("点击打工按钮")
        return super().click()


class DailyTasksButton(Button):
    """日常任务按钮类"""
    
    def __init__(self, perception_module, action_controller, **kwargs):
        super().__init__(
            template_name="daily_tasks_button",
            perception_module=perception_module,
            action_controller=action_controller,
            **kwargs
        )
    
    def click(self) -> bool:
        """点击日常任务按钮"""
        self.logger.info("点击日常任务按钮")
        return super().click()


class IdolSelectionButton(Button):
    """偶像选择按钮类"""
    
    def __init__(self, perception_module, action_controller, **kwargs):
        super().__init__(
            template_name="idol_selection_button",
            perception_module=perception_module,
            action_controller=action_controller,
            **kwargs
        )
    
    def click(self) -> bool:
        """点击偶像选择按钮"""
        self.logger.info("点击偶像选择按钮")
        return super().click()


class SupportCardButton(Button):
    """支援卡选择按钮类"""
    
    def __init__(self, perception_module, action_controller, **kwargs):
        super().__init__(
            template_name="support_card_button",
            perception_module=perception_module,
            action_controller=action_controller,
            **kwargs
        )
    
    def click(self) -> bool:
        """点击支援卡选择按钮"""
        self.logger.info("点击支援卡选择按钮")
        return super().click()


class StartProduceButton(Button):
    """开始育成按钮类"""
    
    def __init__(self, perception_module, action_controller, **kwargs):
        super().__init__(
            template_name="start_produce_button",
            perception_module=perception_module,
            action_controller=action_controller,
            **kwargs
        )
    
    def click(self) -> bool:
        """
        点击开始育成按钮并验证结果
        
        Returns:
            是否成功开始育成
        """
        self.logger.info("点击开始育成按钮")
        
        # 执行基础点击操作
        if not super().click():
            return False
        
        # 验证是否成功进入育成主界面
        return self.verify_produce_start()
    
    def verify_produce_start(self) -> bool:
        """
        验证育成是否成功开始
        
        Returns:
            是否成功进入育成主界面
        """
        try:
            # 等待场景切换
            time.sleep(3.0)
            
            # 检查当前场景
            game_state = self.perception.get_game_state()
            if game_state.current_scene == GameScene.PRODUCE_MAIN:
                self.logger.info("成功开始育成")
                return True
            else:
                self.logger.warning(f"育成开始失败，当前场景: {game_state.current_scene.value}")
                return False
                
        except Exception as e:
            self.logger.error(f"验证育成开始失败: {e}")
            return False


class LessonButton(Button):
    """课程按钮类"""
    
    def __init__(self, lesson_type: str, perception_module, action_controller, **kwargs):
        """
        初始化课程按钮
        
        Args:
            lesson_type: 课程类型 (vocal, dance, visual, mental)
            perception_module: 感知模块
            action_controller: 行动控制器
            **kwargs: 其他参数
        """
        template_name = f"{lesson_type}_lesson_button"
        super().__init__(
            template_name=template_name,
            perception_module=perception_module,
            action_controller=action_controller,
            **kwargs
        )
        self.lesson_type = lesson_type
    
    def click(self) -> bool:
        """点击课程按钮"""
        self.logger.info(f"点击{self.lesson_type}课程按钮")
        return super().click()


class RestButton(Button):
    """休息按钮类"""
    
    def __init__(self, perception_module, action_controller, **kwargs):
        super().__init__(
            template_name="rest_button",
            perception_module=perception_module,
            action_controller=action_controller,
            **kwargs
        )
    
    def click(self) -> bool:
        """点击休息按钮"""
        self.logger.info("点击休息按钮")
        return super().click()


class OutingButton(Button):
    """外出按钮类"""
    
    def __init__(self, perception_module, action_controller, **kwargs):
        super().__init__(
            template_name="outing_button",
            perception_module=perception_module,
            action_controller=action_controller,
            **kwargs
        )
    
    def click(self) -> bool:
        """点击外出按钮"""
        self.logger.info("点击外出按钮")
        return super().click()


class ConfirmButton(Button):
    """确认按钮类"""
    
    def __init__(self, perception_module, action_controller, **kwargs):
        super().__init__(
            template_name="confirm_button",
            perception_module=perception_module,
            action_controller=action_controller,
            **kwargs
        )
    
    def click(self) -> bool:
        """点击确认按钮"""
        self.logger.info("点击确认按钮")
        return super().click()


class CancelButton(Button):
    """取消按钮类"""
    
    def __init__(self, perception_module, action_controller, **kwargs):
        super().__init__(
            template_name="cancel_button",
            perception_module=perception_module,
            action_controller=action_controller,
            **kwargs
        )
    
    def click(self) -> bool:
        """点击取消按钮"""
        self.logger.info("点击取消按钮")
        return super().click()


class MenuButton(Button):
    """菜单按钮类"""
    
    def __init__(self, perception_module, action_controller, **kwargs):
        super().__init__(
            template_name="menu_button",
            perception_module=perception_module,
            action_controller=action_controller,
            **kwargs
        )
    
    def click(self) -> bool:
        """点击菜单按钮"""
        self.logger.info("点击菜单按钮")
        return super().click()


class BackButton(Button):
    """返回按钮类"""
    
    def __init__(self, perception_module, action_controller, **kwargs):
        super().__init__(
            template_name="back_button",
            perception_module=perception_module,
            action_controller=action_controller,
            **kwargs
        )
    
    def click(self) -> bool:
        """点击返回按钮"""
        self.logger.info("点击返回按钮")
        return super().click()
