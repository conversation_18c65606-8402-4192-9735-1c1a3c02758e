# 游戏截图数据收集工具技术文档

**版本：** 1.0  
**日期：** 2025年7月27日  
**项目：** Gakumasu-Bot 游戏截图数据收集工具

## 一、项目概述

### 1.1 项目简介
游戏截图数据收集工具是 Gakumasu-Bot 项目的重要组成部分，专门用于收集和管理游戏运行过程中的截图数据。该工具提供了完整的 Web UI 界面，支持实时预览、多种截图模式和历史记录管理。

### 1.2 核心功能
- **多模式截图**：支持全屏、窗口、区域三种截图模式
- **实时预览**：WebSocket 实时游戏窗口预览
- **历史管理**：完整的截图历史记录和文件管理
- **配置管理**：灵活的截图参数配置
- **批量操作**：支持批量删除和导出功能

### 1.3 技术架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Vue.js 前端   │◄──►│  FastAPI 后端   │◄──►│  截图收集器     │
│                 │    │                 │    │                 │
│ - 用户界面      │    │ - API 接口      │    │ - 屏幕捕获      │
│ - 实时预览      │    │ - WebSocket     │    │ - 文件管理      │
│ - 状态管理      │    │ - 状态管理      │    │ - 历史记录      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 二、系统架构

### 2.1 后端架构

#### 2.1.1 核心模块
- **ScreenshotCollector** (`src/modules/screenshot_collector.py`)
  - 主要截图收集逻辑
  - 文件存储管理
  - 历史记录维护

- **WebAPIAdapter** (`src/web/main.py`)
  - Web API 接口适配
  - WebSocket 连接管理
  - 与核心系统集成

- **数据模型** (`src/web/models.py`)
  - Pydantic 数据模型定义
  - API 请求/响应格式
  - 数据验证规则

#### 2.1.2 API 接口
```
GET    /api/v1/screenshot/preview          # 获取预览图像
POST   /api/v1/screenshot/capture          # 执行截图
GET    /api/v1/screenshot/history          # 获取历史记录
DELETE /api/v1/screenshot/{id}             # 删除截图
GET    /api/v1/screenshot/stats            # 获取统计信息
POST   /api/v1/screenshot/batch-delete     # 批量删除
GET    /api/v1/screenshot/{id}/download     # 下载截图
```

#### 2.1.3 WebSocket 消息
```javascript
// 客户端 → 服务端
{ type: "start_preview" }                   // 启动预览
{ type: "stop_preview" }                    // 停止预览
{ type: "get_screenshot_stats" }            // 获取统计

// 服务端 → 客户端
{ type: "screenshot_preview", data: {...} } // 预览数据
{ type: "screenshot_task_completed", ... }  // 任务完成
{ type: "screenshot_task_failed", ... }     // 任务失败
```

### 2.2 前端架构

#### 2.2.1 组件结构
```
ScreenshotTool.vue (主页面)
├── ControlPanel.vue (控制面板)
│   ├── 截图模式选择
│   ├── 参数配置
│   └── 快速操作
├── PreviewArea.vue (预览区域)
│   ├── Canvas 预览
│   ├── 区域选择
│   └── 预览控制
├── HistoryPanel.vue (历史面板)
│   ├── 历史列表
│   ├── 搜索筛选
│   └── 批量操作
└── SettingsPanel.vue (设置面板)
    ├── 配置管理
    ├── 导入导出
    └── 参数验证
```

#### 2.2.2 状态管理
- **响应式数据**：Vue 3 Composition API
- **API 调用**：`useScreenshotApi` 组合式函数
- **WebSocket**：`useWebSocket` 组合式函数
- **错误处理**：统一的错误处理和用户提示

#### 2.2.3 路由配置
```javascript
{
  path: '/screenshot-tool',
  name: 'ScreenshotTool',
  component: () => import('../views/ScreenshotTool.vue'),
  meta: {
    title: '截图工具',
    icon: 'Camera'
  }
}
```

## 三、核心功能实现

### 3.1 截图功能

#### 3.1.1 截图模式
```python
class ScreenshotMode(Enum):
    FULLSCREEN = "fullscreen"  # 全屏截图
    WINDOW = "window"          # 窗口截图
    REGION = "region"          # 区域截图
```

#### 3.1.2 截图流程
1. **配置验证**：验证截图参数的有效性
2. **窗口检测**：定位游戏窗口
3. **图像捕获**：根据模式执行截图
4. **图像处理**：格式转换和质量调整
5. **文件保存**：保存到指定目录
6. **缩略图生成**：创建预览缩略图
7. **历史记录**：添加到历史记录

#### 3.1.3 错误处理
```python
try:
    result = await capture_single_shot(config)
except GameWindowNotFound:
    # 游戏窗口未找到
except ScreenCaptureError:
    # 屏幕捕获错误
except ScreenshotCollectorError:
    # 截图收集器错误
```

### 3.2 实时预览

#### 3.2.1 预览流实现
```python
async def start_preview_stream():
    while preview_active:
        image = capture_screen()
        if image:
            # 图像缩放优化
            if width > 1280:
                image = resize_image(image, 1280)
            
            # JPEG 压缩
            _, buffer = cv2.imencode('.jpg', image, [QUALITY, 60])
            yield buffer.tobytes()
        
        await asyncio.sleep(frame_interval)
```

#### 3.2.2 前端预览处理
```javascript
onMessage('screenshot_preview', (data) => {
  const imageUrl = 'data:image/jpeg;base64,' + data.image_data
  previewImage.value = imageUrl
  loadImageToCanvas(imageUrl)
})
```

### 3.3 区域选择

#### 3.3.1 鼠标事件处理
```javascript
function startRegionSelect(event) {
  const rect = canvas.getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top
  
  isSelecting.value = true
  selectionStart.value = { x, y }
}

function updateRegionSelect(event) {
  if (!isSelecting.value) return
  
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top
  
  currentRegion.value = {
    x: Math.min(startX, x),
    y: Math.min(startY, y),
    width: Math.abs(x - startX),
    height: Math.abs(y - startY)
  }
}
```

#### 3.3.2 区域调整手柄
```vue
<div class="resize-handles" v-if="selectedRegion">
  <div class="handle handle-nw" @mousedown="startResize('nw', $event)"></div>
  <div class="handle handle-ne" @mousedown="startResize('ne', $event)"></div>
  <div class="handle handle-sw" @mousedown="startResize('sw', $event)"></div>
  <div class="handle handle-se" @mousedown="startResize('se', $event)"></div>
</div>
```

### 3.4 文件管理

#### 3.4.1 存储结构
```
screenshots/
├── screenshot_20250127_143022_a1b2c3d4.png
├── screenshot_20250127_143045_e5f6g7h8.jpg
└── thumbnails/
    ├── screenshot_20250127_143022_a1b2c3d4_thumb.png
    └── screenshot_20250127_143045_e5f6g7h8_thumb.jpg
```

#### 3.4.2 文件命名规则
```python
def generate_filename(config: ScreenshotConfig) -> str:
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    unique_id = str(uuid.uuid4())[:8]
    extension = config.format.value
    
    return f"{config.filename_prefix}_{timestamp}_{unique_id}.{extension}"
```

#### 3.4.3 缩略图生成
```python
def create_thumbnail(image: np.ndarray, size: tuple = (200, 150)) -> str:
    height, width = image.shape[:2]
    aspect_ratio = width / height
    
    if aspect_ratio > size[0] / size[1]:
        new_width = size[0]
        new_height = int(size[0] / aspect_ratio)
    else:
        new_height = size[1]
        new_width = int(size[1] * aspect_ratio)
    
    thumbnail = cv2.resize(image, (new_width, new_height))
    return thumbnail
```

## 四、配置管理

### 4.1 截图配置
```python
@dataclass
class ScreenshotConfig:
    mode: ScreenshotMode = ScreenshotMode.WINDOW
    format: ScreenshotFormat = ScreenshotFormat.PNG
    quality: int = 90
    region: Optional[Dict[str, int]] = None
    save_to_disk: bool = True
    filename_prefix: str = "screenshot"
```

### 4.2 工具配置
```python
class ScreenshotToolConfig(BaseModel):
    game_window: GameWindowConfig
    storage: StorageConfig
    preview_fps: int = 2
    default_quality: int = 90
    default_format: ScreenshotFormatEnum = ScreenshotFormatEnum.PNG
```

### 4.3 配置验证
```python
def validate_config(config: ScreenshotConfig):
    if config.mode == ScreenshotMode.REGION and not config.region:
        raise ScreenshotCollectorError("区域截图需要指定区域坐标")
    
    if not (1 <= config.quality <= 100):
        raise ScreenshotCollectorError("图片质量必须在1-100之间")
```

## 五、性能优化

### 5.1 预览流优化
- **帧率控制**：避免过于频繁的截图
- **图像缩放**：限制预览图像最大尺寸
- **压缩优化**：使用较低质量JPEG减少传输量
- **错误恢复**：连续错误时自动停止预览

### 5.2 内存管理
- **图像释放**：及时释放不需要的图像资源
- **缓存控制**：限制历史记录数量
- **垃圾回收**：定期清理过期文件

### 5.3 网络优化
- **WebSocket 复用**：单一连接处理多种消息
- **数据压缩**：Base64 编码传输图像数据
- **连接管理**：自动重连和心跳检测

## 六、安全考虑

### 6.1 文件安全
- **路径验证**：防止路径遍历攻击
- **文件类型检查**：限制允许的文件格式
- **大小限制**：防止磁盘空间耗尽

### 6.2 API 安全
- **输入验证**：严格验证所有输入参数
- **错误处理**：不泄露敏感信息
- **访问控制**：限制文件访问权限

### 6.3 WebSocket 安全
- **连接验证**：验证连接来源
- **消息验证**：验证消息格式和内容
- **频率限制**：防止消息洪水攻击

---

**下一部分：用户使用指南和API参考**
