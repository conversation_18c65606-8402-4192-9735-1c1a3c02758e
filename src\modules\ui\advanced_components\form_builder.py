"""
表单构建器
提供动态表单创建、验证和提交功能
"""

import time
from typing import List, Dict, Any, Optional, Callable, Union
from enum import Enum
from dataclasses import dataclass, field

from ....utils.logger import get_logger
from ..elements.base_ui_element import BaseUIElement
from ..elements.button import Button
from ..elements.input_field import InputField
from ..elements.label import Label
from ..utils.performance_monitor import measure_block


class FieldType(Enum):
    """字段类型枚举"""
    TEXT = "text"                 # 文本输入
    PASSWORD = "password"         # 密码输入
    EMAIL = "email"              # 邮箱输入
    NUMBER = "number"            # 数字输入
    TEXTAREA = "textarea"        # 多行文本
    SELECT = "select"            # 下拉选择
    CHECKBOX = "checkbox"        # 复选框
    RADIO = "radio"              # 单选框
    DATE = "date"                # 日期选择
    TIME = "time"                # 时间选择
    FILE = "file"                # 文件上传
    SLIDER = "slider"            # 滑块
    RATING = "rating"            # 评分
    COLOR = "color"              # 颜色选择


class ValidationType(Enum):
    """验证类型枚举"""
    REQUIRED = "required"         # 必填
    MIN_LENGTH = "min_length"     # 最小长度
    MAX_LENGTH = "max_length"     # 最大长度
    PATTERN = "pattern"           # 正则模式
    EMAIL = "email"               # 邮箱格式
    NUMBER = "number"             # 数字格式
    RANGE = "range"               # 数值范围
    CUSTOM = "custom"             # 自定义验证


class FormLayout(Enum):
    """表单布局枚举"""
    VERTICAL = "vertical"         # 垂直布局
    HORIZONTAL = "horizontal"     # 水平布局
    INLINE = "inline"            # 内联布局
    GRID = "grid"                # 网格布局


@dataclass
class FormValidator:
    """表单验证器"""
    validation_type: ValidationType
    value: Any = None
    message: str = ""
    custom_validator: Optional[Callable] = None


@dataclass
class FormField:
    """表单字段"""
    id: str
    name: str
    field_type: FieldType
    label: str
    placeholder: str = ""
    default_value: Any = None
    required: bool = False
    disabled: bool = False
    readonly: bool = False
    validators: List[FormValidator] = field(default_factory=list)
    options: List[Dict[str, Any]] = field(default_factory=list)  # 用于select, radio等
    attributes: Dict[str, Any] = field(default_factory=dict)
    css_classes: List[str] = field(default_factory=list)
    help_text: str = ""
    
    # 布局相关
    grid_column: Optional[int] = None
    grid_row: Optional[int] = None
    width: Optional[str] = None


@dataclass
class FormSubmissionResult:
    """表单提交结果"""
    success: bool
    data: Dict[str, Any] = field(default_factory=dict)
    errors: Dict[str, List[str]] = field(default_factory=dict)
    warnings: Dict[str, List[str]] = field(default_factory=dict)
    message: str = ""
    timestamp: float = field(default_factory=time.time)


class FormBuilder:
    """表单构建器"""
    
    def __init__(self, form_id: str, title: str = "", description: str = ""):
        """
        初始化表单构建器
        
        Args:
            form_id: 表单ID
            title: 表单标题
            description: 表单描述
        """
        self.form_id = form_id
        self.title = title
        self.description = description
        self.logger = get_logger("FormBuilder")
        
        # 表单配置
        self.layout = FormLayout.VERTICAL
        self.css_classes: List[str] = []
        self.attributes: Dict[str, Any] = {}
        
        # 表单字段
        self.fields: List[FormField] = []
        self.field_map: Dict[str, FormField] = {}
        
        # 表单状态
        self.form_data: Dict[str, Any] = {}
        self.validation_errors: Dict[str, List[str]] = {}
        self.is_valid = True
        
        # UI元素
        self.ui_elements: Dict[str, BaseUIElement] = {}
        self.submit_button: Optional[Button] = None
        self.reset_button: Optional[Button] = None
        
        # 事件处理器
        self.on_submit: Optional[Callable] = None
        self.on_validate: Optional[Callable] = None
        self.on_field_change: Optional[Callable] = None
        
        self.logger.info(f"表单构建器初始化完成: {form_id}")
    
    def add_field(self, field: FormField) -> 'FormBuilder':
        """
        添加表单字段
        
        Args:
            field: 表单字段
            
        Returns:
            表单构建器实例（支持链式调用）
        """
        if field.id in self.field_map:
            self.logger.warning(f"字段ID已存在，将被覆盖: {field.id}")
        
        self.fields.append(field)
        self.field_map[field.id] = field
        
        # 设置默认值
        if field.default_value is not None:
            self.form_data[field.id] = field.default_value
        
        self.logger.debug(f"添加表单字段: {field.id}")
        return self
    
    def add_text_field(self, field_id: str, label: str, **kwargs) -> 'FormBuilder':
        """添加文本字段"""
        field = FormField(
            id=field_id,
            name=kwargs.get('name', field_id),
            field_type=FieldType.TEXT,
            label=label,
            **{k: v for k, v in kwargs.items() if k != 'name'}
        )
        return self.add_field(field)
    
    def add_password_field(self, field_id: str, label: str, **kwargs) -> 'FormBuilder':
        """添加密码字段"""
        field = FormField(
            id=field_id,
            name=kwargs.get('name', field_id),
            field_type=FieldType.PASSWORD,
            label=label,
            **{k: v for k, v in kwargs.items() if k != 'name'}
        )
        return self.add_field(field)
    
    def add_email_field(self, field_id: str, label: str, **kwargs) -> 'FormBuilder':
        """添加邮箱字段"""
        field = FormField(
            id=field_id,
            name=kwargs.get('name', field_id),
            field_type=FieldType.EMAIL,
            label=label,
            validators=[FormValidator(ValidationType.EMAIL, message="请输入有效的邮箱地址")],
            **{k: v for k, v in kwargs.items() if k != 'name'}
        )
        return self.add_field(field)
    
    def add_number_field(self, field_id: str, label: str, min_val: Optional[float] = None,
                        max_val: Optional[float] = None, **kwargs) -> 'FormBuilder':
        """添加数字字段"""
        validators = [FormValidator(ValidationType.NUMBER, message="请输入有效的数字")]
        
        if min_val is not None or max_val is not None:
            validators.append(FormValidator(
                ValidationType.RANGE,
                value={"min": min_val, "max": max_val},
                message=f"数值应在{min_val or '无限制'}到{max_val or '无限制'}之间"
            ))
        
        field = FormField(
            id=field_id,
            name=kwargs.get('name', field_id),
            field_type=FieldType.NUMBER,
            label=label,
            validators=validators,
            **{k: v for k, v in kwargs.items() if k != 'name'}
        )
        return self.add_field(field)
    
    def add_select_field(self, field_id: str, label: str, options: List[Dict[str, Any]],
                        **kwargs) -> 'FormBuilder':
        """添加下拉选择字段"""
        field = FormField(
            id=field_id,
            name=kwargs.get('name', field_id),
            field_type=FieldType.SELECT,
            label=label,
            options=options,
            **{k: v for k, v in kwargs.items() if k != 'name'}
        )
        return self.add_field(field)
    
    def add_checkbox_field(self, field_id: str, label: str, **kwargs) -> 'FormBuilder':
        """添加复选框字段"""
        field = FormField(
            id=field_id,
            name=kwargs.get('name', field_id),
            field_type=FieldType.CHECKBOX,
            label=label,
            default_value=False,
            **{k: v for k, v in kwargs.items() if k != 'name'}
        )
        return self.add_field(field)
    
    def add_textarea_field(self, field_id: str, label: str, rows: int = 4, **kwargs) -> 'FormBuilder':
        """添加多行文本字段"""
        field = FormField(
            id=field_id,
            name=kwargs.get('name', field_id),
            field_type=FieldType.TEXTAREA,
            label=label,
            attributes={"rows": rows},
            **{k: v for k, v in kwargs.items() if k != 'name'}
        )
        return self.add_field(field)
    
    def add_validator(self, field_id: str, validator: FormValidator) -> 'FormBuilder':
        """为字段添加验证器"""
        if field_id in self.field_map:
            self.field_map[field_id].validators.append(validator)
        else:
            self.logger.error(f"字段不存在: {field_id}")
        
        return self
    
    def set_layout(self, layout: FormLayout) -> 'FormBuilder':
        """设置表单布局"""
        self.layout = layout
        return self
    
    def set_grid_layout(self, columns: int, rows: int = None) -> 'FormBuilder':
        """设置网格布局"""
        self.layout = FormLayout.GRID
        self.attributes["grid_columns"] = columns
        if rows:
            self.attributes["grid_rows"] = rows
        return self
    
    def build(self) -> Dict[str, BaseUIElement]:
        """
        构建表单UI元素
        
        Returns:
            UI元素字典
        """
        try:
            with measure_block("form_building"):
                self.ui_elements.clear()
                
                # 创建表单标题
                if self.title:
                    title_element = Label(
                        f"{self.form_id}_title",
                        confidence_threshold=0.9,
                        text_recognition_enabled=True
                    )
                    self.ui_elements["title"] = title_element
                
                # 创建表单描述
                if self.description:
                    desc_element = Label(
                        f"{self.form_id}_description",
                        confidence_threshold=0.8,
                        text_recognition_enabled=True
                    )
                    self.ui_elements["description"] = desc_element
                
                # 创建字段UI元素
                for field in self.fields:
                    field_elements = self._create_field_elements(field)
                    self.ui_elements.update(field_elements)
                
                # 创建提交按钮
                self.submit_button = Button(
                    f"{self.form_id}_submit",
                    confidence_threshold=0.9,
                    timeout=5.0,
                    verify_click_result=True
                )
                self.ui_elements["submit_button"] = self.submit_button
                
                # 创建重置按钮
                self.reset_button = Button(
                    f"{self.form_id}_reset",
                    confidence_threshold=0.8,
                    timeout=3.0
                )
                self.ui_elements["reset_button"] = self.reset_button
                
                self.logger.info(f"表单构建完成，共{len(self.ui_elements)}个UI元素")
                return self.ui_elements
                
        except Exception as e:
            self.logger.error(f"表单构建失败: {e}")
            return {}
    
    def _create_field_elements(self, field: FormField) -> Dict[str, BaseUIElement]:
        """创建字段UI元素"""
        elements = {}
        
        # 创建字段标签
        if field.label:
            label_element = Label(
                f"{field.id}_label",
                confidence_threshold=0.8,
                text_recognition_enabled=True
            )
            elements[f"{field.id}_label"] = label_element
        
        # 创建字段输入元素
        if field.field_type in [FieldType.TEXT, FieldType.PASSWORD, FieldType.EMAIL, 
                               FieldType.NUMBER]:
            input_element = InputField(
                field.id,
                confidence_threshold=0.9,
                timeout=5.0,
                verify_input_result=True
            )
            elements[field.id] = input_element
        
        elif field.field_type == FieldType.TEXTAREA:
            # 多行文本输入
            textarea_element = InputField(
                field.id,
                confidence_threshold=0.8,
                timeout=5.0,
                verify_input_result=True
            )
            elements[field.id] = textarea_element
        
        elif field.field_type == FieldType.SELECT:
            # 下拉选择
            select_element = InputField(
                field.id,
                confidence_threshold=0.8,
                timeout=3.0
            )
            elements[field.id] = select_element
        
        elif field.field_type == FieldType.CHECKBOX:
            # 复选框
            checkbox_element = Button(
                field.id,
                confidence_threshold=0.8,
                timeout=3.0
            )
            elements[field.id] = checkbox_element
        
        # 创建帮助文本
        if field.help_text:
            help_element = Label(
                f"{field.id}_help",
                confidence_threshold=0.7,
                text_recognition_enabled=True
            )
            elements[f"{field.id}_help"] = help_element
        
        # 创建错误信息显示
        error_element = Label(
            f"{field.id}_error",
            confidence_threshold=0.7,
            text_recognition_enabled=True
        )
        elements[f"{field.id}_error"] = error_element
        
        return elements
    
    def validate(self, data: Dict[str, Any] = None) -> bool:
        """
        验证表单数据
        
        Args:
            data: 要验证的数据，如果为None则验证当前表单数据
            
        Returns:
            是否验证通过
        """
        try:
            with measure_block("form_validation"):
                if data is None:
                    data = self.form_data
                
                self.validation_errors.clear()
                self.is_valid = True
                
                for field in self.fields:
                    field_value = data.get(field.id)
                    field_errors = []
                    
                    # 验证每个验证器
                    for validator in field.validators:
                        error_message = self._validate_field(field, field_value, validator)
                        if error_message:
                            field_errors.append(error_message)
                    
                    if field_errors:
                        self.validation_errors[field.id] = field_errors
                        self.is_valid = False
                
                # 调用自定义验证
                if self.on_validate:
                    try:
                        custom_errors = self.on_validate(data)
                        if custom_errors:
                            self.validation_errors.update(custom_errors)
                            self.is_valid = False
                    except Exception as e:
                        self.logger.error(f"自定义验证失败: {e}")
                        self.is_valid = False
                
                self.logger.debug(f"表单验证完成，结果: {self.is_valid}")
                return self.is_valid
                
        except Exception as e:
            self.logger.error(f"表单验证异常: {e}")
            self.is_valid = False
            return False
    
    def _validate_field(self, field: FormField, value: Any, validator: FormValidator) -> Optional[str]:
        """验证单个字段"""
        try:
            if validator.validation_type == ValidationType.REQUIRED:
                if value is None or value == "" or (isinstance(value, list) and len(value) == 0):
                    return validator.message or f"{field.label}是必填项"
            
            elif validator.validation_type == ValidationType.MIN_LENGTH:
                if value and len(str(value)) < validator.value:
                    return validator.message or f"{field.label}长度不能少于{validator.value}个字符"
            
            elif validator.validation_type == ValidationType.MAX_LENGTH:
                if value and len(str(value)) > validator.value:
                    return validator.message or f"{field.label}长度不能超过{validator.value}个字符"
            
            elif validator.validation_type == ValidationType.PATTERN:
                if value:
                    import re
                    if not re.match(validator.value, str(value)):
                        return validator.message or f"{field.label}格式不正确"
            
            elif validator.validation_type == ValidationType.EMAIL:
                if value:
                    import re
                    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
                    if not re.match(email_pattern, str(value)):
                        return validator.message or "请输入有效的邮箱地址"
            
            elif validator.validation_type == ValidationType.NUMBER:
                if value is not None:
                    try:
                        float(value)
                    except (ValueError, TypeError):
                        return validator.message or f"{field.label}必须是数字"
            
            elif validator.validation_type == ValidationType.RANGE:
                if value is not None:
                    try:
                        num_value = float(value)
                        min_val = validator.value.get("min")
                        max_val = validator.value.get("max")
                        
                        if min_val is not None and num_value < min_val:
                            return validator.message or f"{field.label}不能小于{min_val}"
                        if max_val is not None and num_value > max_val:
                            return validator.message or f"{field.label}不能大于{max_val}"
                    except (ValueError, TypeError):
                        return validator.message or f"{field.label}必须是有效数字"
            
            elif validator.validation_type == ValidationType.CUSTOM:
                if validator.custom_validator:
                    try:
                        if not validator.custom_validator(value, field):
                            return validator.message or f"{field.label}验证失败"
                    except Exception as e:
                        self.logger.error(f"自定义验证器执行失败: {e}")
                        return validator.message or f"{field.label}验证异常"
            
            return None
            
        except Exception as e:
            self.logger.error(f"字段验证异常: {e}")
            return f"{field.label}验证异常"
    
    def submit(self) -> FormSubmissionResult:
        """提交表单"""
        try:
            with measure_block("form_submission"):
                # 验证表单
                if not self.validate():
                    return FormSubmissionResult(
                        success=False,
                        errors=self.validation_errors,
                        message="表单验证失败"
                    )
                
                # 调用提交处理器
                if self.on_submit:
                    try:
                        result = self.on_submit(self.form_data)
                        if isinstance(result, FormSubmissionResult):
                            return result
                        elif isinstance(result, bool):
                            return FormSubmissionResult(
                                success=result,
                                data=self.form_data,
                                message="表单提交成功" if result else "表单提交失败"
                            )
                        else:
                            return FormSubmissionResult(
                                success=True,
                                data=result or self.form_data,
                                message="表单提交成功"
                            )
                    except Exception as e:
                        self.logger.error(f"表单提交处理器执行失败: {e}")
                        return FormSubmissionResult(
                            success=False,
                            message=f"表单提交失败: {e}"
                        )
                
                # 默认成功
                return FormSubmissionResult(
                    success=True,
                    data=self.form_data,
                    message="表单提交成功"
                )
                
        except Exception as e:
            self.logger.error(f"表单提交异常: {e}")
            return FormSubmissionResult(
                success=False,
                message=f"表单提交异常: {e}"
            )
    
    def reset(self):
        """重置表单"""
        self.form_data.clear()
        self.validation_errors.clear()
        self.is_valid = True
        
        # 恢复默认值
        for field in self.fields:
            if field.default_value is not None:
                self.form_data[field.id] = field.default_value
        
        self.logger.info("表单已重置")
    
    def set_field_value(self, field_id: str, value: Any):
        """设置字段值"""
        if field_id in self.field_map:
            self.form_data[field_id] = value
            
            # 触发字段变化事件
            if self.on_field_change:
                try:
                    self.on_field_change(field_id, value, self.form_data)
                except Exception as e:
                    self.logger.error(f"字段变化事件处理失败: {e}")
        else:
            self.logger.error(f"字段不存在: {field_id}")
    
    def get_field_value(self, field_id: str) -> Any:
        """获取字段值"""
        return self.form_data.get(field_id)
    
    def get_form_data(self) -> Dict[str, Any]:
        """获取表单数据"""
        return self.form_data.copy()
    
    def get_validation_errors(self) -> Dict[str, List[str]]:
        """获取验证错误"""
        return self.validation_errors.copy()
    
    def is_field_valid(self, field_id: str) -> bool:
        """检查字段是否有效"""
        return field_id not in self.validation_errors
