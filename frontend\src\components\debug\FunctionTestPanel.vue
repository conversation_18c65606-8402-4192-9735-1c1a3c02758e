<template>
  <div class="function-test-panel">
    <div class="panel-header">
      <h2>功能测试面板</h2>
      <div class="header-actions">
        <el-button 
          type="danger" 
          :icon="Delete"
          @click="clearTestResults"
          size="small"
        >
          清空结果
        </el-button>
      </div>
    </div>

    <el-row :gutter="20">
      <!-- 左侧：测试控制面板 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>测试控制</span>
            </div>
          </template>

          <!-- 感知模块测试 -->
          <div class="test-section">
            <h4>感知模块测试</h4>
            <div class="test-buttons">
              <el-button 
                type="primary" 
                :icon="Camera"
                @click="runTest('screenshot')"
                :loading="loading.test && currentTest === 'screenshot'"
              >
                屏幕捕获测试
              </el-button>
              <el-button 
                type="success" 
                :icon="View"
                @click="runTest('scene_recognition')"
                :loading="loading.test && currentTest === 'scene_recognition'"
              >
                场景识别测试
              </el-button>
            </div>
          </div>

          <!-- 决策模块测试 -->
          <div class="test-section">
            <h4>决策模块测试</h4>
            <div class="test-buttons">
              <el-button 
                type="warning" 
                :icon="BrainIcon"
                @click="runTest('decision_test')"
                :loading="loading.test && currentTest === 'decision_test'"
              >
                决策算法测试
              </el-button>
              <el-button 
                type="info" 
                :icon="TrendCharts"
                @click="runTest('heuristic_test')"
                :loading="loading.test && currentTest === 'heuristic_test'"
              >
                启发式评分测试
              </el-button>
            </div>
          </div>

          <!-- 行动模块测试 -->
          <div class="test-section">
            <h4>行动模块测试</h4>
            <div class="test-buttons">
              <el-button 
                type="danger" 
                :icon="Mouse"
                @click="runTest('input_simulation')"
                :loading="loading.test && currentTest === 'input_simulation'"
              >
                输入模拟测试
              </el-button>
              <el-button 
                type="success" 
                :icon="Check"
                @click="runTest('action_verification')"
                :loading="loading.test && currentTest === 'action_verification'"
              >
                操作验证测试
              </el-button>
            </div>
          </div>

          <!-- 调度模块测试 -->
          <div class="test-section">
            <h4>调度模块测试</h4>
            <div class="test-buttons">
              <el-button 
                type="primary" 
                :icon="Timer"
                @click="runTest('task_scheduling')"
                :loading="loading.test && currentTest === 'task_scheduling'"
              >
                任务调度测试
              </el-button>
              <el-button 
                type="warning" 
                :icon="Setting"
                @click="runTest('config_management')"
                :loading="loading.test && currentTest === 'config_management'"
              >
                配置管理测试
              </el-button>
            </div>
          </div>

          <!-- 综合测试 -->
          <div class="test-section">
            <h4>综合测试</h4>
            <div class="test-buttons">
              <el-button 
                type="success" 
                :icon="CircleCheck"
                @click="runTest('full_system_test')"
                :loading="loading.test && currentTest === 'full_system_test'"
                style="width: 100%;"
              >
                完整系统测试
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧：测试结果显示 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>测试结果</span>
              <el-tag v-if="recentTestResults.length > 0" type="info">
                共{{ recentTestResults.length }}条记录
              </el-tag>
            </div>
          </template>

          <div class="test-results">
            <div v-if="recentTestResults.length === 0" class="no-results">
              <el-empty description="暂无测试结果" :image-size="80">
                <el-button type="primary" @click="runTest('system_status')">
                  开始测试
                </el-button>
              </el-empty>
            </div>

            <div v-else class="results-list">
              <div 
                v-for="result in recentTestResults" 
                :key="result.id"
                class="result-item"
                :class="{ success: result.success, failed: !result.success }"
              >
                <div class="result-header">
                  <div class="result-info">
                    <span class="result-type">{{ getTestTypeName(result.type) }}</span>
                    <span class="result-time">{{ formatTime(result.timestamp) }}</span>
                  </div>
                  <div class="result-status">
                    <el-tag v-if="result.success" type="success" size="small">成功</el-tag>
                    <el-tag v-else type="danger" size="small">失败</el-tag>
                    <span v-if="result.duration" class="duration">
                      {{ result.duration }}ms
                    </span>
                  </div>
                </div>

                <div class="result-content">
                  <div v-if="result.success && result.result" class="result-data">
                    <el-collapse>
                      <el-collapse-item :title="`测试结果详情`" :name="result.id">
                        <pre class="result-json">{{ formatResult(result.result) }}</pre>
                      </el-collapse-item>
                    </el-collapse>
                  </div>
                  <div v-if="!result.success && result.error" class="result-error">
                    <el-alert
                      :title="result.error"
                      type="error"
                      :closable="false"
                      show-icon
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 测试配置对话框 -->
    <el-dialog
      v-model="showTestConfig"
      :title="`${currentTestName}配置`"
      width="50%"
    >
      <div class="test-config">
        <el-form :model="testConfig" label-width="120px">
          <el-form-item v-if="currentTest === 'screenshot'" label="截图模式">
            <el-select v-model="testConfig.mode" placeholder="选择截图模式">
              <el-option label="窗口模式" value="window" />
              <el-option label="全屏模式" value="fullscreen" />
              <el-option label="区域模式" value="region" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="currentTest === 'screenshot'" label="图片格式">
            <el-select v-model="testConfig.format" placeholder="选择图片格式">
              <el-option label="PNG" value="png" />
              <el-option label="JPEG" value="jpeg" />
              <el-option label="BMP" value="bmp" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="currentTest === 'screenshot'" label="图片质量">
            <el-slider v-model="testConfig.quality" :min="10" :max="100" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showTestConfig = false">取消</el-button>
          <el-button type="primary" @click="executeTest">执行测试</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { 
  Camera, View, Timer, Setting, Check, Delete,
  CircleCheck, TrendCharts, Mouse
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useDebugConsole } from '../../composables/useDebugConsole.js'

// 自定义图标组件
const BrainIcon = { template: '<i class="el-icon-cpu"></i>' }

// 使用调试控制台功能
const { 
  debugState,
  loading,
  recentTestResults,
  runFunctionTest,
  testScreenshot,
  getSystemStatus,
  getConfig,
  clearTestResults: clearResults
} = useDebugConsole()

// 响应式数据
const currentTest = ref('')
const showTestConfig = ref(false)
const testConfig = ref({
  mode: 'window',
  format: 'png',
  quality: 90
})

// 计算属性
const currentTestName = computed(() => {
  return getTestTypeName(currentTest.value)
})

// 方法
const runTest = async (testType) => {
  currentTest.value = testType
  
  // 某些测试需要配置
  if (testType === 'screenshot') {
    showTestConfig.value = true
    return
  }
  
  // 直接执行测试
  await executeTest(testType)
}

const executeTest = async (testType = currentTest.value) => {
  showTestConfig.value = false
  
  try {
    let result = null
    
    switch (testType) {
      case 'screenshot':
        result = await testScreenshot(testConfig.value)
        break
      case 'scene_recognition':
        result = await mockSceneRecognition()
        break
      case 'decision_test':
        result = await mockDecisionTest()
        break
      case 'heuristic_test':
        result = await mockHeuristicTest()
        break
      case 'input_simulation':
        result = await mockInputSimulation()
        break
      case 'action_verification':
        result = await mockActionVerification()
        break
      case 'task_scheduling':
        result = await mockTaskScheduling()
        break
      case 'config_management':
        result = await getConfig()
        break
      case 'full_system_test':
        result = await runFullSystemTest()
        break
      case 'system_status':
        result = await getSystemStatus()
        break
      default:
        throw new Error(`未知的测试类型: ${testType}`)
    }
    
    ElMessage.success(`${getTestTypeName(testType)}执行成功`)
  } catch (error) {
    ElMessage.error(`${getTestTypeName(testType)}执行失败`)
    console.error('测试执行错误:', error)
  }
}

// 模拟测试函数
const mockSceneRecognition = async () => {
  await new Promise(resolve => setTimeout(resolve, 1000))
  return {
    scene: 'main_menu',
    confidence: 0.95,
    templates_matched: ['main_menu_template_1', 'main_menu_template_2'],
    processing_time: 45
  }
}

const mockDecisionTest = async () => {
  await new Promise(resolve => setTimeout(resolve, 1500))
  return {
    algorithm: 'MCTS',
    iterations: 1000,
    best_action: 'select_card_1',
    confidence: 0.87,
    processing_time: 1450
  }
}

const mockHeuristicTest = async () => {
  await new Promise(resolve => setTimeout(resolve, 800))
  return {
    score_potential: 85,
    resource_efficiency: 92,
    synergy_bonus: 78,
    risk_assessment: 65,
    long_term_value: 88,
    total_score: 81.6
  }
}

const mockInputSimulation = async () => {
  await new Promise(resolve => setTimeout(resolve, 500))
  return {
    action: 'click',
    coordinates: { x: 640, y: 360 },
    success: true,
    response_time: 45
  }
}

const mockActionVerification = async () => {
  await new Promise(resolve => setTimeout(resolve, 600))
  return {
    expected_result: 'card_selected',
    actual_result: 'card_selected',
    verification_success: true,
    verification_time: 580
  }
}

const mockTaskScheduling = async () => {
  await new Promise(resolve => setTimeout(resolve, 300))
  return {
    active_tasks: 2,
    pending_tasks: 5,
    completed_tasks: 12,
    scheduler_status: 'running',
    next_task_eta: 30
  }
}

const runFullSystemTest = async () => {
  const results = {}
  
  // 依次执行各个模块的测试
  try {
    results.system_status = await getSystemStatus()
    results.screenshot = await testScreenshot({ mode: 'window', format: 'png' })
    results.scene_recognition = await mockSceneRecognition()
    results.config = await getConfig()
    
    return {
      overall_success: true,
      modules_tested: 4,
      modules_passed: 4,
      test_duration: 3000,
      details: results
    }
  } catch (error) {
    return {
      overall_success: false,
      error: error.message,
      partial_results: results
    }
  }
}

const clearTestResults = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有测试结果吗？',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    clearResults()
    ElMessage.success('测试结果已清空')
  } catch {
    // 用户取消
  }
}

const getTestTypeName = (type) => {
  const typeNames = {
    'screenshot': '屏幕捕获测试',
    'scene_recognition': '场景识别测试',
    'decision_test': '决策算法测试',
    'heuristic_test': '启发式评分测试',
    'input_simulation': '输入模拟测试',
    'action_verification': '操作验证测试',
    'task_scheduling': '任务调度测试',
    'config_management': '配置管理测试',
    'full_system_test': '完整系统测试',
    'system_status': '系统状态测试',
    'system_control': '系统控制'
  }
  return typeNames[type] || type
}

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString()
}

const formatResult = (result) => {
  return JSON.stringify(result, null, 2)
}
</script>

<style scoped>
.function-test-panel {
  height: 100%;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e6e6e6;
}

.panel-header h2 {
  margin: 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-section {
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.test-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.test-section h4 {
  margin: 0 0 15px 0;
  color: #409eff;
  font-size: 14px;
  font-weight: 600;
}

.test-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.test-buttons .el-button {
  flex: 1;
  min-width: 140px;
}

.test-results {
  max-height: 600px;
  overflow-y: auto;
}

.no-results {
  padding: 40px 0;
  text-align: center;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.result-item {
  border: 1px solid #e6e6e6;
  border-radius: 8px;
  padding: 15px;
  background: #fafafa;
}

.result-item.success {
  border-left: 4px solid #67c23a;
  background: #f0f9ff;
}

.result-item.failed {
  border-left: 4px solid #f56c6c;
  background: #fef0f0;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.result-info {
  display: flex;
  flex-direction: column;
}

.result-type {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.result-time {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.result-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.duration {
  font-size: 12px;
  color: #909399;
}

.result-content {
  margin-top: 10px;
}

.result-json {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 10px;
  font-size: 12px;
  color: #495057;
  max-height: 200px;
  overflow-y: auto;
}

.test-config {
  padding: 20px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
