"""
配置管理器
负责用户配置的加载、保存、验证和动态应用
"""

import json
import os
from typing import Dict, Any, Optional, List, Union
from pathlib import Path
from datetime import datetime
from threading import Lock

from ...utils.logger import get_logger
from ...core.data_structures import UserStrategy, GakumasuBotException


class ConfigManagerError(GakumasuBotException):
    """配置管理器错误"""
    pass


class ConfigManager:
    """配置管理器类"""
    
    def __init__(self, config_dir: str = "config"):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录
        """
        self.logger = get_logger("ConfigManager")
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # 配置文件路径
        self.user_config_file = self.config_dir / "user_config.json"
        self.system_config_file = self.config_dir / "system_config.json"
        self.profiles_dir = self.config_dir / "profiles"
        self.profiles_dir.mkdir(exist_ok=True)
        
        # 当前配置
        self.current_user_strategy: Optional[UserStrategy] = None
        self.current_system_config: Dict[str, Any] = {}
        self.current_profile: Optional[str] = None
        
        # 线程安全
        self._lock = Lock()
        
        # 默认配置
        self.default_system_config = self._get_default_system_config()
        self.default_user_strategy = UserStrategy()
        
        # 配置变更监听器
        self.config_change_listeners: List[callable] = []
        
        # 统计信息
        self.config_loads = 0
        self.config_saves = 0
        
        # 加载现有配置
        self._load_system_config()
        self._load_user_config()
        
        self.logger.info(f"配置管理器初始化完成，配置目录: {self.config_dir}")
    
    def _get_default_system_config(self) -> Dict[str, Any]:
        """获取默认系统配置"""
        return {
            "version": "1.0.0",
            "debug_mode": False,
            "log_level": "INFO",
            "auto_save_interval": 300,  # 自动保存间隔（秒）
            "max_retry_attempts": 3,
            "operation_timeout": 30,
            "screenshot_interval": 1.0,
            "ui_detection_confidence": 0.8,
            "mcts_enabled": True,
            "mcts_time_limit": 5.0,
            "mcts_iterations": 1000,
            "decision_confidence_threshold": 0.6,
            "task_cleanup_interval": 3600,  # 任务清理间隔（秒）
            "state_snapshot_interval": 600,  # 状态快照间隔（秒）
            "backup_retention_days": 30,
            "performance_monitoring": True,
            "error_reporting": True,
            "modules": {
                "perception": {
                    "enabled": True,
                    "template_matching_threshold": 0.8,
                    "ocr_enabled": True,
                    "scene_detection_interval": 2.0
                },
                "decision": {
                    "enabled": True,
                    "default_mode": "hybrid",
                    "evaluation_weights": {
                        "score_potential": 1.0,
                        "resource_efficiency": 0.8,
                        "synergy_bonus": 0.6,
                        "risk_assessment": 0.7,
                        "long_term_value": 0.5
                    }
                },
                "action": {
                    "enabled": True,
                    "input_delay_range": [0.1, 0.3],
                    "click_offset_range": [-3, 3],
                    "auto_recovery": True,
                    "safety_checks": True
                },
                "scheduler": {
                    "enabled": True,
                    "max_concurrent_tasks": 5,
                    "task_timeout": 300,
                    "priority_scheduling": True
                }
            }
        }
    
    def load_user_config(self, profile_name: Optional[str] = None) -> UserStrategy:
        """
        加载用户配置
        
        Args:
            profile_name: 配置文件名称，如果不提供则加载默认配置
            
        Returns:
            用户策略对象
        """
        try:
            with self._lock:
                if profile_name:
                    return self._load_profile(profile_name)
                else:
                    return self._load_user_config()
                    
        except Exception as e:
            self.logger.error(f"加载用户配置失败: {e}")
            return self.default_user_strategy
    
    def save_user_config(self, user_strategy: UserStrategy, 
                        profile_name: Optional[str] = None) -> bool:
        """
        保存用户配置
        
        Args:
            user_strategy: 用户策略对象
            profile_name: 配置文件名称，如果不提供则保存为默认配置
            
        Returns:
            是否保存成功
        """
        try:
            with self._lock:
                if profile_name:
                    return self._save_profile(user_strategy, profile_name)
                else:
                    return self._save_user_config(user_strategy)
                    
        except Exception as e:
            self.logger.error(f"保存用户配置失败: {e}")
            return False
    
    def load_system_config(self) -> Dict[str, Any]:
        """加载系统配置"""
        try:
            with self._lock:
                return self._load_system_config()
                
        except Exception as e:
            self.logger.error(f"加载系统配置失败: {e}")
            return self.default_system_config.copy()
    
    def save_system_config(self, config: Dict[str, Any]) -> bool:
        """保存系统配置"""
        try:
            with self._lock:
                # 验证配置
                if not self._validate_system_config(config):
                    raise ConfigManagerError("系统配置验证失败")
                
                self.current_system_config = config
                
                # 保存到文件
                with open(self.system_config_file, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)
                
                self.config_saves += 1
                self._notify_config_change("system", config)
                
                self.logger.info("系统配置已保存")
                return True
                
        except Exception as e:
            self.logger.error(f"保存系统配置失败: {e}")
            return False
    
    def get_config_value(self, key_path: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key_path: 配置键路径，使用点号分隔，如 "modules.perception.enabled"
            default: 默认值
            
        Returns:
            配置值
        """
        try:
            keys = key_path.split('.')
            value = self.current_system_config
            
            for key in keys:
                if isinstance(value, dict) and key in value:
                    value = value[key]
                else:
                    return default
            
            return value
            
        except Exception:
            return default
    
    def set_config_value(self, key_path: str, value: Any) -> bool:
        """
        设置配置值
        
        Args:
            key_path: 配置键路径
            value: 配置值
            
        Returns:
            是否设置成功
        """
        try:
            with self._lock:
                keys = key_path.split('.')
                config = self.current_system_config
                
                # 导航到目标位置
                for key in keys[:-1]:
                    if key not in config:
                        config[key] = {}
                    config = config[key]
                
                # 设置值
                config[keys[-1]] = value
                
                # 保存配置
                return self.save_system_config(self.current_system_config)
                
        except Exception as e:
            self.logger.error(f"设置配置值失败: {e}")
            return False
    
    def list_profiles(self) -> List[str]:
        """获取所有配置文件列表"""
        try:
            profiles = []
            for file_path in self.profiles_dir.glob("*.json"):
                profiles.append(file_path.stem)
            return sorted(profiles)
            
        except Exception as e:
            self.logger.error(f"获取配置文件列表失败: {e}")
            return []
    
    def delete_profile(self, profile_name: str) -> bool:
        """删除配置文件"""
        try:
            profile_file = self.profiles_dir / f"{profile_name}.json"
            if profile_file.exists():
                profile_file.unlink()
                self.logger.info(f"删除配置文件: {profile_name}")
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"删除配置文件失败: {e}")
            return False
    
    def export_config(self, export_path: str) -> bool:
        """导出完整配置"""
        try:
            export_data = {
                "export_time": datetime.now().isoformat(),
                "system_config": self.current_system_config,
                "user_strategy": self.current_user_strategy.to_dict() if self.current_user_strategy else None,
                "current_profile": self.current_profile,
                "available_profiles": self.list_profiles()
            }
            
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"配置已导出到: {export_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出配置失败: {e}")
            return False
    
    def import_config(self, import_path: str) -> bool:
        """导入配置"""
        try:
            with open(import_path, 'r', encoding='utf-8') as f:
                import_data = json.load(f)
            
            # 导入系统配置
            if "system_config" in import_data:
                self.save_system_config(import_data["system_config"])
            
            # 导入用户策略
            if "user_strategy" in import_data and import_data["user_strategy"]:
                user_strategy = UserStrategy.from_dict(import_data["user_strategy"])
                self.save_user_config(user_strategy)
            
            self.logger.info(f"配置已从 {import_path} 导入")
            return True
            
        except Exception as e:
            self.logger.error(f"导入配置失败: {e}")
            return False
    
    def add_config_change_listener(self, listener: callable):
        """添加配置变更监听器"""
        if listener not in self.config_change_listeners:
            self.config_change_listeners.append(listener)
    
    def remove_config_change_listener(self, listener: callable):
        """移除配置变更监听器"""
        if listener in self.config_change_listeners:
            self.config_change_listeners.remove(listener)
    
    def _load_user_config(self) -> UserStrategy:
        """加载用户配置文件"""
        try:
            if not self.user_config_file.exists():
                self.current_user_strategy = self.default_user_strategy
                return self.current_user_strategy
            
            with open(self.user_config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            self.current_user_strategy = UserStrategy.from_dict(config_data)
            self.config_loads += 1
            
            self.logger.debug("用户配置加载成功")
            return self.current_user_strategy
            
        except Exception as e:
            self.logger.warning(f"加载用户配置失败，使用默认配置: {e}")
            self.current_user_strategy = self.default_user_strategy
            return self.current_user_strategy
    
    def _save_user_config(self, user_strategy: UserStrategy) -> bool:
        """保存用户配置文件"""
        try:
            self.current_user_strategy = user_strategy
            
            with open(self.user_config_file, 'w', encoding='utf-8') as f:
                json.dump(user_strategy.to_dict(), f, indent=2, ensure_ascii=False)
            
            self.config_saves += 1
            self._notify_config_change("user", user_strategy)
            
            self.logger.debug("用户配置保存成功")
            return True
            
        except Exception as e:
            self.logger.error(f"保存用户配置失败: {e}")
            return False
    
    def _load_system_config(self) -> Dict[str, Any]:
        """加载系统配置文件"""
        try:
            if not self.system_config_file.exists():
                self.current_system_config = self.default_system_config.copy()
                return self.current_system_config
            
            with open(self.system_config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 合并默认配置和加载的配置
            self.current_system_config = self._merge_configs(
                self.default_system_config, config_data
            )
            
            self.config_loads += 1
            self.logger.debug("系统配置加载成功")
            return self.current_system_config
            
        except Exception as e:
            self.logger.warning(f"加载系统配置失败，使用默认配置: {e}")
            self.current_system_config = self.default_system_config.copy()
            return self.current_system_config
    
    def _load_profile(self, profile_name: str) -> UserStrategy:
        """加载指定配置文件"""
        try:
            profile_file = self.profiles_dir / f"{profile_name}.json"
            
            if not profile_file.exists():
                raise ConfigManagerError(f"配置文件不存在: {profile_name}")
            
            with open(profile_file, 'r', encoding='utf-8') as f:
                profile_data = json.load(f)
            
            user_strategy = UserStrategy.from_dict(profile_data)
            self.current_user_strategy = user_strategy
            self.current_profile = profile_name
            
            self.config_loads += 1
            self.logger.info(f"加载配置文件: {profile_name}")
            return user_strategy
            
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            raise ConfigManagerError(f"加载配置文件失败: {e}")
    
    def _save_profile(self, user_strategy: UserStrategy, profile_name: str) -> bool:
        """保存配置文件"""
        try:
            profile_file = self.profiles_dir / f"{profile_name}.json"
            
            with open(profile_file, 'w', encoding='utf-8') as f:
                json.dump(user_strategy.to_dict(), f, indent=2, ensure_ascii=False)
            
            self.config_saves += 1
            self.logger.info(f"保存配置文件: {profile_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")
            return False
    
    def _validate_system_config(self, config: Dict[str, Any]) -> bool:
        """验证系统配置"""
        try:
            # 检查必需的键
            required_keys = ["version", "modules"]
            for key in required_keys:
                if key not in config:
                    self.logger.error(f"缺少必需的配置键: {key}")
                    return False
            
            # 检查模块配置
            if "modules" in config:
                required_modules = ["perception", "decision", "action", "scheduler"]
                for module in required_modules:
                    if module not in config["modules"]:
                        self.logger.error(f"缺少模块配置: {module}")
                        return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            return False
    
    def _merge_configs(self, default: Dict[str, Any], loaded: Dict[str, Any]) -> Dict[str, Any]:
        """合并配置"""
        result = default.copy()
        
        for key, value in loaded.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def _notify_config_change(self, config_type: str, config_data: Any):
        """通知配置变更"""
        for listener in self.config_change_listeners:
            try:
                listener(config_type, config_data)
            except Exception as e:
                self.logger.error(f"配置变更监听器执行失败: {e}")
    
    def get_manager_statistics(self) -> Dict[str, Any]:
        """获取管理器统计信息"""
        return {
            "config_loads": self.config_loads,
            "config_saves": self.config_saves,
            "available_profiles": len(self.list_profiles()),
            "current_profile": self.current_profile,
            "has_user_strategy": self.current_user_strategy is not None,
            "config_directory": str(self.config_dir)
        }
    
    def get_manager_info(self) -> Dict[str, Any]:
        """获取管理器信息"""
        return {
            "config_directory": str(self.config_dir),
            "current_profile": self.current_profile,
            "available_profiles": self.list_profiles(),
            "system_config_loaded": bool(self.current_system_config),
            "user_strategy_loaded": self.current_user_strategy is not None,
            "statistics": self.get_manager_statistics()
        }
