# Gakumasu-Bot 调试模式配置预设
# 适用于开发调试，详细日志和可视化
# 注意: 此配置会产生大量日志和截图文件

system:
  log_level: DEBUG               # 详细日志
  language: ja
  screen_resolution: [1920, 1080]
  game_window_title: "gakumas"

dmm:
  dmm_player_path: ""  # 需要用户配置
  launch_timeout: 120
  auto_launch: false             # 手动启动，便于调试

performance:
  screenshot_interval: 1.0       # 较长间隔，便于观察
  action_delay_min: 0.2          # 较长延迟，便于观察
  action_delay_max: 0.5
  decision_timeout: 60.0         # 充足时间进行调试
  enable_gpu_acceleration: false
  max_worker_threads: 1          # 单线程，便于调试
  enable_performance_monitoring: true

template_matching:
  confidence_threshold: 0.6       # 较低阈值，便于测试
  enable_multi_scale: true
  scale_range: [0.7, 1.3]        # 宽泛的尺度范围
  scale_step: 0.1
  enable_template_cache: false   # 禁用缓存，确保实时加载
  matching_method: "TM_CCOEFF_NORMED"

ocr:
  enable_ocr: true
  languages: ['ja', 'en']
  confidence_threshold: 0.5       # 低阈值，便于测试
  use_gpu: false
  preprocessing:
    enable_denoising: true
    enable_contrast_enhancement: true
    enable_binarization: true

ai:
  enable_mcts: false             # 禁用MCTS，使用简单决策
  mcts_iterations: 100
  mcts_timeout: 5.0
  heuristic_weights:
    score: 1.0
    stamina: 0.8
    vigor: 0.6
    card_synergy: 0.4
    risk_factor: 0.2
  enable_decision_cache: false   # 禁用缓存
  enable_online_learning: false

error_handling:
  max_retries: 1                 # 快速失败，便于调试
  retry_interval: 1.0
  enable_auto_recovery: false    # 禁用自动恢复
  recovery_strategy: "abort"
  error_log_level: "verbose"
  save_error_screenshots: true
  error_screenshot_directory: "logs/debug_errors"

debug:
  enable_debug_mode: true        # 启用调试模式
  save_debug_screenshots: true   # 保存调试截图
  debug_screenshot_directory: "logs/debug_screenshots"
  enable_verbose_logging: true   # 详细日志
  show_match_visualization: true # 显示匹配可视化
  debug_info_interval: 2.0      # 频繁输出调试信息

# 调试专用配置
debug_advanced:
  # 保存每次截图
  save_all_screenshots: true
  screenshot_directory: "logs/all_screenshots"
  
  # 保存模板匹配结果
  save_match_results: true
  match_results_directory: "logs/match_results"
  
  # 保存决策过程
  save_decision_process: true
  decision_log_directory: "logs/decisions"
  
  # 性能分析
  enable_profiling: true
  profiling_output: "logs/performance_profile.txt"
