<template>
  <div class="game-state-monitor">
    <div class="panel-header">
      <h2>游戏状态监控</h2>
      <div class="header-actions">
        <el-button 
          type="primary" 
          :icon="Camera" 
          @click="captureScreenshot"
          :loading="loading.screenshot"
          size="small"
        >
          截图测试
        </el-button>
        <el-button 
          type="success" 
          :icon="View" 
          @click="getPreview"
          :loading="loading.preview"
          size="small"
        >
          获取预览
        </el-button>
      </div>
    </div>

    <el-row :gutter="20">
      <!-- 左侧：游戏截图预览 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>游戏画面预览</span>
              <el-tag v-if="previewImage" type="success">已连接</el-tag>
              <el-tag v-else type="info">无预览</el-tag>
            </div>
          </template>
          
          <div class="screenshot-container">
            <div v-if="previewImage" class="screenshot-wrapper">
              <img 
                :src="previewImage" 
                alt="游戏截图预览" 
                class="screenshot-image"
                @click="showFullscreen = true"
              />
              <div class="screenshot-overlay">
                <el-button 
                  type="primary" 
                  :icon="FullScreen"
                  @click="showFullscreen = true"
                  size="small"
                >
                  全屏查看
                </el-button>
              </div>
            </div>
            <div v-else class="no-screenshot">
              <el-empty description="暂无游戏画面" :image-size="120">
                <el-button type="primary" @click="getPreview">获取预览</el-button>
              </el-empty>
            </div>
          </div>

          <!-- 截图信息 -->
          <div v-if="screenshotInfo" class="screenshot-info">
            <el-divider content-position="left">截图信息</el-divider>
            <el-descriptions :column="2" size="small">
              <el-descriptions-item label="文件名">
                {{ screenshotInfo.filename }}
              </el-descriptions-item>
              <el-descriptions-item label="文件大小">
                {{ formatFileSize(screenshotInfo.file_size) }}
              </el-descriptions-item>
              <el-descriptions-item label="图像尺寸">
                {{ screenshotInfo.image_size?.width }}x{{ screenshotInfo.image_size?.height }}
              </el-descriptions-item>
              <el-descriptions-item label="截图时间">
                {{ formatTime(screenshotInfo.timestamp) }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧：游戏状态信息 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>游戏状态详情</span>
              <el-button 
                type="primary" 
                :icon="Refresh"
                @click="refreshGameState"
                :loading="loading.gameState"
                size="small"
              >
                刷新状态
              </el-button>
            </div>
          </template>

          <div v-if="gameState" class="game-state-content">
            <!-- 基本信息 -->
            <div class="state-section">
              <h4>基本信息</h4>
              <el-descriptions :column="1" size="small">
                <el-descriptions-item label="当前场景">
                  <el-tag type="primary">{{ gameState.current_scene || '未知' }}</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="语言设置">
                  {{ gameState.current_language || '未设置' }}
                </el-descriptions-item>
                <el-descriptions-item label="状态时间戳">
                  {{ formatTime(gameState.timestamp) }}
                </el-descriptions-item>
              </el-descriptions>
            </div>

            <!-- 资源状态 -->
            <div class="state-section">
              <h4>资源状态</h4>
              <div class="resource-bars">
                <div class="resource-item">
                  <div class="resource-label">
                    <span>体力值</span>
                    <span class="resource-value">{{ gameState.stamina || 0 }}</span>
                  </div>
                  <el-progress 
                    :percentage="Math.min((gameState.stamina || 0) / 100 * 100, 100)"
                    :color="getResourceColor(gameState.stamina, 100)"
                    :show-text="false"
                  />
                </div>
                <div class="resource-item">
                  <div class="resource-label">
                    <span>元气值</span>
                    <span class="resource-value">{{ gameState.vigor || 0 }}</span>
                  </div>
                  <el-progress 
                    :percentage="Math.min((gameState.vigor || 0) / 100 * 100, 100)"
                    :color="getResourceColor(gameState.vigor, 100)"
                    :show-text="false"
                  />
                </div>
              </div>
            </div>

            <!-- 游戏进度 -->
            <div class="state-section">
              <h4>游戏进度</h4>
              <el-descriptions :column="2" size="small">
                <el-descriptions-item label="当前分数">
                  <el-tag type="success">{{ formatNumber(gameState.score || 0) }}</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="当前周数">
                  <el-tag type="warning">第{{ gameState.current_week || 0 }}周</el-tag>
                </el-descriptions-item>
              </el-descriptions>
            </div>

            <!-- 手牌信息 -->
            <div v-if="gameState.hand && gameState.hand.length > 0" class="state-section">
              <h4>手牌信息</h4>
              <div class="hand-cards">
                <el-tag 
                  v-for="(card, index) in gameState.hand" 
                  :key="index"
                  class="card-tag"
                  type="info"
                >
                  {{ card.name || `卡牌${index + 1}` }}
                </el-tag>
              </div>
            </div>

            <!-- 偶像属性 -->
            <div v-if="gameState.idol_stats" class="state-section">
              <h4>偶像属性</h4>
              <div class="idol-stats">
                <div 
                  v-for="(value, key) in gameState.idol_stats" 
                  :key="key"
                  class="stat-item"
                >
                  <span class="stat-name">{{ getStatName(key) }}</span>
                  <el-progress 
                    :percentage="Math.min(value / 1000 * 100, 100)"
                    :color="getStatColor(key)"
                    :show-text="false"
                  />
                  <span class="stat-value">{{ value }}</span>
                </div>
              </div>
            </div>
          </div>

          <div v-else class="no-game-state">
            <el-empty description="暂无游戏状态信息" :image-size="80">
              <el-button type="primary" @click="refreshGameState">获取状态</el-button>
            </el-empty>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 全屏预览对话框 -->
    <el-dialog
      v-model="showFullscreen"
      title="游戏画面全屏预览"
      width="80%"
      center
    >
      <div class="fullscreen-image">
        <img v-if="previewImage" :src="previewImage" alt="全屏预览" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { 
  Camera, View, Refresh, FullScreen 
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useDebugConsole } from '../../composables/useDebugConsole.js'

// 使用调试控制台功能
const { 
  debugState, 
  getSystemStatus,
  testScreenshot,
  getScreenshotPreview
} = useDebugConsole()

// 响应式数据
const previewImage = ref(null)
const screenshotInfo = ref(null)
const showFullscreen = ref(false)

const loading = ref({
  screenshot: false,
  preview: false,
  gameState: false
})

// 计算属性
const gameState = computed(() => {
  return debugState.systemStatus.current_game_state
})

// 方法
const captureScreenshot = async () => {
  loading.value.screenshot = true
  try {
    const result = await testScreenshot({
      mode: 'window',
      format: 'png',
      quality: 90,
      save_to_disk: true,
      filename_prefix: 'debug_monitor'
    })
    
    screenshotInfo.value = result
    
    // 如果截图成功，尝试获取预览
    if (result.success) {
      await getPreview()
      ElMessage.success('截图测试成功')
    } else {
      ElMessage.error('截图测试失败: ' + result.error_message)
    }
  } catch (error) {
    ElMessage.error('截图测试失败')
    console.error('截图测试错误:', error)
  } finally {
    loading.value.screenshot = false
  }
}

const getPreview = async () => {
  loading.value.preview = true
  try {
    const imageUrl = await getScreenshotPreview()
    previewImage.value = imageUrl
    ElMessage.success('预览获取成功')
  } catch (error) {
    ElMessage.error('预览获取失败')
    console.error('预览获取错误:', error)
  } finally {
    loading.value.preview = false
  }
}

const refreshGameState = async () => {
  loading.value.gameState = true
  try {
    await getSystemStatus()
    ElMessage.success('游戏状态刷新成功')
  } catch (error) {
    ElMessage.error('游戏状态刷新失败')
  } finally {
    loading.value.gameState = false
  }
}

const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatTime = (timestamp) => {
  if (!timestamp) return '未知'
  return new Date(timestamp).toLocaleString()
}

const formatNumber = (num) => {
  if (!num) return '0'
  return num.toLocaleString()
}

const getResourceColor = (value, max) => {
  const percentage = (value || 0) / max
  if (percentage > 0.7) return '#67c23a'
  if (percentage > 0.3) return '#e6a23c'
  return '#f56c6c'
}

const getStatName = (key) => {
  const statNames = {
    'vocal': '歌唱',
    'dance': '舞蹈',
    'visual': '视觉',
    'mental': '精神',
    'technique': '技巧'
  }
  return statNames[key] || key
}

const getStatColor = (key) => {
  const colors = {
    'vocal': '#ff6b6b',
    'dance': '#4ecdc4',
    'visual': '#45b7d1',
    'mental': '#f9ca24',
    'technique': '#6c5ce7'
  }
  return colors[key] || '#95a5a6'
}

// 生命周期
onMounted(async () => {
  // 初始加载游戏状态
  await refreshGameState()
})
</script>

<style scoped>
.game-state-monitor {
  height: 100%;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e6e6e6;
}

.panel-header h2 {
  margin: 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.screenshot-container {
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.screenshot-wrapper {
  position: relative;
  max-width: 100%;
  cursor: pointer;
}

.screenshot-image {
  max-width: 100%;
  max-height: 300px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.screenshot-overlay {
  position: absolute;
  top: 10px;
  right: 10px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.screenshot-wrapper:hover .screenshot-overlay {
  opacity: 1;
}

.no-screenshot {
  width: 100%;
  padding: 40px 0;
}

.screenshot-info {
  margin-top: 15px;
}

.game-state-content {
  max-height: 500px;
  overflow-y: auto;
}

.state-section {
  margin-bottom: 20px;
}

.state-section h4 {
  margin: 0 0 10px 0;
  color: #409eff;
  font-size: 14px;
  font-weight: 600;
  border-bottom: 1px solid #e6e6e6;
  padding-bottom: 5px;
}

.resource-bars {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.resource-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.resource-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.resource-value {
  font-weight: 600;
  color: #409eff;
}

.hand-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.card-tag {
  margin: 0;
}

.idol-stats {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.stat-name {
  width: 60px;
  font-size: 12px;
  color: #606266;
}

.stat-value {
  width: 50px;
  text-align: right;
  font-size: 12px;
  font-weight: 600;
  color: #303133;
}

.no-game-state {
  padding: 40px 0;
  text-align: center;
}

.fullscreen-image {
  text-align: center;
}

.fullscreen-image img {
  max-width: 100%;
  max-height: 70vh;
  border-radius: 8px;
}
</style>
