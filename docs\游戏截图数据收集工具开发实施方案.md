# 游戏截图数据收集工具开发实施方案

**版本：** 1.0  
**日期：** 2025年7月27日  
**目标：** 基于 Gakumasu-Bot 现有架构开发完整的游戏截图数据收集工具

## 一、项目概述

### 1.1 项目背景
基于现有的 Gakumasu-Bot 项目架构，开发一个专门用于游戏截图数据收集的工具。该工具将复用项目中现有的截图功能，并提供友好的 Web UI 界面，方便用户进行游戏界面截图收集和管理。

### 1.2 核心需求
1. **复用现有截图功能**：基于 `src/modules/perception/screen_capture.py` 和 `enhanced_screen_capture.py`
2. **单次截图功能**：提供手动截图功能，用于收集游戏运行所需的各项截图数据
3. **游戏界面预览**：实时预览游戏窗口，方便用户确认截图内容
4. **多种截图模式**：支持全屏、窗口、区域选择等多种截图模式
5. **Web UI 集成**：集成到现有的前端界面中，作为工具子页面的一项

### 1.3 技术架构
```
前端 Vue.js 界面 ←→ FastAPI 后端 API ←→ 截图收集器 ←→ 现有截图模块
       ↓                    ↓                ↓              ↓
   用户交互界面        WebSocket 实时通信    任务管理        屏幕捕获
```

## 二、现有架构分析

### 2.1 现有截图功能分析
基于代码分析，项目已具备以下截图相关功能：

**核心截图模块**：
- `ScreenCapture` 类：基础屏幕捕获功能
- `EnhancedScreenCapture` 类：增强的智能屏幕捕获
- `BackgroundModeManager` 类：后台模式截图管理

**主要功能**：
- 游戏窗口检测和定位
- 全屏和区域截图
- 前台/后台截图模式
- 截图保存和管理

### 2.2 现有 Web 架构分析
**后端架构**：
- FastAPI 主服务：`src/web/main.py`
- WebAPIAdapter：连接 Web 界面和核心系统
- WebSocket 支持：实时状态更新
- 与 Scheduler、ConfigManager、StateManager 集成

**前端架构**：
- Vue.js + Element Plus
- 路由系统：支持多页面导航
- WebSocket 客户端：实时通信
- 现有工具页面结构

### 2.3 集成点分析
1. **后端集成点**：
   - 在 `WebAPIAdapter` 中添加截图相关方法
   - 在 FastAPI 路由中添加截图 API 端点
   - 利用现有的 WebSocket 连接进行实时更新

2. **前端集成点**：
   - 在现有路由中添加截图工具页面
   - 在侧边栏导航中添加截图工具入口
   - 复用现有的 API 服务和 WebSocket 连接

## 三、详细设计方案

### 3.1 后端架构设计

#### 3.1.1 截图收集器模块
```python
# src/modules/screenshot_collector.py
class ScreenshotCollector:
    """截图数据收集器"""
    
    def __init__(self):
        self.screen_capture = EnhancedScreenCapture()
        self.storage_manager = ScreenshotStorageManager()
        self.task_manager = ScreenshotTaskManager()
    
    async def capture_single_shot(self, config: ScreenshotConfig) -> ScreenshotResult
    async def start_preview_stream(self) -> AsyncGenerator[bytes, None]
    async def get_capture_history(self) -> List[ScreenshotRecord]
    async def delete_screenshot(self, screenshot_id: str) -> bool
```

#### 3.1.2 API 接口设计
```python
# 在 src/web/main.py 中添加的路由
@app.get("/api/v1/screenshot/preview")
async def get_screenshot_preview()

@app.post("/api/v1/screenshot/capture")
async def capture_screenshot(config: ScreenshotConfig)

@app.get("/api/v1/screenshot/history")
async def get_screenshot_history()

@app.delete("/api/v1/screenshot/{screenshot_id}")
async def delete_screenshot(screenshot_id: str)
```

### 3.2 前端架构设计

#### 3.2.1 页面结构
```
截图工具页面 (ScreenshotTool.vue)
├── 控制面板 (ControlPanel.vue)
│   ├── 截图模式选择
│   ├── 区域选择工具
│   └── 快速操作按钮
├── 预览区域 (PreviewArea.vue)
│   ├── 游戏窗口实时预览
│   ├── 区域选择覆盖层
│   └── 预览控制工具
└── 历史记录面板 (HistoryPanel.vue)
    ├── 截图历史列表
    ├── 文件预览
    └── 批量操作工具
```

#### 3.2.2 路由集成
```javascript
// 在 frontend/src/router/index.js 中添加
{
  path: '/screenshot-tool',
  name: 'ScreenshotTool',
  component: () => import('../views/ScreenshotTool.vue')
}
```

### 3.3 数据流设计
```
用户操作 → Vue 组件 → API 调用 → WebAPIAdapter → ScreenshotCollector → 截图模块
                ↓
            WebSocket ← 状态更新 ← 任务执行结果
```

## 四、开发实施计划

### 阶段1：项目分析与设计 (当前阶段)
- [x] 分析现有架构和截图功能
- [x] 设计整体技术方案
- [x] 创建详细的开发实施方案
- [ ] 用户确认设计方案

### 阶段2：后端核心功能开发
- [ ] 开发 ScreenshotCollector 模块
- [ ] 扩展 WebAPIAdapter 添加截图相关方法
- [ ] 在 FastAPI 中添加截图 API 路由
- [ ] 实现 WebSocket 实时预览功能

### 阶段3：前端界面开发
- [ ] 创建截图工具 Vue 组件
- [ ] 实现实时预览功能
- [ ] 开发区域选择工具
- [ ] 集成到现有路由和导航中

### 阶段4：系统集成与测试
- [ ] 集成测试前后端功能
- [ ] 性能优化和错误处理
- [ ] 用户体验优化
- [ ] 兼容性测试

### 阶段5：文档编写与交付
- [ ] 编写技术文档
- [ ] 创建用户使用指南
- [ ] 清理测试文件
- [ ] 项目交付

## 五、技术实现要点

### 5.1 关键技术挑战
1. **实时预览性能**：需要优化截图频率和传输效率
2. **区域选择交互**：在 Web 界面中实现精确的区域选择
3. **文件管理**：截图文件的存储、组织和清理
4. **错误处理**：游戏窗口检测失败等异常情况处理

### 5.2 性能优化策略
1. **预览流优化**：使用适当的帧率和压缩比例
2. **缓存机制**：缓存常用的截图配置和历史记录
3. **异步处理**：所有截图操作使用异步处理
4. **资源管理**：及时释放不需要的图像资源

### 5.3 用户体验设计
1. **直观的界面**：清晰的操作流程和视觉反馈
2. **快速响应**：最小化用户等待时间
3. **错误提示**：友好的错误信息和解决建议
4. **操作记录**：保存用户的操作历史和偏好设置

---

**下一步行动**：等待用户确认设计方案后，开始阶段2的后端核心功能开发。
